<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
if (!defined('__APP__')) { define('__APP__','App\Http\Controllers'); }

Route::middleware(['auth:custom-basic'])->group(function () {
  Route::get('/ajax/get_member_setting',    __APP__.'\ajax\Member@get_member_setting'); //取得會員新增所需設定
  Route::post('/ajax/insert_member',    __APP__.'\ajax\Member@insert_member'); //新增會員
  Route::get('/ajax/get_order_setting',    __APP__.'\ajax\Order@get_order_setting'); //取得訂單新增所需設定
  Route::post('/ajax/insert_order',    __APP__.'\ajax\Order@insert_order'); //新增會員
});
