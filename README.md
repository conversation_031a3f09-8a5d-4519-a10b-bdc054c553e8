# 天脈購物車系統，使用 Laravel 9

## 各位接手的~有空就練練重構吧～難得有緣遇到屎山代碼..

## 重構計劃

-   [x] 重建 migration & seeder
-   [ ] 拆分出 Service, Repository
-   [ ] 將 DB 查詢重構成 Model
-   [ ] 拔掉錯誤的裝飾器，解耦複雜依賴，至少讓 Controller 可測試
-   [ ] 前端重寫，拔掉自行 define 的歷史遺毒
-   [ ] Laravel 升級至最新版..不曉得有沒可能走到這一步

## Migration

### 建立日期 2025-07-02

-   ex: 從真實 table 建立 migrateion

    ```bash
        # see: https://github.com/kitloong/laravel-migrations-generator
    php artisan migrate:generate
    php artisan migrate:generate --connection="main_db"
    ```

-   connection mysql:
    ```bash
    php artisan migrate --path=database/migrations/shop
    ```
-   connection main_db:
    ```bash
    php artisan migrate --path=database/migrations/shop_admin --database=main_db
    php artisan migrate:rollback --database=main_db
    ```

## Seeder

### 建立日期 2025-07-16

-   ex: 從真實資料建立 seeder
    ```bash
        # see https://github.com/tyghaykal/laravel-seeder-generator
        # 建立資料表about_story的seeder..output at database/seeders/Tables/shop/AboutStorySeeder.php
    % php artisan seed:generate --table-mode --tables about_story --output=shop
    ```
