/* 首頁總覽開始 */
    .bi-pencil-square{
        position: absolute;
        right: 0;
        top: 0;
    }


    .indexview{
        margin:0px auto;
        border:2px #9D9D9D solid;
    }
    .indexview td{
        padding:5px;
        border:2px #9D9D9D solid;
        position: relative;
        text-align: center;
    }
    .indexview td:nth-child(1){
        text-align: left;
    }
    .indexview td:nth-child(2){
        text-align: left;
    }
    .indexview td:nth-child(3){
    }
    .indexview td:nth-child(4){    
    }   
    .indexview td:nth-child(5){
        text-align: left;
    }
    .indexview td div{
        border:2px solid #000000;    
        text-align: center;
        font-size:14px;
        position: relative;
        margin:10px;
        padding:1px;
        display:inline-flex;
        align-items:center;
        justify-content:center;
    }
    .indexview td div span{
        /*position: absolute;*/
        right: 0;
        cursor: pointer;
    }
    .indexview td div input{
        text-align:left;
        border:none;
        border-bottom:1px solid #000000;
    }
    .indexview td div textarea{
        width:100%;
        height:100%;
        border:none;
        resize: none
    }
    .iconbox{
        list-style: none;
        padding:0px
    }
    .iconbox li{
        margin:5px;
    }
    .iconbox li img{
        width:40px;
    }

    /* 更換資訊 */
        #block{ 
            position: fixed; 
            top:0; 
            left:0; 
            height:100%; 
            width:100%; 
            background-color:#000000; 
            opacity: .5;
            z-index:998;
        }
        #block{
            visibility: hidden;
        }

        /* 共用區域設定 */
            .send-btn{
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                margin:2%;
            }
            .send-btn:hover{    
                background: #BEBEBE;
            }
            .bi-x-circle-fill{
                font-size:22px;
                position:absolute;
                right:0;
                top: 0;
                cursor:pointer;
                padding:5px;
                opacity: .8;
            }
            .bi-x-circle-fill:hover{
                opacity: 1;
            }
            .infobox{
                /* position: absolute; */
                padding: 5px;
                /* width: 70%; */
                border: 2px solid #000000;
                text-align:center
            }
            .infobox input{
                width:calc(100% - 50px);
                border:none;
                border-bottom:1px solid #000000;
            }
            .changeInfo{
                /* position: fixed;  */
                top: 5%;left: 0;right: 0;bottom: 0;
                width: 100%;
                height: 100%;
                z-index:999; 
            } 
            .changebody{
                position: relative;
                background-color:white; 
                width: 96%;
                margin: auto;
                padding: 1rem;
            }
            @media (min-width: 992px) {
                .changebody{
                    width: 650px;
                 }
            }     

        /* 更換一張圖片 */
            #change-image-box .upl{
                width: 100%;
                height: 100%;
                z-index: 5;
                opacity:0;
                cursor:pointer;
            }
            #change-image-box .image-box {
                height: calc(100% - 200px);
                background-color: #fff;
                text-align: center;
                border: 2px solid #000000;
                
            }
            #change-image-box p{
                display: block;
                width: 100%;
            }

        /* 更換多張圖片 */
          
            #changeGroupBox .content_area_img{
                display: flex;
                align-items: center;
                flex-direction: column;
            }
            #change-group-box .upl1{
                position: absolute;
                width: 100%;
                height: 100%;
                z-index: 5;
                opacity:0;
                cursor:pointer;
            }
            #change-group-box .image-box {
                margin:1.5rem 0 0;
                background-color: #fff;
                text-align: center;
                border: 2px solid #000000;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }
            #change-group-box .sm-group-box {
                width:70%;
                height:80px;
                display: flex;
                justify-content: center;
            }    
            #change-group-box .sm-image-box,
            #slideShow .sm-image-box {
                background-color: #fff;
                border: 2px solid #000000;
                margin-top: -1px;
                margin-left: -1px;
                display: inline-flex;
                align-items: center;
                cursor: pointer;
                flex-basis: 20%;
                height: 100%;
                min-height: 50px;
            }
            #change-group-box .sm-image-box img{
                max-width:100%;
                max-height:100%;
            } 
            #change-group-box .infobox{
                position: static;
                width: 100%;
                border: 2px solid #000000;
                padding: 0.5rem;
                margin-bottom: 1rem;
            }
            #change-group-box .infobox .item{
                display: flex;
                margin-bottom: 5px;
                margin-right: 5px;
            }
             

        /* 更換資訊 */
         
            #change-info-box form{
                position: relative;
                top:8%;
            }
            #change-info-box .infobox{
                position: relative;
                left:15%;
                padding: 5px;
                margin:4% 0;
                width: 70%;
                border: 2px solid #000000;
                text-align:left
            }
            #change-info-box .infobox input{
                width:calc(100% - 70px);
                border:none;
                border-bottom:1px solid #000000;
            }

        /* 限時搶購 */
      
        
            #change-limit-box label{
                margin-right:5px;
            }    
            #change-limit-box form input{
                border:none;
                border-bottom:1px solid #000000;
            }

           

        /* 其他商品 */
            #mask #block{
                visibility: initial;
            }
            #change-product-box{ 
                position: fixed; 
                top: 0;left: 0;right: 0;bottom: 0;
                width: 100%;
                height: 100%;
                z-index:999; 
                /*visibility: hidden; */
                padding:1.5rem;
            }
            
            #change-product-box label{
                margin-right:5px;
            }    
            #change-product-box form input{
                border:none;
                border-bottom:1px solid #000000;
            }
           

        /* 推薦圖文 */
            #change-imgtext-box{ 
                position: fixed; 
                top:calc(50% - 300px ); 
                left:calc(50% - 250px ); 
                width: 500px;
                height: 600px;
                background-color:white; 
                z-index:999; 
                /*visibility: hidden; */
            }
            #change-imgtext-box .upl{
                position: absolute;
                width: 100%;
                height: 100%;
                z-index: 1;
                opacity:0;
                cursor:pointer;
            }
            #change-imgtext-box .image-box {
                position: relative;
                margin-top: 5%;
                left: 15%;
                width: 70%;
                height: 250px;
                background-color: #fff;
                text-align: center;
                border: 2px solid #000000;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }
            #change-imgtext-box p{
                position: relative;
                display: block;
                left: 15%;
                margin-top:5px;
            }

       
        
    /* 更換資訊結束 */
/* 首頁總覽結束 */