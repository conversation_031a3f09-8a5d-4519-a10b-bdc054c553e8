<?php
// Cpanel 自動執行設定處: Cron Jobs
// 請設定『每小時』執行自動程式，參考Cron命令：/usr/local/bin/php /home/<USER>/full.sprlight.net/Auto.php
// 以上命令需修改二處：
// 1. /usr/local/bin/php 需改成Cpanel中「PHP command examples」所寫的那段，對應到主機的php路徑
// 2. /home/<USER>/full.sprlight.net/Auto.php 需改成此主機放置此網站的Auto.php的路徑
date_default_timezone_set('Asia/Taipei');

// 讀取.env設定並存於$_ENV
require __DIR__ . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();
$main_url = $_ENV['APP_URL']; /*購物車主網址(需修改，且注意是否有強制跳轉https，須設定正確協議方式)*/

/*活動提醒信，每小時執行一次*/
$autoTime_sendActRemindMail = 'Y-m-d H:00:00'; /*預設每個整點*/
auto_act($main_url . "/index/examination/send_act_remind_mail", $autoTime_sendActRemindMail);

/*電子發票開立，每日執行一次*/
$autoTime_invoiceCreate = 'Y-m-d 09:00:00'; /*(預設每日早上9點)*/
auto_act($main_url . "/ajax/invoice_create/invoice", $autoTime_invoiceCreate);

/*取消未付款訂單，每日執行一次(暫無此功能)*/
// $autoTime_invoiceCreate = 'Y-m-d 00:00:00'; /*(預設每日凌晨0點)*/
// auto_act($main_url."/order/orderform/check_unpaid", $autoTime_invoiceCreate);

/*處理月分紅再分配，每月1日早上執行一次*/
#$autoTime_month_distribution = 'Y-m-01 04:00:00';
#auto_act($main_url . "/ajax/bonus/auto_month_distribution", $autoTime_month_distribution);

/*處理GV未達標，每月5日早上6點執行一次*/
/*!!!暫時關閉!!! 免得誤清空會員點數*/
// $autoTime_gv_check = 'Y-m-01 06:00:00'; /*(每月5日早上6點)*/
// auto_act($main_url."/ajax/bonus/auto_gv_check", $autoTime_gv_check);

/*處理會員上傳獎勵，每日早上7點執行一次*/
$autoTime_share_article_task = 'Y-m-d 07:00:00'; /*(每天早上7點)*/
auto_act($main_url . "/ajax/bonus/auto_share_article_task", $autoTime_share_article_task);

/*
$url->執行網址
$act_time->執行時間，預設每年每月每天每小時的0分0秒
*/
function auto_act($url, $act_time = 'Y-m-d H:00:00')
{
    if (!$url) {
        return;
    } /* 沒網址就不執行 */
    echo "\n項目:" . $url . "=>";

    $act_time = str_replace('Y', date('Y'), $act_time);
    $act_time = str_replace('m', date('m'), $act_time);
    $act_time = str_replace('d', date('d'), $act_time);
    $act_time = str_replace('H', date('H'), $act_time);
    $act_time = str_replace('i', date('i'), $act_time);
    // echo $act_time."\n";
    $act_time = strtotime($act_time);
    $diff_min = abs((time() - $act_time) / 60);
    // echo $diff_min."\n";

    if ($diff_min > 3) {
        echo "時間不符\n";
        return;
    } /* 差異大於3分鐘就不執行 */
    echo "執行:\n";

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
    if (! empty($data)) {
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    }
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
    $output = curl_exec($curl);
    curl_close($curl);
    echo $output;
}
