<?php

// +----------------------------------------------------------------------
// 綠界相關參數
// +----------------------------------------------------------------------
include(app_path().'/Services/ThirdParty/Ecpay.Logistic.Integration.php');
$ecpay_data = [
  'MerchantID' => Env('ecpay.MerchantID', '2000132'),
  'PlatformID' => Env('ecpay.PlatformID', ''),

  /*金流設定*/
  'HashKey' => Env('ecpay.HashKey', '5294y06JbISpM5x9'),
  'HashIV' => Env('ecpay.HashIV', 'v77hoKGq4kWxNNIS'),
  'CreditInstallment' => Env('ecpay.CreditInstallment', '3,6,12'), //分期期數 0為不分期 3,6,12為分3,6,12期

  /*物流設定*/
  'Logistic_HashKey' => Env('ecpay.Logistic_HashKey', '5294y06JbISpM5x9'),
  'Logistic_HashIV' => Env('ecpay.Logistic_HashIV', 'v77hoKGq4kWxNNIS'),
  'shippable_supermarket' => ['全家取貨','7-11取貨','萊爾富取貨'], /*有配合的物流-超商*/
  'shippable' => ['到店取貨','宅配', '全家取貨','7-11取貨','萊爾富取貨'], /*有配合的物流*/

  /*電子發票設定*/
  'HashKey_invoice' => Env('ecpay.HashKey_invoice', 'ejCk326UnaZWKisg'),
  'HashIV_invoice' => Env('ecpay.HashIV_invoice', 'q9jcZX8Ib9LM8wYk'),
];
/*環境判斷*/
if ($ecpay_data['MerchantID'] == EcpayTestMerchantID::B2C or $ecpay_data['MerchantID'] == EcpayTestMerchantID::C2C) {
  /*測試*/
  /*金流-請求網址*/
  $ecpay_data['ServiceURL'] = 'https://payment-stage.ecpay.com.tw/Cashier/AioCheckOut/V5';
  /*電子發票-請求網址*/
  $ecpay_data['Invoice_Url'] = 'https://einvoice-stage.ecpay.com.tw/';
}else{ 
  /*正式*/
  /*金流-請求網址*/
  $ecpay_data['ServiceURL'] = 'https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5';
  /*電子發票-請求網址*/
  $ecpay_data['Invoice_Url'] = 'https://einvoice.ecpay.com.tw/';
}
return $ecpay_data;