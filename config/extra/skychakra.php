<?php

// +----------------------------------------------------------------------
// 購物車參數
// 此區的參數將被帶入view中，可直接透過{$鍵}顯示內容，ex:{$company_name}
// +----------------------------------------------------------------------
$skychakra_data = [
    'member_system' => 10,          /*系統帳戶id*/
    'member_month_divided' => 11,   /*月分紅帳戶id*/

    'check_buyer_partner_bonus_type_id' => 2, /*「合夥批發回饋」要求購買者「會員級別」低於此級*/
    
    'currency_to_site' => [ /*幣別對應分站*/
        'USD'=> 'B',    /*美金*/
        'NT' => 'A',    /*台幣*/
        'TWD' => 'A',   /*台幣*/
        'RMB' => 'C',   /*人民幣*/
        'CNY' => 'C',   /*人民幣*/
        'HK' => 'D',    /*港幣*/
    ],
    'exchange_rate_set' => [    /*匯率組合*/
        'USD'=> 1,      /*美金換美金*/
        'NT' => 30,     /*美金換台幣*/
        'TWD' => 30,    /*美金換台幣*/
        'RMB' => 7.2,   /*美金換人民幣*/
        'CNY' => 7.2,   /*美金換人民幣*/
        'HK' => 7.85,   /*美金換港幣*/
    ],
    'show_dollar_set' => [  /*顯示幣別文字組合*/
        'USD'=> 'USD$',     /*美金簡稱+符號*/
        'NT' => 'NT$',      /*台幣簡稱+符號*/
        'TWD' => 'NT$',     /*台幣簡稱+符號*/
        'RMB' => 'RMB¥',    /*人民幣簡稱+符號*/
        'CNY' => 'RMB¥',    /*人民幣簡稱+符號*/
        'HK' => 'HK$',      /*港幣簡稱+符號*/
    ],
];
/* 根據 shop.dollar 所設定的值算出 exchange_rate */
$dollar = Env('shop.dollar', 'NT');
$skychakra_data['exchange_rate'] = $skychakra_data['exchange_rate_set'][$dollar] ?? 1;
return $skychakra_data;
