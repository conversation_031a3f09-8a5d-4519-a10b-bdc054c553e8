<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('navigation_menus', function (Blueprint $table) {
            $table->id();
            $table->string('menu_key')->unique()->comment('選單識別碼');
            $table->string('menu_name')->comment('選單名稱');
            $table->string('menu_url')->comment('選單連結');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('狀態 1:啟用 0:停用');
            $table->string('target', 10)->default('_self')->comment('開啟方式 _self:當前視窗 _blank:新視窗');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('navigation_menus');
    }
};
