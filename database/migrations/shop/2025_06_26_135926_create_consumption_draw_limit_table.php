<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('consumption_draw_limit', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('price')->nullable()->comment('滿多少元抽一次');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('consumption_draw_limit');
    }
};
