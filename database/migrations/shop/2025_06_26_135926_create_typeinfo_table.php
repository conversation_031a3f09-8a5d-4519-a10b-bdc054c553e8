<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('typeinfo', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('parent_id')->comment('下掛分館id');
            $table->integer('branch_id')->nullable()->default(0)->comment('下掛分類id,0則代表下掛於分館');
            $table->string('title', 32)->nullable();
            $table->string('pic', 128)->nullable();
            $table->integer('start')->nullable()->default(0);
            $table->integer('end')->nullable()->default(0);
            $table->integer('limit_num')->default(0);
            $table->integer('order_id');
            $table->text('webtype_keywords');
            $table->text('webtype_description');
            $table->tinyInteger('online')->default(1)->comment('狀態 0.隱藏 1.開啟 2.關閉');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('typeinfo');
    }
};
