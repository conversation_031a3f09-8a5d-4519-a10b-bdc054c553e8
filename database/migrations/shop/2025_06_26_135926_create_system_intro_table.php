<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('system_intro', function (Blueprint $table) {
            $table->increments('system_id');
            $table->string('system_title')->nullable()->comment('系統名稱');
            $table->string('system_version')->nullable()->comment('系統版本');
            $table->string('system_note')->nullable()->comment('系統備註');
            $table->string('front_end')->nullable()->comment(' 前台開發程式');
            $table->string('back_end')->nullable()->comment(' 後台開發程式');
            $table->string('php_version')->nullable()->comment('php版本');
            $table->string('sql_version')->nullable()->comment('sql版本');
            $table->string('closing_date')->nullable()->comment(' 結案時間');
            $table->text('note')->nullable()->comment('備註');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('system_intro');
    }
};
