<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('excel_reply', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->string('pro_id', 20)->nullable();
            $table->string('product_code');
            $table->string('product_name')->nullable();
            $table->string('product_brand')->nullable();
            $table->text('pic')->nullable();
            $table->integer('status')->nullable()->default(0);
            $table->string('account_number', 32)->nullable();
            $table->string('account_name')->nullable();
            $table->date('createtime');
            $table->date('regtime')->nullable();
            $table->string('tax_ID_number', 10)->nullable();
            $table->date('buytime')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('excel_reply');
    }
};
