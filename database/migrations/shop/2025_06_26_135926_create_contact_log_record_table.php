<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_log_record', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('contact_log_id')->comment('對應回函id');
            $table->text('message')->nullable()->comment('留言內容');
            $table->string('admin_type', 16)->comment('登入類型');
            $table->integer('admin_id')->comment('留言者id');
            $table->string('datetime', 10)->comment('留言時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contact_log_record');
    }
};
