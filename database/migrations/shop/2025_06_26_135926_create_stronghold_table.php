<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stronghold', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('title', 32)->nullable();
            $table->text('content')->nullable();
            $table->integer('order_id')->nullable()->default(999);
            $table->tinyInteger('online')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stronghold');
    }
};
