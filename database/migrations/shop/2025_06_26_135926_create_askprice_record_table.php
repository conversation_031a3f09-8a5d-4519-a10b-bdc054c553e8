<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('askprice_record', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('askprice_id')->comment('對應askprice id');
            $table->text('ask')->comment('詢問');
            $table->string('ask_time', 32)->comment('詢問時間');
            $table->text('response')->comment('回覆');
            $table->string('response_time', 32)->comment('回覆時間');
            $table->integer('price')->comment('價格');
            $table->integer('price_final')->nullable()->comment('賣家終定價');
            $table->string('expired_date', 32)->nullable()->comment('截止日期');
            $table->boolean('status')->default(false)->comment('處理狀態');
            $table->integer('num')->default(1)->comment('數量');
            $table->boolean('agree')->default(false)->comment('是否同意 0.未選 1.同意');
            $table->boolean('bought')->default(false)->comment('已購買 0.否 1.是');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('askprice_record');
    }
};
