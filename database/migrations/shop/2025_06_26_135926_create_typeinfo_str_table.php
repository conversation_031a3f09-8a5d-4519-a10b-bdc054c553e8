<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('typeinfo_str', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('pic', 128)->nullable();
            $table->text('sub_pics')->nullable()->comment('附圖');
            $table->string('title', 32)->nullable();
            $table->text('content')->nullable();
            $table->string('url')->nullable();
            $table->timestamp('time')->useCurrent();
            $table->boolean('online')->default(true);
            $table->string('parent_id', 10)->nullable();
            $table->integer('orders')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('typeinfo_str');
    }
};
