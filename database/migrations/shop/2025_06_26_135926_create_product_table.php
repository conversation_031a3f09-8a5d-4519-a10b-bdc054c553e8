<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product', function (Blueprint $table) {
            $table->comment('分館列表');
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->string('title', 32)->nullable();
            $table->string('pic', 128)->nullable();
            $table->string('pic_icon')->nullable();
            $table->string('index_adv01_pic', 128)->nullable();
            $table->string('index_adv01_link', 128)->nullable();
            $table->string('index_adv02_pic', 128)->nullable();
            $table->string('index_adv02_link', 128)->nullable();
            $table->string('index_adv03_pic', 128)->nullable();
            $table->string('index_adv03_link', 128)->nullable();
            $table->string('index_adv04_pic', 128)->nullable();
            $table->string('index_adv04_link', 128)->nullable();
            $table->string('index_adv05_pic', 128)->nullable();
            $table->string('index_adv05_link', 128)->nullable();
            $table->string('index_adv06_pic', 128)->nullable();
            $table->string('index_adv06_link', 128)->nullable();
            $table->string('index_adv07_pic', 128)->nullable();
            $table->string('index_adv07_link', 128)->nullable();
            $table->string('inner_adv01_pic', 128)->nullable();
            $table->string('inner_adv01_link', 128)->nullable();
            $table->string('inner_adv02_pic', 128)->nullable();
            $table->string('inner_adv02_link', 128)->nullable();
            $table->text('content')->nullable();
            $table->integer('order_id')->nullable()->default(999);
            $table->tinyInteger('online')->default(1)->comment('0.隱藏 1.開啟 2.關閉');
            $table->boolean('ad_online')->default(false)->comment('顯示分館廣告 0.否 1.是');
            $table->text('webtype_keywords')->nullable();
            $table->text('webtype_description')->nullable();
            $table->text('recommend')->nullable()->comment('推建商品(填入商品id逗點分隔)');
            $table->boolean('show_on_nav')->default(false)->comment('顯示於選單');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product');
    }
};
