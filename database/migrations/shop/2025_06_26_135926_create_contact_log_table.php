<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_log', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->string('name', 32)->nullable();
            $table->string('phone', 32)->nullable();
            $table->integer('homephone')->nullable();
            $table->string('email', 32)->nullable();
            $table->string('type', 32)->nullable();
            $table->text('order_number')->nullable()->comment('訂單編號');
            $table->text('prod_id')->nullable()->comment('商品ID或名稱');
            $table->string('freeTime', 256)->default('皆可');
            $table->string('content', 4096)->nullable();
            $table->integer('status')->default(0);
            $table->text('remessage')->nullable();
            $table->timestamp('time')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contact_log');
    }
};
