<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('act', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->string('number', 32);
            $table->string('name', 32);
            $table->text('img')->nullable()->comment('圖片');
            $table->string('ps', 2048)->nullable();
            $table->string('content', 2048)->nullable();
            $table->integer('start');
            $table->integer('end');
            $table->integer('location')->default(0);
            $table->integer('type');
            $table->tinyInteger('online1')->nullable();
            $table->integer('condition1')->nullable();
            $table->float('discount1', 10, 0)->nullable();
            $table->integer('online2')->nullable();
            $table->integer('condition2')->nullable();
            $table->float('discount2', 10, 0)->nullable();
            $table->integer('online3')->nullable();
            $table->integer('condition3')->nullable();
            $table->float('discount3', 10, 0)->nullable();
            $table->tinyInteger('online')->default(0);
            $table->integer('act_type')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('act');
    }
};
