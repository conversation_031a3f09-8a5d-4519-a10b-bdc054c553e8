<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('admin_info', function (Blueprint $table) {
            $table->integer('id')->primary();
            $table->text('favicon')->nullable()->comment('分頁縮圖favicon.ico');
            $table->text('customer_name')->nullable();
            $table->text('system_name')->nullable();
            $table->text('marketing_name')->nullable();
            $table->text('url')->nullable();
            $table->text('tel')->nullable();
            $table->text('email')->nullable();
            $table->text('address')->nullable();
            $table->text('customer_logo')->nullable();
            $table->text('system_logo')->nullable();
            $table->text('marketing_logo')->nullable();
            $table->text('success_logo')->nullable()->comment('成功提示訊息圖片');
            $table->text('error_logo')->nullable()->comment('失敗提示訊息圖片');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_info');
    }
};
