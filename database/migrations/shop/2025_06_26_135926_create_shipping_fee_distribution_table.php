<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_fee_distribution', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id');
            $table->integer('shipping_fee_id');
            $table->integer('price')->comment('運費金額');
            $table->integer('free_rule')->default(999999999)->comment('滿額免運');
            $table->boolean('online')->default(true)->comment('狀態 0.停用 1.啟用');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_fee_distribution');
    }
};
