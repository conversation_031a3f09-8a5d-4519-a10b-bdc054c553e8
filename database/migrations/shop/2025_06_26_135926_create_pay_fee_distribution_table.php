<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pay_fee_distribution', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id');
            $table->integer('pay_fee_id');
            $table->boolean('online')->default(true)->comment('狀態 0.停用 1.啟用');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pay_fee_distribution');
    }
};
