<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('member_article', function (Blueprint $table) {
            $table->comment('會員文章');
            $table->integer('id', true);
            $table->integer('user_id')->default(0)->comment('所屬會員');
            $table->boolean('show_status')->default(true)->comment('文章狀態(0.隱藏 1.顯示)(使用者控制)');
            $table->boolean('show')->default(true)->comment('狀態(0.隱藏 1.顯示)(管理者控制)');
            $table->integer('orders')->default(0)->comment('排序(管理者控制)');
            $table->char('name', 64)->comment('文章名稱');
            $table->string('img', 128)->default('')->comment('文章圖片路徑');
            $table->text('content')->default('')->comment('文章內容');
            $table->char('create_time', 10)->default('')->comment('建立時間(timestamp)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_article');
    }
};
