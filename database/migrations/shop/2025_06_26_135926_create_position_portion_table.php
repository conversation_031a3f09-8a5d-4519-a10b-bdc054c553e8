<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('position_portion', function (Blueprint $table) {
            $table->comment('實際庫存列表');
            $table->integer('id', true);
            $table->integer('position_id');
            $table->integer('position_number')->comment('編碼號碼');
            $table->integer('num')->comment('實際商品數量');
            $table->integer('product_id')->nullable()->default(0);
            $table->integer('productinfo_type')->nullable()->default(0);
            $table->integer('radio')->nullable()->comment('編碼方式 0.不重複 1.重複');
            $table->dateTime('datetime')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('position_portion');
    }
};
