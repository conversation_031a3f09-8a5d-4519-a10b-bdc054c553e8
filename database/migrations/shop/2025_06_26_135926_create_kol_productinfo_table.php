<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('kol_productinfo', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('kol_id');
            $table->integer('productinfo_id');
            $table->text('time')->comment('開始時間');
            $table->text('time_e')->nullable()->comment('結束時間');
            $table->boolean('is_using')->default(true)->comment('使用中 0.過期 1.使用中');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('kol_productinfo');
    }
};
