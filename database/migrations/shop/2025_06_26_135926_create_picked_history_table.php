<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('picked_history', function (Blueprint $table) {
            $table->comment('揀貨歷史紀錄');
            $table->integer('id', true);
            $table->string('order_id')->nullable()->comment('對應訂單id');
            $table->integer('order_time');
            $table->string('p_code')->nullable();
            $table->integer('num')->comment('揀貨總計');
            $table->integer('product_id')->nullable();
            $table->integer('productinfo_type')->nullable();
            $table->integer('position_id')->nullable();
            $table->integer('position_number')->nullable()->comment('編碼號碼');
            $table->boolean('deal_position')->default(false)->comment('揀貨狀態 0.未揀貨 1.已揀貨');
            $table->dateTime('datetime')->nullable()->comment('揀貨時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('picked_history');
    }
};
