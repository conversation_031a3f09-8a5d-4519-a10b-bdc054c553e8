<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('addprice_rule', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('addprice_id')->comment('加價購id');
            $table->integer('product_type_id')->comment('商品品項id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('addprice_rule');
    }
};
