<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('member_article_interact', function (Blueprint $table) {
            $table->comment('會員文章-互動類型紀錄');
            $table->integer('id', true);
            $table->char('visitorId', 128)->comment('檢舉人ip');
            $table->integer('article_id')->default(0)->comment('對應文章id');
            $table->boolean('act_type')->default(false)->comment('互動類型(1.瀏覽 2.喜歡)');
            $table->char('create_time', 10)->default('')->comment('建立時間(timestamp)');
            $table->integer('value')->default(0)->comment('數值(統計次數用)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_article_interact');
    }
};
