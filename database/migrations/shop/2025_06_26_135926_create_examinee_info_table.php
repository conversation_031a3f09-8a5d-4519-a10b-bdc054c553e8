<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('examinee_info', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('prod_id');
            $table->text('type_id');
            $table->integer('order_id')->comment('0.報名未成立');
            $table->integer('user_id');
            $table->text('datetime')->nullable()->comment('建立日期');
            $table->text('name')->nullable()->comment('姓名');
            $table->text('email')->nullable()->comment('報名者信箱(考生)');
            $table->string('session_id', 256)->comment('session_id');
            $table->text('register_data')->nullable()->comment('報名資料');
            $table->boolean('roll_call')->default(false)->comment('點名 0.無 1.未到 2.到');
            $table->string('roll_call_time', 32)->default('')->comment('點名時間');
            $table->boolean('reg_status')->default(true)->comment('報名狀況 1.已報名 0.候補 2.候補上 3.取消候補');
            $table->boolean('cancel')->default(false)->comment('取消 0.未取消 1.取消');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('examinee_info');
    }
};
