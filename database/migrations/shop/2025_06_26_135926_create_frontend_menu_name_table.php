<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('frontend_menu_name', function (Blueprint $table) {
            $table->integer('id')->primary();
            $table->string('name', 256)->comment('名稱');
            $table->string('en_name', 256)->comment('英文名字');
            $table->string('controller', 256)->comment('控制器名稱');
            $table->text('second_menu')->nullable()->comment('子目錄');
            $table->string('text_color', 12)->nullable()->comment('文字色(色碼)');
            $table->text('pic')->nullable()->comment('背景圖片');
            $table->integer('backstage_menu_second_id')->nullable()->comment('對應功能id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('frontend_menu_name');
    }
};
