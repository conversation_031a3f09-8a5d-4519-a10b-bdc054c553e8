<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seo', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('title', 64);
            $table->string('seokey', 256);
            $table->text('descr');
            $table->string('fb_name', 64);
            $table->string('fb_title', 64);
            $table->text('fb_descr');
            $table->string('fb_img', 128);
            $table->string('twitter_name', 64);
            $table->string('twitter_title', 64);
            $table->text('twitter_descr');
            $table->string('verification', 128);
            $table->text('trackgoogle');
            $table->text('marketgoogle');
            $table->text('marketyahoo');
            $table->text('display');
            $table->string('map', 128)->default('');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('seo');
    }
};
