<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('askprice', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('askprice_record_id')->default(0)->comment('當前詢價內容(對應askprice_record id)');
            $table->integer('user_id')->default(0)->comment('詢問者id');
            $table->text('name')->nullable()->comment('詢問者姓名');
            $table->text('phone')->nullable()->comment('詢問者電話');
            $table->text('email')->nullable()->comment('詢問者信箱');
            $table->integer('product_id')->comment('商品id');
            $table->text('product_name')->nullable()->comment('商品名稱');
            $table->integer('product_type_id')->comment('品項id');
            $table->text('product_type_name')->nullable()->comment('品項名稱');
            $table->string('create_time', 32)->nullable()->comment('建立時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('askprice');
    }
};
