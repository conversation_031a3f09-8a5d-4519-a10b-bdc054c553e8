<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('productinfo_orders', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('prod_id')->comment('商品id');
            $table->integer('prev_id')->comment('分館id');
            $table->integer('branch_id')->comment('分類id');
            $table->integer('order_id')->default(0)->comment('排序');
            $table->integer('top_order_id')->default(999)->comment('置頂排序');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('productinfo_orders');
    }
};
