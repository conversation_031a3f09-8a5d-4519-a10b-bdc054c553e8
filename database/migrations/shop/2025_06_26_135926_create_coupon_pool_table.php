<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupon_pool', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('coupon_id');
            $table->string('number', 64)->nullable();
            $table->integer('login_time')->nullable();
            $table->integer('owner')->nullable();
            $table->integer('use_time')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupon_pool');
    }
};
