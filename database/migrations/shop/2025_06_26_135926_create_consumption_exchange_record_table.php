<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('consumption_exchange_record', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('user_id')->comment('會員id');
            $table->integer('exchange_id')->comment('兌換商品id');
            $table->text('pic')->nullable()->comment('贈品圖片');
            $table->text('name')->nullable()->comment('贈品名稱');
            $table->text('ex_date')->nullable()->comment('兌換時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('consumption_exchange_record');
    }
};
