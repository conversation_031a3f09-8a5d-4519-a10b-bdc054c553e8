<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('kol_period', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('kol_id');
            $table->text('name')->nullable()->comment('檔期名稱');
            $table->string('date_start', 16);
            $table->string('date_end', 16);
            $table->integer('count_days')->comment('結算周期(日)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('kol_period');
    }
};
