<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('kol_period_term', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('kol_period_id');
            $table->string('period_start', 16);
            $table->string('period_end', 16);
            $table->integer('count')->nullable()->comment('檔期數');
            $table->string('confirm_date', 16)->nullable()->comment('核可時間(timestamp)');
            $table->text('confrim_content')->nullable()->comment('核可內容(核可後不再修改)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('kol_period_term');
    }
};
