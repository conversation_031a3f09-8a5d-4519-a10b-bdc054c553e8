<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('consent', function (Blueprint $table) {
            $table->integer('id', true);
            $table->text('member');
            $table->text('point');
            $table->text('coupon');
            $table->text('shopping')->nullable()->comment('購物條款');
            $table->text('other')->nullable()->comment('其他資訊');
            $table->text('privacy')->nullable()->comment('隱私政策');
            $table->text('examination')->nullable()->comment('報名流程說明');
            $table->text('g_process')->nullable()->comment('分數查詢說明');
            $table->text('examinee')->nullable()->comment('考生資料注意事項');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('consent');
    }
};
