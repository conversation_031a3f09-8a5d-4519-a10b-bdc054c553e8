<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupon_direct_record', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id')->comment('會員id');
            $table->integer('coupon_id')->comment('使用優惠券id');
            $table->integer('order_id')->comment('訂單id');
            $table->text('datetime')->nullable()->comment('使用時間');
            $table->integer('total')->comment('購物總金額');
            $table->integer('discount')->comment('優惠金額');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupon_direct_record');
    }
};
