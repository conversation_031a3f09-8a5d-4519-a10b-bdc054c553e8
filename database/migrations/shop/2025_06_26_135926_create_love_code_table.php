<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('love_code', function (Blueprint $table) {
            $table->comment('捐贈碼管理');
            $table->integer('id', true);
            $table->text('name')->comment('捐贈單位名稱');
            $table->string('code', 8)->nullable()->comment('捐贈碼');
            $table->integer('order_id')->default(0)->comment('排序');
            $table->boolean('online')->default(true)->comment('狀態 0.停用 1.啟用');
            $table->boolean('sys_status')->default(true)->comment('系統控制開關 0.關 1.開');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('love_code');
    }
};
