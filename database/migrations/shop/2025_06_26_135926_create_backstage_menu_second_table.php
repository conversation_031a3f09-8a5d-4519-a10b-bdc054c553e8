<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('backstage_menu_second', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('name', 25)->comment('判斷功能起停用名稱');
            $table->text('show_name')->nullable()->comment('顯示名稱');
            $table->string('url', 100)->nullable();
            $table->string('front_desk', 50)->nullable();
            $table->string('count_id', 20)->nullable();
            $table->integer('sort')->nullable();
            $table->integer('backstage_menu_id')->nullable();
            $table->integer('important')->default(0)->comment('醒目標示');
            $table->string('class', 128)->nullable()->comment('active用');
            $table->string('target', 10)->default('_parent')->comment('開啟模式');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('backstage_menu_second');
    }
};
