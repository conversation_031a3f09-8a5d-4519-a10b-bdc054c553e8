<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('kol', function (Blueprint $table) {
            $table->integer('id', true);
            $table->text('email')->comment('信箱');
            $table->text('password')->comment('密碼');
            $table->text('kol_name')->comment('網紅名');
            $table->text('real_name')->nullable()->comment('真實姓名');
            $table->text('english_name')->nullable()->comment('英文姓名');
            $table->text('category')->nullable()->comment('分類');
            $table->text('phone')->nullable()->comment('電話');
            $table->text('mobile')->comment('手機');
            $table->text('address')->nullable()->comment('地址');
            $table->text('address_memo')->nullable()->comment('地址備註');
            $table->text('bank_name')->comment('匯款銀行');
            $table->text('bank_account')->comment('匯款帳號');
            $table->text('id_no')->nullable()->comment('身份証');
            $table->text('memo')->nullable()->comment('備註');
            $table->text('creatdate')->comment('建立時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('kol');
    }
};
