<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('productinfo', function (Blueprint $table) {
            $table->comment('商品詳細資訊');
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('order_id')->default(0);
            $table->integer('top_order_id')->default(999)->comment('置頂排序');
            $table->boolean('product_cate')->default(false)->comment('商品類型(1.投資、2.消費)');
            $table->integer('bonus_model_id')->default(0)->comment('套用回饋模組id(商品類型為1時則應為0)');
            $table->boolean('use_ad')->default(false)->comment('套用廣告(0.非 1.是)');
            $table->integer('vip_type_reward')->default(0)->comment('提升會員級別(對應vip_type的id，0.表示一般，其他表示課程)');
            $table->integer('vip_type_require')->default(0)->comment('需求會員級別(對應vip_type的id，0.表示無限制)');
            $table->text('final_array')->nullable()->comment('顯示位置');
            $table->string('ISBN')->nullable()->comment('條碼');
            $table->string('version', 10)->nullable();
            $table->integer('out_ID')->nullable()->comment('外串EDM');
            $table->string('product_id', 64);
            $table->text('title')->nullable();
            $table->integer('r_repeat')->nullable()->default(1)->comment('庫存編碼方式：0.不重複 1.重複');
            $table->string('Author', 256)->nullable()->comment('文字1');
            $table->string('house', 256)->nullable()->comment('文字2');
            $table->string('house_date', 256)->nullable()->comment('文字3');
            $table->text('pic')->nullable();
            $table->string('pic2', 128)->nullable();
            $table->string('pic3', 128)->nullable();
            $table->string('pic4', 128)->nullable();
            $table->string('keywords', 256)->default('');
            $table->text('content')->nullable();
            $table->text('display')->nullable();
            $table->boolean('ask_price')->default(false)->comment('是否開放詢價 0.否 1.是');
            $table->tinyInteger('has_price')->default(0);
            $table->text('text1')->nullable();
            $table->tinyInteger('text1_online')->default(1);
            $table->text('text2')->nullable();
            $table->tinyInteger('text2_online')->default(1);
            $table->text('text3')->nullable();
            $table->tinyInteger('text3_online')->default(1);
            $table->text('text4')->nullable();
            $table->tinyInteger('text4_online')->default(1);
            $table->text('text5')->nullable();
            $table->tinyInteger('text5_online')->default(1);
            $table->tinyInteger('online')->default(1)->comment('狀態 0.隱藏 1.開啟 2.關閉');
            $table->integer('create_time')->default(0);
            $table->timestamp('updatetime')->useCurrent();
            $table->text('pushitem')->nullable()->comment('推薦商品(品號 ,分隔)');
            $table->text('prodesc')->nullable()->comment('商品說明(下拉選控制)');
            $table->boolean('pre_buy')->default(false)->comment('是否可超額購買 1.可 0.不可');
            $table->integer('pre_buy_limit')->default(0)->comment('超額購買上限');
            $table->tinyInteger('card_pay')->default(0)->comment('是否可刷卡');
            $table->boolean('is_registrable')->default(false)->comment('是否需填報名資料 0.否 1.是');
            $table->boolean('is_roll_call')->default(false)->comment('是否需點名 0.否 1.是');
            $table->text('remind_msg')->nullable()->comment('活動提醒訊息');
            $table->text('pay_type')->nullable()->comment('限用付款方法');
            $table->text('shipping_type')->nullable()->comment('限用運法');
            $table->integer('shipping_fee_tag')->default(0)->comment('套運運費標籤');
            $table->text('register_data_change_limit')->nullable()->comment('報名資料修改截止日');
            $table->integer('cost')->default(0)->comment('成本');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('productinfo');
    }
};
