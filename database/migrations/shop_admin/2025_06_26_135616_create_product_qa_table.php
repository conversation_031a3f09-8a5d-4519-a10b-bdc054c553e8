<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('product_qa', function (Blueprint $table) {
            $table->integer('prod_qa_id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('uid')->comment('詢問者id');
            $table->integer('prod_id')->comment('productinfo id');
            $table->text('prod_q');
            $table->text('prod_a')->nullable();
            $table->string('site_name', 128);
            $table->string('prod_addr', 1028)->nullable();
            $table->dateTime('q_datetime');
            $table->dateTime('a_datetime')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('product_qa');
    }
};
