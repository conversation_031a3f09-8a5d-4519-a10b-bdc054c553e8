<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('vip_type', function (Blueprint $table) {
            $table->comment('會員等級 天脈:會員級別');
            $table->integer('id', true);
            $table->tinyInteger('type')->default(0)->comment('優惠方式 0.打折 1.扣元');
            $table->text('vip_name')->nullable();
            $table->integer('rule')->default(0)->comment('條件金額');
            $table->float('discount', 10, 0)->comment('折扣值');
            $table->text('note')->nullable()->comment('等級說明');
            $table->float('dividend_month_weighted', 10, 0)->default(1)->comment('月分紅加權(ex:1.1)');
            $table->float('burn_cv', 10, 0)->default(0)->comment('會員級別燒傷金額(美金)(ex:1200)');
            $table->float('discount_ratio', 10, 0)->default(10)->comment('消費圓滿點數抵扣消費比率(ex:10)(使用時須除100)');
            $table->integer('liability_gv')->default(0)->comment('個人點數責任額(GV)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('vip_type');
    }
};
