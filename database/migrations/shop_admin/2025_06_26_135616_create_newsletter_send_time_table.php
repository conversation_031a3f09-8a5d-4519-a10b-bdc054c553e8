<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('newsletter_send_time', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('newsletter_id')->comment('對應newsletter');
            $table->text('send_target')->comment('寄送對象');
            $table->integer('newsletter_group_id')->comment('對應newsletter_group id');
            $table->text('msg')->nullable();
            $table->string('email', 100)->nullable();
            $table->string('return_value', 256)->nullable();
            $table->integer('send_time')->nullable();
            $table->text('title')->nullable();
            $table->boolean('open')->default(false)->comment('是否開啟 0.否 1.是');
            $table->string('open_time', 20)->nullable()->comment('開啟時間');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('newsletter_send_time');
    }
};
