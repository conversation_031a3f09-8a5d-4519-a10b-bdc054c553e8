<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('account', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('name', 16)->nullable();
            $table->boolean('registration_from')->default(true)->comment('會員來源(1.直接 2.廣告)');
            $table->boolean('user_type')->nullable()->default(false)->comment('經銷狀態 0.未開通 1.開通');
            $table->boolean('user_type_radio')->nullable()->default(false)->comment('會員類型 0.一般 1.經銷(消費者選擇)');
            $table->boolean('supplier_bonus')->default(true)->comment('經銷回饋方式 1.增值積分 2.現金');
            $table->integer('product_view_id')->default(1)->comment('商品瀏覽id');
            $table->integer('vip_type')->default(0)->comment('vip等級');
            $table->integer('vip_type_course')->default(0)->comment('vip等級課程進度');
            $table->integer('from_order')->nullable()->comment('從哪筆訂單建立會員資料 0.一般註冊, null.未設定');
            $table->string('pwd', 128)->nullable()->default('81dc9bdb52d04dc20036dbd8313ed055')->comment('預設為1234的md5加密');
            $table->string('f_code', 128)->nullable()->comment('忘記密碼用');
            $table->string('number', 32)->nullable();
            $table->timestamp('update_time')->useCurrent();
            $table->tinyInteger('status')->nullable()->default(0);
            $table->boolean('auto_partner')->default(true)->comment('自動升級合夥人等級(1.否 2.是)');
            $table->integer('total')->nullable()->default(0)->comment('累積金額(完成訂單時更新)');
            $table->decimal('point', 32, 8)->default(0)->comment('紅利點數(現金積分)(1點1美金)(小數點後2位)');
            $table->decimal('point_increasable', 32, 8)->default(0)->comment('增值積分(價值會異動)(小數點後2位)');
            $table->decimal('increasing_limit_other', 32, 8)->default(0)->comment('其他圓滿點數(美金)(小數點後2位)');
            $table->decimal('increasing_limit_consumption', 32, 8)->default(0)->comment('消費圓滿點數(美金)(小數點後2位)');
            $table->decimal('increasing_limit_invest', 32, 8)->default(0)->comment('功德圓滿點數(美金)(小數點後2位)');
            $table->decimal('partner_accumulation', 32, 8)->default(0)->comment('合夥人累積投資金額(美金)(處理回饋or調整合夥等級時更新)');
            $table->integer('partner_level_id')->default(0)->comment('合夥人等級id');
            $table->integer('center_level_id')->default(0)->comment('對應中心等級');
            $table->integer('center_raiser_id')->default(0)->comment('中心發起者');
            $table->string('email', 128)->nullable();
            $table->string('gmail')->nullable();
            $table->string('line_id')->nullable();
            $table->string('FB_id')->nullable();
            $table->string('phone', 32)->nullable();
            $table->string('tele', 32)->nullable();
            $table->string('birthday', 11)->nullable();
            $table->string('home', 128)->nullable();
            $table->string('invoice', 10)->nullable();
            $table->string('createtime', 19)->nullable();
            $table->integer('ordernum')->nullable();
            $table->string('gender', 1)->nullable();
            $table->boolean('export')->default(false);
            $table->integer('upline_user')->default(0)->comment('上線會員id');
            $table->text('recommend_content')->nullable()->comment('推廣文字');
            $table->text('share_pic')->nullable()->comment('分享圖片');
            $table->text('share_title')->nullable()->comment('分享標題');
            $table->text('share_text')->nullable()->comment('分享說明');
            $table->text('shop_name')->nullable()->comment('店鋪名稱');
            $table->text('file_company')->nullable()->comment('公司登記文件');
            $table->text('file_person')->nullable()->comment('個人身份文件');
            $table->text('bank')->nullable()->comment('銀行名稱');
            $table->text('bank_code')->nullable()->comment('分行代號');
            $table->text('bank_account_name')->nullable()->comment('戶名');
            $table->text('bank_account_code')->nullable()->comment('銀行帳號');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('account');
    }
};
