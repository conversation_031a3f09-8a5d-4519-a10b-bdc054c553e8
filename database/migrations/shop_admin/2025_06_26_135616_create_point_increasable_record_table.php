<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('point_increasable_record', function (Blueprint $table) {
            $table->comment('增值積分紀錄');
            $table->integer('id', true);
            $table->integer('user_id')->default(0)->comment('對應會員id(必定對應某會員)');
            $table->decimal('num', 32, 8)->default(0)->comment('變動量(可為負)');
            $table->string('msg', 256)->comment('訊息');
            $table->char('create_time', 10)->comment('建立時間(timestamp)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('point_increasable_record');
    }
};
