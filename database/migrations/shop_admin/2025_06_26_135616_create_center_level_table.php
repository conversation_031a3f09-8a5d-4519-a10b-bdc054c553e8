<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('center_level', function (Blueprint $table) {
            $table->comment('中心等級');
            $table->integer('id', true);
            $table->char('name', 16)->comment('等級名稱');
            $table->integer('orders')->default(0)->comment('等級排序(數字愈小排越前，越低級)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('center_level');
    }
};
