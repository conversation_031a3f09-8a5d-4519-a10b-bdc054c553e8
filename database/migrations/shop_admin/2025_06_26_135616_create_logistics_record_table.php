<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('logistics_record', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('order_id')->comment('訂單編號');
            $table->string('logistics_id', 20)->comment('綠界物流交易編號');
            $table->dateTime('time')->comment('紀錄時間');
            $table->text('text')->comment('回覆內容');
            $table->string('LogisticsType', 20)->nullable()->comment('物流方式');
            $table->string('RtnCode', 8)->nullable()->comment('LogisticsStatus 物流狀態');
            $table->string('ShipmentNo', 8)->nullable();
            $table->string('BookingNote', 50)->nullable();
            $table->text('CheckMacValue')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('logistics_record');
    }
};
