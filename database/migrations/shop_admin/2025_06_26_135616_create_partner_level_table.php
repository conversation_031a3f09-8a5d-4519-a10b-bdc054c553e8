<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('partner_level', function (Blueprint $table) {
            $table->comment('合夥人等級');
            $table->integer('id', true);
            $table->char('name', 16)->comment('等級名稱');
            $table->float('ratio', 10, 0)->default(0)->comment('功德圓滿點數倍率(ex:1.25)');
            $table->float('contribution', 10, 0)->default(0)->comment('累積投資金額(數字愈小排越前，越低級)');
            $table->float('partner_bonus_ratio', 10, 0)->default(0)->comment('合夥平級獎勵可回饋比率(ex:25)(使用時須除100)');
            $table->float('orderform_ad_weight', 10, 0)->default(0)->comment('廣告訂單加權');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('partner_level');
    }
};
