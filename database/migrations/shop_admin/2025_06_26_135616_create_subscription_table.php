<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('subscription', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('user_id')->default(0)->comment('會員id');
            $table->text('contentEncoding')->nullable();
            $table->text('endpoint');
            $table->text('expirationTime')->nullable();
            $table->text('auth')->comment('需組合成keys');
            $table->text('p256dh')->comment('需組合成keys');
            $table->tinyInteger('online')->default(1)->comment('狀態 1.啟用 0.停用');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('subscription');
    }
};
