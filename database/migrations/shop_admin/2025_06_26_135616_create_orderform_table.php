<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('orderform', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('distributor_id')->default(0)->comment('會員id 0為平台');
            $table->integer('user_id')->comment('會員id');
            $table->string('order_number', 32);
            $table->integer('create_time');
            $table->dateTime('over_time')->nullable();
            $table->string('payment', 32)->comment('1.貨到付款 2.ATM轉帳\\匯款 3.線上刷卡 4.分期付款 5.LinePay');
            $table->string('transport', 32);
            $table->string('transport_location', 256);
            $table->string('transport_location_name', 20)->nullable();
            $table->string('transport_location_phone', 20)->nullable();
            $table->string('transport_location_tele', 20)->nullable();
            $table->text('transport_location_textarea')->nullable();
            $table->string('transport_email', 1024)->nullable();
            $table->text('product');
            $table->integer('add_point');
            $table->integer('total');
            $table->integer('contribution_deduct')->default(0)->comment('圓滿點數折抵金額');
            $table->text('discount');
            $table->integer('freediscount')->default(0)->comment('免領優惠記錄');
            $table->string('report', 128)->nullable()->comment('匯款回報');
            $table->integer('report_check_time')->nullable();
            $table->tinyInteger('receipts_state');
            $table->tinyInteger('transport_state');
            $table->date('transport_date')->nullable()->comment('出貨日期');
            $table->string('return_ps', 2048)->nullable();
            $table->string('cancel_ps', 2048)->nullable();
            $table->string('ps', 2048)->nullable();
            $table->text('ps2')->comment('訂單備註(消費者查看)');
            $table->string('uniform_numbers', 64)->nullable()->default('');
            $table->string('company_title', 64)->default('');
            $table->boolean('InvoiceStyle')->default(true)->comment('發票類型 1.個人實體紙本發票 2.個人電子郵件寄送發票 3.個人共通性載具 4.公司戶發票 5.捐贈');
            $table->string('LoveCode', 10)->default('')->comment('捐贈碼');
            $table->string('CarrierType', 1)->default('')->comment('載具類型 空.紙本發票 2.自然人 3.手機');
            $table->string('CarrierNum', 10)->default('')->comment('載具編號');
            $table->text('InvoiceNo')->nullable()->comment('發票號碼');
            $table->text('InvoiceDate')->nullable()->comment('發票開立日期');
            $table->text('RandomNumber')->nullable()->comment('發票隨機碼');
            $table->boolean('Print')->default(false)->comment('發票列印 0.否 1.是');
            $table->string('status', 32)->comment('狀態變化：New=>Pickable=>Picked=>Complete Cancel,Return
消費者只在New時可取消訂單');
            $table->char('do_award_time', 10)->comment('消費回饋時間(timestamp)(非空時表示已回饋)');
            $table->integer('user_id_operation')->default(0)->comment('營運者(對應account的id)');
            $table->integer('user_id_lecturer')->default(0)->comment('講師者(對應account的id)');
            $table->integer('user_id_center')->default(0)->comment('中心會員(對應account的id)');
            $table->boolean('stock_status')->default(false)->comment('0: 訂單庫存不足，1: 訂單庫存足夠');
            $table->dateTime('cancel_date');
            $table->integer('show_date')->nullable()->default(1);
            $table->string('card4no', 10)->nullable();
            $table->integer('timeover')->nullable()->default(0);
            $table->text('p_u_time')->nullable();
            $table->string('MerchantTradeNo', 20)->nullable()->comment('廠商交易編號');
            $table->string('LogisticsSubType', 20)->nullable()->comment('物流子類型');
            $table->string('CVSStoreID', 9)->nullable()->comment('超商店舖編號');
            $table->string('CVSStoreName', 10)->nullable()->comment('超商店舖名稱');
            $table->string('CVSAddress', 60)->nullable()->comment('超商店舖地址');
            $table->string('CVSTelephone', 20)->nullable()->comment('超商店舖電話');
            $table->string('CVSOutSide', 1)->nullable()->comment('否為離島店鋪0:本島 1:離島');
            $table->string('ExtraData', 20)->nullable()->comment('額外資訊');
            $table->string('AllPayLogisticsID', 20)->nullable()->comment('物流交易編號');
            $table->char('GoodsWeight', 8)->nullable()->comment('訂單重量');
            $table->text('CheckMacValue')->nullable()->comment('綠界檢查碼');
            $table->text('tspg_return_data')->nullable()->comment('台新信用卡授權回傳內容');
            $table->text('linepay_return_data')->nullable()->comment('LinePay交易結果回傳內容');
            $table->text('error')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('orderform');
    }
};
