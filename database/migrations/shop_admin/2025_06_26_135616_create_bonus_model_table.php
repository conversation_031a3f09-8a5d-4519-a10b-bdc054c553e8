<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('bonus_model', function (Blueprint $table) {
            $table->comment('回饋模組');
            $table->integer('id', true);
            $table->char('name', 32)->comment('模組名稱');
            $table->float('normal_recommend', 10, 0)->default(0)->comment('推廣獎勵(ex:25)(使用時要除100)');
            $table->float('normal_partner', 10, 0)->default(0)->comment('合夥平級獎勵(ex:25)(使用時要除100)');
            $table->float('normal_operation', 10, 0)->default(0)->comment('營運獎勵(ex:12)(使用時要除100)');
            $table->float('normal_lecturer', 10, 0)->default(0)->comment('講師獎勵(ex:3)(使用時要除100)');
            $table->float('normal_center', 10, 0)->default(0)->comment('中心獎勵(ex:15)(使用時要除100)');
            $table->float('normal_dividend_month', 10, 0)->default(0)->comment('月分紅(ex:20)(使用時要除100)');
            $table->float('normal_center_divided_to_raiser', 10, 0)->default(0)->comment('中心獎勵-發起者佔比(ex:30)(使用時要除100)');
            $table->boolean('use_partner_mode')->nullable()->default(false)->comment('是否使用合夥批發回饋(0.否 1.是)');
            $table->float('partner_recommend', 10, 0)->default(0)->comment('合夥設定:推廣獎勵(ex:85)(使用時要除100)');
            $table->float('partner_partner', 10, 0)->default(0)->comment('合夥設定:合夥平級獎勵(ex:0)(使用時要除100)');
            $table->float('partner_operation', 10, 0)->default(0)->comment('合夥設定:營運獎勵(ex:12)(使用時要除100)');
            $table->float('partner_lecturer', 10, 0)->default(0)->comment('合夥設定:講師獎勵(ex:3)(使用時要除100)');
            $table->float('partner_center', 10, 0)->default(0)->comment('合夥設定:中心獎勵(ex:0)(使用時要除100)');
            $table->float('partner_dividend_month', 10, 0)->default(0)->comment('合夥設定:月分紅(ex:0)(使用時要除100)');
            $table->float('partner_center_divided_to_raiser', 10, 0)->default(0)->comment('合夥設定:中心獎勵-發起者佔比(ex:0)(使用時要除100)');
            $table->float('ad_bonus', 10, 0)->default(0)->comment('廣告推廣獎勵(ex:25)(使用時要除100)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('bonus_model');
    }
};
