<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('orderform_product', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('orderform_id')->comment('對應訂單id');
            $table->string('name', 256)->nullable()->comment('商品+品項名稱');
            $table->integer('price')->comment('單價');
            $table->integer('num')->comment('數量');
            $table->integer('total')->comment('總額(單價*數量)');
            $table->string('url', 256)->nullable()->comment('商品網址');
            $table->string('url2', 256)->nullable()->comment('商品圖片網址');
            $table->integer('info_id')->nullable()->comment('對應商品id');
            $table->integer('type_id')->nullable()->comment('對應商品品項id');
            $table->string('key_type', 32)->nullable()->comment('商品優惠類型(normal, 
 addprice...)');
            $table->string('Author', 256)->nullable()->comment('額外資料1');
            $table->string('house_date', 256)->nullable()->comment('額外資料3');
            $table->string('house', 256)->nullable()->comment('額外資料2');
            $table->string('ISBN', 256)->nullable()->comment('ISBN');
            $table->string('position', 256)->nullable()->comment('庫位名稱');
            $table->boolean('is_registrable')->default(false)->comment('是否需報名資料(0.否 1.是)');
            $table->boolean('deal_position')->default(false)->comment('是否已撿貨(0.否 1.是)');
            $table->string('position_code', 256)->nullable()->comment('撿貨結果紀錄');
            $table->boolean('pre_buy')->default(false)->comment('是否可超額購買(0.否 1.是)');
            $table->integer('pre_buy_num')->default(0)->comment('超額購買數量');
            $table->boolean('product_cate')->default(false)->comment('商品類型(1.投資、2.消費)');
            $table->integer('bonus_model_id')->default(0)->comment('回饋模組id');
            $table->boolean('use_ad')->default(false)->comment('套用廣告(0.非 1.是)');
            $table->integer('distributor_id')->default(0)->comment('供應商id(對應會員id)');
            $table->integer('vip_type_reward')->default(0)->comment('提升會員級別(對應vip_type的id，0.表示一般，其他表示課程)');
            $table->integer('vip_type_require')->default(0)->comment('需求會員級別(對應vip_type的id，0.表示無限制)');
            $table->integer('deduct_invest')->default(0)->comment('功德圓滿消費折抵');
            $table->integer('deduct_consumption')->default(0)->comment('消費圓滿消費折抵');
            $table->decimal('price_cv', 32, 8)->default(0)->comment('商品CV金額(美金)(已乘上數量，計算回饋時應扣除deduct_invest、deduct_consumption)');
            $table->decimal('price_supplier', 32, 8)->default(0)->comment('供應商結算金額(美金)(已乘上數量)');
            $table->boolean('supplier_bonus')->default(true)->comment('經銷回饋方式(建單當下紀錄) 1.增值積分 2.現金');
            $table->char('do_award_supplier_time', 10)->comment('供應商回饋時間(timestamp)(非空時表示已回饋)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('orderform_product');
    }
};
