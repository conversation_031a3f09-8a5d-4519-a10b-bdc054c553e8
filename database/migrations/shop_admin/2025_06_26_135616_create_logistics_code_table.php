<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('logistics_code', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('type', 128)->comment('綠界物流種類');
            $table->string('code', 32)->comment('綠界物流狀態碼');
            $table->text('message')->comment('綠界物流狀態訊息');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('logistics_code');
    }
};
