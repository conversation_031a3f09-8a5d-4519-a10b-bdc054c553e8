<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('message_log', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('number', 50);
            $table->text('title');
            $table->integer('total')->nullable();
            $table->text('success')->nullable();
            $table->text('error')->nullable();
            $table->text('error_msgid');
            $table->text('old_msgid')->nullable();
            $table->integer('create_time');
            $table->integer('send_time')->nullable();
            $table->string('message_list_id', 50)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('message_log');
    }
};
