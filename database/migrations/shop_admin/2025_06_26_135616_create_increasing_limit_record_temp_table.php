<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('increasing_limit_record_temp', function (Blueprint $table) {
            $table->comment('圓滿點數紀錄-還原資料用');
            $table->integer('id')->primary();
            $table->integer('user_id')->default(0)->comment('對應會員id(必定對應某會員)');
            $table->decimal('num', 32, 8)->default(0)->comment('變動量(可為負)');
            $table->string('msg', 256)->comment('訊息');
            $table->char('create_time', 10)->comment('建立時間(timestamp)');
            $table->boolean('limit_type')->default(false)->comment('限制類型(1.功德 2.消費 3.其他)');
            $table->boolean('type')->comment('紀錄類型(1.處理回饋 2.處理增值轉換 3.個人責任額檢查 4.消費使用 5.管理員調整)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('increasing_limit_record_temp');
    }
};
