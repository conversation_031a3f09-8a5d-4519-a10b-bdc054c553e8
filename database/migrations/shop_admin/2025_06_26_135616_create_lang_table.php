<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('lang', function (Blueprint $table) {
            $table->increments('lang_id');
            $table->string('lang_type', 15)->unique()->comment('語言代號');
            $table->string('lang_word', 15)->unique()->comment('語言中文');
            $table->string('sub_deparment', 15)->nullable()->unique('lang_db_connect_unique')->comment('對應分站');
            $table->integer('lang_order');
            $table->tinyInteger('lang_status')->comment('1:啟用 0:停用');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('lang');
    }
};
