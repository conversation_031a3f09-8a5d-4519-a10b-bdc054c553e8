<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('points_to_cash_record', function (Blueprint $table) {
            $table->comment('現金積分提現紀錄');
            $table->integer('id', true);
            $table->integer('user_id')->comment('對應會員id(必定對應某會員)');
            $table->char('currency', 16)->comment('幣別');
            $table->decimal('num', 32, 8)->default(0)->comment('金額');
            $table->string('msg', 256)->comment('訊息');
            $table->char('time_create', 10)->comment('紀錄建立時間(timestamp)');
            $table->char('time_pay', 10)->comment('紀錄付款時間(timestamp)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('points_to_cash_record');
    }
};
