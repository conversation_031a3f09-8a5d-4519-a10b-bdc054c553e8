<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('partner_level_relation', function (Blueprint $table) {
            $table->comment('合夥人等級關聯');
            $table->integer('id', true);
            $table->integer('user_id')->comment('會員id');
            $table->integer('level_id')->comment('對應合夥人等級id');
            $table->char('datetime', 20)->nullable()->comment('時間(YYYY-mm-dd HH:ii:ss)');

            $table->unique(['user_id', 'level_id'], 'user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('partner_level_relation');
    }
};
