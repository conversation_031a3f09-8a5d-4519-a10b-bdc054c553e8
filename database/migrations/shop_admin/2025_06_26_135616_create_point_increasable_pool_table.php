<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('main_db')->create('point_increasable_pool', function (Blueprint $table) {
            $table->comment('積分資金池，透過 sum(`num`) 即可得出目前的「積分資金池」總額。「提現」時 orderform_id=0、user_id為某會員、num為負數。point_value可看增值積分價值的歷史變化');
            $table->integer('id', true);
            $table->integer('orderform_id')->default(0)->comment('對應訂單id(可為0，遊客購物或「提現」)');
            $table->integer('user_id')->comment('對應會員id(必定對應某會員)');
            $table->decimal('num', 32, 8)->default(0)->comment('變動量(可為負)');
            $table->string('msg', 256)->comment('變動說明');
            $table->char('datetime', 10)->comment('時間(timestamp)');
            $table->decimal('point_value', 34, 10)->default(0)->comment('增值積分價值(積分池異動&機分派發後)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('main_db')->dropIfExists('point_increasable_pool');
    }
};
