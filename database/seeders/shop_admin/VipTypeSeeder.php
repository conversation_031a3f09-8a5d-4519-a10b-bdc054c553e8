<?php

namespace Database\Seeders\Shop_admin;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class VipTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 為了確保資料的一致性，建議在插入前先清空資料表
        DB::connection('main_db')->table('vip_type')->truncate();

        DB::connection('main_db')->table('vip_type')->insert([
            // ['id' => 0, 'type' => 0, 'vip_name' => '會員首購優惠', 'rule' => 0, 'discount' => 9, 'note' => '首購打9折', 'dividend_month_weighted' => 1, 'burn_cv' => 0, 'discount_ratio' => 10, 'liability_gv' => 0],

            ['id' => 1, 'type' => 0, 'vip_name' => '消費商', 'rule' => 200, 'discount' => 1, 'note' => '', 'dividend_month_weighted' => 1, 'burn_cv' => 132, 'discount_ratio' => 5, 'liability_gv' => 100],
            ['id' => 2, 'type' => 0, 'vip_name' => '任督級別', 'rule' => 2000, 'discount' => 1, 'note' => '', 'dividend_month_weighted' => 1.1, 'burn_cv' => 1320, 'discount_ratio' => 10, 'liability_gv' => 100],
            ['id' => 3, 'type' => 0, 'vip_name' => '中脈級別', 'rule' => 10000, 'discount' => 1, 'note' => '', 'dividend_month_weighted' => 1.2, 'burn_cv' => 3740, 'discount_ratio' => 15, 'liability_gv' => 100],
            ['id' => 4, 'type' => 0, 'vip_name' => '法身級別', 'rule' => 20000, 'discount' => 1, 'note' => '', 'dividend_month_weighted' => 1.3, 'burn_cv' => 6050, 'discount_ratio' => 20, 'liability_gv' => 100],
            ['id' => 5, 'type' => 0, 'vip_name' => '弟子級別', 'rule' => 100000, 'discount' => 1, 'note' => '', 'dividend_month_weighted' => 1.4, 'burn_cv' => 29920, 'discount_ratio' => 25, 'liability_gv' => 100],
        ]);


        // 由於天才少年加帥哥用0當id..所以要先把 AUTO_INCREMENT 關掉，插入 id=0 的資料，再恢復 AUTO_INCREMENT 並設為6
        // 夠好笑吧..什麼人都有-.-"
        DB::connection('main_db')->statement('ALTER TABLE vip_type MODIFY id int NOT NULL;');
        DB::connection('main_db')->table('vip_type')->insert([
            ['id' => 0, 'type' => 0, 'vip_name' => '會員首購優惠', 'rule' => 0, 'discount' => 9, 'note' => '首購打9折', 'dividend_month_weighted' => 1, 'burn_cv' => 0, 'discount_ratio' => 10, 'liability_gv' => 0],
        ]);
        DB::connection('main_db')->statement('ALTER TABLE vip_type MODIFY id int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;');
    }
}
