<?php

namespace Database\Seeders\Shop_admin;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LangSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::connection('main_db')->table('lang')->truncate();

        DB::connection('main_db')->table('lang')->insert([
            [
                'lang_id' => 1,
                'lang_type' => 'tw',
                'lang_word' => '台灣',
                'lang_order' => 1,
                'lang_status' => 1,
                'sub_deparment' => 'A'
            ],
            [
                'lang_id' => 2,
                'lang_type' => 'us',
                'lang_word' => '美國',
                'lang_order' => 1,
                'lang_status' => 0,
                'sub_deparment' => 'B'
            ],
            [
                'lang_id' => 3,
                'lang_type' => 'ch',
                'lang_word' => '中國',
                'lang_order' => 1,
                'lang_status' => 0,
                'sub_deparment' => 'C'
            ],
            [
                'lang_id' => 4,
                'lang_type' => 'hk',
                'lang_word' => '香港',
                'lang_order' => 1,
                'lang_status' => 0,
                'sub_deparment' => 'D'
            ]
        ]);
    }
}
