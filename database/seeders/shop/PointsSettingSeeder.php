<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PointsSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=points_setting --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'value' => '',
                'note' => '點數適用分館 改由points_allow_use控制',
            ],
            [
                'id' => 2,
                'value' => '',
                'note' => '點數適用分類 無用但誤刪刪',
            ],
            [
                'id' => 3,
                'value' => 1000,
                'note' => '多少元換一點',
            ]
        ];
        DB::table('points_setting')->truncate();
        DB::table("points_setting")->insert($dataTables);
    }
}
