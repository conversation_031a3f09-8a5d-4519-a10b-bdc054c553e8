<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProdescSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=prodesc --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 30,
                'distributor_id' => 0,
                'name' => '台灣人最愛的書',
                'number' => 0,
            ],
            [
                'id' => 28,
                'distributor_id' => 0,
                'name' => '新書',
                'number' => 0,
            ],
            [
                'id' => 29,
                'distributor_id' => 0,
                'name' => '==未輸入==',
                'number' => 0,
            ]
        ];
        DB::table('prodesc')->truncate();
        DB::table("prodesc")->insert($dataTables);
    }
}
