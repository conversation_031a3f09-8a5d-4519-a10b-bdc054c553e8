<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SlideshowSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=slideshow --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'title' => '輪播1',
                'pic' => '/upload/slideshow1.jpg?246989174',
                'link' => '/',
                'online' => 1,
            ],
            [
                'id' => 2,
                'title' => '輪播2',
                'pic' => '/upload/slideshow2.jpg?1624424720',
                'link' => '/',
                'online' => 1,
            ],
            [
                'id' => 4,
                'title' => '輪播4',
                'pic' => '/upload/slideshow4.jpg?1596847261',
                'link' => '/',
                'online' => 1,
            ],
            [
                'id' => 5,
                'title' => '輪播5',
                'pic' => '/upload/slideshow5.jpg?1032674404',
                'link' => '/',
                'online' => 1,
            ],
            [
                'id' => 3,
                'title' => '輪播3',
                'pic' => '/upload/slideshow3.jpg?1005488963',
                'link' => '/',
                'online' => 1,
            ]
        ];
        DB::table('slideshow')->truncate();
        DB::table("slideshow")->insert($dataTables);
    }
}
