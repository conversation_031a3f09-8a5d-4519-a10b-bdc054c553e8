<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BackstageMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=backstage_menu --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'title' => '介紹項',
                'name' => 'C首頁展示',
                'sort' => 2,
            ],
            [
                'id' => 2,
                'title' => '功能項',
                'name' => 'F商品管理區',
                'sort' => 6,
            ],
            [
                'id' => 3,
                'title' => '功能項',
                'name' => 'H行銷項目',
                'sort' => 8,
            ],
            [
                'id' => 4,
                'title' => '介紹項',
                'name' => 'E圖文編輯項目',
                'sort' => 5,
            ],
            [
                'id' => 5,
                'title' => '功能項',
                'name' => 'G功能應用項目',
                'sort' => 7,
            ],
            [
                'id' => 6,
                'title' => '介紹項',
                'name' => 'D關於我們',
                'sort' => 3,
            ],
            [
                'id' => 7,
                'title' => '訂單綜合項',
                'name' => 'I訂單管理',
                'sort' => 9,
            ],
            [
                'id' => 8,
                'title' => '訂單綜合項',
                'name' => 'J參數設定',
                'sort' => 10,
            ],
            [
                'id' => 9,
                'title' => '訂單綜合項',
                'name' => 'K其它功能',
                'sort' => 12,
            ],
            [
                'id' => 10,
                'title' => '會員項',
                'name' => 'B會員功能管理',
                'sort' => 1,
            ]
        ];
        DB::table('backstage_menu')->truncate();
        DB::table("backstage_menu")->insert($dataTables);
    }
}
