<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ExpiringProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=expiring_product --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'product_id' => 0,
            ],
            [
                'id' => 2,
                'product_id' => 0,
            ],
            [
                'id' => 3,
                'product_id' => 0,
            ],
            [
                'id' => 4,
                'product_id' => 0,
            ],
            [
                'id' => 5,
                'product_id' => 0,
            ],
            [
                'id' => 6,
                'product_id' => 0,
            ],
            [
                'id' => 7,
                'product_id' => 0,
            ],
            [
                'id' => 8,
                'product_id' => 0,
            ],
            [
                'id' => 9,
                'product_id' => 0,
            ],
            [
                'id' => 10,
                'product_id' => 0,
            ]
        ];
        DB::table('expiring_product')->truncate();
        DB::table("expiring_product")->insert($dataTables);
    }
}
