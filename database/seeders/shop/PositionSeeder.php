<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PositionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=position --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'name' => 'F',
                'number' => 0,
                'max' => 1,
            ],
            [
                'id' => 7,
                'distributor_id' => 0,
                'name' => 'A',
                'number' => 10,
                'max' => 0,
            ],
            [
                'id' => 8,
                'distributor_id' => 0,
                'name' => 'B',
                'number' => 10,
                'max' => 0,
            ],
            [
                'id' => 9,
                'distributor_id' => 0,
                'name' => 'C',
                'number' => 10,
                'max' => 0,
            ],
            [
                'id' => 10,
                'distributor_id' => 0,
                'name' => 'D',
                'number' => 5,
                'max' => 0,
            ],
            [
                'id' => 16,
                'distributor_id' => 0,
                'name' => 'g',
                'number' => 10,
                'max' => 0,
            ],
            [
                'id' => 17,
                'distributor_id' => 0,
                'name' => 'g',
                'number' => 5,
                'max' => 0,
            ],
            [
                'id' => 18,
                'distributor_id' => 0,
                'name' => 'aaww',
                'number' => 122,
                'max' => 0,
            ]
        ];
        DB::table('position')->truncate();
        DB::table("position")->insert($dataTables);
    }
}
