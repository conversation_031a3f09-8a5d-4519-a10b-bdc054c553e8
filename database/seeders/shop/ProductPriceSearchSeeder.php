<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductPriceSearchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=product_price_search --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'distributor_id' => 0,
                'title' => '500以下',
                'content' => '~500',
                'online' => 1,
                'orders' => 0,
            ],
            [
                'id' => 2,
                'distributor_id' => 0,
                'title' => '500~1000',
                'content' => '500~1000',
                'online' => 1,
                'orders' => 1,
            ],
            [
                'id' => 3,
                'distributor_id' => 0,
                'title' => '1000以上',
                'content' => '1000~',
                'online' => 1,
                'orders' => 2,
            ]
        ];
        DB::table('product_price_search')->truncate();
        DB::table("product_price_search")->insert($dataTables);
    }
}
