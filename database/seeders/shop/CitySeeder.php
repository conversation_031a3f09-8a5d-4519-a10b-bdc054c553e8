<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=city --output=shop
         *
         */

        $dataTables = [
            [
                'Name' => '台北市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 1,
            ],
            [
                'Name' => '基隆市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 2,
            ],
            [
                'Name' => '新北市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 3,
            ],
            [
                'Name' => '宜蘭縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 4,
            ],
            [
                'Name' => '新竹市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 5,
            ],
            [
                'Name' => '新竹縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 6,
            ],
            [
                'Name' => '桃園市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 7,
            ],
            [
                'Name' => '苗栗縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 8,
            ],
            [
                'Name' => '台中市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 9,
            ],
            [
                'Name' => '彰化縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 10,
            ],
            [
                'Name' => '南投縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 11,
            ],
            [
                'Name' => '雲林縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 12,
            ],
            [
                'Name' => '嘉義市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 13,
            ],
            [
                'Name' => '嘉義縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 14,
            ],
            [
                'Name' => '台南市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 15,
            ],
            [
                'Name' => '高雄市',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 16,
            ],
            [
                'Name' => '南海諸島',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 17,
            ],
            [
                'Name' => '澎湖縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 18,
            ],
            [
                'Name' => '屏東縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 19,
            ],
            [
                'Name' => '台東縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 20,
            ],
            [
                'Name' => '花蓮縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 21,
            ],
            [
                'Name' => '金門縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 22,
            ],
            [
                'Name' => '連江縣',
                'Order' => 0,
                'State' => 0,
                'AutoNo' => 23,
            ]
        ];
        DB::table('city')->truncate();
        DB::table("city")->insert($dataTables);
    }
}
