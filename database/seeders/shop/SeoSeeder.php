<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SeoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=seo --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'title' => '天脈購物網站',
                'seokey' => '能量,氣',
                'descr' => '天脈購物網站',
                'fb_name' => '天脈購物網站',
                'fb_title' => '天脈購物網站',
                'fb_descr' => '',
                'fb_img' => '/upload/seo_fb_img.png?1619888314',
                'twitter_name' => '天脈購物網站',
                'twitter_title' => '天脈購物網站',
                'twitter_descr' => '',
                'verification' => '',
                'trackgoogle' => '',
                'marketgoogle' => '',
                'marketyahoo' => '',
                'display' => '',
                'map' => 'sitemap.jpg',
            ]
        ];
        DB::table('seo')->truncate();
        DB::table("seo")->insert($dataTables);
    }
}
