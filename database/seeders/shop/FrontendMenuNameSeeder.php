<?php

namespace Database\Seeders\shop;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FrontendMenuNameSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        /**
         * Command :
         * artisan seed:generate --table-mode --tables=frontend_menu_name --output=shop
         *
         */

        $dataTables = [
            [
                'id' => 1,
                'name' => '關於我們',
                'en_name' => 'ABOUT US',
                'controller' => 'about',
                'second_menu' => '{"about_story":{"name":"歷史沿革"},"about_map":{"name":"地圖資訊"},"about_contact":{"name":"聯絡我們"}}',
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => 17,
            ],
            [
                'id' => 2,
                'name' => '各式課程',
                'en_name' => 'PRODUCT',
                'controller' => 'product',
                'second_menu' => '{"activity":{"name":"優惠專區", "en_name":"OFFER"}}',
                'text_color' => '#ffffff',
                'pic' => '/upload/frontend_menu_name_2.png?1309694762',
                'backstage_menu_second_id' => 2,
            ],
            [
                'id' => 3,
                'name' => '有感體驗',
                'en_name' => 'EXPERIENCE',
                'controller' => 'experience',
                'second_menu' => NULL,
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => 16,
            ],
            [
                'id' => 4,
                'name' => '活動公告',
                'en_name' => 'ANNOUNCEMENT',
                'controller' => 'activity',
                'second_menu' => NULL,
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => 18,
            ],
            [
                'id' => 5,
                'name' => '常見問題',
                'en_name' => 'Question',
                'controller' => 'qa',
                'second_menu' => NULL,
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => 11,
            ],
            [
                'id' => 6,
                'name' => '經銷據點',
                'en_name' => 'DISTRIBUTORS',
                'controller' => 'distribution',
                'second_menu' => NULL,
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => 12,
            ],
            [
                'id' => 7,
                'name' => '最新消息',
                'en_name' => 'NEWS',
                'controller' => 'news',
                'second_menu' => NULL,
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => 15,
            ],
            [
                'id' => 8,
                'name' => '幫我找貨',
                'en_name' => 'Findorder',
                'controller' => 'findorder',
                'second_menu' => NULL,
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => NULL,
            ],
            [
                'id' => 9,
                'name' => '消費功能',
                'en_name' => 'Consumption',
                'controller' => 'consumption',
                'second_menu' => '{"gift":{"name":"消費累積兌換", "en_name":"Gift"},"luckdraw":{"name":"消費刮刮樂", "en_name":"Lucky Draw"},"createpay":{"name":"新增付款", "en_name":"Pay"}}',
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => NULL,
            ],
            [
                'id' => 10,
                'name' => '文章分享',
                'en_name' => 'Article Sharing',
                'controller' => 'share_article',
                'second_menu' => NULL,
                'text_color' => NULL,
                'pic' => NULL,
                'backstage_menu_second_id' => NULL,
            ]
        ];
        DB::table('frontend_menu_name')->truncate();
        DB::table("frontend_menu_name")->insert($dataTables);
    }
}
