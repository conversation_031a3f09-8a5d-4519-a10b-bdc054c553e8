<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use Illuminate\Database\Seeder;
use Database\Seeders\Shop_admin\AccountSeeder;
use Database\Seeders\Shop_admin\BasicAuthSeeder;
use Database\Seeders\Shop_admin\BonusSettingSeeder;
use Database\Seeders\Shop_admin\CenterLevelSeeder;
use Database\Seeders\Shop_admin\ExcelSeeder;
use Database\Seeders\Shop_admin\LangSeeder;
use Database\Seeders\Shop_admin\LogisticsCodeSeeder;
use Database\Seeders\Shop_admin\MessageStatusCodeSeeder;
use Database\Seeders\Shop_admin\PartnerLevelSeeder;
use Database\Seeders\Shop_admin\VipTypeSeeder;
use Database\Seeders\shop\AboutStorySeeder;
use Database\Seeders\shop\AdminSeeder;
use Database\Seeders\shop\AdminInfoSeeder;
use Database\Seeders\shop\BackstageMenuSeeder;
use Database\Seeders\shop\BackstageMenuSecondSeeder;
use Database\Seeders\shop\CitySeeder;
use Database\Seeders\shop\ConsentSeeder;
use Database\Seeders\shop\ContactSeeder;
use Database\Seeders\shop\DefaultContentSeeder;
use Database\Seeders\shop\DiscountSeeder;
use Database\Seeders\shop\ExpiringProductSeeder;
use Database\Seeders\shop\FrontendDataNameSeeder;
use Database\Seeders\shop\FrontendMenuNameSeeder;
use Database\Seeders\shop\HotProductSeeder;
use Database\Seeders\shop\IndexExcelSeeder;
use Database\Seeders\shop\IndexOnlineSeeder;
use Database\Seeders\shop\PointsSettingSeeder;
use Database\Seeders\shop\PositionSeeder;
use Database\Seeders\shop\ProdescSeeder;
use Database\Seeders\shop\ProductSeeder;
use Database\Seeders\shop\ProductinfoSeeder;
use Database\Seeders\shop\RecommendProductSeeder;
use Database\Seeders\shop\SeoSeeder;
use Database\Seeders\shop\ShippingFeeSeeder;
use Database\Seeders\shop\SlideshowSeeder;
use Database\Seeders\shop\SystemEmailSeeder;
use Database\Seeders\shop\SystemIntroSeeder;
use Database\Seeders\shop\TimeProductSeeder;
use Database\Seeders\shop\TownSeeder;
use Database\Seeders\shop\FieldsSetSeeder;
use Database\Seeders\shop\ConsumptionDrawLimitSeeder;
use Database\Seeders\shop\WanpayDataSeeder;
use Database\Seeders\shop\ProductPriceSearchSeeder;
use Database\Seeders\shop\PayFeeSeeder;
use Database\Seeders\shop\PointsAllowUseSeeder;
use Database\Seeders\shop\LovecodeSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            //------------ main_db (shop_admin)------------
            AccountSeeder::class,
            BasicAuthSeeder::class,
            BonusSettingSeeder::class,
            CenterLevelSeeder::class,
            ExcelSeeder::class,
            LangSeeder::class,
            LogisticsCodeSeeder::class,
            MessageStatusCodeSeeder::class,
            PartnerLevelSeeder::class,
            VipTypeSeeder::class,

            //----------------------(shop)-----------------------
            AboutStorySeeder::class,
            AdminSeeder::class,
            AdminInfoSeeder::class,
            BackstageMenuSeeder::class,
            BackstageMenuSecondSeeder::class,
            CitySeeder::class,
            ConsentSeeder::class,
            ContactSeeder::class,
            DefaultContentSeeder::class,
            DiscountSeeder::class,
            ExpiringProductSeeder::class,
            FrontendDataNameSeeder::class,
            FrontendMenuNameSeeder::class,
            HotProductSeeder::class,
            IndexExcelSeeder::class,
            IndexOnlineSeeder::class,
            PointsSettingSeeder::class,
            PositionSeeder::class,
            ProdescSeeder::class,
            ProductSeeder::class,
            ProductinfoSeeder::class,
            RecommendProductSeeder::class,
            SeoSeeder::class,
            ShippingFeeSeeder::class,
            SlideshowSeeder::class,
            SystemEmailSeeder::class,
            SystemIntroSeeder::class,
            TimeProductSeeder::class,
            TownSeeder::class,
            FieldsSetSeeder::class,
            ConsumptionDrawLimitSeeder::class,
            WanpayDataSeeder::class,
            ProductPriceSearchSeeder::class,
            PayFeeSeeder::class,
            PointsAllowUseSeeder::class,
            LovecodeSeeder::class,
        ]);
    }
}
