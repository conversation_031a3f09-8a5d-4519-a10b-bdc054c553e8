
INSERT INTO `backstage_menu_second` 
(`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
(90, '訂單管理', '揀貨列表', '/order/order_ctrl/index.html?state=New', NULL, NULL, '5', '7', '0', 'order_order_ctrl_index_New', '_parent');

UPDATE `backstage_menu_second` SET `class` = 'order_pick_index' WHERE `backstage_menu_second`.`id` = 90;
UPDATE `backstage_menu_second` SET `url` = '/order/pick/index' WHERE `backstage_menu_second`.`id` = 90;


CREATE TABLE `picked_history` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(255) DEFAULT NULL COMMENT '對應訂單id',
  `order_time` int(11) NOT NULL,
  `p_code` varchar(255) DEFAULT NULL,
  `num` int(10) NOT NULL COMMENT '揀貨總計',
  `product_id` int(11) DEFAULT NULL,
  `productinfo_type` int(11) DEFAULT NULL,
  `position_id` int(10) DEFAULT NULL,
  `position_number` int(10) DEFAULT NULL COMMENT '編碼號碼',
  `deal_position` tinyint(1) NOT NULL DEFAULT 0 COMMENT '揀貨狀態 0.未揀貨 1.已揀貨',
  `datetime` datetime DEFAULT NULL COMMENT '揀貨時間',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='揀貨歷史紀錄';

ALTER TABLE `product` COMMENT = '分館列表';
ALTER TABLE `productinfo` COMMENT = '商品詳細資訊';
ALTER TABLE `productinfo_type` COMMENT = '商品規格詳細資訊';
ALTER TABLE `position` COMMENT = '貨架列表';
ALTER TABLE `position_portion` COMMENT = '實際庫存列表';

