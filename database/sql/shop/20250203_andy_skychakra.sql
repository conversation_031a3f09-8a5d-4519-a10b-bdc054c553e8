-- 會員文章
CREATE TABLE `member_article` (
  `id` INT(11) NOT NULL AUTO_INCREMENT , 
  `user_id` INT(11) NOT NULL DEFAULT '0' COMMENT '所屬會員',
  `show_status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '文章狀態(0.隱藏 1.顯示)(使用者控制)' , 
  `show` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '狀態(0.隱藏 1.顯示)(管理者控制)' , 
  `orders` INT(11) NOT NULL DEFAULT '0' COMMENT '排序(管理者控制)' , 
  `name` CHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章名稱' , 
  `img` VARCHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文章圖片路徑' , 
  `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文章內容' , 
  `create_time` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '建立時間(timestamp)' , 
  PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '會員文章';

CREATE TABLE `member_article_interact` (
  `id` INT(11) NOT NULL AUTO_INCREMENT , 
  `visitorId` CHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '檢舉人ip' , 
  `article_id` INT(11) NOT NULL DEFAULT '0' COMMENT '對應文章id',
  `act_type` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '互動類型(1.瀏覽 2.喜歡)',
  `create_time` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '建立時間(timestamp)' , 
  `value` INT(11) NOT NULL DEFAULT '0' COMMENT '數值(統計次數用)',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '會員文章-互動類型紀錄';

CREATE TABLE `member_article_report` (
  `id` INT(11) NOT NULL AUTO_INCREMENT , 
  `ip` CHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '檢舉人ip' , 
  `article_id` INT(11) NOT NULL DEFAULT '0' COMMENT '對應文章id',
  `note` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '檢舉內容' , 
  `create_time` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '建立時間(timestamp)' , 
  PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '會員文章-檢舉紀錄';

-- 添加前台選單資料
INSERT INTO `frontend_menu_name` (`id`, `name`, `en_name`, `controller`, `second_menu`, `text_color`, `pic`, `backstage_menu_second_id`) VALUES ('10', '文章分享', 'Article Sharing', 'share_article', NULL, NULL, NULL, NULL);

INSERT INTO `backstage_menu_second` 
(`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
('102', '會員文章分享', '會員文章分享', '/admin/share_article/index', 'index/share_article/index', NULL, '10', '4', '0', 'share_article_index', '_parent');

