-- 添加積分總計
INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
(97, '增值積分總覽', '增值積分總覽', '/order/point/point_increasable_record', NULL, '', '16', '10', '0', 'order_point_point_increasable_record', '_parent'),
(98, '現金積分總覽', '現金積分總覽', '/order/point/points_record', NULL, '', '17', '10', '0', 'order_point_points_record', '_parent');
