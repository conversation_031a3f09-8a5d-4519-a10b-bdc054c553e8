-- 允許積分計算有小數
ALTER TABLE `productinfo_type` CHANGE `price_cv` `price_cv` DECIMAL(26, 2) NOT NULL DEFAULT '0' COMMENT 'CV金額';

-- 添加提現紀錄管理
INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
('96', '積分提現紀錄', '積分提現紀錄', '/order/point/cash', NULL, '', '23', '10', '0', 'order_point_cash', '_parent');
