-- 更正需求php版本
    UPDATE `system_intro` SET `php_version` = '7.2' WHERE `system_intro`.`system_id` = 1;

-- 加入商品詢問信
    ALTER TABLE `system_email` ADD `product_qa` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '商品詢問信' AFTER `order_cancel`;


-- 2021-07-06 新增商品品項對應商品圖片
    ALTER TABLE `productinfo_type` ADD `pic_index` INT NOT NULL DEFAULT '1' COMMENT '對應商品圖片第幾張' AFTER `title`;
-- 2021-07-06 商品新增第五塊說明
    ALTER TABLE `productinfo` CHANGE `updatetime` `updatetime` TIMESTAMP NULL DEFAULT NULL;
    ALTER TABLE `productinfo` ADD `text5` TEXT CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL AFTER `text4_online`, 
    ADD `text5_online` TINYINT(4) NOT NULL DEFAULT '1' AFTER `text5`;
    ALTER TABLE `default_content` ADD `text5` TEXT CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL AFTER `text4`;
-- 2021-07-06 推薦商品修正改為無限量
    ALTER TABLE `productinfo` ADD `pushitem` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '推薦商品(品號 ,分隔)' AFTER `updatetime`;
    ALTER TABLE `productinfo`
      DROP `pushitem1`,
      DROP `pushitem2`,
      DROP `pushitem3`,
      DROP `pushitem4`;
-- 2021-07-08 商品加入人氣功能
    CREATE TABLE `product_love` ( `id` INT(11) NOT NULL , `user_id` INT(11) NOT NULL COMMENT '使用者id' , `product_id` INT(11) NOT NULL COMMENT '商品id' , `datetime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '時間' ) ENGINE = InnoDB;
    ALTER TABLE `product_love` ADD PRIMARY KEY( `id`);
    ALTER TABLE `product_love` CHANGE `id` `id` int(11) AUTO_INCREMENT;
-- 2021-07-09 商品加入收藏功能
    CREATE TABLE `product_store` (
      `id` int(11) NOT NULL,
      `user_id` int(11) NOT NULL COMMENT '使用者id',
      `product_id` int(11) NOT NULL COMMENT '商品id',
      `datetime` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '時間'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
    ALTER TABLE `product_store`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `product_store`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;


-- 2021-07-26 加入我要找貨功能
    CREATE TABLE `contact_find_prod` ( 
      `id` INT NOT NULL , 
      `user_id` INT NOT NULL , 
      `ask` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '詢問內容{name, unit, num, img, note}',
      `createdate` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '建立時間' , 
      `status` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '狀態 0.未處理 1.以處理' , 
      `resp` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '回覆內容' , 
      `respdate` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '回覆時間'
    ) ENGINE = MyISAM;
    ALTER TABLE `contact_find_prod` ADD PRIMARY KEY( `id`);
    ALTER TABLE `contact_find_prod` CHANGE id id int(11) AUTO_INCREMENT;

    ALTER TABLE `contact_find_prod` 
    ADD `user_name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '姓名' AFTER `user_id`, 
    ADD `user_phone` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '手機' AFTER `user_name`, 
    ADD `user_email` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '信箱' AFTER `user_phone`;


    INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) 
    VALUES (NULL, '找貨回函', '找貨回函', '/admin/Findorder/findorder', 'index/findorder/findorder', NULL, '2', '4', '0', 'findorder_findorder', '_parent');
    UPDATE `backstage_menu_second` SET `sort` = '3' WHERE `backstage_menu_second`.`name` = '常見問題';
    UPDATE `backstage_menu_second` SET `sort` = '4' WHERE `backstage_menu_second`.`name` = '經銷據點';

    INSERT INTO `frontend_menu_name` (`id`, `name`, `en_name`, `controller`, `second_menu`) VALUES ('8', '幫我找貨', 'Findorder', 'findorder', NULL);


-- 2021-08-11 首頁管理關閉分館廣告不關閉分館
    ALTER TABLE `product` ADD `ad_online` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '顯示分館廣告 0.否 1.是' AFTER `online`;


-- 2021-09-01 優惠券、直接輸入型優惠券改名稱
    UPDATE `backstage_menu_second` SET `show_name` = '會員優惠券' WHERE `backstage_menu_second`.`id` = 9;
    UPDATE `backstage_menu_second` SET `show_name` = '活動優惠券' WHERE `backstage_menu_second`.`id` = 68;
-- 2021-09-10 後台可自行修改favicon
    ALTER TABLE `admin_info` ADD `favicon` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '分頁縮圖favicon.ico' AFTER `id`;

-- 2021-09-17 可自行設定成功/錯誤提示訊息圖片
    ALTER TABLE `admin_info` ADD 
    `success_logo` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '成功提示訊息圖片' AFTER `marketing_logo`, 
    ADD `error_logo` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '失敗提示訊息圖片' AFTER `success_logo`;

-- 2021-09-24 活動優惠券做資料庫防呆(user_code不可重複)
    ALTER TABLE `coupon_direct` CHANGE `user_code` `user_code` VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;
    ALTER TABLE `coupon_direct` CHANGE `content` `content` TEXT CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;
    ALTER TABLE `coupon_direct` ADD UNIQUE( `user_code`);
-- 2021-09-27 拔除angular
    UPDATE `system_intro` set `front_end` = 'bootstrap\r\nvue.js', `back_end` = 'bootstrap\r\nvue.js';


-- 2021-09-27 開發報名功能
    INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (NULL, '常用欄位管理', '常用欄位管理', '/admin/fields/fields_set', NULL, NULL, '16', '8', '0', 'fields_fields_set', '_parent'),
    (NULL, '常用註記詞管理', '常用註記詞管理', '/admin/fields/comments_set', NULL, NULL, '16', '8', '0', 'fields_comments_set', '_parent');
    -- 常用註記詞管理
        CREATE TABLE `comments_set` (
          `id` int(11) NOT NULL,
          `title` text COLLATE utf8_unicode_ci NOT NULL COMMENT '標題',
          `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '內容',
          `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
          `online` tinyint(1) NOT NULL DEFAULT 1 COMMENT '狀態 0.停用 1.啟用'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
        ALTER TABLE `comments_set`
          ADD PRIMARY KEY (`id`);
        ALTER TABLE `comments_set`
          MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
    -- 常用欄位管理
        CREATE TABLE `fields_set` ( 
        `id` INT NOT NULL , 
        `title` TEXT NOT NULL COMMENT '標題' , 
        `type` TEXT NOT NULL COMMENT '輸入方式' , 
        `required` TINYINT NOT NULL DEFAULT '0' COMMENT '必填 0.否 1.是' , 
        `special` TINYINT NOT NULL DEFAULT '0' COMMENT '特殊欄位 0.否 1.是' , 
        `limit` TEXT NULL DEFAULT NULL COMMENT '限定格式' , 
        `discription` TEXT NOT NULL DEFAULT '' COMMENT '說明' , 
        `options` TEXT NULL DEFAULT NULL COMMENT '選項(json格式)' , 
        `order_id` INT(11) NOT NULL COMMENT '排序' , 
        `online` TINYINT NOT NULL DEFAULT '1' COMMENT '狀態 0.停用 1.啟用' ) ENGINE = InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
        ALTER TABLE `fields_set`
          ADD PRIMARY KEY (`id`);
        ALTER TABLE `fields_set`
          MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
        INSERT INTO `fields_set` (`id`, `title`, `type`, `required`, `special`, `limit`, `discription`, `options`, `order_id`, `online`) VALUES
        (1, '姓名', 'text', 1, 0, '.+', '<br />', '[\"選項內容1\",\"選項內容2\",\"選項內容3\",\"選項內容4\"]', -100, 1),
        (2, 'Email', 'text', 1, 0, '[^@ \\t\\r\\n]+@[^@ \\t\\r\\n]+\\.[^@ \\t\\r\\n]+', '', '[]', -99, 1);
    -- 商品設定獨立報名欄位
        CREATE TABLE `productinfo_register_fields` ( 
        `id` INT NOT NULL , 
        `prod_id` INT(11) NOT NULL COMMENT '商品id', 
        `title` TEXT NOT NULL COMMENT '標題' , 
        `type` TEXT NOT NULL COMMENT '輸入方式' , 
        `required` TINYINT NOT NULL DEFAULT '0' COMMENT '必填 0.否 1.是' , 
        `special` TINYINT NOT NULL DEFAULT '0' COMMENT '特殊欄位 0.否 1.是' , 
        `limit` TEXT NULL DEFAULT NULL COMMENT '限定格式' , 
        `discription` TEXT NOT NULL DEFAULT '' COMMENT '說明' , 
        `options` TEXT NULL DEFAULT NULL COMMENT '選項(json格式)' , 
        `order_id` INT(11) NOT NULL COMMENT '排序' , 
        `online` TINYINT NOT NULL DEFAULT '1' COMMENT '狀態 0.停用 1.啟用' ) ENGINE = InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
        ALTER TABLE `productinfo_register_fields`
            ADD PRIMARY KEY (`id`);
        ALTER TABLE `productinfo_register_fields`
            MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
        ALTER TABLE `productinfo_register_fields` ADD `fields_set_id` INT(11) NOT NULL DEFAULT '0' COMMENT '常用欄位id' AFTER `id`;
    -- 調整報名資料表欄位
        ALTER TABLE `examinee_info`
            DROP `parents_name`,
            DROP `parents_phone`,
            DROP `parents_mail`,
            DROP `parents_tel`,
            DROP `parents_add`,
            DROP `examinee_school`,
            DROP `examinee_class`,
            DROP `examinee_name`,
            DROP `examinee_birthday`,
            DROP `examinee_id`,
            DROP `examinee_note`,
            DROP `exam_school`,
            DROP `examinee_room`,
            DROP `examinee_site`,
            DROP `examinee_ticket`,
            DROP `grade`,
            DROP `grade_point`,
            DROP `grade_note`,
            DROP `grade_show`;
        ALTER TABLE `examinee_info` 
        ADD `session_id` VARCHAR(256) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'session_id' AFTER `user_id`, 
        ADD `register_data` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '報名資料' AFTER `session_id`;
        ALTER TABLE `examinee_info` CHANGE `order_id` `order_id` INT(11) NOT NULL COMMENT '0.報名未成立';
        ALTER TABLE `examinee_info` ADD `email` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '報名者信箱(考生)' AFTER `user_id`;
        ALTER TABLE `examinee_info` ADD `cancel` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '取消 0.未取消 1.取消' AFTER `register_data`;
        ALTER TABLE `examinee_info` 
        ADD `roll_call` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '點名 0.無 1.未到 2.到' AFTER `register_data`, 
        ADD `roll_call_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '點名時間' AFTER `roll_call`;
        ALTER TABLE `examinee_info` ADD `datetime` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '建立日期' AFTER `user_id`;
        ALTER TABLE `examinee_info` ADD `name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '姓名' AFTER `datetime`;
    -- 調整商品上架(品項)
        ALTER TABLE `productinfo_type` ADD 
        `start_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL COMMENT '報名開始時間' AFTER `online`, 
        ADD `end_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL COMMENT '報名結束時間' AFTER `start_time`, 
        ADD `act_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL COMMENT '活動日期' AFTER `end_time`, 
        ADD `closed` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '停止開課 0.否 1.是' AFTER `act_time`, 
        ADD `closed_date` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL COMMENT '取消課程日期' AFTER `closed`;
        ALTER TABLE `productinfo_type` 
        CHANGE `start_time` `start_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '報名開始時間', 
        CHANGE `end_time` `end_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '報名結束時間', 
        CHANGE `act_time` `act_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '活動提醒時間';
    -- 商品加入點名判斷
        ALTER TABLE `productinfo` ADD `is_roll_call` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否需點名 0.否 1.是' AFTER `is_registrable`;
        ALTER TABLE `productinfo` ADD `remind_msg` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '活動提醒訊息' AFTER `expire_date`;
    -- 活動提醒信
        ALTER TABLE `system_email` ADD `act_remind` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '活動提醒信' AFTER `product_qa`;
        ALTER TABLE `system_email` ADD `act_cancel` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '活動取消信' AFTER `act_remind`;
        UPDATE `system_email` SET 
        `act_remind` = '<p class=\"MsoNormal\"> 若有任何疑問，<br />歡迎聯繫傳訊光測試商城shopfull001a客服：<span>XXXXXXXXXX&nbsp;</span>或 XXXXXXXXXX</p><p class=\"MsoNormal\"> <span style=\"color:#E53333;\">【營業時間】</span>週一至週五XX:XX-XX:XX</p><p class=\"MsoNormal\"> <span style=\"color:#E53333;\">【電話】</span>XXXXXXXXXX<br /><span style=\"color:#E53333;\">【傳真】</span>XXXXXXXXXX<br /><span style=\"color:#E53333;\">【地址】</span>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</p><p class=\"MsoNormal\"> <span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span></p>',
        `act_cancel` = '<p class=\"MsoNormal\"> 若有任何疑問，<br />歡迎聯繫傳訊光測試商城shopfull001a客服：<span>XXXXXXXXXX&nbsp;</span>或 XXXXXXXXXX</p><p class=\"MsoNormal\"> <span style=\"color:#E53333;\">【營業時間】</span>週一至週五XX:XX-XX:XX</p><p class=\"MsoNormal\"> <span style=\"color:#E53333;\">【電話】</span>XXXXXXXXXX<br /><span style=\"color:#E53333;\">【傳真】</span>XXXXXXXXXX<br /><span style=\"color:#E53333;\">【地址】</span>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</p><p class=\"MsoNormal\"> <span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span></p>' 
        WHERE `system_email`.`id` = 1;
    -- 調整商品、品項、報名關係
        ALTER TABLE `productinfo_type` 
        CHANGE `act_time` `act_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '活動開始時間';
        ALTER TABLE `productinfo_type` 
        ADD `act_time_end` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '活動結束時間' AFTER `act_time`, 
        ADD `act_remind_time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '活動提醒時間' AFTER `act_time_end`;
    -- 刪除不必要的時間欄位
        ALTER TABLE `productinfo`
        DROP `examinee_date`,
        DROP `expire_date`;

-- 2021-10-20 修改成各館可獨立控制兌換點數比率
    INSERT INTO `points_setting` (`id`, `value`, `note`) VALUES ('3', '1000', '多少元換一點');

-- 2021-11-03 累積消費兌換、單次消費抽抽樂
    INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (NULL, '消費累積兌換', '消費累積兌換', '/admin/Consumption/exchange', 'index/consumption/exchange', NULL, '7', '3', '0', 'consumption_exchange', '_parent'), 
    (NULL, '消費抽抽樂', '消費抽抽樂', '/admin/Consumption/draw', 'index/consumption/draw', NULL, '8', '3', '0', 'consumption_draw', '_parent');
    CREATE TABLE `consumption_exchange` (
      `id` int(11) NOT NULL,
      `pic` text COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '圖片',
      `name` text COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '商品名稱',
      `price` int(11) DEFAULT NULL COMMENT '兌換累積金額',
      `online` tinyint(1) NOT NULL DEFAULT 1 COMMENT '狀態0.停用 1.啟用'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
    ALTER TABLE `consumption_exchange`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `consumption_exchange`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
    CREATE TABLE `consumption_draw` (
      `id` int(11) NOT NULL,
      `pic` text COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '圖片',
      `name` text COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '商品名稱',
      `ratio` int(11) DEFAULT NULL COMMENT '佔比',
      `online` tinyint(1) NOT NULL DEFAULT 1 COMMENT '狀態0.停用 1.啟用'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
    ALTER TABLE `consumption_draw`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `consumption_draw`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
    ALTER TABLE `consumption_draw` CHANGE `ratio` `ratio` INT(11) NULL DEFAULT '1' COMMENT '佔比';
    CREATE TABLE `consumption_draw_limit` (
      `id` int(11) NOT NULL,
      `price` int(11) DEFAULT NULL COMMENT '滿多少元抽一次'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
    ALTER TABLE `consumption_draw_limit`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `consumption_draw_limit`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
    INSERT INTO `consumption_draw_limit` (`id`, `price`) VALUES ('1', '1000');
    INSERT INTO `frontend_menu_name` (`id`, `name`, `en_name`, `controller`, `second_menu`) VALUES (9, '消費功能', 'Consumption', 'consumption', NULL);
    UPDATE `frontend_menu_name` SET `second_menu` = '{\"gift\":{\"name\":\"消費累積兌換\", \"en_name\":\"Gift\"},\"luckdraw\":{\"name\":\"消費抽抽樂\", \"en_name\":\"Lucky Draw\"},\"createpay\":{\"name\":\"新增付款\", \"en_name\":\"Pay\"}}' 
    WHERE `frontend_menu_name`.`id` = 9;
    CREATE TABLE `consumption_pay_record` ( 
      `id` INT(11) NOT NULL , 
      `user_id` INT(11) NOT NULL COMMENT '會員id' , 
      `price` INT(11) NOT NULL COMMENT '金額' , 
      `datetime` TEXT NULL DEFAULT NULL COMMENT '付款時間' , 
      `audit` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '審核狀態 0.未通過 1.通過' 
    ) ENGINE = InnoDB;
    ALTER TABLE `consumption_pay_record` ADD PRIMARY KEY( `id`);
    ALTER TABLE `consumption_pay_record` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

    INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (NULL, '掃碼付款管理', '掃碼付款管理', '/admin/consumption/pay_list', 'index/consumption/create_pay', NULL, '3', '5', '0', 'consumption_pay_list', '_parent');
    CREATE TABLE `consumption_draw_record` ( 
      `id` INT(11) NOT NULL , 
      `user_id` INT(11) NOT NULL COMMENT '會員id' , 
      `gift_pic` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '贈品圖片' , 
      `gift_name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '贈品名稱' , 
      `createdate` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '建立日期' , 
      `show` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '刮刮樂狀態 0.未刮 1.刮完' , 
      `ex_date` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '兌換日期(無則表示未兌換)' 
    ) ENGINE = InnoDB;
    ALTER TABLE `consumption_draw_record` ADD PRIMARY KEY( `id`);
    ALTER TABLE `consumption_draw_record` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
    ALTER TABLE `consumption_draw_record` ADD `order_id` INT NOT NULL COMMENT '訂單id' AFTER `user_id`;
    ALTER TABLE `consumption_draw_record` ADD `pay_record_id` INT NOT NULL COMMENT '對應付款紀錄id(非0表掃碼付款) ' AFTER `user_id`;
    ALTER TABLE `consumption_draw_record` CHANGE `ex_date` `ex_date` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '兌換日期(無則表示未兌換)';
    ALTER TABLE `consumption_draw_record` ADD `draw_id` INT(11) NOT NULL COMMENT '對應刮刮樂贈品id' AFTER `order_id`;
    CREATE TABLE `consumption_exchange_record` ( 
      `id` INT(11) NOT NULL , 
      `user_id` INT(11) NOT NULL COMMENT '會員id' , 
      `exchange_id` INT(11) NOT NULL COMMENT '兌換商品id' , 
      `ex_date` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '兌換時間' 
    ) ENGINE = InnoDB;
    ALTER TABLE `consumption_exchange_record` ADD PRIMARY KEY( `id`);
    ALTER TABLE `consumption_exchange_record` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
    ALTER TABLE `consumption_exchange_record` 
    ADD `pic` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '贈品圖片' AFTER `exchange_id`, 
    ADD `name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '贈品名稱' AFTER `pic`;

-- 2021-11-25 首頁大圖輪播可停用
    ALTER TABLE `slideshow` ADD `online` TINYINT NOT NULL DEFAULT '1' COMMENT '狀態 0.隱藏 1.顯示' AFTER `link`;

-- 2021-12-08 修正資料庫格式
    ALTER TABLE `consumption_draw_record` 
    CHANGE `gift_pic` `gift_pic` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '贈品圖片', 
    CHANGE `gift_name` `gift_name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '贈品名稱';
    UPDATE `frontend_menu_name` 
    SET `second_menu` = '{\"gift\":{\"name\":\"消費累積兌換\", \"en_name\":\"Gift\"},\"luckdraw\":{\"name\":\"消費刮刮樂\", \"en_name\":\"Lucky Draw\"},\"createpay\":{\"name\":\"新增付款\", \"en_name\":\"Pay\"}}' WHERE `frontend_menu_name`.`id` = 9;
    UPDATE `backstage_menu_second` SET `show_name` = '消費刮刮樂' WHERE `backstage_menu_second`.`id` = 73;
    ALTER TABLE `examinee_info` CHANGE `type_id` `type_id` TEXT NOT NULL;
    ALTER TABLE `examinee_info` CHANGE `roll_call_time` `roll_call_time` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '點名時間';
    ALTER TABLE `productinfo` CHANGE `display` `display` TEXT CHARACTER SET utf8 COLLATE utf8_bin NULL;

-- 2022-01-17 更新隱私政策(加入資料刪除)
    UPDATE `consent` SET `privacy` = '<p> 非常歡迎您光臨<span>「傳訊光科技測試購物</span><span>網站</span><span>」</span>（以下簡稱本網站），為了讓您能夠安心的使用本網站的各項服務與資訊，特此向您說明本網站的隱私權保護政策，以保障您的權益，請您詳閱下列內容：</p><h3> <a id=\"user-content-一隱私權保護政策的適用範圍-1\" class=\"anchor\" href=\"#一隱私權保護政策的適用範圍-1\"></a>一、隱私權保護政策的適用範圍</h3><p>  隱私權保護政策內容，包括本網站如何處理在您使用網站服務時收集到的個人識別資料。隱私權保護政策不適用於本網站以外的相關連結網站，也不適用於非本網站所委託或參與管理的人員。</p><h3>  <a id=\"user-content-二個人資料的蒐集處理及利用方式-1\" class=\"anchor\" href=\"#二個人資料的蒐集處理及利用方式-1\"></a>二、個人資料的蒐集、處理及利用方式與資料刪除</h3><ul> <li>    當您造訪本網站或使用本網站所提供之功能服務時，我們將視該服務功能性質，請您提供必要的個人資料，並在該特定目的範圍內處理及利用您的個人資料；非經您書面同意，本網站不會將個人資料用於其他用途。  </li> <li>    本網站在您使用服務信箱、問卷調查等互動性功能時，會保留您所提供的姓名、電子郵件地址、聯絡方式及使用時間等。 </li> <li>    於一般瀏覽時，伺服器會自行記錄相關行徑，包括您使用連線設備的IP位址、使用時間、使用的瀏覽器、瀏覽及點選資料記錄等，做為我們增進網站服務的參考依據，此記錄為內部應用，決不對外公佈。  </li> <li>    為提供精確的服務，我們會將收集的問卷調查內容進行統計與分析，分析結果之統計數據或說明文字呈現，除供內部研究外，我們會視需要公佈統計數據及說明文字，但不涉及特定個人之資料。 </li><li>如希望刪除本網站對個人蒐集之資料，請填寫聯絡我們回函表，並附上相關帳戶資訊，我們在驗證過後將刪除相關資料。</li></ul><h3>  <a id=\"user-content-三資料之保護-1\" class=\"anchor\" href=\"#三資料之保護-1\"></a>三、資料之保護</h3><ul>  <li>    本網站主機均設有防火牆、防毒系統等相關的各項資訊安全設備及必要的安全防護措施，加以保護網站及您的個人資料採用嚴格的保護措施，只由經過授權的人員才能接觸您的個人資料，相關處理人員皆簽有保密合約，如有違反保密義務者，將會受到相關的法律處分。  </li> <li>    如因業務需要有必要委託其他單位提供服務時，本網站亦會嚴格要求其遵守保密義務，並且採取必要檢查程序以確定其將確實遵守。  </li></ul><h3>  <a id=\"user-content-四網站對外的相關連結-1\" class=\"anchor\" href=\"#四網站對外的相關連結-1\"></a>四、網站對外的相關連結</h3><p> 本網站的網頁提供其他網站的網路連結，您也可經由本網站所提供的連結，點選進入其他網站。但該連結網站不適用本網站的隱私權保護政策，您必須參考該連結網站中的隱私權保護政策。</p><h3> <a id=\"user-content-五與第三人共用個人資料之政策-1\" class=\"anchor\" href=\"#五與第三人共用個人資料之政策-1\"></a>五、與第三人共用個人資料之政策</h3><p> 本網站絕不會提供、交換、出租或出售任何您的個人資料給其他個人、團體、私人企業或公務機關，但有法律依據或合約義務者，不在此限。</p><p> 前項但書之情形包括不限於：</p><ul> <li>    經由您書面同意。  </li> <li>    法律明文規定。 </li> <li>    為免除您生命、身體、自由或財產上之危險。  </li> <li>    與公務機關或學術研究機構合作，基於公共利益為統計或學術研究而有必要，且資料經過提供者處理或蒐集者依其揭露方式無從識別特定之當事人。 </li> <li>    當您在網站的行為，違反服務條款或可能損害或妨礙網站與其他使用者權益或導致任何人遭受損害時，經網站管理單位研析揭露您的個人資料是為了辨識、聯絡或採取法律行動所必要者。  </li> <li>    有利於您的權益。  </li> <li>    本網站委託廠商協助蒐集、處理或利用您的個人資料時，將對委外廠商或個人善盡監督管理之責。 </li></ul><h3>  <a id=\"user-content-六cookie之使用-1\" class=\"anchor\" href=\"#六cookie之使用-1\"></a>六、Cookie之使用</h3><p> 為了提供您最佳的服務，本網站會在您的電腦中放置並取用我們的Cookie，若您不願接受Cookie的寫入，您可在您使用的瀏覽器功能項中設定隱私權等級為高，即可拒絕Cookie的寫入，但可能會導致網站某些功能無法正常執行 。</p><h3>  <a id=\"user-content-七隱私權保護政策之修正-1\" class=\"anchor\" href=\"#七隱私權保護政策之修正-1\"></a>七、隱私權保護政策之修正</h3><p>  本網站隱私權保護政策將因應需求隨時進行修正，修正後的條款將刊登於網站上。</p>' WHERE `consent`.`id` = 1;

-- 2022-01-18 運費標籤功能
    CREATE TABLE `shipping_fee_tag` ( 
      `id` INT NOT NULL , 
      `name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '運費金額名稱' , 
      `price` INT NOT NULL COMMENT '運費標籤金額' 
    ) ENGINE = InnoDB;
    ALTER TABLE `shipping_fee_tag` ADD PRIMARY KEY( `id`);
    ALTER TABLE `shipping_fee_tag` CHANGE `id` `id` int(11) AUTO_INCREMENT;
    ALTER TABLE `shipping_fee_tag` ADD `order_id` INT NOT NULL DEFAULT '0' COMMENT '排序' AFTER `price`;
    ALTER TABLE `productinfo` ADD `shipping_fee_tag` INT NOT NULL DEFAULT '0' COMMENT '套運運費標籤' AFTER `shipping_type`;
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (NULL, '運費標籤管理', '運費標籤管理', '/admin/shippingfeetag/index', NULL, NULL, '11', '8', '0', 'shippingfeetag_index', '_parent');
    UPDATE `backstage_menu_second` SET `show_name` = '運法管理' WHERE `backstage_menu_second`.`id` = 66;

-- 2022-02-07 名稱更換
    UPDATE `backstage_menu_second` SET `show_name` = '立馬省優惠' WHERE `backstage_menu_second`.`id` = 7;

-- 2022-03-10 紀錄申請wanpay金流
    CREATE TABLE `wanpay_data` ( 
        `id` INT NOT NULL , 
        `pay_way` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '無金流付款方式' , 
        `wanpay_way` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '旺沛金流付款方式' , 
        `shop_no` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '特店代號' , 
        `wanpay_key` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '特店金鑰' , 
        `regist_time` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '申請旺沛時間，吾則是未申請' 
    ) ENGINE = InnoDB;
    ALTER TABLE `wanpay_data` ADD PRIMARY KEY( `id`);
    ALTER TABLE `wanpay_data` CHANGE id id int AUTO_INCREMENT;
    INSERT INTO `wanpay_data` (`id`, `pay_way`, `wanpay_way`, `shop_no`, `wanpay_key`, `regist_time`) VALUES ('1', '[\r\n {\"show_name\":\"現場付款\", \"channel\":\"現場付款\"},\r\n {\"show_name\":\"ATM付款\", \"channel\":\"ATM付款\"}\r\n]', '[]', NULL, NULL, NULL);
    ALTER TABLE `wanpay_data` ADD `wanpay_content` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '金流填寫內容' AFTER `wanpay_key`;

-- 2022-03-11 首頁廣 三幅廣告-1 改 無限上傳
    CREATE TABLE `index_ad` (
      `id` int(11) NOT NULL,
      `pic` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
      `title` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
      `content` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
      `url` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
      `time` timestamp NOT NULL DEFAULT current_timestamp(),
      `online` tinyint(1) NOT NULL DEFAULT 1,
      `orders` int(11) NOT NULL DEFAULT 0 COMMENT '排序'
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1;
    ALTER TABLE `index_ad`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `index_ad`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;

-- 2022-04-08 新增首頁管理功能
    ALTER TABLE `index_online` 
    ADD `block_iframe` TINYINT(4) NOT NULL DEFAULT '1' COMMENT '嵌入區開關' AFTER `block_edm`, 
    ADD `block_news` TINYINT(4) NOT NULL DEFAULT '1' COMMENT '最新消息開關' AFTER `block_iframe`;
    INSERT INTO `index_excel` (`id`, `data1`, `data2`, `data3`) VALUES (38, 'block_iframe', '', '');
-- 2022-04-08 最新消息加圖、小說明
    ALTER TABLE `news` ADD `pic` VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL AFTER `id`;
    ALTER TABLE `news` ADD `description` TEXT CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL COMMENT '小說明' AFTER `title`;
-- 2022-04-08 展示頁面可上傳banner圖
    ALTER TABLE `frontend_menu_name`
    ADD `text_color` VARCHAR(12) NULL DEFAULT NULL COMMENT '文字色(色碼)' AFTER `second_menu`, 
    ADD `pic` TEXT NULL DEFAULT NULL COMMENT '背景圖片' AFTER `text_color`;
    INSERT INTO `backstage_menu_second`
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES
    (NULL, 'banner管理', 'banner管理', '/admin/banner/index', '', NULL, '-1', '6', '0', 'banner_index', '_parent');
-- 2022-04-11 校正有感體驗、活動專區資料流
    UPDATE `frontend_menu_name` SET `controller` = 'experience' WHERE `frontend_menu_name`.`id` = 3;
    UPDATE `frontend_menu_name` SET `controller` = 'activity' WHERE `frontend_menu_name`.`id` = 4;
    ALTER TABLE `experience` RENAME TO `experience1`;
    ALTER TABLE `activity` RENAME TO `experience`;
    ALTER TABLE `experience1` RENAME TO `activity`;
    UPDATE `backstage_menu_second` SET `Front_desk` = 'index/activity/activity' WHERE `backstage_menu_second`.`id` = 18;
    UPDATE `backstage_menu_second` SET `url` = '/admin/activity/index' WHERE `backstage_menu_second`.`id` = 18;
    UPDATE `backstage_menu_second` SET `class` = 'activity_index' WHERE `backstage_menu_second`.`id` = 18;
    UPDATE `backstage_menu_second` SET `Front_desk` = 'index/experience/experience' WHERE `backstage_menu_second`.`id` = 16;
    UPDATE `backstage_menu_second` SET `url` = '/admin/experience/index' WHERE `backstage_menu_second`.`id` = 16;
    UPDATE `backstage_menu_second` SET `class` = 'experience_index' WHERE `backstage_menu_second`.`id` = 16;
-- 2022-04-12 桃園改市
    UPDATE `city` SET `Name` = '桃園市' WHERE `city`.`AutoNo` = 7;
    UPDATE `town` SET `Name` = '中壢區' WHERE `town`.`AutoNo` = 76;
    UPDATE `town` SET `Name` = '平鎮區' WHERE `town`.`AutoNo` = 77;
    UPDATE `town` SET `Name` = '龍潭區' WHERE `town`.`AutoNo` = 78;
    UPDATE `town` SET `Name` = '楊梅區' WHERE `town`.`AutoNo` = 79;
    UPDATE `town` SET `Name` = '新屋區' WHERE `town`.`AutoNo` = 80;
    UPDATE `town` SET `Name` = '觀音區' WHERE `town`.`AutoNo` = 81;
    UPDATE `town` SET `Name` = '桃園區' WHERE `town`.`AutoNo` = 82;
    UPDATE `town` SET `Name` = '龜山區' WHERE `town`.`AutoNo` = 83;
    UPDATE `town` SET `Name` = '八德區' WHERE `town`.`AutoNo` = 84;
    UPDATE `town` SET `Name` = '大溪區' WHERE `town`.`AutoNo` = 85;
    UPDATE `town` SET `Name` = '復興區' WHERE `town`.`AutoNo` = 86;
    UPDATE `town` SET `Name` = '大園區' WHERE `town`.`AutoNo` = 87;
    UPDATE `town` SET `Name` = '蘆竹區' WHERE `town`.`AutoNo` = 88;
-- 2022-04-12 分館可顯示於選單及推薦商品
    ALTER TABLE `product` 
    ADD `recommend` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '推建商品(填入商品id逗點分隔)' AFTER `webtype_description`, 
    ADD `show_on_nav` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '顯示於選單' AFTER `recommend`;
-- 2022-04-13 報名資料添加修改截止日設定
    ALTER TABLE `productinfo` ADD `register_data_change_limit` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '報名資料修改截止日' AFTER `shipping_fee_tag`;
-- 2022-04-14 可開關商品總分類選單
    ALTER TABLE `index_online` ADD `product_nav_total` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '總商品選單' AFTER `block_news`;


-- 2022-04-22 控制顯示其他選單
    ALTER TABLE `index_online` 
    ADD `nav_other` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '顯示其他選單' AFTER `product_nav_total`, 
    ADD `nav_other_footer` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '顯示其他選單頁尾' AFTER `nav_other`;

-- 2022-05-30 經銷據點修改
    UPDATE `backstage_menu_second` SET `url` = '/admin/stronghold/index' WHERE `backstage_menu_second`.`id` = 12;
    UPDATE `backstage_menu_second` SET `class` = 'stronghold_index' WHERE `backstage_menu_second`.`id` = 12;
    ALTER TABLE `stronghold`
      DROP `pic_icon`,
      DROP `pic`,
      DROP `index_adv01_pic`,
      DROP `index_adv01_link`,
      DROP `index_adv02_pic`,
      DROP `index_adv02_link`,
      DROP `index_adv03_pic`,
      DROP `index_adv03_link`,
      DROP `index_adv04_pic`,
      DROP `index_adv04_link`,
      DROP `index_adv05_pic`,
      DROP `index_adv05_link`,
      DROP `index_adv06_pic`,
      DROP `index_adv06_link`,
      DROP `index_adv07_pic`,
      DROP `index_adv07_link`,
      DROP `inner_adv01_pic`,
      DROP `inner_adv01_link`,
      DROP `inner_adv02_pic`,
      DROP `inner_adv02_link`,
      DROP `webtype_keywords`,
      DROP `webtype_description`;

-- 2022-06-02 權限連動調整
    UPDATE `backstage_menu_second` SET `name` = '會員管理', `show_name` = '會員管理' WHERE `backstage_menu_second`.`id` = 21;
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (NULL, '訂單管理', '訂單管理', '/order/order_ctrl/index/state/New.html', NULL, NULL, '0', '7', '0', NULL, '_blank');
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (NULL, '商品問答', '商品問答', '/order/productqa/index.html', NULL, NULL, '0', '7', '0', NULL, '_blank');
    DELETE FROM `backstage_menu_second` WHERE `backstage_menu_second`.`id` = 19;
    UPDATE `backstage_menu_second` SET `sort` = '5' WHERE `backstage_menu_second`.`id` = 77;
    UPDATE `backstage_menu_second` SET `sort` = '10' WHERE `backstage_menu_second`.`id` = 78;
    UPDATE `backstage_menu_second` SET `sort` = '15' WHERE `backstage_menu_second`.`id` = 60;
    UPDATE `backstage_menu_second` SET `sort` = '20' WHERE `backstage_menu_second`.`id` = 59;

-- 2022-07-11 修改商品品項名稱長度
    ALTER TABLE `productinfo_type` CHANGE `title` `title` TEXT CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;

-- 2022-07-15 新增IG
    INSERT INTO `index_excel` (`id`, `data1`, `data2`, `data3`) VALUES ('39', NULL, '', NULL);

-- 2022-08-08 活動優惠券可個別設定限制使用次數
    ALTER TABLE `coupon_direct` ADD `limit_num` INT NULL DEFAULT NULL COMMENT '限制使用次數' AFTER `content`;

-- 2022-08-15 購物車選單調整
    DROP TABLE `backstage_menu`, `backstage_menu_second`;
    CREATE TABLE `backstage_menu` (
      `id` int(10) NOT NULL,
      `title` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '功能類型',
      `name` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
      `sort` int(10) NOT NULL
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1;
    INSERT INTO `backstage_menu` (`id`, `title`, `name`, `sort`) VALUES
    (1, '介紹項', 'C首頁展示', 2),
    (2, '功能項', 'F商品管理區', 6),
    (3, '', 'H行銷項目', 8),
    (4, '', 'E圖文編輯項目', 5),
    (5, '', 'G功能應用項目', 7),
    (6, '', 'D關於我們', 3),
    (7, '訂單綜合項', 'I訂單管理', 9),
    (8, '', 'J參數設定', 10),
    (9, '', 'K其它功能', 12),
    (10, '會員項', 'B會員功能管理', 1);

    CREATE TABLE `backstage_menu_second` (
      `id` int(11) NOT NULL,
      `name` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
      `show_name` text CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '顯示名稱',
      `url` varchar(100) DEFAULT NULL,
      `Front_desk` varchar(50) DEFAULT NULL,
      `count_id` varchar(20) DEFAULT NULL,
      `sort` int(10) DEFAULT NULL,
      `backstage_menu_id` int(10) DEFAULT NULL,
      `important` int(2) NOT NULL DEFAULT 0 COMMENT '醒目標示',
      `class` varchar(128) DEFAULT NULL COMMENT 'active用',
      `target` varchar(10) NOT NULL DEFAULT '_parent' COMMENT '開啟模式'
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1;
    INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES
    (1, '首頁編輯', '首頁管理', '/admin/index/index', 'index/index/index', NULL, 0, 1, 0, 'index_index', '_parent'),
    (2, '新增商品', '新增商品', '/admin/all/index', NULL, NULL, 1, 2, 0, 'all_index', '_parent'),
    (3, '商品銷售', '實體銷存', '/admin/sell/index', NULL, NULL, 12, 5, 0, 'sell_index', '_parent'),
    (4, '庫存警示', '庫存警示', '/admin/limit/index', NULL, 'limitCount', 3, 5, 0, 'limit_index', '_parent'),
    (6, '活動優惠', '活動優惠', '/admin/Act/index', NULL, 'ActCount', 1, 3, 0, 'act_index', '_parent'),
    (7, '折扣優惠', '立馬省優惠', '/admin/discount/index', 'index/product/activity', 'DisCount', 2, 3, 0, 'discount_index', '_parent'),
    (8, '價格組合設定', '價格組合設定', '/admin/Disset/index', NULL, NULL, 3, 2, 0, 'disset_index', '_parent'),
    (9, '優惠券專區', '會員優惠券', '/admin/Coupon/index', 'index/coupon/coupon', NULL, 4, 3, 0, 'coupon_index', '_parent'),
    (10, '客戶來函', '客戶來函', '/admin/Contact/index', 'index/about/about_contact', NULL, 2, 6, 0, 'contact_index', '_parent'),
    (11, '常見問題', '常見問題', '/admin/qa/index', 'index/qa/qa', NULL, 4, 4, 0, 'qa_index', '_parent'),
    (12, '經銷據點', '經銷據點', '/admin/stronghold/index', 'index/distribution/distribution', NULL, 4, 4, 0, 'stronghold_index', '_parent'),
    (13, '機身碼管理', '機身碼管理', '/admin/excel/index', NULL, NULL, 6, 5, 0, 'excel_index', '_parent'),
    (14, '註冊商品回函', '註冊商品回函', '/admin/excel/reply', 'index/member/reg_product', NULL, 8, 5, 0, 'excel_reply', '_parent'),
    (15, '最新消息', '最新消息', '/admin/news/index', 'index/news/news_c', NULL, 1, 4, 0, 'news_index', '_parent'),
    (16, '有感體驗', '有感體驗', '/admin/experience/index', 'index/experience/experience', NULL, 3, 4, 0, 'experience_index', '_parent'),
    (17, '關於我們', '關於我們', '/admin/about/index', 'index/about/about_story', NULL, 1, 6, 0, 'about_index', '_parent'),
    (18, '活動專區', '活動專區', '/admin/activity/index', 'index/activity/activity', NULL, 0, 4, 0, 'activity_index', '_parent'),
    (21, '會員管理', '會員管理↗', '/order/index/index.html', NULL, '', 0, 10, 0, NULL, '_parent'),
    (22, '同意書設定', '同意書設定', '/admin/consent/index', NULL, NULL, 1, 8, 0, 'consent_index', '_parent'),
    (23, '帳號管理', '帳號管理', '/admin/admin/edit', NULL, NULL, 1, 5, 0, 'admin_edit', '_parent'),
    (24, 'SEO設定', 'SEO設定', '/admin/seo/edit', NULL, NULL, 2, 8, 0, 'seo_edit', '_parent'),
    (25, '存放位置管理', '存放位置管理', '/admin/Position/index', NULL, NULL, 11, 5, 0, 'position_index', '_parent'),
    (26, '點數設定', '消費點數設定', '/admin/admin/point_set', 'index/points/points', NULL, 0, 3, 0, 'admin_point_set', '_parent'),
    (27, '自動登出設定', '自動登出設定', '/admin/admin/maxlifetime_set', NULL, NULL, 25, 8, 0, 'admin_maxlifetime_set', '_parent'),
    (28, '商品描述設定', '屬性1', '/admin/ProDesc/index', '', NULL, 5, 2, 0, 'prodesc_index', '_parent'),
    (56, 'FB發佈圖文', 'FB發佈圖文', '/admin/seo/edit_social', NULL, NULL, 4, 8, 0, 'seo_edit_social', '_parent'),
    (57, '進階SEO設定', '進階SEO設定', '/admin/seo/edit_advance', NULL, NULL, 3, 8, 0, 'seo_edit_advance', '_parent'),
    (58, '標籤設定', '熱推分類', '/admin/tag/tag', NULL, NULL, 5, 2, 0, 'tag_tag', '_parent'),
    (59, 'EDM管理後台', 'EDM管理後台↗', 'http://shop-edm.sprlight.net/admin', NULL, NULL, 3, 5, 0, NULL, '_blank'),
    (60, '簡訊/寄信管理後台', '簡訊-電子報管理↗', 'http://shop-email.sprlight.net/', NULL, NULL, 15, 3, 0, NULL, '_blank'),
    (61, '館/分類樹', '分館分類樹', '/admin/layertree/tree', 'index/product/product', NULL, 0, 2, 0, 'layertree_tree', '_parent'),
    (62, 'LOGO管理', 'LOGO管理', '/admin/admin/admin_info', NULL, NULL, 20, 8, 0, 'admin_admin_info', '_parent'),
    (63, '系統信管理', '系統信管理', '/admin/admin/system_email', NULL, NULL, 15, 8, 0, 'admin_system_email', '_parent'),
    (64, '版本說明', '版本說明', '/admin/System/index', NULL, NULL, 30, 8, 0, 'system_index', '_parent'),
    (65, '網紅列表', '代銷會員管理(網紅分潤)', '/admin/Kol/index', NULL, NULL, 10, 3, 0, 'kol_index', '_parent'),
    (66, '運費管理', '運法管理', '/admin/shippingfee/index', NULL, NULL, 4, 5, 0, 'shippingfee_index', '_parent'),
    (67, '加價購設定', '加價購設定', '/admin/Addprice/index', '', NULL, 7, 3, 0, 'addprice_index', '_parent'),
    (68, '直接輸入型優惠券', '活動優惠券', '/admin/CouponDirect/index', '', NULL, 5, 3, 0, 'coupondirect_index', '_parent'),
    (69, '找貨回函', '找貨回函', '/admin/Findorder/findorder', 'index/findorder/findorder', NULL, 8, 5, 0, 'findorder_findorder', '_parent'),
    (70, '常用欄位管理', '常用欄位管理', '/admin/fields/fields_set', NULL, NULL, 16, 9, 0, 'fields_fields_set', '_parent'),
    (71, '常用註記詞管理', '常用註記詞管理', '/admin/fields/comments_set', NULL, NULL, 16, 9, 0, 'fields_comments_set', '_parent'),
    (72, '消費累積兌換', '消費累積兌換', '/admin/Consumption/exchange', 'index/consumption/exchange', NULL, 7, 3, 0, 'consumption_exchange', '_parent'),
    (73, '消費抽抽樂', '消費刮刮樂', '/admin/Consumption/draw', 'index/consumption/draw', NULL, 12, 3, 0, 'consumption_draw', '_parent'),
    (74, '掃碼付款管理', '掃碼付款管理', '/admin/consumption/pay_list', 'index/consumption/create_pay', NULL, 10, 5, 0, 'consumption_pay_list', '_parent'),
    (75, '運費標籤管理', '特規運費管理', '/admin/shippingfeetag/index', NULL, NULL, 5, 5, 0, 'shippingfeetag_index', '_parent'),
    (76, 'banner管理', '次頁形象圖', '/admin/banner/index', '', NULL, 6, 4, 0, 'banner_index', '_parent'),
    (77, '訂單管理', '訂單管理↗', '/order/order_ctrl/index/state/New.html', NULL, NULL, 5, 7, 0, NULL, '_blank'),
    (78, '商品問答', '商品問答↗', '/order/productqa/index.html', NULL, NULL, 2, 5, 0, NULL, '_blank'),
    (79, '點數週期設定', '點數週期設定', '/order/admin/point_set/status/4.html\r\n', NULL, NULL, 0, 3, 0, 'NULL', '_parent');

    ALTER TABLE `backstage_menu`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `backstage_menu_second`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `backstage_menu`
      MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;
    ALTER TABLE `backstage_menu_second`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;
-- 2022-08-15 後台權限管理同步選單名稱(但程式判斷名稱照舊)
    ALTER TABLE `backstage_menu_second` CHANGE `name` `name` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '判斷功能起停用名稱';

-- 2022-08-17 後台選單名稱調整seo預設修改名稱
    UPDATE `backstage_menu_second` SET `show_name` = '點數週期設定↗' WHERE `backstage_menu_second`.`id` = 79;
    UPDATE `seo` SET `title` = '公司名', `seokey` = '服務,服務 ', `descr` = '介紹平台' WHERE `seo`.`id` = 1;
-- 2022-08-29 雜修
    UPDATE `backstage_menu_second` SET `count_id` = NULL WHERE `backstage_menu_second`.`id` = 6;
    UPDATE `backstage_menu_second` SET `count_id` = NULL WHERE `backstage_menu_second`.`id` = 7;
    UPDATE `backstage_menu_second` SET `count_id` = 'ConCount' WHERE `backstage_menu_second`.`id` = 10;

-- 2022-09-14 同步 前後台展示頁顯示文字
    ALTER TABLE `frontend_menu_name` ADD `backstage_menu_second_id` INT NULL DEFAULT NULL COMMENT '對應功能id' AFTER `pic`;
    UPDATE `frontend_menu_name` SET `backstage_menu_second_id` = '17' WHERE `frontend_menu_name`.`id` = 1;
    UPDATE `frontend_menu_name` SET `backstage_menu_second_id` = '2' WHERE `frontend_menu_name`.`id` = 2;
    UPDATE `frontend_menu_name` SET `backstage_menu_second_id` = '16' WHERE `frontend_menu_name`.`id` = 3;
    UPDATE `frontend_menu_name` SET `backstage_menu_second_id` = '18' WHERE `frontend_menu_name`.`id` = 4;
    UPDATE `frontend_menu_name` SET `backstage_menu_second_id` = '11' WHERE `frontend_menu_name`.`id` = 5;
    UPDATE `frontend_menu_name` SET `backstage_menu_second_id` = '12' WHERE `frontend_menu_name`.`id` = 6;
    UPDATE `frontend_menu_name` SET `backstage_menu_second_id` = '15' WHERE `frontend_menu_name`.`id` = 7;
-- 2022-09-14 自行新增價格搜尋選項
    INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (NULL, '價格搜尋設定', '價格搜尋設定', '/admin/pricesearch/index', NULL, NULL, '15', '5', '0', 'pricesearch_index', '_parent');
    CREATE TABLE `product_price_search` ( 
        `id` INT NOT NULL , 
        `title` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '顯示文字' , 
        `content` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '搜尋區間(XX~XX)' , 
        `online` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '狀態' , 
        `orders` INT NOT NULL DEFAULT '0' COMMENT '排序'
    ) ENGINE = InnoDB;
    ALTER TABLE `product_price_search` ADD PRIMARY KEY( `id`);
    ALTER TABLE `product_price_search` CHANGE id id int(11) AUTO_INCREMENT;
    INSERT INTO `product_price_search` (`id`, `title`, `content`, `online`, `orders`) VALUES 
        (NULL, '500以下', '~500', '1', '0'), 
        (NULL, '500~1000', '500~1000', '1', '1'), 
        (NULL, '1000以上', '1000~', '1', '2');

-- 2022-10-13 修改報名欄位預設填寫的信箱 為 手機
    UPDATE `fields_set` SET `title` = '手機', `limit` = '^09\\d{8}$', `discription` = '請輸入10碼手機號:09XXXXXXXX' WHERE `fields_set`.`id` = 2;
-- 2022-10-13 補選縣市時 選新竹市的區資料
    INSERT INTO `town` (`CNo`, `Name`, `Post`, `State`, `AutoNo`) VALUES ('5', '東區', '300', '0', NULL);
    INSERT INTO `town` (`CNo`, `Name`, `Post`, `State`, `AutoNo`) VALUES ('5', '北區', '300', '0', NULL);
    INSERT INTO `town` (`CNo`, `Name`, `Post`, `State`, `AutoNo`) VALUES ('5', '香山區', '300', '0', NULL);
    UPDATE `town` SET `Name` = '員林市' WHERE `town`.`AutoNo` = 145;
    UPDATE `town` SET `Name` = '頭份市' WHERE `town`.`AutoNo` = 90;

-- 2022-10-26 超額購買功能(預購or候補報名)
    ALTER TABLE `productinfo` 
    ADD `pre_buy` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否可超額購買 1.可 0.不可' AFTER `prodesc`, 
    ADD `pre_buy_limit` INT(11) NOT NULL DEFAULT '0' COMMENT '超額購買上限' AFTER `pre_buy`;
    ALTER TABLE `examinee_info` ADD `reg_status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '報名狀況 1.已報名 0.候補 2.候補上' AFTER `roll_call_time`;
    ALTER TABLE `examinee_info` CHANGE `reg_status` `reg_status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '報名狀況 1.已報名 0.候補 2.候補上 3.取消候補';

-- 2022-11-10 商品可指定付款方式
  ALTER TABLE `productinfo` CHANGE `updatetime` `updatetime` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;
  ALTER TABLE `productinfo` ADD `pay_type` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '限用付款方法' AFTER `remind_msg`;

  CREATE TABLE `pay_fee` (
    `id` int(11) NOT NULL,
    `name` text CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '付法名稱',
    `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
    `online` tinyint(1) NOT NULL DEFAULT 1 COMMENT '狀態 0.停用 1.啟用'
  ) ENGINE=MyISAM DEFAULT CHARSET=latin1;
  ALTER TABLE `pay_fee`
    ADD PRIMARY KEY (`id`);
  ALTER TABLE `pay_fee`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;
  INSERT INTO `pay_fee` (`name`, `order_id`, `online`) VALUES
  ('貨到付款', 0, 1),
  ('ATM轉帳\\匯款', 1, 1),
  ('線上刷卡', 2, 1),
  ('分期付款', 3, 1);

-- 2022-11-11 商品置頂排序功能
    ALTER TABLE `productinfo` ADD `top_order_id` INT(11) NOT NULL DEFAULT '999' COMMENT '置頂排序' AFTER `order_id`;
    ALTER TABLE `productinfo_orders` ADD `top_order_id` INT NOT NULL DEFAULT '999' COMMENT '置頂排序' AFTER `order_id`;

-- 2022-11-14 改成經銷購物車平台
    UPDATE `backstage_menu` SET `title` = '介紹項' WHERE `backstage_menu`.`id` = 6;
    UPDATE `backstage_menu` SET `title` = '介紹項' WHERE `backstage_menu`.`id` = 4;
    UPDATE `backstage_menu` SET `title` = '功能項' WHERE `backstage_menu`.`id` = 5;
    UPDATE `backstage_menu` SET `title` = '功能項' WHERE `backstage_menu`.`id` = 3;
    UPDATE `backstage_menu` SET `title` = '訂單綜合項' WHERE `backstage_menu`.`id` = 8;
    UPDATE `backstage_menu` SET `title` = '訂單綜合項' WHERE `backstage_menu`.`id` = 9;

    ALTER TABLE `productinfo` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `product` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `typeinfo` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `contact_log` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `product_price_search` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `discount` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `position` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `prodesc` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;

    -- 供應商運法
    CREATE TABLE `shipping_fee_distribution` (
      `id` int(11) NOT NULL,
      `distributor_id` int(11) NOT NULL,
      `shipping_fee_id` int(11) NOT NULL,
      `price` int(11) NOT NULL COMMENT '運費金額',
      `free_rule` int(11) NOT NULL DEFAULT 999999999 COMMENT '滿額免運',
      `online` tinyint(1) NOT NULL DEFAULT 1 COMMENT '狀態 0.停用 1.啟用'
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1;
    ALTER TABLE `shipping_fee_distribution`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `shipping_fee_distribution`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;

    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (81, '付款方式管理', '付款方式管理', '/admin/payfee/index', NULL, NULL, '3', '5', '0', 'payfee_index', '_parent');
    -- 供應商付款方法
    CREATE TABLE `pay_fee_distribution` (
      `id` int(11) NOT NULL,
      `distributor_id` int(11) NOT NULL,
      `pay_fee_id` int(11) NOT NULL,
      `online` tinyint(1) NOT NULL DEFAULT 1 COMMENT '狀態 0.停用 1.啟用'
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1;
    ALTER TABLE `pay_fee_distribution`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `pay_fee_distribution`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;

    ALTER TABLE `shipping_fee_tag` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `pay_fee` ADD `sys_status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '系統控制開關 0.關 1.開' AFTER `online`;
    ALTER TABLE `shipping_fee` ADD `sys_status` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '系統控制開關 0.關 1.開' AFTER `online`;

    ALTER TABLE `excel` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `excel_reply` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `contact_find_prod` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `consumption_exchange` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `consumption_exchange_record` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `consumption_draw` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `consumption_draw_record` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `consumption_pay_record` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `consumption_draw_limit` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `contact` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    -- 調整商品問答
    UPDATE `backstage_menu_second` SET `show_name` = '商品問答' WHERE `backstage_menu_second`.`id` = 78;
    UPDATE `backstage_menu_second` SET `url` = '/admin/productqa/index' WHERE `backstage_menu_second`.`id` = 78;
    UPDATE `backstage_menu_second` SET `class` = 'productqa_index' WHERE `backstage_menu_second`.`id` = 78;
    UPDATE `backstage_menu_second` SET `target` = '_parent' WHERE `backstage_menu_second`.`id` = 78;
    -- 調整活動優惠、立馬省
    ALTER TABLE `act` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `act` CHANGE `location` `location` INT(11) NOT NULL DEFAULT '0';
    -- 調整商品預設文字
    ALTER TABLE `default_content` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `default_content` ADD `default_type` VARCHAR(32) NULL DEFAULT NULL COMMENT '對應預設內容' AFTER `distributor_id`;
    UPDATE `default_content` SET `default_type` = 'productinfo' WHERE `default_content`.`id` = 1;
    UPDATE `default_content` SET `default_type` = 'coupon' WHERE `default_content`.`id` = 2;
    -- 調整會員優惠券
    ALTER TABLE `coupon` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `coupon_direct` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `addprice` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    -- 報名欄位
    ALTER TABLE `fields_set` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    ALTER TABLE `comments_set` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;
    -- 點數適用分類
    CREATE TABLE `points_allow_use` ( 
        `id` INT(11) NOT NULL , 
        `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' , 
        `value` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '點數適用分類' 
    ) ENGINE = InnoDB;
    ALTER TABLE `points_allow_use`
      ADD PRIMARY KEY (`id`);
    ALTER TABLE `points_allow_use`
      MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;
    INSERT INTO `points_allow_use` (`id`, `distributor_id`, `value`) VALUES (NULL, '0', '');
    UPDATE `points_setting` SET `note` = '點數適用分類改由points_allow_use控制' WHERE `points_setting`.`id` = 2;

    -- 訂單管理
    UPDATE `backstage_menu_second` SET `show_name` = '新進訂單' WHERE `backstage_menu_second`.`id` = 77;
    UPDATE `backstage_menu_second` SET `url` = '/order/order_ctrl/index.html?state=New' WHERE `backstage_menu_second`.`id` = 77;
    UPDATE `backstage_menu_second` SET `target` = '_parent' WHERE `backstage_menu_second`.`id` = 77;
    UPDATE `backstage_menu_second` SET `class` = 'order_order_ctrl_index_New' WHERE `backstage_menu_second`.`id` = 77;
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (82, '訂單管理', '完成訂單', '/order/order_ctrl/index.html?state=Complete', NULL, NULL, '10', '7', '0', 'order_order_ctrl_index_Complete', '_parent');
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (83, '訂單管理', '垃圾桶訂單', '/order/order_ctrl/index.html?state=Trash', NULL, NULL, '15', '7', '0', 'order_order_ctrl_index_Trash', '_parent');
    DELETE FROM `backstage_menu_second` WHERE `backstage_menu_second`.`id` = 79;

    -- 會員列表
    UPDATE `backstage_menu_second` SET `show_name` = '會員全部列表' WHERE `backstage_menu_second`.`id` = 21;
    UPDATE `backstage_menu_second` SET `class` = 'order_index_index_' WHERE `backstage_menu_second`.`id` = 21;
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (84, '會員管理', '新進未開通', '/order/index/index.html?status=0', NULL, '', '5', '10', '0', 'order_index_index_0', '_parent'),
    (85, '會員管理', '黑名單列表', '/order/index/index.html?status=2', NULL, '', '10', '10', '0', 'order_index_index_2', '_parent'),
    (86, '會員管理', '停用名單列表', '/order/index/index.html?status=3', NULL, '', '15', '10', '0', 'order_index_index_3', '_parent'),
    (87, '會員優惠設定', '會員優惠設定', '/order/admin/member_discount', NULL, '', '20', '10', '0', 'order_admin_member_discount', '_parent');
    UPDATE `contact` SET `contact_type` = '行銷活動,其他' WHERE `contact`.`id` = 1;

    ALTER TABLE `contact_log` ADD `prod_id` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '商品ID或名稱' AFTER `order_number`;
    UPDATE `system_email` SET 
        `signup_complete` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>',
        `contact_complete` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>',
        `order_complete` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>',
        `forget_password` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>',
        `order_cancel` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>',
        `product_qa` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>',
        `act_remind` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>',
        `act_cancel` = '<span style=\"color:#E53333;\">≡ 此信件為系統自動發送，請勿直接回覆</span><span style=\"color:#E53333;\">&nbsp;≡</span>'
    WHERE `system_email`.`id` = 1;
    -- 刪除無用資料表
    DROP TABLE `cart`;

    -- 詢價功能
    ALTER TABLE `productinfo` ADD `ask_price` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否開放詢價 0.否 1.是' AFTER `display`;
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (88, '詢價回函', '詢價回函', '/admin/Askprice/index', '', NULL, '9', '5', '0', 'askprice_index', '_parent');
    CREATE TABLE `askprice` ( 
        `id` INT NOT NULL , `distributor_id` INT NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' , 
        `askprice_record_id` INT NOT NULL DEFAULT '0' COMMENT '當前詢價內容(對應askprice_record id)' , 
        `user_id` INT NOT NULL DEFAULT '0' COMMENT '詢問者id' , 
        `name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '詢問者姓名' , 
        `phone` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '詢問者電話' , 
        `email` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '詢問者信箱' , 
        `product_id` INT NOT NULL COMMENT '商品id' , 
        `product_name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '商品名稱' , 
        `product_type_id` INT NOT NULL COMMENT '品項id' , 
        `product_type_name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '品項名稱' , 
        `create_time` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '建立時間'
    ) ENGINE = InnoDB;
    ALTER TABLE `askprice` ADD PRIMARY KEY(`id`);
    ALTER TABLE `askprice` CHANGE id id int(11) AUTO_INCREMENT;

    CREATE TABLE `askprice_record` ( 
        `id` INT NOT NULL , `askprice_id` INT NOT NULL COMMENT '對應askprice id' , 
        `ask` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '詢問' , 
        `ask_time` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '詢問時間' , 
        `response` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '回覆' , 
        `response_time` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '回覆時間' , 
        `price` INT NOT NULL COMMENT '價格' , 
        `num` INT NOT NULL DEFAULT '1' COMMENT '數量' , 
        `agree` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否同意 0.未選 1.同意' 
    ) ENGINE = InnoDB;
    ALTER TABLE `askprice_record` ADD PRIMARY KEY(`id`);
    ALTER TABLE `askprice_record` CHANGE id id int(11) AUTO_INCREMENT;
    ALTER TABLE `askprice` CHANGE `create_time` `create_time` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '建立時間';
    ALTER TABLE `askprice_record` 
    CHANGE `ask_time` `ask_time` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '詢問時間', 
    CHANGE `response_time` `response_time` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '回覆時間';
    ALTER TABLE `askprice_record` ADD `expired_date` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '截止日期' AFTER `price`;
    ALTER TABLE `askprice_record` ADD `status` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '處理狀態' AFTER `expired_date`;
    ALTER TABLE `askprice_record` ADD `price_final` INT NULL DEFAULT NULL COMMENT '賣家終定價' AFTER `price`;
    UPDATE `backstage_menu_second` SET `count_id` = 'askpriceCount' WHERE `backstage_menu_second`.`id` = 88;
    UPDATE `backstage_menu_second` SET `Front_desk` = 'index/askprice/askprice' WHERE `backstage_menu_second`.`id` = 88;
    -- INSERT INTO `frontend_menu_name` 
    -- (`id`, `name`, `en_name`, `controller`, `second_menu`, `text_color`, `pic`, `backstage_menu_second_id`) VALUES 
    -- ('10', '商品詢價', 'Askprice', 'askprice', NULL, '', '', NULL);
    ALTER TABLE `askprice_record` ADD `bought` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '已購買 0.否 1.是' AFTER `agree`;

    ALTER TABLE `backstage_menu_second` CHANGE `Front_desk` `front_desk` VARCHAR(50) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL;

-- 2022-12-26 會員指定瀏覽商品
    INSERT INTO `backstage_menu_second` 
    (`id`, `name`, `show_name`, `url`, `Front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
    (89, '會員瀏覽商品設定', '會員瀏覽商品設定', '/order/member/product_view', NULL, '', '25', '10', '0', 'order_member_product_view', '_parent');

-- 2023-01-06 更換名稱
    UPDATE `frontend_menu_name` SET `name` = '活動公告' WHERE `frontend_menu_name`.`id` = 4;
    UPDATE `frontend_menu_name` SET `en_name` = 'ANNOUNCEMENT' WHERE `frontend_menu_name`.`id` = 4;
-- 2023-01-06 活動&立馬省商品重複防呆
    ALTER TABLE `act_product` ADD UNIQUE(`prod_id`);

-- 2023-02-06 重置系統信內容
    UPDATE `system_email` SET `signup_complete` = NULL WHERE `system_email`.`id` = 1;
    UPDATE `system_email` SET `contact_complete` = NULL WHERE `system_email`.`id` = 1;
    UPDATE `system_email` SET `order_complete` = NULL WHERE `system_email`.`id` = 1;
    UPDATE `system_email` SET `forget_password` = NULL WHERE `system_email`.`id` = 1;
    UPDATE `system_email` SET `order_cancel` = NULL WHERE `system_email`.`id` = 1;
    UPDATE `system_email` SET `product_qa` = NULL WHERE `system_email`.`id` = 1;
    UPDATE `system_email` SET `act_remind` = NULL WHERE `system_email`.`id` = 1;
    UPDATE `system_email` SET `act_cancel` = NULL WHERE `system_email`.`id` = 1;

-- 2023-02-17 調整 points_setting 說明
    UPDATE `points_setting` SET `note` = '點數適用分館 改由points_allow_use控制' WHERE `points_setting`.`id` = 1;
    UPDATE `points_setting` SET `note` = '點數適用分類 無用但誤刪刪' WHERE `points_setting`.`id` = 2;
-- 2023-02-17 admin表限制帳戶不得重複
    ALTER TABLE `admin` CHANGE `account` `account` VARCHAR(256) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL;
    ALTER TABLE `admin` ADD UNIQUE(`account`);

-- 2023-03-17 網紅商品紀錄+期數調整
    ALTER TABLE `kol_productinfo` CHANGE `time` `time` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '開始時間';
    ALTER TABLE `kol_productinfo` ADD `time_e` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '結束時間' AFTER `time`;
    UPDATE `kol_productinfo` SET `time_e` = '';

    CREATE TABLE `kol_period` ( 
        `id` INT NOT NULL , 
        `kol_id` INT NOT NULL , 
        `date_start` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL , 
        `date_end` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL , 
        `count_days` INT(11) NOT NULL COMMENT '結算周期(日)' 
    ) ENGINE = InnoDB;
    ALTER TABLE `kol_period` ADD PRIMARY KEY( `id`);
    ALTER TABLE `kol_period` CHANGE `id` `id` INT(11) AUTO_INCREMENT;
    ALTER TABLE `kol_period` ADD `name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '檔期名稱' AFTER `kol_id`;

    CREATE TABLE `kol_period_term` ( 
        `id` INT(11) NOT NULL , 
        `kol_period_id` INT(11) NOT NULL , 
        `period_start` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL , 
        `period_end` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL , 
        `confirm_date` VARCHAR(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '核可時間(timestamp)' , 
        `confrim_content` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '核可內容(核可後不再修改)' 
    ) ENGINE = InnoDB;
    ALTER TABLE `kol_period_term` ADD PRIMARY KEY( `id`);
    ALTER TABLE `kol_period_term` CHANGE `id` `id` INT(11) AUTO_INCREMENT;
    ALTER TABLE `kol_period_term` ADD `count` INT(11) NULL DEFAULT NULL COMMENT '檔期數' AFTER `period_end`;

    ALTER TABLE `kol`
      DROP `start_date`,
      DROP `count_days`;
    DROP TABLE `kol_confirm_sale`;

-- 2023-08-08 權限綁定頁面顯示調整
    UPDATE `backstage_menu_second` SET `front_desk` = NULL WHERE `backstage_menu_second`.`id` = 1;
-- 2023-10-06 更改編碼(可存emoji)
    ALTER TABLE `experience` CHANGE `content` `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `news` CHANGE `content` `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `activity` CHANGE `content` `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `about_story` CHANGE `content` `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `typeinfo_str` CHANGE `content` `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `productinfo` CHANGE `content` `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `productinfo` CHANGE `text1` `text1` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `productinfo` CHANGE `text2` `text2` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `productinfo` CHANGE `text3` `text3` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
    ALTER TABLE `productinfo` CHANGE `text4` `text4` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
-- 2023-10-30 回函表改成可持續記錄
  CREATE TABLE `contact_log_record` ( 
    `id` INT NOT NULL , 
    `contact_log_id` INT(11) NOT NULL COMMENT '對應回函id' , 
    `message` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '留言內容' , 
    `admin_id` INT(11) NOT NULL COMMENT '留言者id' , 
    `datetime` VARCHAR(10) NOT NULL COMMENT '留言時間' 
  ) ENGINE = InnoDB;
  ALTER TABLE `contact_log_record` ADD PRIMARY KEY( `id`);
  ALTER TABLE `contact_log_record` CHANGE `id` `id` INT(11) AUTO_INCREMENT;
  ALTER TABLE `contact_log_record` ADD `admin_type` VARCHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登入類型' AFTER `message`;

-- 2023-12-11 新增LinePay付款方式
  INSERT INTO `pay_fee` (`id`, `name`, `order_id`, `online`, `sys_status`) VALUES (5, 'LinePay', '4', '1', '1');
  UPDATE `pay_fee` SET `sys_status` = '0' WHERE `pay_fee`.`id` = 5; /*預設關閉line pay*/