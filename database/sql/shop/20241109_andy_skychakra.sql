-- -- 客製天脈功能
-- 添加功能
INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
(92, '回饋設定', '回饋模組', '/admin/bonusmodel/index', NULL, NULL, '0', '8', '0', 'bonusmodel_index', '_parent'),
(93, '回饋設定', '回饋設定', '/admin/bonussetting/index', NULL, NULL, '0', '8', '0', 'bonussetting_index', '_parent');
INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
(94, '合夥人等級設定', '合夥人等級設定', '/order/partner_level/index', NULL, '', '21', '10', '0', 'order_partner_level_index', '_parent'),
(95, '中心等級設定', '中心等級設定', '/order/center_level/index', NULL, '', '22', '10', '0', 'order_center_level_index', '_parent');

-- 積分模組
CREATE TABLE `bonus_model` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `name` CHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模組名稱' , 
    
    `normal_recommend` FLOAT NOT NULL DEFAULT 0 COMMENT '推廣獎勵(ex:25)(使用時要除100)' ,
    `normal_partner` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥平級獎勵(ex:25)(使用時要除100)' ,
    `normal_operation` FLOAT NOT NULL DEFAULT 0 COMMENT '營運獎勵(ex:12)(使用時要除100)' ,
    `normal_lecturer` FLOAT NOT NULL DEFAULT 0 COMMENT '講師獎勵(ex:3)(使用時要除100)' ,
    `normal_center` FLOAT NOT NULL DEFAULT 0 COMMENT '中心獎勵(ex:15)(使用時要除100)' ,
    `normal_dividend_month` FLOAT NOT NULL DEFAULT 0 COMMENT '月分紅(ex:20)(使用時要除100)' ,
    `use_partner_mode` tinyint(1) DEFAULT 0 COMMENT '是否使用合夥批發回饋(0.否 1.是)' ,
    
    `partner_recommend` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:推廣獎勵(ex:85)(使用時要除100)' ,
    `partner_partner` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:合夥平級獎勵(ex:0)(使用時要除100)' ,
    `partner_operation` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:營運獎勵(ex:12)(使用時要除100)' ,
    `partner_lecturer` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:講師獎勵(ex:3)(使用時要除100)' ,
    `partner_center` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:中心獎勵(ex:0)(使用時要除100)' ,
    `partner_dividend_month` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:月分紅(ex:0)(使用時要除100)' ,

    `ad_bonus` FLOAT NOT NULL DEFAULT 0 COMMENT '廣告推廣獎勵(ex:25)(使用時要除100)' ,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '回饋模組';

-- 商品添加欄位
ALTER TABLE `productinfo` 
ADD `product_cate` TINYINT(1) NOT NULL DEFAULT '2' COMMENT '商品類型(1.投資、2.消費)' AFTER `top_order_id`, 
ADD `bonus_model_id` INT(11) NOT NULL DEFAULT '0' COMMENT '套用回饋模組id(商品類型為1時則應為0)' AFTER `product_cate`, 
ADD `vip_type_reward` INT(11) NOT NULL DEFAULT '0' COMMENT '提升會員級別(對應vip_type的id，0.表示一般，其他表示課程)' AFTER `bonus_model_id`, 
ADD `vip_type_require` INT(11) NOT NULL DEFAULT '0' COMMENT '需求會員級別(對應vip_type的id，0.表示無限制)' AFTER `vip_type_reward`;
-- 商品品項添加欄位
ALTER TABLE `productinfo_type` ADD `price_cv` INT(11) NOT NULL DEFAULT '0' COMMENT 'CV金額' AFTER `count`;

UPDATE `backstage_menu_second` SET `show_name` = '會員級別設定' WHERE `backstage_menu_second`.`id` = 87;