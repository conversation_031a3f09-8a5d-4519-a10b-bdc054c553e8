-- 任務牆功能
INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
(100, '任務牆', '任務牆', '/admin/task/index', NULL, NULL, '0', '8', '0', 'task_index', '_parent');

-- 任務牆
CREATE TABLE `task` (
  `id` INT(11) NOT NULL AUTO_INCREMENT , 
  `name` CHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任務名稱' , 
  `type` INT(11) NOT NULL COMMENT '活動類型' , 
  `time_s` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '開始時間(空表示無限制)(YYYY-mm-dd)' , 
  `time_e` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '結束時間(空表示無限制)(YYYY-mm-dd)' , 
  `msg` VARCHAR(128) NOT NULL COMMENT '贈送記錄文字(消費者查看)' , 
  `bonus_column1` CHAR(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回饋欄位1(依據活動類型有不同意義)' , 
  PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '任務牆(未來可能添加更 bonus_column2...，或 rule_column1...，並根據不同 type 可有不同效力)';

-- 任務牆-會員登入紀錄
CREATE TABLE `task_login` (
  `id` INT(11) NOT NULL AUTO_INCREMENT , 
  `user_id` INT(11) NOT NULL COMMENT '會員id' , 
  `time` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登入時間(timestamp)' , 
  PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '任務牆-會員登入紀錄';

-- 加入圓滿點數總覽
INSERT INTO `backstage_menu_second` (`id`, `name`, `show_name`, `url`, `front_desk`, `count_id`, `sort`, `backstage_menu_id`, `important`, `class`, `target`) VALUES 
('101', '圓滿點數總覽', '圓滿點數總覽', '/order/point/point_limit_record', NULL, '', '18', '10', '0', 'order_point_point_limit_record', '_parent');

UPDATE `backstage_menu_second` SET `sort` = '19' WHERE `backstage_menu_second`.`id` = 96;
