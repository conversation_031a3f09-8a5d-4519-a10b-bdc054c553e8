-- 2021-10-20 修改email寄送對象設定
	ALTER TABLE `newsletter_send_time` CHANGE 
	`send_target` `send_target` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '寄送對象';

-- 2021-10-20 修改成各館可獨立控制兌換點數比率
	UPDATE `excel` SET `value1` = 0, `value2` = '無用誤刪' WHERE `excel`.`id` in (1, 4);

-- 2021-11-11 修改會員資料預設值
	ALTER TABLE `account` CHANGE `birthday` `birthday` VARCHAR(11) NULL DEFAULT NULL;

-- 2021-12-17 再行銷系統加入範本功能
	CREATE TABLE `format` (
		`id` int(20) NOT NULL,
		`name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
		`data` text CHARACTER SET utf8 COLLATE utf8_unicode_ci,
		`creat_time` int(255) DEFAULT NULL,
		`update_time` int(255) DEFAULT NULL,
		`status` int(11) DEFAULT '1'
	) ENGINE=MyISAM DEFAULT CHARSET=latin1;
	ALTER TABLE `format`
		ADD PRIMARY KEY (`id`),
		ADD UNIQUE KEY `id` (`id`);
	ALTER TABLE `format`
		MODIFY `id` int(20) NOT NULL AUTO_INCREMENT;

-- 2022-02-08 會員加入推薦功能
    ALTER TABLE `account` 
    ADD `upline_user` INT(11) NOT NULL DEFAULT '0' COMMENT '上線會員id' AFTER `export`, 
    ADD `recommend_content` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '推廣文字' AFTER `upline_user`,
    ADD `share_pic` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '分享圖片' AFTER `recommend_content`,
    ADD `share_title` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '分享標題' AFTER `share_pic`,
    ADD `share_text` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '分享說明' AFTER `share_title`;

-- 2022-06-02 權限連動調整
	UPDATE `excel` SET `value2` = '商品階層品項 0.可 1.不可' WHERE `excel`.`id` = 4;

-- 2022-10-13 更新傳訊光測試會員密碼為photo3599
	UPDATE `account` SET `pwd` = '48913f026dabd50a7dbe3b4621e84a8d' WHERE `account`.`id` = 1;

-- 2022-10-20 超額購買功能(預購or候補報名)
	UPDATE `excel` SET `value2` = '使否啟用超額購買功能 1.可 0.不可' WHERE `excel`.`id` = 1;

-- 2022-10-25 解除通知信標題長度限制
	ALTER TABLE `member_group` CHANGE `name` `name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL;
	ALTER TABLE `newsletter` CHANGE `title` `title` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL;
	ALTER TABLE `newsletter_send_time` CHANGE `title` `title` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL;
	ALTER TABLE `format` CHANGE `name` `name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL;
	ALTER TABLE `message_list` CHANGE `title` `title` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL;
	ALTER TABLE `message_log` CHANGE `title` `title` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL;

-- 2022-10-28 串接三竹簡訊
	CREATE TABLE `message_statuscode` ( 
		`id` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'statuscode' , 
		`msg` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT '中文說明'
	) ENGINE = InnoDB;
	ALTER TABLE `message_statuscode` CHANGE `id` `id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'statuscode';
	ALTER TABLE `message_statuscode` ADD PRIMARY KEY( `id`);
	INSERT INTO `message_statuscode` (`id`, `msg`) VALUES 
	('*', '系統發生錯誤，請聯絡三竹資訊窗口人員'),
	('a', '簡訊發送功能暫時停止服務，請稍候再試'),
	('b', '簡訊發送功能暫時停止服務，請稍候再試'),
	('c', '請輸入帳號'),
	('d', '請輸入密碼'),
	('e', '帳號、密碼錯誤'),
	('f', '帳號已過期'),
	('h', '帳號已被停用'),
	('k', '無效的連線位址'),
	('l', '帳號已達到同時連線數上限'),
	('m', '必須變更密碼，在變更密碼前，無法使用簡訊發送服務'),
	('n', '密碼已逾期，在變更密碼前，將無法使用簡訊發送服務'),
	('p', '沒有權限使用外部Http程式'),
	('r', '系統暫停服務，請稍後再試'),
	('s', '帳務處理失敗，無法發送簡訊'),
	('t', '簡訊已過期'),
	('u', '簡訊內容不得為空白'),
	('v', '無效的手機號碼'),
	('w', '查詢筆數超過上限'),
	('x', '發送檔案過大，無法發送簡訊'),
	('y', '參數錯誤'),
	('z', '查無資料'),
	('0', '預約傳送中'),
	('1', '已送達業者'),
	('2', '已送達業者'),
	('4', '已送達手機'),
	('5', '內容有錯誤'),
	('6', '門號有錯誤'),
	('7', '簡訊已停用'),
	('8', '逾時無送達'),
	('9', '預約已取消');
	INSERT INTO `message_statuscode` (`id`, `msg`) VALUES ('-1', '已確認');

-- 2022-11-10 商品可指定付款方式
	INSERT INTO `excel` (`id`, `value1`, `value2`) VALUES ('21', '0', '商品可指定付款方式 0.不可 1.可');
	UPDATE `excel` SET `value2` = '是否啟用超額購買功能 1.可 0.不可' WHERE `excel`.`id` = 1;
	UPDATE `excel` SET `value2` = '是否可以指定商品付款方式 0.不可 1.可' WHERE `excel`.`id` = 21;
	UPDATE `orderform` set `payment` = 1 WHERE `payment`='貨到付款';
	UPDATE `orderform` set `payment` = 2 WHERE `payment`='ATM\\匯款';
	UPDATE `orderform` set `payment` = 2 WHERE `payment`='ATM轉帳\\匯款';
	UPDATE `orderform` set `payment` = 3 WHERE `payment`='線上刷卡';
	UPDATE `orderform` set `payment` = 4 WHERE `payment`='分期付款';

-- 2022-11-14 改成經銷購物車平台
	ALTER TABLE `account` 
	ADD `user_type` TINYINT(1) NULL DEFAULT '0' COMMENT '經銷狀態 0.未開通 1.開通' AFTER `name`,
	ADD `user_type_radio` TINYINT(1) NULL DEFAULT '0' COMMENT '會員類型 0.一般 1.經銷(消費者選擇)' AFTER `user_type`;
	UPDATE `account` SET `user_type` = '1' WHERE `account`.`id` = 1;
	UPDATE `account` SET `user_type_radio` = '1' WHERE `account`.`id` = 1;

	ALTER TABLE `orderform` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `id`;

-- 2022-12-14 平台化開關
	INSERT INTO `excel` (`id`, `value1`, `value2`) VALUES ('22', '0', '是否平台化 0.否 1.是');
	ALTER TABLE `account` 
	ADD `shop_name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '店鋪名稱' AFTER `share_text`, 
	ADD `file_company` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '公司登記文件' AFTER `shop_name`, 
	ADD `file_person` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '個人身份文件' AFTER `file_company`, 
	ADD `bank` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '銀行名稱' AFTER `file_person`, 
	ADD `bank_code` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '分行代號' AFTER `bank`, 
	ADD `bank_account_name` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '戶名' AFTER `bank_code`, 
	ADD `bank_account_code` TEXT CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '銀行帳號' AFTER `bank_account_name`;

-- 調整商品問答
    ALTER TABLE `product_qa` ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '會員id 0為平台' AFTER `prod_qa_id`;
    ALTER TABLE `product_qa` CHANGE `uid` `uid` INT(11) NOT NULL COMMENT '詢問者id';

-- 加入語言版/分站紀錄
	CREATE TABLE `lang` (
	  `lang_id` int(10) UNSIGNED NOT NULL,
	  `lang_type` varchar(15) COLLATE utf8_unicode_ci NOT NULL COMMENT '語言代號',
	  `lang_word` varchar(15) COLLATE utf8_unicode_ci NOT NULL COMMENT '語言中文',
	  `db_connect` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '對應資料庫連線',
	  `lang_order` int(11) NOT NULL,
	  `menu` text COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '選單名稱',
	  `lang_status` tinyint(4) NOT NULL COMMENT '1:啟用 0:停用'
	) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
	INSERT INTO `lang` (`lang_id`, `lang_type`, `lang_word`, `db_connect`, `lang_order`, `menu`, `lang_status`) VALUES
	(1, '', '台灣', 'A_sub', 1, '{}', 1),
	(2, 'ch', '中國', 'B_sub', 1, '{}', 0),
	(3, 'en', '國際', 'C_sub', 1, '{}', 0),
	(4, 'jp', '日本', 'D_sub', 1, '{}', 0);
	UPDATE `lang` SET `lang_word` = '標準購物車' WHERE `lang`.`lang_id` = 1;
	ALTER TABLE `lang`
	  ADD PRIMARY KEY (`lang_id`),
	  ADD UNIQUE KEY `lang_lang_type_unique` (`lang_type`),
	  ADD UNIQUE KEY `lang_lang_word_unique` (`lang_word`),
	  ADD UNIQUE KEY `lang_db_connect_unique` (`db_connect`);
	ALTER TABLE `lang`
	  MODIFY `lang_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

-- 2022-12-26 會員指定瀏覽商品
	CREATE TABLE `product_view` (
	  `id` int(11) NOT NULL,
	  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	  `online` tinyint(4) NOT NULL DEFAULT 0
	) ENGINE=InnoDB DEFAULT CHARSET=latin1;
	ALTER TABLE `product_view`
	  ADD PRIMARY KEY (`id`);
	ALTER TABLE `product_view`
	  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;
	CREATE TABLE `product_view_product` (
	  `view_prod_id` int(11) NOT NULL,
	  `lang_id` int(11) NOT NULL COMMENT '語言版id',
	  `view_id` int(11) NOT NULL,
	  `prod_id` int(11) NOT NULL
	) ENGINE=InnoDB DEFAULT CHARSET=utf8;
	ALTER TABLE `product_view_product`
	  ADD PRIMARY KEY (`view_prod_id`);
	ALTER TABLE `product_view_product`
	  MODIFY `view_prod_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;
	INSERT INTO `product_view` (`id`, `name`, `online`) VALUES (1, '預設瀏覽', '1');
	ALTER TABLE `account` ADD `product_view_id` INT NOT NULL DEFAULT '1' COMMENT '商品瀏覽id' AFTER `user_type_radio`;

-- 修改忘記密碼
	ALTER TABLE `account` ADD `f_code` VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '忘記密碼用' AFTER `pwd`;

-- 完善語言版
	ALTER TABLE `lang` 
	ADD `menu_admin` text COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '選單名稱(後台)' AFTER `menu`;
	ALTER TABLE `lang` CHANGE `db_connect` `sub_deparment` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT '對應分站';
	UPDATE `lang` SET `sub_deparment` = 'A', `menu_admin` = '{}' WHERE `lang`.`lang_id` = 1;
	UPDATE `lang` SET `sub_deparment` = 'B', `menu_admin` = '{}' WHERE `lang`.`lang_id` = 2;
	UPDATE `lang` SET `sub_deparment` = 'C', `menu_admin` = '{}' WHERE `lang`.`lang_id` = 3;
	UPDATE `lang` SET `sub_deparment` = 'D', `menu_admin` = '{}' WHERE `lang`.`lang_id` = 4;

-- 控制招募會員
	INSERT INTO `excel` (`id`, `value1`, `value2`) VALUES ('23', '1', '是否使用招募會員 0.否 1.是');

-- 購物資料轉會員(要排除首購計算)
	ALTER TABLE `account` DROP `account`;
	ALTER TABLE `account` ADD `from_order` INT NOT NULL DEFAULT '0' COMMENT '從哪筆訂單建立會員資料 0.一般註冊' AFTER `vip_type`;
	ALTER TABLE `account` CHANGE `from_order` `from_order` INT(11) NULL DEFAULT NULL COMMENT '從哪筆訂單建立會員資料 0.一般註冊, null.未設定';
	ALTER TABLE `account` CHANGE `pwd` `pwd` VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '81dc9bdb52d04dc20036dbd8313ed055' COMMENT '預設為1234的md5加密';
	UPDATE `account` SET `from_order` = 0;

-- 2023-08-08 修正參數控制表的文字說明
	UPDATE `excel` SET `value2` = '商品階層品項 1.可 0.不可' WHERE `excel`.`id` = 4;

-- 2023-08-16更改語言追蹤方式
	ALTER TABLE `lang`
		DROP `menu`,
		DROP `menu_admin`;

-- 2023-11-28統一金物流控制
	UPDATE `excel` SET `value2` = '第三方金流 0.不可 1.可' WHERE `excel`.`id` = 16;
	UPDATE `excel` SET `value2` = '第三方物流 0.不可 1.可' WHERE `excel`.`id` = 17;
	UPDATE `excel` SET `value1` = 0, `value2` = '第三方發票 0.不可 1.可' WHERE `excel`.`id` = 18;

-- 2023-12-11
	-- 在account新增載具欄位(invoice)
	ALTER TABLE `account` ADD `invoice` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL AFTER `home`;
    -- 在orderform新增出貨日期欄位(開立發票用)
    ALTER TABLE `orderform` ADD `transport_date` DATE NULL COMMENT '出貨日期' AFTER `transport_state`;
	-- 在orderform新增載具相關欄位
	ALTER TABLE `orderform` ADD `Donation` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否捐贈發票 0.否 1.是' AFTER `company_title`;
	ALTER TABLE `orderform` ADD `LoveCode` VARCHAR(10) COMMENT '捐贈碼' AFTER `Donation`;
	ALTER TABLE `orderform` ADD `CarrierType` VARCHAR(1) COMMENT '載具類型 2.自然人 3.手機' AFTER `LoveCode`;
	ALTER TABLE `orderform` ADD `CarrierNum` VARCHAR(10) DEFAULT NULL COMMENT '載具編號' AFTER `CarrierType`;
	ALTER TABLE `orderform` ADD `InvoiceNo` TEXT DEFAULT NULL COMMENT '發票號碼' AFTER `CarrierNum`;
	ALTER TABLE `orderform` ADD `InvoiceDate` TEXT DEFAULT NULL COMMENT '發票開立日期' AFTER `InvoiceNo`;
	ALTER TABLE `orderform` ADD `RandomNumber` TEXT DEFAULT NULL COMMENT '發票隨機碼' AFTER `InvoiceDate`;
	ALTER TABLE `orderform` ADD `Print` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '發票列印 0.否 1.是' AFTER `RandomNumber`;
	-- 在orderform新增新竹物流相關欄位
	ALTER TABLE `orderform` ADD `HCT_edelno` INT(20) DEFAULT NULL COMMENT '新竹物流 - 貨號' AFTER `CheckMacValue`;
	ALTER TABLE `orderform` ADD `HCT_epino` VARCHAR(40) DEFAULT NULL COMMENT '新竹物流 - 訂單編號' AFTER `HCT_edelno`;
	ALTER TABLE `orderform` ADD `HCT_erstno` INT(20) DEFAULT NULL COMMENT '新竹物流 - 到著站號碼' AFTER `HCT_epino`;
	ALTER TABLE `orderform` ADD `HCT_status_url` TEXT DEFAULT NULL COMMENT '新竹物流 - 貨態查詢網址' AFTER `HCT_erstno`;
	-- 在orderform新增LinePay相關欄位
	ALTER TABLE `orderform` ADD `linepay_return_data` TEXT DEFAULT NULL COMMENT 'LinePay交易結果回傳內容' AFTER `tspg_return_data`;
  -- 在orderform修改AllPayLogisticsID備註
  ALTER TABLE `orderform` CHANGE `AllPayLogisticsID` `AllPayLogisticsID` VARCHAR(20) CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT NULL COMMENT '物流交易編號';
	-- 在orderform刪除新竹物流相關欄位
	ALTER TABLE `orderform` DROP `HCT_edelno`;
	ALTER TABLE `orderform` DROP `HCT_epino`;
	ALTER TABLE `orderform` DROP `HCT_erstno`;
	ALTER TABLE `orderform` DROP `HCT_status_url`;

	ALTER TABLE `orderform` CHANGE `payment` `payment` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '1.貨到付款 2.ATM轉帳\\匯款 3.線上刷卡 4.分期付款 5.LinePay';

-- 2024-05-16 因應HTMLPurifier套件問題，將地址分隔號從<fat>改為|||
UPDATE `account` SET `home` = REPLACE(`home`, '<fat>', '|||') WHERE `id` = 1;