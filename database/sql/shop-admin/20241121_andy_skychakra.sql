-- 允許積分計算有小數
ALTER TABLE `account` 
CHANGE `point` `point` DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '紅利點數(現金積分)(1點1美金)(小數點後2位)',
CHANGE `point_increasable` `point_increasable` DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '增值積分(價值會異動)(小數點後2位)',
CHANGE `increasing_limit_other` `increasing_limit_other` DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '其他圓滿點數(美金)(小數點後2位)',
CHANGE `increasing_limit_consumption` `increasing_limit_consumption` DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '消費圓滿點數(美金)(小數點後2位)',
CHANGE `increasing_limit_invest` `increasing_limit_invest` DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '功德圓滿點數(美金)(小數點後2位)',
CHANGE `partner_accumulation` `partner_accumulation` DECIMAL(26, 2) NOT NULL DEFAULT '0' COMMENT '合夥人累積投資金額(美金)(處理回饋or調整合夥等級時更新)';

ALTER TABLE `point_increasable_pool` 
CHANGE `num` `num` DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '變動量(可為負)',
CHANGE `point_value` `point_value` DECIMAL(34, 10) NOT NULL DEFAULT '0' COMMENT '增值積分價值(積分池異動&機分派發後)';

ALTER TABLE `points_record` CHANGE `points` `points`  DECIMAL(32, 8) NOT NULL COMMENT '點數(可負數)';
ALTER TABLE `point_increasable_record` CHANGE `num` `num`  DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '變動量(可為負)';
ALTER TABLE `increasing_limit_record` CHANGE `num` `num`  DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '變動量(可為負)';
ALTER TABLE `dividend_month_record` CHANGE `num` `num`  DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '增值積分數量';
ALTER TABLE `points_to_cash_record` CHANGE `num` `num`  DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '金額';

ALTER TABLE `orderform_product` CHANGE `price_cv` `price_cv`  DECIMAL(32, 8) NOT NULL DEFAULT '0' COMMENT '商品CV金額(計算回饋時應扣除deduct_invest、deduct_consumption)';

ALTER TABLE `increasing_limit_record` 
CHANGE `type` `type` TINYINT(1) NOT NULL COMMENT '紀錄類型(1.處理回饋 2.處理增值轉換 3.自動升級合夥人等級 4.消費使用 5.管理員調整 6.提現...)';

-- 補提現的幣別欄位
ALTER TABLE `points_to_cash_record` ADD `currency` CHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '幣別' AFTER `user_id`;
