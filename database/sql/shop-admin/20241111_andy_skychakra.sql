-- 圓滿點數紀錄
CREATE TABLE `increasing_limit_record` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `user_id` INT(11) NOT NULL DEFAULT 0 COMMENT '對應會員id(必定對應某會員)' ,
    `num` INT(11) NOT NULL DEFAULT 0 COMMENT '變動量(可為負)' ,
    `msg` VARCHAR(256) NOT NULL COMMENT '訊息' , 
    `create_time` CHAR(10) NOT NULL COMMENT '建立時間(timestamp)' , 
    `limit_type` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '限制類型(1.功德 2.消費 3.其他)' ,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '圓滿點數紀錄'; 

-- 增值積分紀錄
CREATE TABLE `point_increasable_record` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `user_id` INT(11) NOT NULL DEFAULT 0 COMMENT '對應會員id(必定對應某會員)' ,
    `num` INT(11) NOT NULL DEFAULT 0 COMMENT '變動量(可為負)' ,
    `msg` VARCHAR(256) NOT NULL COMMENT '訊息' , 
    `create_time` CHAR(10) NOT NULL COMMENT '建立時間(timestamp)' , 
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '增值積分紀錄'; 

-- 調整紅利點數記錄資料表備註
ALTER TABLE `points_record` COMMENT = '紅利點數紀錄 天脈:現金積分紀錄';

-- 積分資金池
CREATE TABLE `point_increasable_pool` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `orderform_id` INT(11) NOT NULL DEFAULT 0 COMMENT '對應訂單id(可為0，遊客購物或「提現」)' ,
    `user_id` INT(11) NOT NULL COMMENT '對應會員id(必定對應某會員)' ,
    `num` INT(11) NOT NULL DEFAULT 0 COMMENT '變動量(可為負)' ,
    `msg` VARCHAR(256) NOT NULL COMMENT '變動說明' , 
    `datetime` CHAR(10) NOT NULL COMMENT '時間(timestamp)' , 
    `point_value` float NOT NULL DEFAULT 0 COMMENT '增值積分價值(積分池異動&機分派發後)' ,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '積分資金池，透過 sum(`num`) 即可得出目前的「積分資金池」總額。「提現」時 orderform_id=0、user_id為某會員、num為負數。point_value可看增值積分價值的歷史變化';

-- 月分紅認列紀錄
CREATE TABLE `dividend_month_record` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `orderform_id` INT(11) NOT NULL COMMENT '對應訂單id' ,
    `num` INT(11) NOT NULL DEFAULT 0 COMMENT '增值積分數量' ,
    `datetime` CHAR(10) NOT NULL COMMENT '認列月份(timestamp)(次月再分配)' , 
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '月分紅認列紀錄，若以月分紅期數[50,30,20]來看，3月的訂單若處理回饋時，將建立3筆認列紀錄月份分別是3、4、5，實際分配月份分別是4、5、6。';

-- 現金積分提現紀錄
CREATE TABLE `points_to_cash_record` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `user_id` INT(11) NOT NULL COMMENT '對應會員id(必定對應某會員)' ,
    `num` INT(11) NOT NULL DEFAULT 0 COMMENT '金額' ,
    `msg` VARCHAR(256) NOT NULL COMMENT '訊息' , 
    `time_create` CHAR(10) NOT NULL COMMENT '紀錄建立時間(timestamp)' , 
    `time_pay` CHAR(10) NOT NULL COMMENT '紀錄付款時間(timestamp)' , 
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '現金積分提現紀錄';

-- 會員添加自動升級合夥人等級欄位
ALTER TABLE `account` ADD `auto_partner` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '自動升級合夥人等級(1.否 2.是)' AFTER `status`;

-- 訂單添加自動升級合夥人等級欄位
ALTER TABLE `orderform` 
ADD `do_feedback_time` CHAR(10) NOT NULL DEFAULT '' COMMENT '分潤時間(timestamp)(非空時表示已回饋)' AFTER `status`,
ADD `user_id_operation` INT(11) NOT NULL DEFAULT '0' COMMENT '營運者(對應account的id)' AFTER `do_feedback_time`,
ADD `user_id_lecturer` INT(11) NOT NULL DEFAULT '0' COMMENT '講師者(對應account的id)' AFTER `user_id_operation`,
ADD `user_id_center` INT(11) NOT NULL DEFAULT '0' COMMENT '中心會員(對應account的id)' AFTER `user_id_lecturer`;

-- 預設系統&月分紅帳戶
INSERT INTO `account` 
(`id`, `name`, `registration_from`, `user_type`, `user_type_radio`, `product_view_id`, `vip_type`, `vip_type_course`, `from_order`, `pwd`, `f_code`, `number`, `update_time`, `status`, `auto_partner`, `total`, `responsibility_num`, `point`, `point_increasable`, `increasing_limit_other`, `increasing_limit_consumption`, `increasing_limit_invest`, `partner_accumulation`, `partner_level_id`, `center_level_id`, `email`, `gmail`, `line_id`, `FB_id`, `phone`, `tele`, `birthday`, `home`, `invoice`, `createtime`, `ordernum`, `gender`, `export`, `upline_user`, `recommend_content`, `share_pic`, `share_title`, `share_text`, `shop_name`, `file_company`, `file_person`, `bank`, `bank_code`, `bank_account_name`, `bank_account_code`) VALUES 
(10, '系統帳號', '1', '0', '0', '1', '0', '0', '0', '4eef1e1ea34879a2ae60c60815927ed9', NULL, 'XXX00000000001', '2019-10-03 14:01:40', '1', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '<EMAIL>', NULL, NULL, NULL, '**********', '0', '', '', NULL, '**********', NULL, NULL, '1', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(11, '月分紅帳號', '1', '0', '0', '1', '0', '0', '0', '4eef1e1ea34879a2ae60c60815927ed9', NULL, 'XXX00000000002', '2019-10-03 14:01:40', '1', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '<EMAIL>', NULL, NULL, NULL, '**********', '0', '', '', NULL, '**********', NULL, NULL, '1', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
ALTER TABLE `account` DROP `responsibility_num`; /*從orderform_product的price_cv等去計算即可得知「GV值」*/
