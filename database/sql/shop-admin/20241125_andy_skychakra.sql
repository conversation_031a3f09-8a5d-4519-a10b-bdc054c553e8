-- 調整積分設定備註
UPDATE `bonus_setting` SET `note` = '定時處理-增值轉現金月數' WHERE `bonus_setting`.`id` = 10;
UPDATE `bonus_setting` SET `note` = '定時處理-圓滿點清零月數' WHERE `bonus_setting`.`id` = 11;

UPDATE `bonus_setting` SET `note` = 'CV金額分潤比率(使用時要除100)(cv_rate)' WHERE `bonus_setting`.`id` = 2;
UPDATE `bonus_setting` SET `note` = '廣告合夥分潤比率(使用時要除100)(ad_partner_rate)' WHERE `bonus_setting`.`id` = 3;
UPDATE `bonus_setting` SET `note` = '消費圓滿點數倍率(limit_c_rate)' WHERE `bonus_setting`.`id` = 4;
UPDATE `bonus_setting` SET `note` = '其他圓滿點數倍率(limit_o_rate)' WHERE `bonus_setting`.`id` = 5;
UPDATE `bonus_setting` SET `note` = '月分紅期數(json格式)\n(使用時要除100)(空時不進行月分紅)(divided_times)' WHERE `bonus_setting`.`id` = 6;
UPDATE `bonus_setting` SET `note` = '推三反本第幾個會員比率(json格式)\n(使用時要除100)(空時不進行推三反本)(recommend_times)' WHERE `bonus_setting`.`id` = 7;
UPDATE `bonus_setting` SET `note` = '提現-代扣稅費(使用時要除100)(charge_tax)' WHERE `bonus_setting`.`id` = 8;
UPDATE `bonus_setting` SET `note` = '提現-轉入資金池(使用時要除100)(charge_pool)' WHERE `bonus_setting`.`id` = 9;
UPDATE `bonus_setting` SET `note` = '定時處理-增值轉現金月數(month_point2cash)' WHERE `bonus_setting`.`id` = 10;
UPDATE `bonus_setting` SET `note` = '定時處理-圓滿點清零月數(month_limit2zero)' WHERE `bonus_setting`.`id` = 11;


-- 紅利點數分配添加是否使用判斷
ALTER TABLE `dividend_month_record` ADD `used` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '已使用 0.否 1.是' AFTER `datetime`;

-- 添加回饋模組設定
INSERT INTO `bonus_setting` (`id`, `value`, `note`) VALUES (12, '0.1', '月分紅-新推廣合伙人人數加權');
INSERT INTO `bonus_setting` (`id`, `value`, `note`) VALUES (13, '10000', '月分紅-個人點數達標金額');
INSERT INTO `bonus_setting` (`id`, `value`, `note`) VALUES (14, '0.1', '月分紅-個人點數達標金額次數加權');
UPDATE `bonus_setting` SET `note` = '月分紅-新推廣合伙人人數加權(month_weight_partner)' WHERE `bonus_setting`.`id` = 12;
UPDATE `bonus_setting` SET `note` = '月分紅-個人點數達標金額(month_weight_gv_num)' WHERE `bonus_setting`.`id` = 13;
UPDATE `bonus_setting` SET `note` = '月分紅-個人點數達標金額次數加權(month_weight_gv)' WHERE `bonus_setting`.`id` = 14;
