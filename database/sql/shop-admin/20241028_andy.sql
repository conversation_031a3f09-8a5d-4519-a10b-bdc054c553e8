-- 調整參數備註
UPDATE `excel` SET `value2` = '是否啟用超額購買功能(control_pre_buy) 1.可 0.不可' WHERE `excel`.`id` = 1;
UPDATE `excel` SET `value2` = '是否可以指定商品刷卡(control_card_pay) 0.不可  1.可' WHERE `excel`.`id` = 7;
UPDATE `excel` SET `value2` = '是否可以指定商品付款方式(control_product_paying) 0.不可 1.可' WHERE `excel`.`id` = 21;
UPDATE `excel` SET `value2` = '使用商品報名(control_register) 0.不可 1.可' WHERE `excel`.`id` = 14;
UPDATE `excel` SET `value2` = '運費關聯商品(control_product_shipping) 0.不可 1.可' WHERE `excel`.`id` = 15;
UPDATE `excel` SET `value2` = '首頁顯示限時搶購(control_time_limit_prod) 0.不可 1.可' WHERE `excel`.`id` = 9;
UPDATE `excel` SET `value2` = '首頁插入EDM(control_index_edm) 0.不可 1.可' WHERE `excel`.`id` = 11;
UPDATE `excel` SET `value2` = '使用特價商品(無上限商品標籤)(control_sepc_price) 0.不可 1.可' WHERE `excel`.`id` = 10;
UPDATE `excel` SET `value2` = '第三方金流(thirdpart_money) 0.不可 1.可' WHERE `excel`.`id` = 16;
UPDATE `excel` SET `value2` = '第三方物流(thirdpart_logistic) 0.不可 1.可' WHERE `excel`.`id` = 17;
UPDATE `excel` SET `value2` = '第三方發票(thirdpart_invoice) 0.不可 1.可' WHERE `excel`.`id` = 18;
UPDATE `excel` SET `value2` = '首購優惠(control_FirstBuyDiscount) 0.不可 1.可' WHERE `excel`.`id` = 19;
UPDATE `excel` SET `value2` = 'VIP等級優惠(control_VipDiscount) 0.不可 1.可' WHERE `excel`.`id` = 20;
UPDATE `excel` SET `value2` = '是否平台化(control_platform) 0.否 1.是' WHERE `excel`.`id` = 22;
UPDATE `excel` SET `value2` = '商品圖是否可以放影片(control_upload_film) 0.不可  1.可' WHERE `excel`.`id` = 6;
UPDATE `excel` SET `value2` = '商品插入EDM(control_prod_edm) 0.不可 1.可' WHERE `excel`.`id` = 8;
UPDATE `excel` SET `value2` = '單一商品社群分享(control_social_share) 0.不可 1.可' WHERE `excel`.`id` = 13;
UPDATE `excel` SET `value2` = '是否使用招募會員(control_down_line) 0.否 1.是' WHERE `excel`.`id` = 23;

UPDATE `excel` SET `value2` = '商品圖片上限(control_img_quantity)' WHERE `excel`.`id` = 3;
UPDATE `excel` SET `value2` = '商品階層品項(control_prod_type_layer) 1.可 0.不可' WHERE `excel`.`id` = 4;
UPDATE `excel` SET `value2` = '後台複製商品(control_copy_product) 0.不可 1.可' WHERE `excel`.`id` = 12;
UPDATE `excel` SET `value2` = '自動登出(control_auto_logout)' WHERE `excel`.`id` = 5;

-- 修改舊訂單錯誤的發票設定
UPDATE `orderform` SET `InvoiceStyle` = '1' WHERE `orderform`.`InvoiceStyle` = 0;

-- 調整訂單狀態備註
ALTER TABLE `orderform` CHANGE `status` `status` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '狀態變化：New=>Pickable=>Picked=>Complete Cancel,Return\r\n消費者只在New時可取消訂單';

-- 暫時關閉功能
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 1;
UPDATE `excel` SET `value1` = '99' WHERE `excel`.`id` = 2;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 6;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 14;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 16;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 17;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 18;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 22;