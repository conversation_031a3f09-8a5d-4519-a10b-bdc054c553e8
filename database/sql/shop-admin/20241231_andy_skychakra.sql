-- 會員添加供應商回饋設定
ALTER TABLE `account` ADD `supplier_bonus` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '經銷回饋方式 1.增值積分 2.現金' AFTER `user_type_radio`;

-- 訂單商品資料調整
ALTER TABLE `orderform_product` 
ADD `use_ad` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '套用廣告(0.非 1.是)' AFTER `bonus_model_id`, 
ADD `distributor_id` INT(11) NOT NULL DEFAULT '0' COMMENT '供應商id(對應會員id)' AFTER `use_ad`;
ALTER TABLE `orderform_product` 
ADD `price_supplier` decimal(32,8) NOT NULL DEFAULT '0' COMMENT '供應商結算金額(美金)(已乘上數量)' AFTER `price_cv`, 
ADD `supplier_bonus` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '經銷回饋方式(建單當下紀錄) 1.增值積分 2.現金' AFTER `price_supplier`, 
ADD `do_supplier_share` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '處理供應商回饋時間(timestamp)(非空時表示已回饋)' AFTER `supplier_bonus`;
ALTER TABLE `orderform_product` CHANGE `price_cv` `price_cv` DECIMAL(32,8) NOT NULL DEFAULT '0.00000000' COMMENT '商品CV金額(美金)(已乘上數量，計算回饋時應扣除deduct_invest、deduct_consumption)';

-- 部分開放平台化
UPDATE `excel` SET `value1` = '1' WHERE `excel`.`id` = 22;

-- basic auth 授權列表
CREATE TABLE `basic_auth` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `username` CHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '帳號' , 
    `password` CHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密碼' , 
    `note` CHAR(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '備註' , 
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = 'API basic auth帳密列表';
INSERT INTO `basic_auth` (`id`, `username`, `password`, `note`) VALUES 
('1', 'newmorehot', '$2y$10$ong3jgbexrbdR4OGWIeotu8YGZJve4b8fCu1/i525HujLLupweqRi', '牛魔王'); -- O#yw4Guggusp
