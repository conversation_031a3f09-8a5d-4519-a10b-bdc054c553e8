-- 新增stock_status欄位(訂單管理)
ALTER TABLE `orderform` ADD COLUMN `stock_status` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0: 訂單庫存不足，1: 訂單庫存足夠' AFTER `status`;

-- 將Donation欄位更改為發票類型欄位
ALTER TABLE `orderform` CHANGE `Donation` `InvoiceStyle` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '發票類型 1.個人實體紙本發票 2.個人電子郵件寄送發票 3.個人共通性載具 4.公司戶發票 5.捐贈';
ALTER TABLE `orderform` CHANGE `transport_email` `transport_email` VARCHAR(1024) NULL;
ALTER TABLE `orderform` CHANGE `LoveCode` `LoveCode` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '捐贈碼';
ALTER TABLE `orderform` CHANGE `CarrierType` `CarrierType` VARCHAR(1) NOT NULL DEFAULT '' COMMENT '載具類型 空.紙本發票 2.自然人 3.手機';
ALTER TABLE `orderform` CHANGE `CarrierNum` `CarrierNum` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '載具編號';