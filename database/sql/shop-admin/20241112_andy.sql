-- 補充訂單商品欄位
ALTER TABLE `orderform_product` 
ADD `position_code` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '撿貨結果紀錄' AFTER `deal_position`;

-- 訂單添加欄位
ALTER TABLE `orderform` ADD `contribution_deduct` INT(11) NOT NULL DEFAULT '0' COMMENT '圓滿點數折抵金額' AFTER `total`;

-- 改積分模組到admin資料表
CREATE TABLE `bonus_model` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `name` CHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模組名稱' , 
    
    `normal_recommend` FLOAT NOT NULL DEFAULT 0 COMMENT '推廣獎勵(ex:25)(使用時要除100)' ,
    `normal_partner` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥平級獎勵(ex:25)(使用時要除100)' ,
    `normal_operation` FLOAT NOT NULL DEFAULT 0 COMMENT '營運獎勵(ex:12)(使用時要除100)' ,
    `normal_lecturer` FLOAT NOT NULL DEFAULT 0 COMMENT '講師獎勵(ex:3)(使用時要除100)' ,
    `normal_center` FLOAT NOT NULL DEFAULT 0 COMMENT '中心獎勵(ex:15)(使用時要除100)' ,
    `normal_dividend_month` FLOAT NOT NULL DEFAULT 0 COMMENT '月分紅(ex:20)(使用時要除100)' ,
    `use_partner_mode` tinyint(1) DEFAULT 0 COMMENT '是否使用合夥批發回饋(0.否 1.是)' ,
    
    `partner_recommend` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:推廣獎勵(ex:85)(使用時要除100)' ,
    `partner_partner` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:合夥平級獎勵(ex:0)(使用時要除100)' ,
    `partner_operation` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:營運獎勵(ex:12)(使用時要除100)' ,
    `partner_lecturer` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:講師獎勵(ex:3)(使用時要除100)' ,
    `partner_center` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:中心獎勵(ex:0)(使用時要除100)' ,
    `partner_dividend_month` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥設定:月分紅(ex:0)(使用時要除100)' ,

    `ad_bonus` FLOAT NOT NULL DEFAULT 0 COMMENT '廣告推廣獎勵(ex:25)(使用時要除100)' ,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '回饋模組';
ALTER TABLE `bonus_model` 
ADD `normal_center_divided_to_raiser` FLOAT NOT NULL DEFAULT '0' COMMENT '中心獎勵-發起者佔比(ex:30)(使用時要除100)' AFTER `normal_dividend_month`,
ADD `partner_center_divided_to_raiser` FLOAT NOT NULL DEFAULT '0' COMMENT '合夥設定:中心獎勵-發起者佔比(ex:0)(使用時要除100)' AFTER `partner_dividend_month`;
UPDATE `bonus_model` SET `normal_center_divided_to_raiser` = '30';

-- 會員添加發起者
ALTER TABLE `account` 
ADD `center_raiser_id` INT(11) NOT NULL DEFAULT '0' COMMENT '中心發起者' AFTER `center_level_id`;

-- 調整訂單表欄位備註
ALTER TABLE `account` 
CHANGE `total` `total` INT(11) NULL DEFAULT '0' COMMENT '累積金額(完成訂單時更新)', 
CHANGE `partner_accumulation` `partner_accumulation` FLOAT NOT NULL DEFAULT '0' COMMENT '合夥人累積投資金額(美金)(處理回饋or調整合夥等級時更新)';

-- 預設 增值積分 為1元
UPDATE `bonus_setting` 
SET `value` = '1' WHERE `bonus_setting`.`id` = 1;
