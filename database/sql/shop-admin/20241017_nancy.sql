UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 6;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 8;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 9;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 11;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 14;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 15;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 17;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 18;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 19;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 21;
UPDATE `excel` SET `value1` = '0' WHERE `excel`.`id` = 22;