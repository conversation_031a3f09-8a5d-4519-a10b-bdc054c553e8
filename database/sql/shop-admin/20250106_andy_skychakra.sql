-- 會員級別燒傷調整
ALTER TABLE `vip_type` CHANGE `recommend_bonus_ratio` `burn_cv` FLOAT NOT NULL DEFAULT '0' COMMENT '會員級別燒傷金額(美金)(ex:1200)';

-- 調整合夥等級關係的防呆
ALTER TABLE `partner_level_relation` ADD UNIQUE(`user_id`, `level_id`);

-- 調整圓滿點數紀錄的紀錄類型欄位備註
ALTER TABLE `increasing_limit_record` CHANGE `type` `type` TINYINT(1) NOT NULL COMMENT '紀錄類型(1.處理回饋 2.處理增值轉換 3.個人責任額檢查 4.消費使用 5.管理員調整)';

-- 訂單&訂單商品回饋欄位名稱調整
ALTER TABLE `orderform` CHANGE `do_feedback_time` `do_award_time` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消費回饋時間(timestamp)(非空時表示已回饋)';
ALTER TABLE `orderform_product` CHANGE `do_supplier_share` `do_award_supplier_time` CHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '供應商回饋時間(timestamp)(非空時表示已回饋)';

-- 控制前台是否開放增值積分轉移
INSERT INTO `bonus_setting` (`id`, `value`, `note`) VALUES ('15', '0', '前台開放會員轉移「增值積分」(1.允許 0.不允許)');
INSERT INTO `bonus_setting` (`id`, `value`, `note`) VALUES ('16', '0', '會員「現金積分」提現(1.允許 0.不允許)');

-- 修改欄位格式
ALTER TABLE `account` CHANGE `partner_accumulation` `partner_accumulation` DECIMAL(32,8) NOT NULL DEFAULT '0.00' COMMENT '合夥人累積投資金額(美金)(處理回饋or調整合夥等級時更新)';
-- ALTER TABLE `account` ADD `check_num` DECIMAL(32.8) NOT NULL DEFAULT '0' COMMENT '待檢查增值金額' AFTER `point_increasable`;

-- 修改語言版資料
UPDATE `lang` SET `lang_type` = 'tw', `lang_word` = '台灣' WHERE `lang`.`lang_id` = 1;
UPDATE `lang` SET `lang_type` = 'us', `lang_word` = '美國' WHERE `lang`.`lang_id` = 2;
UPDATE `lang` SET `lang_type` = 'ch', `lang_word` = '中國' WHERE `lang`.`lang_id` = 3;
UPDATE `lang` SET `lang_type` = 'hk', `lang_word` = '香港' WHERE `lang`.`lang_id` = 4;
