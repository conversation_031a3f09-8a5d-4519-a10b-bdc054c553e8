-- 添加 orderform_product(暫供查看用，利於後續計算分潤撈取資料用)
CREATE TABLE `orderform_product` (
    `id` INT(11) NOT NULL , 
    `orderform_id` INT(11) NOT NULL COMMENT '對應訂單id' , 
    `name` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品+品項名稱' , 
    `price` INT(11) NOT NULL COMMENT '單價' , 
    `num` INT(11) NOT NULL COMMENT '數量' , 
    `total` INT(11) NOT NULL COMMENT '總額(單價*數量)' , 
    `url` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品網址' , 
    `url2` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品圖片網址' , 
    `info_id` INT(11) NOT NULL COMMENT '對應商品id' , 
    `type_id` INT(11) NOT NULL COMMENT '對應商品品項id' , 
    `key_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品優惠類型(normal, 
    addprice...)' , 
    `Author` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '額外資料1' , 
    `house` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '額外資料2' , 
    `ISBN` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ISBN' , 
    `position` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '庫位名稱' , 
    `is_registrable` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否需報名資料(0.否 1.是)' , 
    `deal_position` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否已撿貨(0.否 1.是)' , 
    `pre_buy` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否可超額購買(0.否 1.是)' , 
    `pre_buy_num` INT(11) NOT NULL DEFAULT '0' COMMENT '超額購買數量' 
) ENGINE = InnoDB;
ALTER TABLE `orderform_product` ADD PRIMARY KEY(`id`);
ALTER TABLE `orderform_product` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `orderform_product` 
CHANGE `info_id` `info_id` INT(11) NULL DEFAULT NULL COMMENT '對應商品id', 
CHANGE `type_id` `type_id` INT(11) NULL DEFAULT NULL COMMENT '對應商品品項id', 
CHANGE `key_type` `key_type` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品優惠類型(normal, \r\n addprice...)';
ALTER TABLE `orderform_product` ADD `house_date` VARCHAR(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '額外資料3' AFTER `Author`;
