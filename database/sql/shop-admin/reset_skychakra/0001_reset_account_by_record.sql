-- 依增值積分紀錄更新會員的增值積分數
UPDATE `account` SET
`account`.`point_increasable` = (
     SELECT sum(`num`)
     FROM `point_increasable_record` 
     WHERE `account`.`id` = `point_increasable_record`.`user_id`
);

-- 依圓滿點數紀錄中限制類型為「其他」的紀錄，更新會員的其他圓滿點數
UPDATE `account` SET
`account`.`increasing_limit_other` = (
     SELECT sum(`num`)
     FROM `increasing_limit_record` 
     WHERE `account`.`id` = `increasing_limit_record`.`user_id` AND `limit_type`=3
);
