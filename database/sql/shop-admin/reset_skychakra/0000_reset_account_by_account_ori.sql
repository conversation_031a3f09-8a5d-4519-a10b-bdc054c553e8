-- 回歸積分到初始狀態(舊制度分潤完)
-- 此檔案不應該在開始使用新機制處理會饋後使用，因為會完全清空已使用新制度的分潤結果，並且需要重新處理回饋。
-- 而重新處理回饋會導致認列的時間更新，包含：訂單處理回饋時間、合夥等級關聯、月份紅認列月份...，連帶影響到「月分紅再分配」&「定期責任額檢查」運作，影響重大！！！

UPDATE `account_ori` SET `partner_level_id` = '4' WHERE `account_ori`.`id` = 1294;

-- 清空訂單設定
UPDATE `orderform` SET `do_award_time` = '';
UPDATE `orderform_product` SET `do_award_supplier_time` = '';

-- 更改初始現值
UPDATE `bonus_setting` SET `value` = '2.**********' WHERE `bonus_setting`.`id` = 1;
-- 還原初始資金池
UPDATE `point_increasable_pool` SET `point_value` = '2.**********' WHERE `point_increasable_pool`.`id` = 1;
UPDATE `point_increasable_pool` SET `num` = '95935.5765487' WHERE `point_increasable_pool`.`id` = 1;
DELETE FROM `point_increasable_pool` WHERE id>1;
ALTER TABLE `point_increasable_pool` AUTO_INCREMENT=2;
-- 清空會員等級變化、合夥狀態變化、月分紅認列紀錄
TRUNCATE `vip_type_relation`;
TRUNCATE `partner_level_relation`;
TRUNCATE `dividend_month_record`;
TRUNCATE `points_to_cash_record`;
-- 刪除非舊積分結果的增值積分異動
DELETE FROM `point_increasable_record` WHERE id>60;
ALTER TABLE `point_increasable_record` AUTO_INCREMENT=61;

-- 清空&重設現金積分紀錄
TRUNCATE `points_record`;
INSERT INTO  `points_record`  
SELECT 
null AS id,
id AS user_id,
'舊分潤結果' AS msg,
point AS points,
'2024-12-12 13:54:50' AS msg_time,
'**********' AS belongs_time
FROM `account_ori`  WHERE point>0;


-- 清空暫存圓滿點數紀錄，並把 訊息非「舊分潤結果」且是「管理員調整」的紀錄複製到 increasing_limit_record_temp
TRUNCATE `increasing_limit_record_temp`;
INSERT INTO  `increasing_limit_record_temp`
SELECT * FROM `increasing_limit_record` WHERE msg!='舊分潤結果' AND `type`=5;

-- 清空 increasing_limit_record，並依照 account_ori 表的 increasing_limit_invest, increasing_limit_consumption 新增圓滿點數紀錄
TRUNCATE `increasing_limit_record`;
INSERT INTO  `increasing_limit_record`  
SELECT 
null AS id,
id AS user_id,
increasing_limit_invest AS num,
'舊分潤結果' AS msg,
'**********' AS create_time,
'1' AS limit_type,
'1' AS type
FROM `account_ori`  WHERE increasing_limit_invest>0;
INSERT INTO  `increasing_limit_record`  
SELECT 
null AS id,
id AS user_id,
increasing_limit_consumption AS num,
'舊分潤結果' AS msg,
'**********' AS create_time,
'2' AS limit_type,
'1' AS type
FROM `account_ori`  WHERE increasing_limit_consumption>0;
-- 將暫存到 increasing_limit_record_temp 的紀錄捕回到 increasing_limit_record 中
INSERT INTO  `increasing_limit_record`
SELECT null AS id, `user_id`,`num`,`msg`,`create_time`,`limit_type`,`type` FROM `increasing_limit_record_temp`;

-- 統一清0會員狀態
UPDATE `account` SET
vip_type = 0,
vip_type_course = 0,
total = 0,
point = 0,
point_increasable = 0,
increasing_limit_other = 0,
increasing_limit_consumption = 0,
increasing_limit_invest = 0,
partner_accumulation = 0,
partner_level_id = 0;

-- 依照 account_ori 重置會員狀態，其中「消費圓滿點數」(increasing_limit_consumption)改填入 increasing_limit_record 中加總 imit_type=2 的 num 的數量(併入「管理員調整」數量)
UPDATE `account` SET
`account`.`vip_type` = (
     SELECT `account_ori`.`vip_type`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
),
`account`.`vip_type_course` = (
     SELECT `account_ori`.`vip_type_course`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
),
`account`.`point` = (
     SELECT `account_ori`.`point`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
),
`account`.`point_increasable` = (
     SELECT `account_ori`.`point_increasable`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
),
`account`.`increasing_limit_other` = (
     SELECT `account_ori`.`increasing_limit_other`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
),
`account`.`increasing_limit_invest` = (
     SELECT `account_ori`.`increasing_limit_invest`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
),
`account`.`partner_accumulation` = (
     SELECT `account_ori`.`partner_accumulation`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
),
`account`.`partner_level_id` = (
     SELECT `account_ori`.`partner_level_id`
     FROM `account_ori` 
     WHERE `account`.`number` = `account_ori`.`number`
)
WHERE EXISTS (
     SELECT 1
     FROM `account_ori`
     WHERE `account`.`number` = `account_ori`.`number`
);

UPDATE `account` SET
`account`.`increasing_limit_consumption` = (
     SELECT SUM(`num`)
     FROM `increasing_limit_record` 
     WHERE `account`.`id` = `increasing_limit_record`.`user_id` AND `limit_type`=2
);
