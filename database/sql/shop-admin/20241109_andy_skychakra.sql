-- -- 客製天脈功能
-- 回饋設定
CREATE TABLE `bonus_setting` (
    `id` INT(11) NOT NULL AUTO_INCREMENT, 
    `value` CHAR(128) NOT NULL COMMENT '值' , 
    `note` CHAR(128) NOT NULL COMMENT '說明' , 
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '回饋設定';
INSERT INTO `bonus_setting` (`id`, `value`, `note`) VALUES 
('1', '0', '增值積分現值(會在「處理回饋」後，會根據\r\n『「積分資金池」總額 / 目前增值積分總數』\r\n算出，勿以其他方式任意修改。)'),
('2', '80', 'CV金額分潤比率(使用時要除100)'),
('3', '10', '廣告合夥分潤比率(使用時要除100)'),
('4', '2', '消費圓滿點數倍率'),
('5', '1.5', '其他圓滿點數倍率'),
('6', '[50,30,20]', '月分紅期數(json格式)\r\n(使用時要除100)(空時不進行月分紅)'),
('7', '[10,20,70]', '推三反本第幾個會員比率(json格式)\r\n(使用時要除100)(空時不進行推三反本)'),
('8', '6', '提現-代扣稅費(使用時要除100)'),
('9	', '10', '提現-轉入資金池(使用時要除100)'),
('10', '2', '定時處理-積分轉現金月數'),
('11', '3', '定時處理-限額清零月數');

-- 調整VIP會員等級成天脈的「會員級別」
ALTER TABLE `vip_type` 
ADD `dividend_month_weighted` FLOAT NOT NULL DEFAULT '1' COMMENT '月分紅加權(ex:1.1)' AFTER `note`, 
ADD `recommend_bonus_ratio` FLOAT NOT NULL DEFAULT '0' COMMENT '推廣獎勵可回饋比率(ex:100)(燒傷)(使用時須除100)' AFTER `dividend_month_weighted`, 
ADD `discount_ratio` FLOAT NOT NULL DEFAULT '10' COMMENT '消費圓滿點數抵扣消費比率(ex:10)(使用時須除100)' AFTER `recommend_bonus_ratio`,
ADD `liability_gv` INT(11) NOT NULL DEFAULT '0' COMMENT '個人點數責任額(GV)' AFTER `discount_ratio`;
ALTER TABLE `vip_type` COMMENT = '會員等級 天脈:會員級別';
DELETE FROM `vip_type` WHERE `vip_type`.`id` > 0;
INSERT INTO `vip_type` (`id`, `type`, `vip_name`, `rule`, `discount`, `note`, `dividend_month_weighted`, `recommend_bonus_ratio`, `discount_ratio`, `liability_gv`) VALUES 
(1, '0', '消費商', '200', '1', '', '1', '20', '10', '1000'),
(2, '0', '任督級別', '2000', '1', '', '1.1', '40', '10', '1000'),
(3, '0', '中脈級別', '10000', '1', '', '1.2', '60', '10', '1000'),
(4, '0', '法身級別', '20000', '1', '', '1.3', '80', '10', '1000'),
(5, '0', '弟子級別', '100000', '1', '', '1.4', '100', '10', '1000');

-- 合夥人等級
CREATE TABLE `partner_level` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `name` CHAR(16) NOT NULL COMMENT '等級名稱' , 
    `ratio` FLOAT NOT NULL DEFAULT 0 COMMENT '功德圓滿點數倍率(ex:1.25)' ,
    `contribution` FLOAT NOT NULL DEFAULT 0 COMMENT '累積投資金額(數字愈小排越前，越低級)' ,
    `partner_bonus_ratio` FLOAT NOT NULL DEFAULT 0 COMMENT '合夥平級獎勵可回饋比率(ex:25)(使用時須除100)' ,
    `orderform_ad_weight` FLOAT NOT NULL DEFAULT 0 COMMENT '廣告訂單加權' ,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '合夥人等級';
INSERT INTO `partner_level` (`id`,`name`,`ratio`,`contribution`,`partner_bonus_ratio`,`orderform_ad_weight`) VALUES 
(1, '微合夥人', 1.25, 120, 0, 1),
(2, '創業合夥人', 1.5, 400, 0, 2),
(3, '準合夥人', 1.75, 1200, 0, 4),
(4, '初級合夥人', 2, 4000, 20, 8),
(5, '高級合夥人', 2.25, 12000, 40, 16),
(6, '區級合夥人', 2.5, 40000, 60, 32),
(7, '市級合夥人', 2.75, 120000, 80, 64),
(8, '省級合夥人', 3, 400000, 100, 128);
CREATE TABLE `partner_level_relation` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '會員id',
  `level_id` int(11) NOT NULL COMMENT '對應合夥人等級id',
  `datetime` char(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '時間(YYYY-mm-dd HH:ii:ss)'
)  ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '合夥人等級關聯';
ALTER TABLE `partner_level_relation` ADD PRIMARY KEY (`id`);
ALTER TABLE `partner_level_relation` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

-- 中心等級
CREATE TABLE `center_level` (
    `id` INT NOT NULL AUTO_INCREMENT, 
    `name` CHAR(16) NOT NULL COMMENT '等級名稱' , 
    `orders` INT(11) NOT NULL DEFAULT 0 COMMENT '等級排序(數字愈小排越前，越低級)' ,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB CHARSET=utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT = '中心等級'; 
INSERT INTO `center_level` (`id`,`name`,`orders`) VALUES 
(1, '區級', 1),
(2, '市級', 2),
(3, '省級', 3);

-- 會員欄位調整
ALTER TABLE `account` ADD `vip_type_course` INT(11) NOT NULL DEFAULT '0' COMMENT 'vip等級課程進度' AFTER `vip_type`;
ALTER TABLE `account` CHANGE `total` `total` INT(11) NULL DEFAULT '0' COMMENT '累積金額';
ALTER TABLE `account` ADD `responsibility_num` INT(11) NOT NULL DEFAULT '0' COMMENT '累積責任額(與「現金積分」拋轉有關)' AFTER `total`;
ALTER TABLE `account` CHANGE `point` `point` FLOAT NULL DEFAULT '0' COMMENT '紅利點數(現金積分)(1點1美金)(小數點後2位)';
ALTER TABLE `account` ADD `registration_from` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '會員來源(1.直接 2.廣告)' AFTER `name`;
ALTER TABLE `account` 
ADD `point_increasable` FLOAT NOT NULL DEFAULT '0' COMMENT '增值積分(價值會異動)(小數點後2位)' AFTER `point`, 
ADD `increasing_limit_other` FLOAT NOT NULL DEFAULT '0' COMMENT '其他圓滿點數(美金)(小數點後2位)' AFTER `point_increasable`, 
ADD `increasing_limit_consumption` FLOAT NOT NULL DEFAULT '0' COMMENT '消費圓滿點數(美金)(小數點後2位)' AFTER `increasing_limit_other`, 
ADD `increasing_limit_invest` FLOAT NOT NULL DEFAULT '0' COMMENT '功德圓滿點數(美金)(小數點後2位)' AFTER `increasing_limit_consumption`, 
ADD `partner_accumulation` FLOAT NOT NULL DEFAULT '0' COMMENT '合夥人累積投資金額(美金)' AFTER `increasing_limit_invest`, 
ADD `partner_level_id` INT(11) NOT NULL DEFAULT '0' COMMENT '合夥人等級id' AFTER `partner_accumulation`, 
ADD `center_level_id` INT(11) NOT NULL DEFAULT '0' COMMENT '對應中心等級' AFTER `partner_level_id`;

-- 添加訂單商品紀錄欄位
ALTER TABLE `orderform_product` 
ADD `product_cate` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '商品類型(1.投資、2.消費)' AFTER `pre_buy_num`, 
ADD `vip_type_reward` INT(11) NOT NULL DEFAULT '0' COMMENT '提升會員級別(對應vip_type的id，0.表示一般，其他表示課程)' AFTER `product_cate`, 
ADD `vip_type_require` INT(11) NOT NULL DEFAULT '0' COMMENT '需求會員級別(對應vip_type的id，0.表示無限制)' AFTER `vip_type_reward`, 
ADD `deduct_invest` INT(11) NOT NULL DEFAULT '0' COMMENT '功德圓滿消費折抵' AFTER `vip_type_require`, 
ADD `deduct_consumption` INT(11) NOT NULL DEFAULT '0' COMMENT '消費圓滿消費折抵' AFTER `deduct_invest`, 
ADD `price_cv` INT(11) NOT NULL DEFAULT '0' COMMENT '商品CV金額(計算回饋時應扣除deduct_invest、deduct_consumption)' AFTER `deduct_consumption`;
ALTER TABLE `orderform_product` ADD `bonus_model_id` INT(11) NOT NULL DEFAULT '0' COMMENT '回饋模組id' AFTER `product_cate`;

-- 清空過去會員設定
UPDATE `account` SET `vip_type` = '0';
TRUNCATE `vip_type_relation`;
UPDATE `account` SET `total` = '0', `point` = '0';
TRUNCATE `points_record`;
TRUNCATE `orderform`;
TRUNCATE `orderform_product`;
