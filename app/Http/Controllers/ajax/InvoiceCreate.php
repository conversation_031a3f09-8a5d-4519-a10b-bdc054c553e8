<?php
namespace App\Http\Controllers\ajax;

use App\Services\CommonService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\Invoice;

class InvoiceCreate extends Controller
{
  protected $InvoiceDays;

  public function __construct(){
    parent::__construct();
    $invoice_status = config('control.thirdpart_invoice');
    if ($invoice_status != 1) {
      $this->error(Lang::get('發票功能未開啟'));
    }

    $this->InvoiceDays = config('extra.ecpay.invoice_days');
  }

  public function invoice(){
    $result_transportComplete = Db::connection('main_db')->table('orderform')
                                                        ->where('transport_state = "1"')
                                                        ->where('transport_date <= "' . date('Y-m-d', strtotime('-' . $this->InvoiceDays . ' day')) . '"')
                                                        ->whereRaw('InvoiceNo IS NULL OR InvoiceNo = ""')
                                                        ->whereRaw('InvoiceDate IS NULL OR InvoiceDate = ""')
                                                        ->whereRaw('RandomNumber IS NULL OR RandomNumber = ""')
                                                        ->get();
    $result_transportComplete = CommonService::objectToArray($result_transportComplete);

    $result_receiptsComplete = Db::connection('main_db')->table('orderform')
                                                        ->where('status', 'New')
                                                        ->where('receipts_state = "1"')
                                                        ->whereRaw('InvoiceNo IS NULL OR InvoiceNo = ""')
                                                        ->whereRaw('InvoiceDate IS NULL OR InvoiceDate = ""')
                                                        ->whereRaw('RandomNumber IS NULL OR RandomNumber = ""')
                                                        ->get();
    $result_receiptsComplete = CommonService::objectToArray($result_receiptsComplete);

    $result = array_merge($result_transportComplete, $result_receiptsComplete);
    $result = array_unique($result);

    if (empty($result)) {
      echo '沒有資料';
      exit;
    }

    foreach ($result as $key => $arr) {
      $display_arr = [];

      $id = $arr['id'];
      $display_arr['訂單ID'] = $id;
      $display_arr['訂單編號'] = $arr['order_number'];

      /*開立發票*/
      $invoice_result = Invoice::instance()->create_invoice($id);

      if ($invoice_result['RtnCode'] != 1) {
        $display_arr['發票號碼'] = $invoice_result['RtnMsg'];
      } else {
        $display_arr['發票日期'] = $invoice_result['InvoiceDate'];
        $display_arr['發票號碼'] = $invoice_result['InvoiceNo'];
        $display_arr['發票隨機碼'] = $invoice_result['RandomNumber'];

        /*發送發票通知*/
        $invoice_notify = Invoice::instance()->invoice_notify($id);
        $display_arr['發票寄送通知'] = $invoice_notify['RtnMsg'];
      }

      // 將display_arr的內容轉成key: value的形式
      $result = '';
      foreach ($display_arr as $key => $value) {
        $result .= $key . ': ' . $value . "<br>";
      }

      echo $result . "<br>";
    }

    echo Lang::get('操作成功');
  }

  public function print(){
    $order_id = request()->get('order_id');
    $captcha = request()->get('captcha');

    $singleData = Db::connection('main_db')->table('orderform')->whereRaw('id = "' . $order_id . '"')->first();
    $order_number = $singleData->order_number;

    if (isset($captcha) == false) {
      $this->error(Lang::get('連結有誤'), url('Orderform/orderform_c') .'?id='. $order_number);
    }
    if ($captcha != hash('sha256', $order_id . '-photonic-' . date('Ymd') . 'ahJPzRUqpYNCxHgKFGA3')) {
      $this->error(Lang::get('連結有誤'), url('Orderform/orderform_c') .'?id='. $order_number);
    }

    $result = Invoice::instance()->print_invoice($order_id);

    if ($result['RtnCode'] != 1) {
      $this->error($result['RtnMsg'], url('/index/orderform/orderform_c').'?id='.$order_number);
    }else{
      $this->redirect($result['InvoiceHtml']);
    }
  }
}
