<?php
namespace App\Http\Controllers\ajax;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//photonicClass
use App\Services\CommonService;
use App\Services\pattern\HelperService;

class Ecpaylogistic extends Controller
{
  public function __construct() {
    parent::__construct();
  }

  public function createTransportPaper($id, $home_type='TCAT') {
    $singleData = DB::connection('main_db')->table('orderform')->find($id);
    $singleData = CommonService::objectToArray($singleData);
    //綠界建物流訂單
    try{
      $this->expressCreate($id, $singleData, $home_type);
    }catch(\Exception $e) {
      $this->error($e->getMessage());
    }

    //綠界物流託運單
    $html = $this->getTransportPaper($id);
    $html = $html.'<script>
      document.getElementById("ECPayForm").target = "";
      document.getElementById("ECPayForm").submit();
    </script>';
    return $html;
  }
  public function checkTransportPaper($id) {
    //綠界物流託運單
    $html = $this->getTransportPaper($id);
    return $html;
  }
  //綠界物流託運單
  public function getTransportPaper($id){
    $AllPayLogisticsID = DB::connection('main_db')->table('orderform')->find($id)->AllPayLogisticsID;
    include(ROOT_PATH.'app/Services/ThirdParty/Ecpay.Logistic.Integration.php');
    try {
      $AL = new \EcpayLogistics();
      $AL->HashKey = config('extra.ecpay.Logistic_HashKey');
      $AL->HashIV = config('extra.ecpay.Logistic_HashIV');
      $AL->Send = array(
        'MerchantID' => config('extra.ecpay.MerchantID'),
        'AllPayLogisticsID' => $AllPayLogisticsID,
        'PlatformID' => config('extra.ecpay.PlatformID'),
      );
      // PrintTradeDoc(Button名稱, Form target)
      $html = $AL->PrintTradeDoc(Lang::get('產生托運單'));
      // echo $html;
    } catch(Exception $e) {
      echo $e->getMessage();
    }

    return $html;
  }
  //綠界建物流訂單
  public function expressCreate($id, $orderData, $home_type='TCAT'){
    if((int)$orderData['total']>20000){
      throw new \Exception(Lang::get('超商取貨無法運送金額超過20000元之商品'));
    }

    // 超商取貨物流訂單幕後建立
    include(ROOT_PATH.'app/Services/ThirdParty/Ecpay.Logistic.Integration.php');

    //檢查是否需超商付款
    if($orderData['payment'] == "1"){ /*貨到付款*/
      $CollectionAmount = (int)$orderData['total'];
      $IsCollection = \EcpayIsCollection::YES;
    }else{
      $CollectionAmount = 0;
      $IsCollection = \EcpayIsCollection::NO;
    }

    //檢查哪種超商取貨
    $LogisticsType = \EcpayLogisticsType::CVS;
    if($orderData['transport'] == Lang::get('全家取貨')){
      $LogisticsSubType = \EcpayLogisticsSubType::FAMILY; //FAMILY(B2C), FAMILY_C2C(C2C)
    }elseif ($orderData['transport'] == Lang::get('7-11取貨')) {
      $LogisticsSubType = \EcpayLogisticsSubType::UNIMART; //UNIMART(B2C), UNIMART_C2C(C2C)
    }elseif ($orderData['transport'] == Lang::get('萊爾富取貨')) {
      $LogisticsSubType = \EcpayLogisticsSubType::HILIFE; //HILIFE(B2C), HILIFE_C2C(C2C)
    }elseif ($orderData['transport'] == Lang::get('宅配')){
      $LogisticsType = \EcpayLogisticsType::HOME;
      if($home_type=='TCAT'){ /*指定用黑貓*/
        $LogisticsSubType = \EcpayLogisticsSubType::TCAT; //TCAT.黑貓(宅配)
      }else{
        $LogisticsSubType = \EcpayLogisticsSubType::POST; //POST.中華郵政(宅配)
      }
      if($IsCollection ==  'Y'){
        throw new \Exception(Lang::get('宅配無法使用貨到付款'));
      }
    }else{
      throw new \Exception(Lang::get('無此運送方式'));
    }

    $seo = HelperService::getSeoData();
    $AL = new \EcpayLogistics();
    $AL->HashKey = config('extra.ecpay.Logistic_HashKey');
    $AL->HashIV = config('extra.ecpay.Logistic_HashIV');
    $AL->Send = array(
      'MerchantID' => config('extra.ecpay.MerchantID'),
      'MerchantTradeNo' => 'no' . date('YmdHis'),
      'MerchantTradeDate' => date('Y/m/d H:i:s'),
      'LogisticsType' => $LogisticsType,
      'LogisticsSubType' => $LogisticsSubType,
      'GoodsAmount' => (int)$orderData['total'],
      'CollectionAmount' => $CollectionAmount,
      'IsCollection' => $IsCollection,
      'GoodsName' => $seo['title'],
      'GoodsWeight' => (float)$orderData['GoodsWeight'],
      'SenderName' => config('extra.shop.sender_name'),
      'SenderPhone' => config('extra.shop.sender_phone'),
      'SenderCellPhone' => config('extra.shop.sender_cellphone'),
      'ReceiverName' => $orderData['transport_location_name'],
      'ReceiverPhone' => '', 	//$orderData['transport_location_tele']
      'ReceiverCellPhone' => $orderData['transport_location_phone'],
      'ReceiverEmail' => $orderData['transport_email'],
      'TradeDesc' => '', // 交易敘述
      'ServerReplyURL' => url('ajax/Ecpaylogistic/updateStatus').'/'.$id, //更新物流狀態用
      'LogisticsC2CReplyURL' => url('ajax/Ecpaylogistic/LogisticsC2CReplyURLs').'/'.$id, //取貨超商有問時，提醒修改超商用
      'Remark' => '', // 備註
      'PlatformID' => config('extra.ecpay.PlatformID'),
    );

    if($orderData['transport'] ==Lang::get("宅配")){
      //產生郵遞區號
      try{
        $ReceiverZipCode = HelperService::zip32_api($orderData['transport_location']);
        // dump($ReceiverZipCode);exit;
      }catch(\Exception $e) {
        throw new \Exception(Lang::get('地址錯誤'));
      }
      if($ReceiverZipCode==""){
        throw new \Exception(Lang::get('地址錯誤，找不到對應郵遞區號'));
      }

      $transport_location = str_replace($ReceiverZipCode, '', $orderData['transport_location']);
      $transport_location = trim($transport_location);
      //填入收件資訊(宅配)
      $AL->SendExtend = array(
        'SenderZipCode' => config('extra.shop.sender_zipcode'),
        'SenderAddress' => config('extra.shop.sender_address'),
        'ReceiverZipCode' => $ReceiverZipCode,
        'ReceiverAddress' => $transport_location,

        'Temperature' => \EcpayTemperature::ROOM,
        'Distance' => \EcpayDistance::SAME,
        'Specification' => \EcpaySpecification::CM_120,
        'ScheduledDeliveryTime' => \EcpayScheduledDeliveryTime::TIME_17_20
      );
    }
    else{
      //填入收件資訊(超商)
      $AL->SendExtend = array(
        'ReceiverStoreID' => $orderData['CVSStoreID'],
        'ReturnStoreID' => $orderData['CVSStoreID']
      );
    }

    // BGCreateShippingOrder()
    $Result = $AL->BGCreateShippingOrder();
    // dump($Result);exit;
    if(!isset($Result['AllPayLogisticsID'])){
      $error_msg = array_keys($Result)[1];
      if($Result[$error_msg]){
        throw new \Exception($Result[$error_msg]);
      }else{
        throw new \Exception($error_msg);
      }
      exit;
    }

    //儲存資料
    DB::connection('main_db')->table('orderform')->whereRaw('id ='.$id)->update([
      'AllPayLogisticsID'=>$Result['AllPayLogisticsID'], 
      'CheckMacValue'=>$Result['CheckMacValue']
    ]);

    //更新物流狀態
    $this->checkStatus($id);
  }
  //物流狀態更新
  public function updateStatus($id){
    $text = request()->post();
    if(empty($text) || $text == ''){
      echo '0|' . 'empty post';
      return;
    }

    include(ROOT_PATH.'app/Services/Thirdparty/Ecpay.Logistic.Integration.php');
    try {
      // 收到綠界科技的物流狀態，並判斷檢查碼是否相符
      $AL = new \EcpayLogistics();
      $AL->HashKey = config('extra.ecpay.Logistic_HashKey');
      $AL->HashIV = config('extra.ecpay.Logistic_HashIV');
      $AL->CheckOutFeedback(request()->post());

      $insertData = [
        'order_id' => $id,
        'logistics_id' => $text['AllPayLogisticsID'],
        'time' => date('Y-m-d H:i:s'),
        'text' => json_encode($text),
        'LogisticsType' => $text['LogisticsType'].'_'.$text['LogisticsSubType'],
        'RtnCode' => (string)$text['RtnCode'],
        'ShipmentNo' => $text['CVSPaymentNo'],
        'BookingNote' => $text['BookingNote'],
        'CheckMacValue' => $text['CheckMacValue']
      ];

      DB::connection('main_db')->table('logistics_record')->insert($insertData);

      // 在網頁端回應 1|OK
      echo '1|OK';
    } catch(\Exception $e) {
      echo '0|' . $e->getMessage();
    }
  }

  //查詢物流狀態
  public function checkStatus($id){
    $AllPayLogisticsID = DB::connection('main_db')->table('orderform')->find($id)->AllPayLogisticsID;

    include(ROOT_PATH.'app/Services/ThirdParty/Ecpay.Logistic.Integration.php');
    try {
      $AL = new \EcpayLogistics();
      $AL->HashKey = config('extra.ecpay.Logistic_HashKey');
      $AL->HashIV = config('extra.ecpay.Logistic_HashIV');
      $AL->Send = array(
        'MerchantID' => config('extra.ecpay.MerchantID'),
        'AllPayLogisticsID' => $AllPayLogisticsID,
        'PlatformID' => config('extra.ecpay.PlatformID'),
      );
      // QueryLogisticsInfo()
      $Result = $AL->QueryLogisticsInfo();
      // echo '<pre>' . print_r($Result, true) . '</pre>';

      $order = DB::connection('main_db')->table('orderform')->find($id);
      $AllPayLogisticsID = $order->AllPayLogisticsID;

      $insertData = [
        'order_id' => $id,
        'logistics_id' => $AllPayLogisticsID,
        'time' => date('Y-m-d H:i:s'),
        'text' => json_encode($Result),
        'LogisticsType' => $Result['LogisticsType'],
        'RtnCode' => $Result['LogisticsStatus'],
        'ShipmentNo' => $Result['ShipmentNo'],
        'BookingNote' => $Result['BookingNote'],
        'CheckMacValue' => $Result['CheckMacValue']
      ];

      DB::connection('main_db')->table('logistics_record')->insert($insertData);

    } catch(Exception $e) {
      echo $e->getMessage();
    }
  }
}