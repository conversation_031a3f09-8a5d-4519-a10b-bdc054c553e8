<?php
namespace App\Http\Controllers\ajax;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\PointRecords;

class Points extends Controller{
  /*!!!此專案棄用!!!*/
  public function check_expire(){
    $PointRecords = new PointRecords(0);
    $records = $PointRecords->get_member_has_points(); /*取得所有要檢查的會員id*/
    // dump($records);

    foreach ($records as $key => $value) {
      $PointRecords->change_user_id($value['user_id']);	/*更換檢查對象*/
      $result = $PointRecords->set_point_expire();		/*扣除過期點數*/

      if($result['expired_points']!=0){
        dump(Lang::get('帳號').":\t".$result['user_id']."\t".Lang::get('使用紅利').":\t".$result['expired_points']);
      }
    }
    dump(Lang::get('操作成功'));
  }
}