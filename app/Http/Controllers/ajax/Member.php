<?php
namespace App\Http\Controllers\ajax;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Controllers\Controller;

use App\Services\CommonService;
use App\Services\pattern\MemberInstance;

class Member extends Controller {
  private function response(int $errorcode, array $data=[], string $message=''){
    return [
      'errorcode' => $errorcode,
      'data' => $data,
      'message' => $message,
    ];
  }
  public function get_member_setting(Request $request){
    $arr_registration_from = MemberInstance::get_registration_from([])['db_data'];
    $arr_vip_types = MemberInstance::get_vip_types([])['db_data'];
    $arr_partner_levels = MemberInstance::get_partner_levels([])['db_data'];
    $arr_center_levels = MemberInstance::get_center_levels([])['db_data'];
    return $this->response(0, [
      'arr_registration_from' => $arr_registration_from,
      'arr_vip_types' => $arr_vip_types,
      'arr_vip_type_course' => $arr_vip_types,
      'arr_partner_levels' => $arr_partner_levels,
      'arr_center_levels' => $arr_center_levels,
    ], '');
  }

  public function insert_member(Request $request){
    $post_data = [];
    $post_data['number'] = $request->post('number'); /*會員編號*/
    $post_data['name'] = $request->post('name'); /*會員姓名*/
    $post_data['upline_user'] = $request->post('upline_user'); /*推薦者(會員編號)*/
    $post_data['phone'] = $request->post('phone'); /*會員手機(帳號)*/
    $post_data['email'] = $request->post('email'); /*會員信箱*/
    $post_data['tele'] = $request->post('tele'); /*會員電話*/
    $post_data['home'] = $request->post('home'); /*會員地址*/
    $post_data['registration_from'] = $request->post('registration_from'); /*會員來源(會員、廣告)*/
    $post_data['vip_type'] = $request->post('vip_type'); /*會員級別(級別名稱)*/
    $post_data['vip_type_course'] = $request->post('vip_type_course'); /*課程進度(級別名稱)*/
    $post_data['center_level_id'] = $request->post('center_level_id'); /*中心等級(級別名稱)*/
    $post_data['center_raiser_id'] = $request->post('center_raiser_id'); /*中心發起者(會員編號)*/
    $post_data['partner_level_id'] = $request->post('partner_level_id'); /*合夥人等級(級別名稱)*/
    $post_data['partner_accumulation'] = $request->post('partner_accumulation'); /*累積投資金額*/

    $MemberInstance = new MemberInstance(0);
    $array_error_data = $MemberInstance->import_member([$post_data]);
    if(count($array_error_data)>0){
      return $this->response(1, $array_error_data, '匯入失敗');
    }
    return $this->response(0, $array_error_data, '匯入成功');
  }
}