<?php
namespace App\Http\Controllers\ajax;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Http\Controllers\Controller;

use App\Services\CommonService;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\BonusSettingHelper;
use App\Services\pattern\OrderHelper;

class Order extends Controller {
  private function response(int $errorcode, array $data=[], string $message=''){
    return [
      'errorcode' => $errorcode,
      'data' => $data,
      'message' => $message,
    ];
  }
  public function get_order_setting(Request $request){
    $currency_to_site = config('extra.skychakra.currency_to_site');
    $currency = array_keys($currency_to_site);

    $arr_vip_types = MemberInstance::get_vip_types([], true)['db_data'];
    $arr_bonus_models = BonusSettingHelper::get_bonus_models([], true)['db_data'];
    $arr_product_cate = BonusSettingHelper::get_product_cate([], true)['db_data'];
    $arr_use_ad = BonusSettingHelper::get_use_ad([], true)['db_data'];
    return $this->response(0, [
      'currency' => $currency,

      'arr_vip_types' => $arr_vip_types,
      'arr_bonus_models' => $arr_bonus_models,
      'arr_product_cate' => $arr_product_cate,
      'arr_use_ad' => $arr_use_ad,
    ], '');
  }

  public function insert_order(Request $request){
    $post_data = [];
    $post_data['create_time'] = $request->post('create_time'); /*建立時間(YYYY-mm-dd HH:ii)*/
    $post_data['user_id'] = $request->post('user_id'); /*消費者(會員編號)*/
    $post_data['transport_location_name'] = $request->post('transport_location_name'); /*消費者姓名*/
    $post_data['transport_email'] = $request->post('transport_email'); /*消費者信箱*/
    $post_data['transport_location_phone'] = $request->post('transport_location_phone'); /*消費者手機*/
    $post_data['transport_location'] = $request->post('transport_location'); /*送貨地址*/
    $post_data['user_id_operation'] = $request->post('user_id_operation'); /*運營者(會員編號)*/
    $post_data['user_id_lecturer'] = $request->post('user_id_lecturer'); /*導師(會員編號)*/
    $post_data['user_id_center'] = $request->post('user_id_center'); /*區域中心(會員編號)*/
    $post_data['currency'] = $request->post('currency'); /*幣別(NT、USD、RMB)*/
    $post_data['total'] = $request->post('total'); /*訂單總金額(幣別依照「幣別」設定)*/
    $post_data['product'] = $request->post('product'); /*訂單商品*/
    // return $post_data;
    $array_error_data = OrderHelper::importOrder([$post_data]);
    if(count($array_error_data)>0){
      return $this->response(1, $array_error_data, '匯入失敗');
    }
    return $this->response(0, $array_error_data, '匯入成功');
  }
}