<?php
namespace App\Http\Controllers\ajax;

use App\Services\CommonService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class Hctlogistic extends Controller
{
  const HCT_LOGISTIC_TRANSDATA_URL = 'http://hctrt.hct.com.tw/EDI_WebService2/Service1.asmx';
  const HCT_LOGISTIC_SEARCH_URL = 'https://hctapiweb.hct.com.tw/phone/searchGoods_Main.aspx';

  public function __construct()
  {
    parent::__construct();
  }

  public function createTransportPaper($id)
  {
    $singleData = Db::connection('main_db')->table('orderform')->find($id);
    $singleData = CommonService::objectToArray($singleData);

    //建立新竹物流訂單
    try {
      $this->expressCreate($id, $singleData);
    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }

    //新竹物流託運單
    $html = $this->getTransportPaper($id);

    return $html;
  }

  public function checkTransportPaper($id)
  {
    //新竹物流託運單
    $html = $this->getTransportPaper($id);
    return $html;
  }

  //建立新竹物流訂單
  public static function expressCreate($id, $orderData)
  {
    if ($orderData['payment'] == "1") { /*貨到付款*/
      throw new Exception(Lang::get('宅配無法使用貨到付款'));
    }

    $requestBody = [
      // 訂單編號: char(30)
      'epino' => $orderData['order_number'].time(),
      // 收貨人名稱: char(40)
      'ercsig' => $orderData['transport_location_name'],
      // 收貨人電話: char(15)
      'ertel1' => $orderData['transport_location_phone'],
      // 收貨人地址: char(100)
      'eraddr' => $orderData['transport_location'],
      // 件數: char(4)
      'ejamt' => '1',
      // 重量(kg): char(5)
      'eqamt' => '5',
      // 出貨日期(YYYYMMDD, 預設為今天): char(8)
      'esdate' => date('Ymd', strtotime('+3 day')),
    ];

    $xmlBody = '<?xml version="1.0" encoding="utf-8"?>
            <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
            <soap:Body>
            <TransData_Json xmlns="http://tempuri.org/">
            <company>' . config('extra.hct.company') . '</company>
            <password>' . config('extra.hct.password') . '</password>
            <json>[' . json_encode($requestBody) . ']</json>
            </TransData_Json>
            </soap:Body>
          </soap:Envelope>';

    $ch = curl_init(self::HCT_LOGISTIC_TRANSDATA_URL);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $xmlBody);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
      'Content-Type: text/xml; charset=utf-8',
      'Content-Length: ' . strlen($xmlBody),
      'SOAPAction: "http://tempuri.org/TransData_Json"'
    ));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $response = curl_exec($ch);
    curl_close($ch);

    $position_start = strpos($response, '[');
    $position_end = strrpos($response, ']');
    $result = json_decode(substr($response, $position_start, $position_end - $position_start + 1), true)[0];

    if ($result['success'] == 'N') {
      throw new Exception("物流單建立失敗，" . $result['ErrMsg']);
    }

    $image = imagecreatefromstring(hex2bin($result['image']));

    if ($image === false) {
      throw new Exception(Lang::get('託運單建立失敗'));
    }

    if (file_exists(ROOT_PATH . 'public/downloads') == false) {
      mkdir(ROOT_PATH . 'public/downloads');
    }
    if (file_exists(ROOT_PATH . 'public/downloads/logistic') == false) {
      mkdir(ROOT_PATH . 'public/downloads/logistic');
    }
    if (file_exists(ROOT_PATH . 'public/downloads/logistic/Hct') == false) {
      mkdir(ROOT_PATH . 'public/downloads/logistic/Hct');
    }
    $file_name = ROOT_PATH . 'public/downloads/logistic/Hct/' . $orderData['order_number'] . '.png';

    imagepng($image, $file_name);
    imagedestroy($image);

    $cargo_data = [
      // 新竹物流貨號
      'AllPayLogisticsID' => $result['edelno'],
    ];

    //儲存資料
    Db::connection('main_db')->table('orderform')->whereRaw('id =' . $id)->update($cargo_data);
  }
  //新竹物流託運單
  public static function getTransportPaper($id)
  {
    $inner_html = '<button onclick="window.open(\'/ajax/Hctlogistic/logisticPrintPage?id=' . $id . '\' , \'_blank\')">' . Lang::get('產生托運單') . '</button>';
    return $inner_html;
  }
  //查詢物流狀態
  public static function checkStatus($cargo_num)
  {
    $params = [
      'v' => config('extra.hct.Var'),
      'no' => openssl_encrypt($cargo_num, 'des-cbc', date('Ymd', strtotime('+356 days')), 0, config('extra.hct.IVector')),
    ];

    $url = self::HCT_LOGISTIC_SEARCH_URL . "?" . http_build_query($params);

    return $url;
  }

  public function logisticPrintPage($id)
  {
    $order_number = Db::connection('main_db')->table('orderform')->find($id)->order_number;

    $inner_html = '<button onclick="window.print();">列印</button>';
    $inner_html .= '<br><br>';
    $inner_html .= '<img id="consignment" src="' . $this->logisticImageUrl($order_number) . '" style="width:65%;height:65%;">';

    return $inner_html;
  }
  public static function logisticImageUrl($order_number)
  {
    return request()->server('REQUEST_SCHEME') . '://' . request()->server('HTTP_HOST') . '/public/downloads/logistic/Hct/' . $order_number . '.png';
  }
}
