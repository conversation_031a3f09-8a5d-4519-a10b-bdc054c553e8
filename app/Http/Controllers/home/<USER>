<?php

namespace App\Http\Controllers\home;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Services\pattern\HelperService;
use App\Services\Admin\NavigationMenuService;
use App\Repositories\Home\NavigationMenuRepository;
use App\Models\NavigationMenuModel;
use App\Services\CommonService;
use Illuminate\Support\Facades\Lang;

class GlobalController extends Controller
{
    public $data;

    public function __construct()
    {
        parent::__construct();

        $this->data['company_name'] = config('extra.shop.company_name');

        /***購物車總開啟/關閉功能***/
        $function_result_current = HelperService::get_frontend_user_use_function();
        $this->data['show_list_current'] = $function_result_current['show_list'];
        $this->data['show_list_group_current'] = $function_result_current['show_list_group'];
        $this->data['close_function_current'] = $function_result_current['close_function'];
        $close_desk = $function_result_current['close_desk'];                /*前台關閉頁面的網址*/
        $close_desk_admin = $function_result_current['close_desk_admin'];    /*後臺關閉頁面的網址*/

        $route = app('request')->route()->getAction();
        $route = explode('Controllers\\', $route['controller'])[1];

        $action = explode('@', $route)[1];
        $module_and_controller = explode('@', $route)[0];
        $module = explode('\\', $module_and_controller)[0];
        $controller = explode('\\', $module_and_controller)[1];

        $close_desk_str = strtolower($module . '/' . $controller . '/' . $action);
        $not_show = false;

        if (empty($close_desk[$close_desk_str]) == false) {
            $not_show = true;
        } else if (empty($close_desk_admin['/' . $close_desk_str]) == false) {
            $not_show = true;
        }

        if ($not_show) {
            if (in_array($close_desk_str, ['admin/index/index', '/admin/index/index',])) {
                $this->redirect(url('All/index'));
            } else {
                $this->error(Lang::get('查無此頁'));
            }
        }
        $this->data['controller'] = $controller;
        $this->data['action'] = $action;
        // dump($controller);
        // dump($action);
        /***開啟/關閉購物車套用功能***/

        /*取得前台選單文字*/
        $frontend_menu_name = DB::table('frontend_menu_name')->orderBy('id', 'asc')->get();
        $frontend_menu_name = CommonService::objectToArray($frontend_menu_name);
        $frontend_menu = [];
        foreach ($frontend_menu_name as $key => $value) {
            $frontend_menu[$value['controller']] = $value;
            $frontend_menu[$value['controller']]['second_menu'] = $value['second_menu'] ? json_decode($value['second_menu'], true) : [];
        }
        // dump($frontend_menu);
        $this->data['frontend_menu'] = $frontend_menu;

        /* 標籤名稱(特價、即期...) */
        $tag = DB::table('frontend_data_name')->where('show_type', 'tag')->orderBy('id', 'asc')->get();
        $tag = CommonService::objectToArray($tag);
        $this->data['tag'] = $tag;

        $admin_info = DB::table('admin_info')->find(1);
        $admin_info = CommonService::objectToArray($admin_info);
        $this->data['admin_info'] = $admin_info;

        /** 導航選單 */
        $navigationMenuModel = new NavigationMenuModel();
        $navigationMenuRepository = new NavigationMenuRepository($navigationMenuModel);
        $navigationMenuService = new NavigationMenuService($navigationMenuRepository);
        $this->data['navigation_menu'] = $navigationMenuService->formatMenusForFrontend();
    }
}
