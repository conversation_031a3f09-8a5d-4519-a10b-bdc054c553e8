<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;

class Distribution extends PublicController
{
  public function distribution()
  {
    $getId = request()->get('id')?request()->get('id'):0;
    if(!is_numeric($getId)){ $this->error('發生錯誤'); }

    $dataList = DB::table('stronghold')->select('id', 'title')->orderByRaw('order_id asc, id desc')->get();
    $this->data['distrmenu'] = CommonService::objectToArray($dataList);

    $strongHolds = DB::table('typeinfo_str')->select('pic', 'title', 'content', 'url', 'sub_pics')
                                            ->whereRaw('parent_id ='.$getId.' and online = 1')
                                            ->orderBy('orders','asc')->orderBy('id', 'desc')
                                            ->paginate(12)
                                            ->appends([request()->query()]);

    if (empty($strongHolds->items()) == false) {
      foreach ($strongHolds->items() as $key => $item) {
        $strongHolds[$key]->sub_pics = $item->sub_pics ? json_decode($item->sub_pics) : [];
      }
    }
    $this->data['strongholds'] = $strongHolds;

    return view('home.distribution.distribution',['data'=>$this->data]);		
  }
}
