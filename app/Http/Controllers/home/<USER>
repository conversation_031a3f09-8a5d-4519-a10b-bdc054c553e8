<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;

class News extends PublicController
{
  public function news()
  {
    $news = DB::table('news')->where('online', 1)->orderByRaw('orders asc, time desc')->paginate(10);

    if (empty($news->items()) == false) {
      foreach ($news as $key => $item) {
        $news[$key]->time = date("Y-m-d", strtotime($item->time));
      }
    }

    $this->data['news'] = $news;
    return view('home.news.news',['data'=>$this->data]);
  }

  public function news_c()
  {
    $id = request()->get('id');
    if(!is_numeric($id)){ $this->error('發生錯誤'); }

    $news = DB::table('news')->where('id', $id)->first();
    $news->time = date("Y-m-d", strtotime($news->time));
    $this->data['news'] = $news;

    $pageup = DB::table('news')->select("id")->where("id", "<", $id)->where("online", 1)->orderBy("id", "desc")->first();
    $pageup = DB::table('news')->select("id")->where("id", ">", $id)->where("online", 1)->orderBy("id", "desc")->first();
    $this->data['pageup'] = CommonService::objectToArray($pageup);
    $this->data['pagedown'] = CommonService::objectToArray($pagedown);

    return view('home.news.news_c',['data'=>$this->data]);
  }
}

