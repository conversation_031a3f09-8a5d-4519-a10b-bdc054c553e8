<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PHPMailer\PHPMailer\PHPMailer;

use App\Services\pattern\simpleFactory\orderFactory\OrderFactory;

class Tspg extends PublicController
{	
  const API_ROOT = "tspg.taishinbank.com.tw/tspgapi/restapi";
  public $Tspg_DATA = [
    "sender"	=>	"rest",
    "ver"		=>	"1.0.0",  // 格式版本
    "pay_type"	=>	1,    // 付款類別
  ];
  public function __construct() {
    parent::__construct(request()->instance());
    self::$Tspg_DATA['mid'] = config('extra.tspg.mid');     // 特店代號
    self::$Tspg_DATA['s_mid'] = config('extra.tspg.s_mid'); // 子特店代號
    self::$Tspg_DATA['tid'] = config('extra.tspg.tid');     // 端末代號
    
    $this->tableName = 'orderform';
    $this->coupon_tableName = 'coupon_pool';
  }

  public function returnurl(Request $request, $id) {
    $postdata = file_get_contents("php://input",'r');
    DB::connection('main_db')->table('orderform')->where('id', $id)->update([
      'tspg_return_data' => $postdata
    ]);

    $postdata = json_decode($postdata);
    if($postdata->params->ret_code == "00"){
      $this->Order = OrderFactory::createOrder($id, $this->tableName, $this->coupon_tableName);
      $this->Order->setReceiptsState(1);
    }
  }

  public function auth($order_number, $total, $mailFromName, $backurl, $order_id){	// 信用卡授權交易
    $post_data = self::$Tspg_DATA;
    $post_data["tx_type"] = 1;                                // 交易類別	1:授權

    $layout = self::isMobile() ? '2' : '1';                   // 檢查流覽方式

    $post_data["params"] = [                                  // 交易要求參數清單
      "layout"		=>	$layout,                                // 裝置 '1'.電腦  '2'.手機
      "order_no"		=>	$order_number,                        // 訂單編號
      "amt"			=>	$total."00",                              // 交易金額
      "cur"			=>	"NTD",                                    // 幣別
      "order_desc"	=>	$mailFromName." ".Lang::get('訂單'),  // 交易說明
      "amt"			=>	$total.'00',                              // 交易金額
      "capt_flag"		=>	"0",                                  // 不同步請款
      "result_flag"	=>	"1",                                  // 要回傳詳細訂單資料
      "post_back_url"	=>	$backurl,                           // 接續網址
      "result_url"	=>  url('Tspg/returnurl').'/'.$order_id   // 交易狀況回傳網址
    ];
    $post_jsondata = json_encode($post_data);
    // dump($post_jsondata);exit;

    $post_url = "https://".(self::API_ROOT)."/auth.ashx";
    $res = parent::send_requset($post_url, $post_jsondata);
    return json_decode($res);
  }

  public function check_order(Request $request){
    $order_no = $request->get('order_no');
    $post_data = self::$Tspg_DATA;
    $post_data["tx_type"] = 7;    // 交易類別	7:查詢
    $post_data["params"] = [      // 交易要求參數清單
      "order_no"	=>	$order_no,  // 訂單編號
      "result_flag"	=>	"1",      // 要回傳詳細訂單資料
    ];
    $post_jsondata = json_encode($post_data);
    // dump($post_jsondata);exit;

    $post_url = "https://".(self::API_ROOT)."/other.ashx";
    $res = parent::send_requset($post_url, $post_jsondata);
    return json_decode($res);
  }

  public static function isMobile() {
    return preg_match("/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i", $_SERVER["HTTP_USER_AGENT"]);
  }
}
