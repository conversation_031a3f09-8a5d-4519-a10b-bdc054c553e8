<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;

class Experience extends PublicController
{
  public function experience()
  {
    $experience = DB::table('experience')->select('id', 'title', 'pic', 'content')->where('online',1)->orderBy('orders','asc')->orderBy('time','desc')->paginate(10);
    $this->data['experience'] = $experience;
    return view('home.experience.experience',['data'=>$this->data]);
  }

  public function experience_c()
  {
    $id = request()->get('id');
    if(!is_numeric($id)){ $this->error('發生錯誤'); }

    $experience = DB::table('experience')->select('id', 'title', 'pic', 'content')->where('id', $id)->first();
    
    $this->data['experience'] = CommonService::objectToArray($experience);

    $pageup = DB::table('experience')->select('id')->whereRaw("id < '".$id."' and online = 1")->orderBy('id','desc')->first();
    $pagedown = DB::table('experience')->select('id')->whereRaw("id > '".$id."' and online = 1")->orderBy('id','asc')->first();
    $this->data['pageup'] = $pageup;
    $this->data['pagedown'] = $pagedown;
    return view('home.experience.experience_c',['data'=>$this->data]);
  }
}