<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Services\pattern\simpleFactory\orderFactory\OrderFactory;
use PHPMailer\PHPMailer\PHPMailer;

use App\Http\Controllers\home\Tspg;
use App\Http\Controllers\admin\Payfee;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\HelperService;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\Invoice;
use app\ajax\controller\Hctlogistic;

class Orderform extends PublicController{
  private $order_tableName = 'orderform';
  private $coupon_tableName = 'coupon_pool';
  private $MemberInstance;

  public function __construct(){
    parent::__construct(request());
    $this->data['mebermenu_active'] = 'orderform';
    
    $this->MemberInstance = new MemberInstance(session()->get('user.id'));
    $userD = $this->MemberInstance->get_user_data($addr_change="split");
    $this->data['userD'] = $userD;

    $consent = DB::table('consent')->where("id",1)->first();
    $this->data['consent_other'] = $consent->other;

    if (isset(config('control.close_function_current')['會員管理']) == true) {
      $this->data['memberFunction'] = 0;
    } else {
      $this->data['memberFunction'] = 1;
    }
  }

  /*訂單列表頁面*/
  public function orderform() {
    if($this->user["id"]==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};

    $orderform = DB::connection('main_db')
                    ->table('orderform')
                    ->where([
                      'user_id' => $this->user['id'],
                      'show_date' => '1'
                    ])
                    ->whereIn('status', ['New', 'Pickable', 'Picked'])
                    ->orderBy('id','desc');
    $date_s = '';
    if(request()->get('date_s')){
      $date_s = request()->get('date_s');
      $orderform = $orderform->where('create_time', '>=', strtotime($date_s));
    }
    $date_e = '';
    if(request()->get('date_e')){
      $date_e = request()->get('date_e');
      $orderform = $orderform->where('create_time', '<=', strtotime($date_e.' +1Day'));
    }
    $orderform = $orderform->paginate(10)
                            ->appends([
                              'date_s' => $date_s,
                              'date_e' => $date_e,
                            ]);
    $this->data['orderform'] = $orderform;
    //dump($orderform);exit();

    $pay_fee_dict = Payfee::get_pay_fee_dict();
    $this->data['pay_fee_dict'] = $pay_fee_dict;

    return view('home.orderform.orderform',['data'=>$this->data]);
  }

  /*訂單列表-歷史紀錄頁面*/
  public function history() {
    if($this->user["id"]==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};
    
    if($this->user == null){
      $this->redirect(url('Index/index'));
    }

    $orderform = DB::connection('main_db')
                    ->table('orderform')
                    ->where([
                      'user_id' => $this->user['id'],
                      'show_date' => '1'
                    ])
                    ->whereIn('status', ['Complete','Cancel','Return'])
                    ->orderBy('id','desc');
    $date_s = '';
    if(request()->get('date_s')){
      $date_s = request()->get('date_s');
      $orderform = $orderform->where('create_time', '>=', strtotime($date_s));
    }
    $date_e = '';
    if(request()->get('date_e')){
      $date_e = request()->get('date_e');
      $orderform = $orderform->where('create_time', '<=', strtotime($date_e.' +1Day'));
    }
    $orderform = $orderform->paginate(10)
                            ->appends([
                              'date_s' => $date_s,
                              'date_e' => $date_e,
                            ]);
    $this->data['orderform'] = $orderform;
    //dump($orderform);exit();

    $pay_fee_dict = Payfee::get_pay_fee_dict();
    $this->data['pay_fee_dict'] = $pay_fee_dict;

    return view('home.orderform.history',['data'=>$this->data]);
  }

  /* 訂購成功頁面 */
  public function orderform_success() {
    $id = request()->get('id');
    $this->get_order_detail($id);
    // return view('home.orderform.orderform_success');
    return view('home.orderform.orderform_c',['data'=>$this->data]);
  }
  /* 訂單詳細內容頁面 */
  public function orderform_c() {
    $id = request()->get('id');
    $this->get_order_detail($id);
    return view('home.orderform.orderform_c',['data'=>$this->data]);
  }
  /*以id取得訂單詳細內容資料*/
  private function get_order_detail($id){
    $this->data['company_tel'] = config('extra.shop.service_tel');
    // $singleData = DB::connection('main_db')->table('orderform')->find($id);
    $singleData = DB::connection('main_db')->table('orderform')->where('order_number', $id)->first();
    $singleData = CommonService::objectToArray($singleData);
    if (empty($singleData) == true) {
      $this->error(Lang::get('無資料'), url('Index/index'));
    }
    $userId = session()->get('user.id');

    if (isset(config('control.close_function_current')['會員管理']) == true) {
      if (($singleData['user_id'] != $userId) && ($singleData['user_id'] != 0)) {
        $this->error(Lang::get('請先登入會員'), url('Login/login') . '?jumpUri=' . request()->server('REQUEST_URI'));
      }
    }

    if ($singleData['receipts_state'] == 1 && empty($singleData['InvoiceNo']) == true) {
      Invoice::instance()->create_invoice($singleData['id']);
      $singleData = DB::connection('main_db')->table('orderform')->where('order_number', $id)->first();
      $singleData = CommonService::objectToArray($singleData);
    }

    $product_data = OrderHelper::get_orderform_products([$singleData['id']]);
    $singleData['product'] = $product_data;

    if(isset(config('control.invoice_style_text')[$singleData['InvoiceStyle']])){
      $singleData['InvoiceStyleText'] = config('control.invoice_style_text')[$singleData['InvoiceStyle']];
    }else{
      $singleData['InvoiceStyleText'] = config('control.invoice_style_text')[1];
    }

    $love_code_result = DB::table('lovecode')->where('code', $singleData['LoveCode'])->first();
    
    if (empty($love_code_result) == false) {
      $singleData['LoveCodeFoundation'] = $love_code_result->name;
    } else {
      $singleData['LoveCodeFoundation'] = '其他';
    }

    $get_params =[
      'order_id' => $singleData['id'],
      'captcha' => hash('sha256', $singleData['id'] . '-photonic-' . date('Ymd') . 'ahJPzRUqpYNCxHgKFGA3'),
      'character' => 'user',
    ];
    $singleData['InvoiceHtml'] = request()->server('REQUEST_SCHEME') . '://' . request()->server('HTTP_HOST') . url('Ajax/invoice_create/print') . '?' . http_build_query($get_params);

    $singleData['ecpay_shippable'] = in_array($singleData['transport'], config('extra.ecpay.shippable'));
    $this->data['singleData'] = $singleData;

    $singleData['discount'] = json_decode($singleData['discount'],true);

    // $singleData['discount'] = array_map(function ($value)
    // {
    // 	return get_object_vars($value);
    // }, $singleData['discount']);

    $orderform = DB::connection('main_db')
                    ->table('orderform')
                    ->where([
                      'order_number' => $singleData['order_number'],
                    ])->get();
    $orderform = CommonService::objectToArray($orderform);

    if (isset($this->data['closeFunction']['會員管理']) == false) {
      $singleData['name']=DB::connection('main_db')->table('account')->where('id',$singleData['user_id'])->first()->name ?? '';
      // 資料加密
      $singleData['name'] = parent::hidestr($singleData['name'], 1,2);
    }

    $singleData['transport_location_name'] = parent::hidestr($singleData['transport_location_name'], 1,2);

    if ($singleData['transport'] == Lang::get('到店取貨') || $singleData['transport'] == Lang::get('宅配')) {
      $singleData['transport_location'] = parent::hidestr($singleData['transport_location'], 7);
    }

    $singleData['transport_location_phone'] = parent::hidestr($singleData['transport_location_phone'], -3);
    $singleData['transport_location_tele'] = $singleData['transport_location_tele'] == '' ? '無' : parent::hidestr($singleData['transport_location_tele'], -3);
    //dump($singleData);exit();
    $this->data['singleData'] = $singleData;

    $examinee_info = DB::table('examinee_info')->where(['order_id' => $singleData['id']])->get();
    $examinee_info = CommonService::objectToArray($examinee_info);
    $examinee_info_array = [];
    foreach($examinee_info as $k => $v){
      if(empty($examinee_info_array[$v['type_id']]))
        $examinee_info_array[$v['type_id']] = [];
      array_push($examinee_info_array[$v['type_id']],$v);
    }
    // dump($examinee_info);
    $this->data['examinee_info'] = $examinee_info_array;

    $pay_fee_dict = Payfee::get_pay_fee_dict();
    $this->data['pay_fee_dict'] = $pay_fee_dict;

    $this->data['orderform'] = $orderform;
  }


  /* 回報匯款 */
  public function setReportNumber() {
    $id = request()->post('id');
    $reportNumber = request()->post('reportNumber');
    $Order = OrderFactory::createOrder($id, $this->order_tableName, $this->coupon_tableName);
    return $Order->setReportNumber($reportNumber, $this->user["id"]);
  }
  // 取消訂單
  public function cancel(){
    $order_number = request()->post('order_number');
    $singleData = DB::connection('main_db')->table('orderform')->whereRaw('order_number = ?',$order_number)->first();
    $singleData = CommonService::objectToArray($singleData);
    $userId = session()->get('user.id');

    if($singleData['user_id']!=0 && $singleData['user_id']!=$userId){ // 訂單為會員訂單，且帳號不同
      $this->error(Lang::get('請先登入會員'));
    }
    if(in_array($singleData['status'], ['Complete'])){ /*檢查訂單是否已為「完成」*/
      $this->error(Lang::get(Lang::get('已完成訂單，無法取消')));
    }
    else if(in_array($singleData['status'], ['Cancel', 'Return'])){ /*檢查訂單是否已為「完成」*/
      $this->error(Lang::get(Lang::get('已取消過訂單')));
    }
    else if(in_array($singleData['status'], ['Pickable','Picked'])){ /*是否已在揀貨流程*/
      if(empty(config('control.close_function_current')['揀貨列表'])){
        $this->error(Lang::get(Lang::get('訂單已待揀貨，不可取消')));
      }
    }

    $id = $singleData['id'];
    if ($id != '' && $id != null){      
      $order_factory = OrderFactory::createOrder($id, $this->order_tableName, $this->coupon_tableName);
      $order_factory->changeStatus2Cancel(Lang::get('消費者取消'));
      if($userId){ /*有登入會員*/
        $return_data = ['code'=>1,'msg'=>Lang::get('操作成功'),'url'=>url('Orderform/orderform')];
      }else{
        $return_data = ['code'=>1,'msg'=>Lang::get('操作成功'),'url'=>'/'];
      }
      return $return_data;
    }else{
      $this->error(Lang::get('操作失敗'));
    }
  }

  /*訂單查詢頁面*/
  public function tracking(){
    return view('home.orderform.tracking',['data'=>$this->data]);
  }
  /*訂單查詢結果頁面*/
  public function orderTracking(){
    $jData = request()->post();
    $singleData = DB::connection('main_db')->table('orderform')->where('order_number',$jData['orderNum'])->first();
    $singleData = CommonService::objectToArray($singleData);
    if ($singleData!=null){
      if ($singleData['user_id']!=0){
        if ($this->user['id']!=$singleData['user_id']){
          
          return ['code'=>false,'msg'=>Lang::get('請先登入會員')];
        }else{
          return ['code'=>true,'msg'=>$singleData['order_number']];
          
        }
      }else{
        return ['code'=>true,'msg'=>$singleData['order_number']];
        
      }
      return ['code'=>false,'msg'=>Lang::get('無資料')];
    }else{
      return ['code'=>false,'msg'=>Lang::get('無資料')];
    }
  }

  /*AJAX 物流狀態*/
  public function ajax_logistic_status() {
    $id = request()->get('id');
    $singleData = DB::connection('main_db')->table('orderform')->whereRaw('id = ?',$id)->first();
    $singleData = CommonService::objectToArray($singleData);
    $logisticsRecord = DB::connection('main_db')->table('logistics_record as lr')
                                                ->Distinct(true)
                                                ->select('lr.time', 'lc.type', 'lc.message')
                                                ->leftJoin('logistics_code as lc', 'lr.RtnCode','lc.code')
                                                ->where('lr.order_id',$id)
                                                ->where('lr.logistics_id',$singleData['AllPayLogisticsID'])
                                                ->whereRaw('lr.LogisticsType = lc.type  and lr.RtnCode = lc.code')
                                                ->orderBy('lr.id','asc')
                                                ->get();
    // dump($logisticsRecord);
    return $logisticsRecord;
  }
}