<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\PointRecords;
use App\Services\pattern\BonusHelper;
use App\Services\pattern\BonusSettingHelper;

class Points extends PublicController{
  public function __construct() {
    parent::__construct(request());
    $this->data['mebermenu_active'] = 'points';

    if($this->user["id"]==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};
  }

  public function set_point_expire() {
    $PointRecords = new PointRecords($this->user['id']);
    $PointRecords->set_point_expire();
  }

  public function points() {
    if($this->user == null){
      $this->redirect(url('Index/index'));
    }
    $PointRecords = new PointRecords($this->user['id']);
    $expiring_points = $PointRecords->get_expiring_points();
    $this->data['expiring_points'] = $expiring_points;

    $consent = DB::table('consent')->select('point')->first();
    $this->data['consent']=$consent->point;

    return view('home.points.points',['data'=> $this->data]);
  }

  public function point_increasable() {
    $this->data['mebermenu_active'] = 'point_increasable';
    return view('home.points.point_increasable',['data'=> $this->data]);
  }
  public function transfer_point_increasable(Request $request){
    $bonus_setting = BonusSettingHelper::get_bonus_setting();
    if($bonus_setting['member_transfer_point_increasable']!=1){
      $this->error(Lang::get('功能未開放使用'));
    }

    $from = $this->user['number'];
    $to = $request->get('to');
    $num = $request->get('num');
    $msg = Lang::get('會員轉移增值積分');

    $PointRecords = new PointRecords(0);
    try {
      $PointRecords->transfer_point_increasable($from, $to, $num, $msg);
    } catch (\Throwable $th) {
      // throw $th;
      $this->error($th->getMessage());
    }
    $this->success(Lang::get('操作成功'));
  }

  public function point_increasable_to_point(Request $request){
    $bonus_setting = BonusSettingHelper::get_bonus_setting();
    if($bonus_setting['member_transfer_point_increasable']!=1){
      $this->error(Lang::get('功能未開放使用'));
    }
  
    $from = $this->user['number'];
    $num = $request->post('to_point_num');
    $msg = Lang::get('會員增值積分換現金積分');

    try {
      $BonusHelper = new BonusHelper();
      $BonusHelper->set_point_increasable_to_point($from, $num, $msg);
      $BonusHelper->send_by_cal($msg);
    } catch (\Throwable $th) {
      // throw $th;
      $this->error($th->getMessage());
    }
    $this->success(Lang::get('操作成功'));
  }

  public function get_point_data(Request $request){
    $params = $request->post();
    $point_type = $request->post('point_type');
    $params['user_id'] = $this->user['id'];

    /*依需求搜尋紀錄*/
    $PointRecords = new PointRecords($this->user['id']);
    if($point_type=='point_increasable'){
      $records_show = $PointRecords->get_records_increasable($params, true);
    }else{
      $records_show = $PointRecords->get_records($params, true);
    }
    
    /*找出符合搜尋紀錄的全部數量*/
    unset($params['count_of_items']);
    if($point_type=='point_increasable'){
      $records_all= $PointRecords->get_records_increasable($params, false);
    }else{
      $records_all = $PointRecords->get_records($params, true);
    }
    $records_total = count($records_all);

    $points = $PointRecords->get_current_points();
    if($point_type=='point_increasable'){
      $current_points = $points['point_increasable'];
    }else{
      $current_points = $points['point'];
    }

    return [
      'records_show' => $records_show,
      'records_total' => $records_total,
      'current_points' => number_format($current_points, 3),
    ];
  }
}