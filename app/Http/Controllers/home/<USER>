<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\ReCaptcha\ReCaptcha;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\MemberArticle;

class ShareArticle extends PublicController
{
  public function __construct(){
    $request = request();
    parent::__construct($request);
  }

  public function index(){
    $share_article = DB::table('member_article')->where('show_status', 1)->where('show', 1)->orderBy('orders', 'asc')->orderBy('create_time', 'desc')->paginate(12);
    if (empty($share_article->items()) == false) {
      foreach ($share_article as $key => $item) {
        $share_article[$key]->create_time_f = date("Y-m-d", $item->create_time);
      }
    }
    $this->data['share_article'] = $share_article;
    
    return view('home.share_article.index',['data'=> $this->data]);
  }
  public function detail(){
    $id = request()->get('id');
    if(!is_numeric($id)){ $this->error(Lang::get('發生錯誤')); }
    
    $article = DB::table('member_article')->where('id', $id)->where('show_status', 1)->where('show', 1)->first();
    if(!$article){ $this->error(Lang::get('連結有誤')); }
    $article->create_time_f = date("Y-m-d", $article->create_time);
    $this->data['article'] = $article;

    $visitorId = request()->get('visitorId');
    $love_article = Db::table('member_article_interact')
                      ->orderBy('id', 'desc')
                      ->where('visitorId', $visitorId)
                      ->where('article_id', $id)
                      ->where('act_type', 2)
                      ->first();
    $this->data['love_article'] = $love_article->value??-1;

    return view('home.share_article.detail',['data'=>$this->data]);
  }

  public function report(){
    $request = request();
    $ip = $request->ip(); 
    $article_id = $request->post('article_id'); 
    $note = $request->post('note');
    if (config('extra.shop.google_recaptcha_sitekey')) {
      // _GOOGLE_RECAPTCHA_SEC_KEY 就是 google 給的 Secret Key
      $google_recaptcha_seckey = config('extra.shop.google_recaptcha_seckey');
      if($google_recaptcha_seckey){
        $recaptcha = new ReCaptcha($google_recaptcha_seckey);
        $gRecaptchaResponse = $request->post('g-recaptcha-response');
        $remoteIp = $request->server('REMOTE_ADDR');
        $resp = $recaptcha->verify($gRecaptchaResponse, $remoteIp);
        if(!$resp->isSuccess()){
          $this->error(Lang::get('請先證明您不是機器人'));
        }
      }
    }

    try {
      MemberArticle::report_article($ip, $article_id, $note);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success(Lang::get('操作成功'));
  }
  public function get_interact_status(){
    $request = request();
    $search = $request->post();
    $search['visitorId'] = $search['visitorId'] ?? '-1';
    try {
      $record = MemberArticle::get_interact_status($search);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success($record);
  }
  public function interact(){
    $request = request();
    $visitorId = $request->post('visitorId');
    $article_id = $request->post('article_id'); 
    $act_type = $request->post('act_type');

    try {
      $value = MemberArticle::interact($visitorId, $article_id, $act_type);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success($value);
  }
}