<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Services\DBtool\DBTextConnecter;

class Findorder extends PublicController
{
  private $DBTextConnecter;

  public function findorder() {
    //session()->get('user.email');
    $user_name = session()->get('user.name');
    $user_phone = session()->get('user.phone');
    $user_email = session()->get('user.email') ? session()->get('user.email') : session()->get('user.gmail');
    $this->data['user_name'] = $user_name;
    $this->data['user_phone'] = $user_phone;
    $this->data['user_email'] = $user_email;

    $contact = DB::table('contact')->find(1);
    return view('home.findorder.findorder',['data'=>$this->data]);
  }

  function doFindorder(){
    $user_id = session()->get('user.id');
    if(!$user_id){$this->error(Lang::get('請先登入會員'));}

    $user_name = request()->post('user_name');
    $user_phone = request()->post('user_phone');
    $user_email = request()->post('user_email');
    if(!$user_name){$this->error(Lang::get('請輸入姓名'));}
    if(!$user_phone){$this->error(Lang::get('請輸入手機'));}
    if(!$user_email){$this->error(Lang::get('請輸入信箱'));}

    $data = request()->post('products');
    foreach ($data as $k => $v) {
      if($v['name']==""){$this->error(Lang::get('資料不完整'));}
      if($v['num']==""){$this->error(Lang::get('資料不完整'));}
      if($v['num'] < 1){$this->error(Lang::get('資料有誤'));}

      /*處理圖片*/
      if($v['img']!=""){
        $data[$k]['img'] = CommonService::uploadFile('/public/uploads/findorderForm', $v['img']);
      }
    }
    // dump($data);exit;

    $this->DBTextConnecter = DBTextConnecter::withTableName('contact_find_prod');
    try{
      $this->DBTextConnecter->setDataArray([
        'user_id' => $user_id,
        'user_name' => $user_name, 
        'user_phone' => $user_phone,
        'user_email' => $user_email,
        'ask' => json_encode($data, JSON_UNESCAPED_UNICODE),
        'createdate' => date('Y-m-d H:i'),
        'status' => 0,
      ]);
      $this->DBTextConnecter->createTextRow();
    } catch (\Exception $e){
      $this->error(Lang::get('發生錯誤'));
    }

    $globalMailData = parent::getMailData();
    $findorder_letter = Lang::get('找貨回函建立信消費者');
    $findorder_letter = str_replace("{user_name}", $user_name, $findorder_letter);
    if($user_email){
      $mailBody = "
        <html>
          <head></head>
          <body>
            <div>
              ".$findorder_letter."
            </div>
            <div>
              ". $globalMailData['system_email']['contact_complete'] ."
            </div>
            <div style='color:red;'>
              ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
            </div>
          </body>
        </html>
      ";
      $mail_return = parent::Mail_Send($mailBody,'client',$user_email, Lang::get('找貨回函建立成功'));
    }

    $findorder_admin_letter = Lang::get('找貨回函建立信管理者');
    $data_text = '';
    foreach ($data as $k => $v) {
      $img_text = $v['img'] ? "<a href='".request()->server('REQUEST_SCHEME')."://".request()->server('HTTP_HOST')."/".$v['img']."'>查看</a>," : "無";
      $data_text .= ($k+1).".
            ".Lang::get('品名')."： ".$v['name'].", 
            ".Lang::get('單位')."： ".$v['unit'].", 
            ".Lang::get('數量')."： ".$v['num'].", 
            ".Lang::get('圖片')."： ".$img_text.", 
            ".Lang::get('備註')."： ".$v['note']."<br>";
    }
    $findorder_admin_letter = str_replace("{data_text}", $data_text, $findorder_admin_letter);
    $mailBody = "
      <html>
        <head></head>
        <body>
          <div>
            ".$findorder_admin_letter."
          </div>
          <div style='color:red;'>
            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
          </div>
        </body>
      </html>
    ";
    $mail_return = parent::Mail_Send($mailBody,'admin','', Lang::get('新找貨回函提醒'));
    $this->success(Lang::get('操作成功'));
  }
}
