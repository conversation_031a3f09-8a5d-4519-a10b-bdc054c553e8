<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\pattern\simpleFactory\orderFactory\OrderFactory;

class Ecreturn extends PublicController
{
  private $tableName;
  private $coupon_tableName;
  private $Order;

  public function __construct() {
    parent::__construct(request());
    $this->tableName = 'orderform';
    $this->coupon_tableName = 'coupon_pool';
  }

  public function returnurl(Request $request, $id) {
    include(ROOT_PATH.'app/Services/ThirdParty/ECPay.Payment.Integration.php');

    try {
      $obj = new \ECPay_AllInOne();

      //服務參數
      $obj->ServiceURL  = config('extra.ecpay.ServiceURL');
      $obj->HashKey     = config('extra.ecpay.HashKey');
      $obj->HashIV      = config('extra.ecpay.HashIV');
      $obj->MerchantID  = config('extra.ecpay.MerchantID');
      $obj->EncryptType = '1';

      $obj->CheckOutFeedback();

      if(request()->post('RtnCode')=='1'){
        $this->Order = OrderFactory::createOrder($id, $this->tableName, $this->coupon_tableName);
        $this->Order->setReceiptsState(1);

        DB::connection('main_db')->table('orderform')->where('id', $id)->update([
          'card4no' =>request()->post('card4no'),
        ]);
      }else{
        // DB::connection('main_db')->table('orderform')->where('id', $id)->update([
        // 	'receipts_state' => 0
        // ]);
      }
      echo '1|OK';
    } catch(\Exception $e) {
      DB::connection('main_db')->table('orderform')->where('id', $id)->update([
        'error' => json_encode(request()->post(), JSON_UNESCAPED_UNICODE),
      ]);
      echo '0|' . $e->getMessage();
    }
  }
}
