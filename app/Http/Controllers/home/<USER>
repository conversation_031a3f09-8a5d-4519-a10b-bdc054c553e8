<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Gregwar\Image\Adapter\Common;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class Coupon extends PublicController
{
  public function __construct(){
    parent::__construct(request());
    $this->data['mebermenu_active'] = 'coupon';

    $consent = DB::table('consent')->where("id",1)->first();
    $this->data['consent_other']= $consent->other;

    if($this->user["id"]==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};
  }

  public function coupon() {
    if($this->user == null){
      $this->redirect(url('/'));
    }

    $consent = DB::table('consent')->select('coupon')->first()->coupon;
    $this->data['consent']=$consent;

    $Coupons = DB::table('coupon')
                ->select(
                  'coupon.id AS coupon_id', 
                  'coupon.title AS coupon_title', 
                  'coupon.transfer AS coupon_transfer', 
                  'coupon_pool.login_time AS coupon_pool_login_time', 
                  'coupon_pool.id AS coupon_pool_id', 
                  'coupon.end AS coupon_end'
                )
                ->join('coupon_pool', 'coupon.id','=','coupon_pool.coupon_id')
                ->where([
                  'coupon_pool.owner' => $this->user['id'],
                  'coupon.online' => 1,
                  ['coupon.start','<', time()],
                  ['coupon.end', '>', time()]
                ])
                ->whereNull('coupon_pool.use_time')
                ->whereNotNull('coupon_pool.login_time')
                ->get();
    $this->data['Coupons'] = CommonService::objectToArray($Coupons);
    return view('home.coupon.coupon',['data'=> $this->data]);
  }

  public function record() {
    $Coupons = DB::table('coupon as cn')
                  ->select('cn.id AS coupon_id',
                    'cn.title AS coupon_title',
                    'cn.transfer AS coupon_transfer',
                    'cp.use_time AS coupon_pool_use_time',
                    'cp.id AS coupon_pool_id',
                    'cn.end AS coupon_end')                
                  ->join('coupon_pool as cp', 'cn.id','=','cp.coupon_id')
                  ->whereRaw(
                    'cp.owner = ' . $this->user['id'] . ' AND ' .
                    'cn.online = 1 AND (' .
                      'cn.end < ' . time() . ' OR ' .
                      'cp.use_time IS NOT NULL' .
                    ')'
                  )
                  ->get();
    $consent = DB::table('consent')->select('coupon')->first()->coupon;
    $this->data['consent'] = $consent;
    $this->data['Coupons'] = CommonService::objectToArray($Coupons);

    return view('home.coupon.record',['data'=> $this->data]);
  }

  public function description() {
    $id = request()->post('id');
    if(!is_numeric($id)){ $this->error('發生錯誤'); }

    $description = DB::table('coupon')->select('content')->find($id);
    $description = CommonService::objectToArray($description);
    return $description;
  }

  public function getCouponByNumber() {
    try {
      $number = request()->post('number');
      $coupon = DB::table('coupon_pool')
                  ->where('number', $number)
                  ->whereNull('owner')
                  ->first();
      $coupon = CommonService::objectToArray($coupon);
      if(!$coupon){
        throw new \Exception(Lang::get('序號有誤或已被領取'));
      }
      if (!sizeof($coupon)) {
        throw new \Exception(Lang::get('序號有誤或已被領取'));
      }

      $coupon_type = DB::table('coupon')
                        ->where('id', $coupon['coupon_id'])
                        ->first();
      $coupon_type = CommonService::objectToArray($coupon_type);
      $limit_num = $coupon_type['limit_num'];
      $user_coupons = DB::table('coupon_pool')
                        ->where('coupon_id', $coupon['coupon_id'])
                        ->where('owner', $this->user['id'])
                        ->get();
      if(count($user_coupons)>=$limit_num){
        throw new \Exception(Lang::get('超出上限'));
      }

      DB::table('coupon_pool')
          ->where('number', $number)
          ->update([
            'owner' => $this->user['id'],
            'login_time' => time()
          ]);
    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }

    $this->success(Lang::get('操作成功'));
  }

  public function transforCoupon() {
    try {
      $userNumber = request()->post('number');
      $userId = DB::connection('main_db')
                  ->table('account')
                  ->select('id')
                  ->where('number', $userNumber)
                  ->first();
      $userId = CommonService::objectToArray($userId);

      if (!$userId) {
        throw new \Exception(Lang::get('資料有誤'));
      }
      if($userId['id']==$this->user['id']){
        throw new \Exception(Lang::get('請勿輸入自己的編號'));
      }

      $couponId = request()->post('id');
      $coupon = DB::table('coupon_pool')
                  ->where('id', $couponId)
                  ->update([
                    'owner' => $userId['id'],
                    'login_time' => time()
                  ]);
    } catch (\Exception $e) {
      $this->error($e->getMessage());
    }

    $this->success(Lang::get('操作成功'));
  }
}
