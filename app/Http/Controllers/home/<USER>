<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;

class Qa extends PublicController
{
  public function qa()
  {
    $page = request()->get('page') ?? '1';
    $this->data['page'] = $page;

    $cate = request()->get('cate') ?? "";
    $this->data['cate'] = $cate;

    $searchText = request()->get('searchText') ?? "";
    $this->data['searchText'] = $searchText;

    $qa = DB::table('qa')->select('q', 'a')->where('online',1);
    if($cate) $qa = $qa->where('category', $cate);
    if($searchText) $qa = $qa->where(function ($query) use ($searchText) {
      $query->where('q', 'like', "%".$searchText."%")
            ->orWhere('a', 'like', "%".$searchText."%");
    });

    $qa = $qa->orderByRaw('order_id')->paginate(10)->appends([
      'cate' => $cate,
      'searchText' => $searchText,
    ]);
    $this->data['qa'] = $qa;

    $qa_cate = DB::table('qa')->select('id' ,'category')->where('online', 1)->groupBy('category')->orderBy('category','asc')->get();
    $this->data['qa_cate'] = CommonService::objectToArray($qa_cate);

    return view('home.qa.qa',['data'=>$this->data]);
  }
}