<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\pattern\MemberInstance;

use App\Services\ReCaptcha\ReCaptcha;

class About extends PublicController
{
  protected $DBTextConnecter;

  public function about_contact(Request $request) {
    $distributor_id = $request->get('distributor_id') ?? 0;

    $default_wanted = '';

    $order_number = $request->get('order_number') ?? '';
    if($order_number){
      $default_wanted = Lang::get('訂單問題');
      $orderform = Db::connection('main_db')->table('orderform')->where('order_number', $order_number)->first();
      $orderform = CommonService::objectToArray($orderform);
      if($orderform){
        $distributor_id = $orderform['distributor_id'];
      }
    }
    $this->data['order_number'] = $order_number;

    $default_message = '';
    $prod_id = $request->get('prod_id') ?? '';
    $this->data['prod_id'] = $prod_id;
    if($prod_id){
      $productinfo = DB::table('productinfo')->where('id', $prod_id)->first();
      $productinfo = CommonService::objectToArray($productinfo);
      if($productinfo){
        $default_wanted = Lang::get('商品詢問');
        $default_message = Lang::get('商品').'：'.$productinfo['title'];
      }
    }
    $type_id = $request->get('type_id') ?? 0;
    $type_id = explode('_', $type_id)[0];
    $productinfo_type = DB::table('productinfo_type')->where('id', $type_id)->where('online', 1)->first();
    $productinfo_type = CommonService::objectToArray($productinfo_type);
    if($productinfo_type){
      if($productinfo_type['title']){
        $default_message .= (' '.Lang::get('品項').'：'.$productinfo_type['title']);
      }
    }
    $this->data['default_message'] = $default_message;

    $this->data['distributor_id'] = $distributor_id;
    $this->data['default_wanted'] = $default_wanted;

    $contact = DB::table('contact')->where('distributor_id', $distributor_id)->first();
    $contact = CommonService::objectToArray($contact);
    $contact_type = $contact ? explode(",", $contact['contact_type']) : [Lang::get('其他')];
    $this->data['contact_type'] = $contact_type;

    return view('home.about.about_contact',['data'=>$this->data]);
  }

  public function doContact(Request $request){
    $distributor_id =  htmlspecialchars($request->post('distributor_id'));
    $name = htmlspecialchars($request->post('name'));
    $homephone = htmlspecialchars($request->post('homephone'));
    $phone = htmlspecialchars($request->post('phone'));
    $clientEmail = htmlspecialchars($request->post('email'));
    $wanted = htmlspecialchars($request->post('wanted'));
    $freeTime = htmlspecialchars($request->post('freeTime'));
    $message =  htmlspecialchars($request->post('message'));
    $order_number =  htmlspecialchars($request->post('order_number'));
    $prod_id =  htmlspecialchars($request->post('prod_id'));

    if($name==""){ $this->error(Lang::get('請輸入姓名')); }
    if($clientEmail==""){ $this->error(Lang::get('請輸入信箱')); }
    if($wanted==Lang::get('訂單問題')){ 
      if($order_number==""){ $this->error(Lang::get('請輸入訂單編號')); }
    }
    else if($wanted==Lang::get('商品詢問')){ 
      if($prod_id==""){ $this->error(Lang::get('請輸入商品ID或名稱')); }
    }

    /*處理distributor_id*/
    if(config('control.control_platform')==1){
      if($order_number){ /*依詢問訂單設定distributor_id*/
        $orderform = Db::connection('main_db')->table('orderform')->where('order_number', $order_number)->first();; /*依照訂單編號尋找*/
        $orderform = CommonService::objectToArray($orderform);
        if($orderform){
          $distributor_id = $orderform['distributor_id'];
        }else{
          $distributor_id = 0;
        }
      }else if($prod_id){ /*依詢問商品設定distributor_id*/
        $productinfo = DB::table('productinfo')->orWhere('id', $prod_id)->orWhere('title', $prod_id)->first(); /*依照ID或名稱尋找*/
        $productinfo = CommonService::objectToArray($productinfo);
        if($productinfo){
          $distributor_id = $productinfo['distributor_id'];
        }else{
          $distributor_id = 0;
        }
      }else{ /*依詢post值設定distributor_id*/
        $MemberInstance = new MemberInstance($distributor_id);
        $user_data = $MemberInstance->get_user_data_distributor();
        if(!$user_data){ $distributor_id = 0; }
      }
    }else{
      $distributor_id = 0;
    }

    if (config('extra.shop.google_recaptcha_sitekey')) {
      // _GOOGLE_RECAPTCHA_SEC_KEY 就是 google 給的 Secret Key
      $google_recaptcha_seckey = config('extra.shop.google_recaptcha_seckey');
      if($google_recaptcha_seckey){
        $recaptcha = new ReCaptcha($google_recaptcha_seckey);
        $gRecaptchaResponse = $request->post('recaptcha');
        $remoteIp = $request->server('REMOTE_ADDR');
        $resp = $recaptcha->verify($gRecaptchaResponse, $remoteIp);
        if(!$resp->isSuccess()){
          $this->error(Lang::get('請先證明您不是機器人'));
        }
      }
    }

    $this->DBTextConnecter = DBTextConnecter::withTableName('contact_log');
    try{
      $newData = [
        'distributor_id' => $distributor_id,
        'name' => $name,
        'homephone' => $homephone,
        'phone' => $phone,
        'email' => $clientEmail,
        'type' => $wanted,
        'freeTime' => $freeTime,
        'content' => $message,
        'order_number' => $order_number,
        'prod_id' => $prod_id,
      ];
      // dd($newData);
      $this->DBTextConnecter->setDataArray($newData);
      $this->DBTextConnecter->createTextRow();
    } catch (\Exception $e){
      $this->error(Lang::get('操作失敗'));
    }

    $globalMailData = parent::getMailData();
    $contact_letter = Lang::get('menu.回函成功信消費者');
    $contact_letter = str_replace("{name}", $name, $contact_letter);
    $contact_letter = str_replace("{message}", $message, $contact_letter);
    $mailBody = "
      <html>
        <head></head>
        <body>
        <div>
          ".$contact_letter."
        </div>
        <div>
          ".$globalMailData['system_email']['contact_complete']."
        </div>
        <div style='color:red;'>
          ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
        </div>
        </body>
      </html>
    ";
    // dump($mailBody);
    $mail_return = parent::Mail_Send($mailBody,'client',$clientEmail, Lang::get('回函立成功'));

    $contact_letter_admin = Lang::get('menu.回函成功信管理者');
    $contact_letter_admin = str_replace("{name}", $name, $contact_letter_admin);
    $contact_letter_admin = str_replace("{homephone}", $homephone, $contact_letter_admin);
    $contact_letter_admin = str_replace("{phone}", $phone, $contact_letter_admin);
    $contact_letter_admin = str_replace("{clientEmail}", $clientEmail, $contact_letter_admin);
    $contact_letter_admin = str_replace("{message}", $message, $contact_letter_admin);
    $mailBody = "
      <html>
        <head></head>
        <body>
        <div>
          ".$contact_letter_admin."
        </div>
        <div style='color:red;'>
          ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
        </div>
        </body>
      </html>
    ";
    // dump($mailBody);
    if($distributor_id==0){
      $mail_return = parent::Mail_Send($mailBody,'admin','', Lang::get('新回函提醒'));
    }else{
      $MemberInstance = new MemberInstance($distributor_id);
      $user_data = $MemberInstance->get_user_data_distributor();
      //dd($user_data);
      if($user_data){
        $mail_return = parent::Mail_Send($mailBody,'client',$user_data['email'], Lang::get('新回函提醒'));
      }
    }

    $this->success(Lang::get('操作成功'));
  }
  public function test_mail(Request $request){
    $mail = $request->get('mail') ?? '<EMAIL>';
    dump('mail：'.$mail);
    $mail_return = parent::Mail_Send('test', 'client', $mail, Lang::get('測試回函'));
    dump($mail_return);
  }

  public function about_map(Request $request) {
    $about = DB::table('about_story')->select('mapurl')->find(1);
    $this->data['about'] = CommonService::objectToArray($about);
    return view('home.about.about_map',['data'=>$this->data]);
  }

  public function about_story(Request $request) {
    $about = DB::table('about_story')
                ->select(
                  'image_left_top',
                  'image_right_top',
                  'image_right_bottom',
                  'content'
                )
                ->find(1);
    $this->data['about'] = CommonService::objectToArray($about);
    return view('home.about.about_story',['data'=>$this->data]);
  }
}