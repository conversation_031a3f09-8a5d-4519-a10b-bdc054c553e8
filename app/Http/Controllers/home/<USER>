<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use PHPMailer\PHPMailer\PHPMailer;

use App\Services\pattern\simpleFactory\orderFactory\OrderFactory;

class LinePay extends PublicController
{
  protected $baseURL;
  protected $tableName;
  protected $coupon_tableName;
  protected $Order;

  const PaymentRequestUri = '/v3/payments/request';

  public function __construct(){
    parent::__construct(request()->instance());
    $this->baseURL = config('extra.line_pay.base_url');
    $this->tableName = 'orderform';
    $this->coupon_tableName = 'coupon_pool';
  }

  public function returnurl(Request $request, $id, $total, $order_number)
  {
    if (!empty(request()->get('orderId')) == false || !empty(request()->get('transactionId')) == false) {
      $this->error(Lang::get('發生錯誤'), url('Orderform/orderform_c').'?id='.$id);
    }

    $transactionId = request()->get('transactionId');
    $uri = "/v3/payments/{$transactionId}/confirm";

    $requestBody = [
      'amount' => intval($total),
      'currency' => 'TWD',
    ];

    $nonce = round(microtime(true) * 1000);
    $authorization = base64_encode(hash_hmac('sha256', config('extra.line_pay.channel_secret') . $uri . json_encode($requestBody) . $nonce, config('extra.line_pay.channel_secret'), true));
    $requestHeaders = [
      'Content-Type: application/json',
      'X-LINE-ChannelId: ' . config('extra.line_pay.channel_secret'),
      'X-LINE-Authorization-Nonce: ' . $nonce,
      'X-LINE-Authorization: ' . $authorization,
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
      CURLOPT_URL => $this->baseURL . $uri,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_CUSTOMREQUEST => 'POST',
      CURLOPT_POST => true,
      CURLOPT_POSTFIELDS => json_encode($requestBody),
      CURLOPT_HTTPHEADER => $requestHeaders,
    ));
    $postdata = curl_exec($curl);
    curl_close($curl);

    DB::connection('main_db')->table('orderform')->where('id', $id)->update([
      'linepay_return_data' => $postdata
    ]);

    $postdata = json_decode($postdata);
    if ($postdata->returnCode == "0000") {
      $this->Order = OrderFactory::createOrder($id, $this->tableName, $this->coupon_tableName);
      $this->Order->setReceiptsState(1);
    }

    $this->redirect(url('Orderform/orderform_success?id='.$order_number));
  }

  public function auth($order_data, $mailFromName)
  {
    $uri = '/v3/payments/request';

    $requestBody = [
      'currency' => 'TWD',
      'amount' => intval($order_data['total']),
      'orderId' => $order_data['order_number'],
      'redirectUrls' => [
        'confirmUrl' => url('LinePay/returnurl', [
          'mode' => 'test',
          'id' => $order_data['id'],
          'total' => $order_data['total'],
          'order_number' => $order_data['order_number']
        ], '', true),
        'cancelUrl' => url('orderform/orderform_c').'?id='.$order_data['order_number'],
      ],
      'packages' => [
        [
          'id' => base64_encode(session()->get('user.id') . time()),
          'amount' => intval($order_data['total']),
          'name' => $mailFromName . " " . Lang::get('訂單'),
          'products' => [],
        ]
      ],
    ];

    if (isset($order_data['product']) == false) {
      $order_data['product'] = json_decode(DB::connection('main_db')->table('orderform')->where('id', $order_data['id'])->first()->product, true);
    }

    foreach ($order_data['product'] as $arr) {
      $requestBody['packages'][0]['products'][] = [
        'name' => $arr['name'],
        'quantity' => $arr['num'],
        'price' => intval($arr['price']),
      ];
    }

    $nonce = round(microtime(true) * 1000);
    $authorization = base64_encode(hash_hmac('sha256', config('extra.line_pay.channel_secret') . $uri . json_encode($requestBody) . $nonce, config('extra.line_pay.channel_secret'), true));
    $requestHeaders = [
      'Content-Type: application/json',
      'X-LINE-ChannelId: ' . config('extra.line_pay.channel_secret'),
      'X-LINE-Authorization-Nonce: ' . $nonce,
      'X-LINE-Authorization: ' . $authorization,
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
      CURLOPT_URL => $this->baseURL . $uri,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_CUSTOMREQUEST => 'POST',
      CURLOPT_POST => true,
      CURLOPT_POSTFIELDS => json_encode($requestBody),
      CURLOPT_HTTPHEADER => $requestHeaders,
    ));
    $response = json_decode(curl_exec($curl));
    curl_close($curl);

    return $response;
  }

  public function check_order()
  {
    $id = request()->get('id');
    $uri = '/v3/payments';

    $data = DB::connection('main_db')->table('orderform')->where('id', $id)->get();
    $line_return_data = json_decode($data[0]['linepay_return_data'], true);
    $params = [
      'transactionId' => intval($line_return_data['info']['transactionId']),
      'orderId' => $line_return_data['info']['orderId'],
    ];

    $nonce = round(microtime(true) * 1000);

    $headers = [
      'Content-Type: application/json',
      'X-LINE-ChannelId: ' . config('extra.line_pay.channel_secret'),
      'X-LINE-Authorization-Nonce: ' . $nonce,
      'X-LINE-Authorization: ' . base64_encode(hash_hmac('sha256', config('extra.line_pay.channel_secret') . $uri . http_build_query($params) . $nonce, config('extra.line_pay.channel_secret'), true)),
    ];

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $this->baseURL . $uri . '?' . http_build_query($params));
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($curl);
    curl_close($curl);

    return json_decode($response);
  }
}
