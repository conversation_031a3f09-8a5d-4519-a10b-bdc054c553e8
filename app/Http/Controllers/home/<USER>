<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class Buyform extends PublicController{
  public function __construct(){
    parent::__construct(request());
    $this->data['mebermenu_active'] = 'buyform';

    $consent = DB::table('consent')->where("id",1)->first();
    $this->data['consent_other'] = $consent->other;

    if($this->user["id"]==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};
  }
  
  public function buyform() {
    $user_id = session()->get('user.id');
    $contact_log = DB::table('contact_find_prod')
                      ->where('user_id', $user_id)
                      ->get();
    $contact_log = CommonService::objectToArray($contact_log);

    foreach($contact_log as $ck => $cv){
      $contact_log[$ck]['ask'] = json_decode($cv['ask'],true);
    }
    // dd($contact_log);
    $this->data['contact_log'] = $contact_log;

    return view('home.buyform.buyform',['data'=> $this->data]);
  }
}