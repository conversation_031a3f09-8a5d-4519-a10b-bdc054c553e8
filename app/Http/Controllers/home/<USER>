<?php
namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class Activity extends PublicController
{
  public function activity(Request $request)
  {
    $activity = DB::table('activity')
                  ->select('id', 'title', 'pic', 'content', 'url')
                  ->where('online',1)
                  ->orderBy('orders','asc')
                  ->orderBy('time', 'desc')
                  ->paginate(10);
    $this->data['activity'] = $activity;
    return view('home.activity.activity',['data'=>$this->data]);
  }
}

