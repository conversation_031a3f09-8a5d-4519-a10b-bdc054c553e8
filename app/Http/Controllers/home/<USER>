<?php
namespace App\Http\Controllers\home;

use Illuminate\Support\Facades\DB;
use Firebase\JWT\JWT;

class Linelogin extends PublicController
{
  public function callBack() {
    if(empty(request()->get('code'))){ $this->error('授權失敗', url('Index/index')); }
    $this->data['code'] = request()->get('code');
    return view('home.linelogin.callBack',['data'=>$this->data]);
  }

  public function open() {
    if(empty(request()->get('code'))){ $this->error('授權失敗', url('Index/index')); }
    $this->data['code'] = request()->get('code');
    return view('home.linelogin.open',['data'=>$this->data]);
  }

  /*-----------------------------------*/
  /*LIFF APP-----------*/
  public function consumption_exchange(){
    $this->data['LIFF_ID'] = config('extra.social_media.LIFF_ID');

    if(session()->get('user.line_id')){
      $this->redirect( url('Consumption/exchange') );
    }else{
      $this->data['redirect_url'] = url('Consumption/exchange');
      return view('home.linelogin.liff_app_auth_redirect',['data'=>$this->data]);
    }
  }
  public function consumption_scratch_history(){
    $this->data['LIFF_ID'] = config('extra.social_media.LIFF_ID');

    if(session()->get('user.line_id')){
      $this->redirect( url('Consumption/scratch_history') );
    }else{
      $this->data['redirect_url'] = url('Consumption/scratch_history');
      return view('home.linelogin.liff_app_auth_redirect',['data'=>$this->data]);
    }
  }
}
