<?php
namespace App\Http\Controllers\home;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\simpleFactory\discountFactory\directcouponDiscount;

class Coupondirect extends Controller
{
  public function get_discount(){
    $user_id = session('user.id');
    if ($user_id === null) {
      return ['status' => 0, 'discount' => 0, 'msg' => Lang::get('請先登入會員'), 'user_code' => $user_code];
    }

    $user_code = urldecode(request()->post('user_code'));
    if(empty($user_code) == true) {
      return ['status' => 0, 'discount' => 0, 'msg' => Lang::get('請輸入優惠券代碼'), 'user_code' => $user_code];
    }

    return directcouponDiscount::get_discount($user_code);
  }
}