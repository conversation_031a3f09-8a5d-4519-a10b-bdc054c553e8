<?php
namespace App\Http\Controllers\home;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use App\Services\DBtool\DBTextConnecter;

//Photonic Class
use App\Services\pattern\MemberInstance;

class Consumption extends PublicController
{

  public function __construct()
  {
    parent::__construct(request());

    if($this->user["id"]==0){ $this->error(Lang::get('請先登入會員'), url('Login/login'));};
  }

  /*輸入付款頁面*/
  public function create_pay() {
    $this->data['mebermenu_active'] = 'no';
    return view('home.consumption.create_pay',['data'=>$this->data]);;
  }
  /*建立付款資料*/
  public function do_create_pay(){
    if(empty(request()->post('price'))){
      $this->error(Lang::get('請輸入金額'));
    }
    if(request()->post('price')==0){
      $this->error(Lang::get('請輸入金額'));
    }

    $data = [
      'user_id' => $this->user['id'],
      'distributor_id' => request()->post('distributor_id') ?? 0,
      'price' => request()->post('price'),
      'datetime' => time(),
      'audit'	=> 0,
    ];
    // dump($data);exit;
    
    $id = DB::table('consumption_pay_record')->insertGetId($data);
    $this->redirect( url('Consumption/pay_success').'?pay_record_id='.$id);
  }
  
  /*付款成功等候審核頁面(含掃code付款的刮刮樂畫面)*/
  public function pay_success(){
    $pay_record_id = request()->get('pay_record_id');
    $consumption_pay_record = DB::table('consumption_pay_record')->find($pay_record_id);
    if( empty($consumption_pay_record)==true ){ $this->error(Lang::get('連結有誤'), '', '', -1); }

    if($consumption_pay_record['audit']==0){
      $this->success(Lang::get('請待管理人員審核'), '', '', 3);
    }


    /*顯示還未刮完的刮刮樂*/
    $draw_records = DB::table('consumption_draw_record')
              ->where('user_id', $this->user['id'])
              ->where('pay_record_id', $pay_record_id)
              ->where('show', 0);
    /*排除取消的訂單*/
    $cancel_order_ids_sql = $this->get_not_in_cancel_order_sql();
    if($cancel_order_ids_sql){ /*排除取消訂單的刮刮樂*/
      $draw_records = $draw_records->whereRaw($cancel_order_ids_sql);
    }
    
    $draw_records = $draw_records->get();
    
    if(count($draw_records)==0){
      $this->success(Lang::get('付款已通過，請關閉此畫面'), '', '', -1);
    }
    $this->data['draw_records'] = $draw_records;

    return view('home.consumption.pay_success',['data'=>$this->data]);
  }
  /*刮刮樂歷史紀錄頁面*/
  public function scratch_history(){
    $this->data['mebermenu_active'] = 'scratch_history';

    /*排除取消的訂單*/
    $cancel_order_ids_sql = $this->get_not_in_cancel_order_sql();

    /*找出該會員的刮刮樂紀錄*/
    $consumption_draw_record = DB::table('consumption_draw_record')->where('user_id', $this->user['id']);
    if($cancel_order_ids_sql){ /*排除取消訂單的刮刮樂*/
      $consumption_draw_record = $consumption_draw_record->whereRaw($cancel_order_ids_sql);
    }
    $consumption_draw_record = $consumption_draw_record->orderBy('id','desc')->get();
    $this->data['draw_records'] = $consumption_draw_record;
    // dump($consumption_draw_record);

    return view('home.consumption.scratch_history',['data'=>$this->data]);
  }
  /*單一刮刮樂畫面*/
  public function scratch(){
    $draw_record_id = request()->get('draw_record_id');
    /*找出目標刮刮樂*/
    $draw_record = DB::table('consumption_draw_record')->where('user_id', $this->user['id'])->find($draw_record_id);
    // dump($draw_record);
    $this->data['draw_record'] = $draw_record;

    if($draw_record){
      return view('home.consumption.scratch',['data'=>$this->data]);
    }
  }
  /*修改刮刮樂紀錄*/
  public function draw_result_save(){
    $draw_record_id = !empty(request()->post('draw_record_id')) ? request()->post('draw_record_id') : "0";
    $consumption_draw_record = DB::table('consumption_draw_record')->find($draw_record_id);
    if(!$consumption_draw_record){ $this->error(Lang::get('資料有誤')); }
    if($consumption_draw_record['user_id'] != $this->user['id']){ $this->error(Lang::get('操作失敗')); }

    $column = !empty(request()->post('column')) ? request()->post('column') : "";
    if($column==""){ $this->error(Lang::get('資料不完整')); }

    $data = [];
    if($column=="show"){ /*刮完*/
      $data[$column] = 1;
    }
    else if($column=="ex_date"){ /*兌獎*/
      if($consumption_draw_record['ex_date']!=""){ $this->error(Lang::get('無法重複領取')); }
      $data[$column] = time();
    }

    if($data){
      DB::table('consumption_draw_record')->where('id', $draw_record_id)->update($data);
    }

    return ['code'=>1,'msg'=>Lang::get('操作成功'),'url'=>'/'];
  }

  /*累績消費兌換*/
  public function exchange(){
    $this->data['LIFF_ID'] = config('extra.social_media.LIFF_ID');
    $this->data['mebermenu_active'] = 'consumption_exchange';

    /*取得累積消費總金額*/
    $MemberInstance = new MemberInstance($this->user['id']);
    $total_dollar = $MemberInstance->get_user_order_data([ 'status'=> 'All', 'method'=>'sum' ]);
    $this->data['total_dollar'] = $total_dollar;

    $datas = DB::table('consumption_exchange')->orderBy('price','asc')->orderBy('id','desc')->get();
    $datas = CommonService::objectToArray($datas);
    foreach ($datas as $key => $value) {
      $exchange_record = DB::table('consumption_exchange_record')->where('exchange_id', $value['id'])->where('user_id', $this->user['id'])->get();
      $exchange_record = CommonService::objectToArray($exchange_record);
      
      if($exchange_record){

        $datas[$key]['name'] = $exchange_record['name'];
        $datas[$key]['pic'] = $exchange_record['pic'];
        $datas[$key]['ex_date'] = date('Y/m/d H:i', $exchange_record['ex_date']);
      }else{
        $datas[$key]['ex_date'] = "";
      }
      
      if($value['online']==0 && empty($exchange_record)){ /*如果 設定關閉 且 沒有領取紀錄*/
        $datas[$key] = ""; /*不顯示此累積消費兌換*/
      }
    }
    // dump($datas);
    $this->data['datas'] = $datas;
    return view('home.consumption.exchange',['data'=>$this->data]);
  
  }
  /*兌換累積消費贈品*/
  public function get_exchange_gift(){
    $exchange_id = !empty(request()->post('exchange_id')) ? request()->post('exchange_id') : "0";
    $exchange = DB::table('consumption_exchange')->where('online', 1)->find($exchange_id);
    if(!$exchange){ $this->error(Lang::get('資料有誤')); }

    /*取得累積消費金額*/
    $MemberInstance = new MemberInstance($this->user['id']);
    $total_dollar = $MemberInstance->get_user_order_data([ 'status'=> 'All', 'method'=>'sum' ]);
    if($total_dollar < $exchange['price']){ $this->error(Lang::get('資格不符')); }

    /*檢查領取紀錄*/
    $exchange_record = DB::table('consumption_exchange_record')
              ->where('user_id', $this->user['id'])
              ->where('exchange_id', $exchange_id)
              ->get();
    if($exchange_record){ $this->error(Lang::get('無法重複領取')); }

    $data = [
      'user_id' => $this->user['id'],
      'exchange_id' => $exchange['id'],
      'pic' => $exchange['pic'],
      'name' => $exchange['name'],
      'ex_date' => time(),
    ];
    DB::table('consumption_exchange_record')->insert($data);

    return ['code'=>1,'msg'=>Lang::get('操作成功'),'url'=>'/'];
  }


  private function get_not_in_cancel_order_sql(){
    /*找出該會員的取消訂單*/
    $MemberInstance = new MemberInstance($this->user['id']);
    $cancel_orders = $MemberInstance->get_user_order_data(['status'=>'All_no', 'method'=>'select']);
    $cancel_orders = CommonService::objectToArray($cancel_orders);
    // dump($cancel_orders);
    $cancel_order_ids = [];
    foreach ($cancel_orders as $value) {
      array_push($cancel_order_ids, $value['id']);
    }
    $cancel_order_ids_sql = "";
    if($cancel_order_ids){
      $cancel_order_ids_sql = 'order_id not in ('. implode(',', $cancel_order_ids) .')';
    }

    return $cancel_order_ids_sql;
  }
}