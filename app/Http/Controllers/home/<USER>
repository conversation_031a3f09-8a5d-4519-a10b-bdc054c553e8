<?php

namespace App\Http\Controllers\home;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\BonusSettingHelper;
use App\Services\pattern\BonusHelper;

class Dividend extends PublicController
{
    private $MemberInstance; //會員實例

    public function __construct()
    {
        $request = request();
        parent::__construct($request);
        if ($this->user['id'] == 0) {
            $this->error(Lang::get('請先登入會員'), url('Login/login'));
        };

        $this->MemberInstance = new MemberInstance(session()->get('user.id'));
        $userD = $this->MemberInstance->get_user_data($addr_change = "split");
        $this->data['userD'] = $userD;
    }

    public function my_status(Request $request)
    {
        $this->data['mebermenu_active'] = 'my_status';

        $currency = $request->get('currency') ?? config('extra.shop.dollar');
        $exchange_reate = config('extra.skychakra.exchange_rate_set')[$currency] ?? 1;
        $show_dollar = config('extra.skychakra.show_dollar_set')[$currency] ?? 'USD$';
        $this->data['currency_selected'] = $currency;
        $this->data['exchange_reate'] = $exchange_reate;
        $this->data['show_dollar'] = $show_dollar;

        $bonus_setting = BonusSettingHelper::get_bonus_setting();
        $this->data['pi_value'] = $bonus_setting['pi_value'];
        $arr_partner_levels = MemberInstance::get_partner_levels([], true)['db_data'];
        $this->data['arr_partner_levels'] = $arr_partner_levels;
        $this->data['partner_level_now'] = $arr_partner_levels[$this->data['userD']['partner_level_id']] ?? [];
        /*取得自己的下一級合夥等級*/
        $type_key_rank = array_keys($arr_partner_levels);
        $cur_rank = array_search($this->data['userD']['partner_level_id'], $type_key_rank);
        $cur_rank = $cur_rank !== false ? $cur_rank : -1;
        if ($cur_rank == count($type_key_rank) - 1) {
            $partner_level_next = [];
        } else {
            $partner_level_next = $arr_partner_levels[$type_key_rank[$cur_rank + 1]];
        }
        $this->data['partner_level_next'] = $partner_level_next;
        $this->data['partner_level_first'] = $arr_partner_levels[$type_key_rank[0]];
        $this->data['invest_num_added']      = $this->increasing_limit_record($this->data['userD']['id'], 1);
        $this->data['consumption_num_added'] = $this->increasing_limit_record($this->data['userD']['id'], 2);
        $this->data['other_num_added']       = $this->increasing_limit_record($this->data['userD']['id'], 3);

        return view('home.dividend.my_status', ['data' => $this->data]);
    }

    private function increasing_limit_record($user, $type)
    {
        $increasing_limit_record = Db::connection('main_db')->table('increasing_limit_record')
            ->where('user_id', $user)
            ->where('num', '<', 0)
            ->where('type', 2)
            ->whereIn('limit_type', [$type])
            ->sum('num');

        return $increasing_limit_record * -1;
    }

    public function set_auto_partner(Request $request)
    {
        $auto_partner = $request->post('auto_partner');
        $MemberInstance = new MemberInstance(session()->get('user.id'));
        $returnData = $MemberInstance->update_user_data(['auto_partner' => $auto_partner]);
        if ($returnData['code']) {
            $this->success($returnData['msg']);
        } else {
            $this->error($returnData['msg']);
        }
    }

    public function cash(Request $request)
    {
        $this->data['mebermenu_active'] = 'cash';

        $bonus_setting = BonusSettingHelper::get_bonus_setting();
        $this->data['charge_tax_txt'] = ($bonus_setting['charge_tax'] * 100) . '%';
        $this->data['charge_pool_txt'] = ($bonus_setting['charge_pool'] * 100) . '%';

        #提現紀錄
        $cash_record = DB::connection('main_db')
            ->table('points_to_cash_record')
            ->where([
                'user_id' => $this->user['id'],
            ])
            ->orderBy('id', 'desc');
        $date_s = '';
        if (request()->get('date_s')) {
            $date_s = request()->get('date_s');
            $cash_record = $cash_record->where('time_create', '>=', strtotime($date_s));
        }
        $date_e = '';
        if (request()->get('date_e')) {
            $date_e = request()->get('date_e');
            $cash_record = $cash_record->where('time_create', '<=', strtotime($date_e . ' +1Day'));
        }
        $cash_record = $cash_record->paginate(20)
            ->appends([
                'date_s' => $date_s,
                'date_e' => $date_e,
            ]);
        $this->data['cash_record'] = $cash_record;

        return view('home.dividend.cash', ['data' => $this->data]);
    }

    /**
     * 積分提現
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create_record(Request $request)
    {
        $bonus_setting = BonusSettingHelper::get_bonus_setting();
        if ($bonus_setting['member_point_to_cash'] != 1) {
            $this->error(Lang::get('功能未開放使用'));
        }

        $currency = $request->get('currency') ?? config('extra.shop.dollar');
        $num = $request->get('num');
        if ($num <= 0) {
            $this->error(Lang::get('請輸入正整數'));
        }
        if ($num != round($num)) {
            $this->error(Lang::get('請輸入整數'));
        }
        if ($num > $this->data['userD']['point']) {
            $this->error(Lang::get('超過持有現金積分上限'));
        }

        try {
            $msg = '積分提現';
            $BonusHelper = new BonusHelper();
            /*現金積分轉現金*/
            $BonusHelper->point_to_cash($this->user['id'], $currency, $num, $msg);
            /*實際發送*/
            $BonusHelper->send_by_cal($msg);
        } catch (\Throwable $th) {
            // throw $th;
            $this->error($th->getMessage());
        }
        $this->success('操作成功');
    }
}
