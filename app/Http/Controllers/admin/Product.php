<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;
use App\Services\pattern\MemberInstance;

class Product extends MainController
{
    private $resTableName;
    private $DBTextConnecter;
    private $DBFileConnecter;

    public function __construct()
    {
        parent::__construct();
        $this->resTableName = 'product';
        $this->DBTextConnecter = DBTextConnecter::withTableName('product');
        $this->DBFileConnecter = DBFileConnecter::withTableName('product');
    }

    public function index(Request $request)
    {
        $id = $request->get('id');
        if (!parent::check_controll($this->DBTextConnecter->getTableName(), $id)) {
            $this->error('您無法操作此項目');
        }

        $product = Db::table('product')->find($id);
        $product = CommonService::objectToArray($product);

        if (!$product) {
            $this->redirect(url('admin/index'));
        }
        if ($product['index_adv01_pic']) {
            $product['index_adv01_pic'] = __UPLOAD__ . $product['index_adv01_pic'];
        }
        if ($product['index_adv02_pic']) {
            $product['index_adv02_pic'] = __UPLOAD__ . $product['index_adv02_pic'];
        }
        if ($product['index_adv04_pic']) {
            $product['index_adv04_pic'] = __UPLOAD__ . $product['index_adv04_pic'];
        }
        if ($product['index_adv06_pic']) {
            $product['index_adv06_pic'] = __UPLOAD__ . $product['index_adv06_pic'];
        }
        if ($product['index_adv03_pic']) {
            $product['index_adv03_pic'] = __UPLOAD__ . $product['index_adv03_pic'];
        }
        if ($product['index_adv05_pic']) {
            $product['index_adv05_pic'] = __UPLOAD__ . $product['index_adv05_pic'];
        }
        if ($product['index_adv07_pic']) {
            $product['index_adv07_pic'] = __UPLOAD__ . $product['index_adv07_pic'];
        }
        if ($product['inner_adv01_pic']) {
            $product['inner_adv01_pic'] = __UPLOAD__ . $product['inner_adv01_pic'];
        }
        if ($product['inner_adv02_pic']) {
            $product['inner_adv02_pic'] = __UPLOAD__ . $product['inner_adv02_pic'];
        }
        if ($product['pic']) {
            $product['pic'] = __UPLOAD__ . $product['pic'];
        }
        if ($product['pic_icon']) {
            $product['pic_icon'] = __UPLOAD__ . $product['pic_icon'];
        }
        $this->data['product'] = $product;
        // dump($product);exit();
        return view('admin.product.adgroup', ['data' => $this->data]);
    }

    public function update(Request $request)
    {
        $updateData = $request->post();

        if (!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])) {
            $this->error('您無法操作此項目');
        }

        // 自動更新排序
        if (isset($updateData['order_id'])) {
            $table = $this->resTableName;
            $column = 'order_id';
            $order_num = $updateData['order_id'];
            $primary_key = 'id';
            $primary_value = $updateData['id'];
            parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $this->distributor_id_where_sql);
            unset($updateData['order_id']);
        }

        //the post data name show-btn is for online,
        //but online is change by AJAX,
        //so we don't need the post data that name show-btn
        unset($updateData['show-btn']);
        $picNameList = [
            'pic',
            'index_adv01_pic',
            'index_adv02_pic',
            'index_adv03_pic',
            'index_adv04_pic',
            'index_adv05_pic',
            'index_adv06_pic',
            'index_adv07_pic',
            'inner_adv01_pic',
            'inner_adv02_pic',
            'pic_icon'
        ];
        $picSizeList = [
            ['width' => 430, 'height' => 150],
            ['width' => 400, 'height' => 730],
            ['width' => 370, 'height' => 370],
            ['width' => 800, 'height' => 640],
            ['width' => 370, 'height' => 370],
            ['width' => 800, 'height' => 640],
            ['width' => 370, 'height' => 370],
            ['width' => 800, 'height' => 640],
            ['width' => 880, 'height' => 128],
            ['width' => 880, 'height' => 280],
            ['width' => 50, 'height' => 50]
        ];
        $file = $request->file();
        try {
            for ($i = 0; $i < count($picNameList); $i++) {
                if ($updateData['del_' . $picNameList[$i]]) {
                    $updateData[$picNameList[$i]] = '';
                    unset($file[$picNameList[$i]]);
                }
                unset($updateData['del_' . $picNameList[$i]]);
            }
        } catch (\Exception $e) {
            // $this->dumpException($e);
        }
        unset($updateData['_token']);
        try {
            $this->DBTextConnecter->setDataArray($updateData);
            $this->DBTextConnecter->upTextRow();
            if (!empty($file)) {
                if (count($file) > 0) {
                    $this->DBFileConnecter->setFileArray($file);
                    $this->DBFileConnecter->setPrivateKeyId($updateData['id']);
                    $this->DBFileConnecter->upFileRow();
                }
            }
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
        $this->success('更新成功');
    }
    public function cellCtrl(Request $request)
    {
        try {
            $updateData = $request->post();

            if (!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])) {
                throw new \Exception("您無法編輯此項目", 1);
            }

            // 自動更新排序
            if (isset($updateData['order_id'])) {
                $table = $this->resTableName;
                $column = 'order_id';
                $order_num = $updateData['order_id'];
                $primary_key = 'id';
                $primary_value = $updateData['id'];
                parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $this->distributor_id_where_sql);
                unset($updateData['order_id']);
            }

            $this->DBTextConnecter->setDataArray($updateData);
            $this->DBTextConnecter->upTextRow();
            $outputData = [
                'status' => true,
                'message' => 'success'
            ];
        } catch (\Exception $e) {
            $outputData = [
                'status' => false,
                'message' => $e->getMessage()
            ];
            return $outputData;
        }
        return $outputData;
    }

    public function delete(Request $request)
    {
        $id = $request->get('id');
        if (!parent::check_controll($this->DBTextConnecter->getTableName(), $id)) {
            $this->error('您無法操作此項目');
        }

        $produc_in_layer = Db::table('productinfo')->select('id')->whereRaw("final_array like '%" . '"prev_id":"' . $id . '","branch_id":"0","parent_id":"0"' . "%'")->get();
        $layer_in_layer = Db::table('typeinfo')->select('id')->where('parent_id', $id)->get();

        if (sizeof($produc_in_layer)) {
            $this->error('此分館還有商品，請先清光');
        } else if (sizeof($layer_in_layer)) {
            $this->error('此分館下還有分類，請先清光');
        }
        Db::table('product')->delete($id);
        $this->success('刪除成功');
    }

    /*AJAX*/
    public function create(Request $request)
    {
        try {
            $updateData = $request->post();

            if ($this->admin_type == 'distribution') {
                $updateData['distributor_id'] = session()->get($this->admin_type)['id'];
            }

            $this->DBTextConnecter->setDataArray($updateData);
            $id = $this->DBTextConnecter->createTextRow();

            // 自動更新排序
            $table = $this->resTableName;
            $column = 'order_id';
            $order_num = $updateData['order_id'] ? $updateData['order_id'] : 0;
            $primary_key = 'id';
            $primary_value = $id;
            parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $this->distributor_id_where_sql);

            $outputData = [
                'status' => true,
                'message' => 'success'
            ];
        } catch (\Exception $e) {
            $outputData = [
                'status' => false,
                'message' => $e->getMessage()
            ];
            return $outputData;
        }
        return $outputData;
    }

    private function get_distributor_id_where(Request $request)
    {
        $distributor_id_where = '';
        $distributor_id = $request->post('distributor_id');
        if ($this->admin_type == 'distribution') { /*供應商限看自己的*/
            $distributor_id_where = $this->distributor_id_where_sql;
        } else if ($this->admin_type == 'admin') { /*若為管理者，可看指定供應商*/
            if (isset($distributor_id)) {
                if ($distributor_id !== '' && $distributor_id !== '-1') {
                    $distributor_id_where = 'distributor_id="' . $distributor_id . '"';
                }
            } else {
                $distributor_id_where = $this->distributor_id_where_sql;
            }
        }
        return $distributor_id_where;
    }
    public function getList(Request $request)
    {
        $langId = $request->post('langId') ?? config('LangId');
        $db_connect = CommonService::get_db_connect_by_lang_id($langId);
        // dd($db_connect);
        $product = DB::connection($db_connect)->table('product')->select('id', 'title');

        $distributor_id_where = $this->get_distributor_id_where($request);
        if ($distributor_id_where) {
            $product = $product->whereRaw($distributor_id_where);
        }
        $product = $product->orderByRaw('order_id asc, id desc')->get();
        $product = CommonService::objectToArray($product);

        try {
            $product = array_map(
                function ($arrayItem) use ($db_connect) {
                    $arrayItem['url'] = url('Product/index', ['id' => $arrayItem['id']]);
                    $arrayItem['all_count'] =  Db::connection($db_connect)->table('productinfo')
                        ->whereRaw($this->distributor_id_where_sql)
                        ->whereRaw("final_array like '%\"prev_id\": \"" . $arrayItem['id'] . "\",%'")
                        ->count();
                    return $arrayItem;
                },
                $product
            );
            $outputData = [
                'status' => true,
                'message' => $product
            ];
        } catch (\Exception $e) {
            $outputData = [
                'status' => false,
                'message' => $e->getMessage()
            ];
        }
        return $outputData;
    }
    public function getCate(Request $request)
    {
        $langId = $request->post('langId') ?? config('langId');
        $db_connect = CommonService::get_db_connect_by_lang_id($langId);
        // dd($db_connect);

        $distributor_id_where = $this->get_distributor_id_where($request);

        $id = $request->post('seriesId');
        $cate = Db::connection($db_connect)->table('typeinfo')
            ->whereRaw("parent_id = '" . $id . "' AND branch_id = '0'");
        if ($distributor_id_where) {
            $cate = $cate->whereRaw($distributor_id_where);
        }
        $cate = $cate->orderByRaw('order_id asc')->get();
        if (empty($cate)) {
            $cate = Db::connection($db_connect)->table('typeinfo')
                ->whereRaw("branch_id = '" . $id . "'");
            if ($distributor_id_where) {
                $cate = $cate->whereRaw($distributor_id_where);
            }
            $cate = $cate->orderByRaw('order_id asc')->get();
        }
        if (empty($cate)) {
            $data = ['seriesId' => $id, 'cate' => $cate, 'getCateProd' => 1];
        } else {
            $data = ['seriesId' => $id, 'cate' => $cate];
        }
        return $data;
    }
    public function getCate2(Request $request)
    {
        $langId = $request->post('langId') ?? config('langId');
        $db_connect = CommonService::get_db_connect_by_lang_id($langId);
        // dump($db_connect);exit();

        $id = $request->post('seriesId');
        $cate = Db::connection($db_connect)->table('typeinfo')->whereRaw("parent_id = '" . $id . "'");

        $distributor_id_where = $this->get_distributor_id_where($request);
        if ($distributor_id_where) {
            $cate = $cate->whereRaw($distributor_id_where);
        }
        $cate = $cate->get();
        $cate = CommonService::objectToArray($cate);

        if (empty($cate))
            $data = ['seriesId' => $id, 'cate' => $cate, 'getCateProd' => 1];
        else
            $data = ['seriesId' => $id, 'cate' => $cate];
        return $data;
    }
    // 活動、折扣取得商品
    public function getCateProd(Request $request)
    {
        $langId = $request->post('langId') ?? config('langId');
        $db_connect = CommonService::get_db_connect_by_lang_id($langId);
        // dump($db_connect);exit();

        $id = $request->post('cateId');
        $first = $request->post('first');
        $category_type = $request->post('category_type');
        if ($first) {
            //是第一階
            $productinfo = Db::connection($db_connect)->table('productinfo as pi')
                ->leftJoin('act_product as ap', 'pi.id', 'ap.prod_id');
            if ($id) {
                $productinfo = $productinfo->whereRaw("final_array like '%\"prev_id\":\"" . $id . "\"%\"parent_id\":\"0\"%'");
            }
            if ($category_type !== null) {
                $productinfo = $productinfo->where('pi.category_type', $category_type);
            }
            $productinfo = $productinfo->whereRaw('ap.act_prod_id is null')
                ->whereRaw($this->distributor_id_where_sql)
                ->orderByRaw('pi.order_id, pi.id desc')->get();
        } else {
            $productinfo = Db::connection($db_connect)->table('productinfo as pi')
                ->leftJoin('act_product as ap', 'pi.id', 'ap.prod_id');
            if ($id) {
                $productinfo = $productinfo->whereRaw("final_array like '%\"parent_id\":\"" . $id . "\"%'");
            }
            if ($category_type !== null) {
                $productinfo = $productinfo->where('pi.category_type', $category_type);
            }
            $productinfo = $productinfo->whereRaw('ap.act_prod_id is null')
                ->whereRaw($this->distributor_id_where_sql)
                ->orderByRaw('pi.order_id, pi.id desc')->get();
        }

        if (empty($productinfo)) {
            $productinfo = Db::table('productinfo as pi')
                ->leftJoin('act_product as ap', 'pi.id', 'ap.prod_id');
            if ($id) {
                $productinfo = $productinfo->whereRaw("final_array like '%\"branch_id\": \"" . $id . "\"%' ");
            }
            $productinfo = $productinfo->whereRaw('ap.act_prod_id is null')
                ->whereRaw($this->distributor_id_where_sql)
                ->orderByRaw('pi.order_id, pi.id desc')->get();
        }
        $productinfo = CommonService::objectToArray($productinfo);
        foreach ($productinfo as $k => $v) {
            $pic1 = json_decode($v['pic'], true);
            if ($pic1 == null) {
                $productinfo[$k]['pic1'] = "";
            } else {
                $productinfo[$k]['pic1'] = $pic1[0];
            }
        }

        //echo Db::connection($db_connect)->table('productinfo')->toSql();
        $data = ['cateId' => $id, 'productinfo' => $productinfo];
        return $data;
    }

    //批量上傳商品
    public function import(Request $request)
    {
        $prev = $request->get("searchPrev");
        $branch = $request->get("searchBranch");

        $files = $request->file("file");

        // ??????
        $type = explode(".", $_FILES['file']['name']);

        if (!$type[1] == 'xls') {
            $this->error("格式錯誤，請上傳Excel檔");
        }
        //儲存檔案
        $info = $files->move(ROOT_PATH . 'public' . DS . 'uploads' . DS . 'excel');
        //檔案路徑
        $filename = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'excel' . DS . $info->getFilename();

        $PHPReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader("Xls");
        $PHPExcel = $PHPReader->load($filename);
        $sheet = $PHPExcel->getSheet(0);
        $allRow = $sheet->getHighestRow(); //取得最大的行號
        $allColumn = $sheet->getHighestColumn(); //取得最大的列號

        if ($allRow > 30000) {
            $this->error('匯入資料超過3萬筆，請分批操作');
        }

        $save_data = [];
        $error_data = [];
        for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
            $data['title'] = trim($PHPExcel->getActiveSheet()->getCell("A" . $currentRow)->getCalculatedValue());
            $data['content'] = trim($PHPExcel->getActiveSheet()->getCell("B" . $currentRow)->getCalculatedValue());
            $data['ISBN'] = trim($PHPExcel->getActiveSheet()->getCell("C" . $currentRow)->getCalculatedValue());
            $data['cost'] = trim($PHPExcel->getActiveSheet()->getCell("D" . $currentRow)->getCalculatedValue());
            $returnData = MemberInstance::arrange_data_to_db_format($data);
            // 檢查無誤，紀錄要儲存的資料
            array_push($save_data, $returnData['data']);
        }

        if ($error_data) { /*有錯誤資料*/
            $error_data = implode("<br>", $error_data);
            $this->error('匯入失敗', $url = null, $error_data, $wait = -1);
        }

        $product = new Product;
        foreach ($save_data as $key => $value) {
            $item = $product->get_one_product($value['ISBN']);
            if ($item != null) { /*商品已存在，視為編輯*/
                $product->update_item_data($value, $prev, $branch);
            } else { /*資料不存在，視為新增*/
                $product->insert_item_data($value, $prev, $branch);
            }
        }
        return ['code' => 1, 'msg' => Lang::get('操作成功'), 'url' => '/'];
    }
    private function get_one_product($ISBN)
    {
        $item = Db::table('productinfo')->where("ISBN = '" . $ISBN . "'")->get();
        //dump(Db::getLastSql());exit;
        return CommonService::objectToArray($item);
    }
    private function update_item_data($data, $prev, $branch)
    {
        if ($branch != null) {
            $typeinfo = Db::table('typeinfo')->find($branch);
            $typeinfo = CommonService::objectToArray($typeinfo);
            $this_branch = '{"prev_id":"' . $typeinfo['parent_id'] . '","branch_id":"' . $branch . '","parent_id":"1"}';
        } else {
            $this_branch = '{"prev_id":"' . $prev . '","branch_id":"0","parent_id":"0"}';
        }
        //dump($typeinfo);exit;
        $ISBN = $data['ISBN'];
        $product = Db::table('productinfo')->select('id', 'final_array')->where("ISBN = '" . $ISBN . "'")->first();
        $product = CommonService::objectToArray($product);

        $item_branch_array = json_decode($product['final_array'], true);

        //dump($item_branch_array);exit;
        $has_data = false;
        if (!empty($item_branch_array)) {
            foreach ($item_branch_array as $item_branch) {
                if ($item_branch == json_decode($this_branch, true)) {
                    $has_data = true;
                }
            }

            if ($has_data) {
            } else {
                $item_branch_array[] = json_decode($this_branch, true);
            }

            $data['final_array'] = json_encode($item_branch_array);
            //dump($data['final_array']);exit;
            unset($data['title']);
            Db::table('productinfo')->where("ISBN = '" . $ISBN . "'")->update($data);

            //dump($item_branch_array);exit;
            $this->update_productinfo_orders($product['id'], $item_branch_array);
        }
    }
    private function insert_item_data($data, $prev, $branch)
    {
        if (empty($prev)) {
            $typeinfo = Db::table('typeinfo')->find($branch);

            $data['final_array'] = '[{"prev_id":"' . $typeinfo['parent_id'] . '","branch_id":"' . $branch . '","parent_id":"1"}]';
        } else {

            $data['final_array'] = '[{"prev_id":"' . $prev . '","branch_id":"0","parent_id":"0"}]';
        }

        $data['out_ID'] = 0;
        $find_product_id = 'AP' . date('Ymd');
        $productinfo = Db::table('productinfo')->select('product_id')->where("product_id like '" . $find_product_id . "%'")->orderByRaw('id desc')->first();
        $productinfo = CommonService::objectToArray($productinfo);

        if ($productinfo == null) {
            $new_id = '001';
        } else {
            $last_id = (int) substr($productinfo['product_id'], -3);
            $new_id = substr("00" . ($last_id + 1), -3);
        }

        $data['product_id'] = 'AP' . date('Ymd') . $new_id;
        $data['pic'] = '["\/upload\/square_01.jpg"]';
        $data['create_time'] = time();
        $last_id = Db::table('productinfo')->insertGetId($data);

        if (empty($prev)) {
            $productinfo_orders = [
                'prod_id' => $last_id,
                'prev_id' => $typeinfo['parent_id'],
                'branch_id' => $branch,
            ];
        } else {
            $productinfo_orders = [
                'prod_id' => $last_id,
                'prev_id' => $prev,
                'branch_id' => 0,
            ];
        }
        $item = Db::table('productinfo_orders')->insert($productinfo_orders);
    }
    /* 批量更新商品與分館/分類的紀錄(排序用) */
    private function update_productinfo_orders($prod_id, $final_array)
    {
        //dump($prod_id);dump($final_array);exit;
        $id_in = [];
        // 針對每個階層處理
        foreach ($final_array as $key => $value) {
            $productinfo_orders = DB::table('productinfo_orders')->whereRaw('prod_id =' . $prod_id . ' AND prev_id =' . $value['prev_id'] . ' AND branch_id =' . $value['branch_id'])->get();
            $productinfo_orders = CommonService::objectToArray($productinfo_orders);
            if (count($productinfo_orders) > 0) {
                // 有紀錄則紀錄有此筆紀錄
                array_push($id_in, $productinfo_orders[0]['id']);
            } else {
                // 沒紀錄則新增紀錄
                DB::table('productinfo_orders')->insert([
                    'prod_id' => $prod_id,
                    'prev_id' => $value['prev_id'],
                    'branch_id' => $value['branch_id'],
                    'order_id' => 0
                ]);
                // 記錄剛建立出的資料
                $productinfo_orders = DB::table('productinfo_orders')->whereRaw('prod_id =' . $prod_id . ' AND prev_id =' . $value['prev_id'] . ' AND branch_id =' . $value['branch_id'])->get();
                $productinfo_orders = CommonService::objectToArray($productinfo_orders);
                array_push($id_in, $productinfo_orders[0]['id']);
            }
        }

        // 把沒有的階層刪掉
        if (count($id_in) > 0) {
            $id_in = join(',', $id_in);
            DB::table('productinfo_orders')->whereRaw('prod_id =' . $prod_id . ' AND id not in (' . $id_in . ')')->delete();
        }
    }
}
