<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;

class Findorder extends MainController{
  private $DBTextConnecter;
  private $typeDBTextConnecter;
  private $resTableName;
  private $typeTableName;
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('contact_find_prod');
    $this->resTableName = 'contact_find_prod';
  }

  public function findorder(Request $request) {
    $search_by_date = $request->get('start') ? true : false;
    if($search_by_date){
      $start = $request->get('start') ?? '1970-01-01';
      $s_start = strtotime($start);
      $end = $request->get('end') ?? '9999-01-01';
      $s_end = strtotime($end.' +1Day');
      // dump("UNIX_TIMESTAMP(createdate) between '$s_start' AND '$s_end'");
      $this->data['searchKey'] = '時間：' . $start . '到' . $end;
      $this->data['searchKey_input'] =  "";
      $this->data['searchDate_input'] = $start.' - '.$end;
      $contact_log = Db::table($this->resTableName)
                      ->whereRaw($this->distributor_id_where_sql)
                      ->whereRaw("UNIX_TIMESTAMP(createdate) between '$s_start' AND '$s_end'");
    }else{
      $searchKey = $request->get('searchKey') ?? '';
      $searchKey = trim($searchKey);
      $this->data['searchKey'] = $searchKey;
      $this->data['searchKey_input'] = $searchKey;
      $this->data['searchDate_input'] = '';
      $contact_log = Db::table($this->resTableName)
                      ->whereRaw($this->distributor_id_where_sql)
                      ->whereRaw('
                        user_name LIKE \'%'. $searchKey .'%\' or 
                        user_phone LIKE \'%'. $searchKey .'%\' or 
                        user_email LIKE \'%'. $searchKey .'%\' or 
                        ask LIKE \'%"name":"%'. $searchKey .'%","unit"%\' or 
                        ask LIKE \'%"unit":"%'. $searchKey .'%","num"%\' or 
                        ask LIKE \'%"note":"%'. $searchKey .'%"%\'
                      ');
    }

    $contact_log = $contact_log->orderByRaw('id desc');
    
    if($search_by_date){
      $contact_log = $contact_log->paginate(
                self::PER_PAGE_ROWS
              )->appends([
                'start' => $start,
                'end' => $end,				
              ]);
    }else{
      $contact_log = $contact_log->paginate(
                self::PER_PAGE_ROWS
              )->appends([
                'searchKey' => $searchKey,								
              ]);
    }
    if (empty($contact_log->items()) == false) {
      foreach ($contact_log->items() as $item) {
        $item->ask = json_decode($item->ask, true);
        $ask_text = "";
        foreach ($item->ask as $k => $v) {
          $ask_text .= ($k + 1) . ".
          品名： " . $v['name'] . ",
          單位: " . $v['unit'] . ",
          數量: " . $v['num'] . ", ";
          if ($v['img']) {
            $ask_text .= "圖片: <a href='" . $v['img'] . "' target='_blank'>
                    <img class='small_pic' src='" . $v['img'] . "'>
                  </a>,";
          } else {
            $ask_text .= "圖片: 無,";
          }
          $ask_text .= "備註：" . $v['note'] . "<br>";
        }
        $item->ask_text = $ask_text;
      }
    }
    // dump($contact_log->items());
    $this->data['contact_log'] = $contact_log;

    $this->data['empty'] = '沒有數據';
    return view('admin.findorder.findorder',['data'=>$this->data]);
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->resTableName, $id)){
      $this->error('您無法操作此項目');
    }
    try{
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功', url('/admin/Findorder/index'));
  }
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll($this->resTableName, $id)){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
        Db::table($this->resTableName)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  /*更改status和新增回覆，直接對JQ post下tp5看得懂得更新指令*/
  public function status(Request $request) {
    $update = $request->post();
    if(!parent::check_controll($this->DBTextConnecter->getTableName(), $update['id'])){
      $this->error('您無法操作此項目');
    }

    $update['respdate'] = date('Y-m-d H:i');
    try{
      $this->DBTextConnecter->setDataArray($update);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function getCount(Request $request){
    try{
      $unprocess = Db::table($this->resTableName)->whereRaw($this->distributor_id_where_sql)->whereRaw("status='0'")->count();
      $count = Db::table($this->resTableName)->whereRaw($this->distributor_id_where_sql)->count();
      $outputData = [
        'status' => true,
        'message' => array($unprocess,$count),
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
      return $outputData;
    }
    return $outputData;
  }
}
