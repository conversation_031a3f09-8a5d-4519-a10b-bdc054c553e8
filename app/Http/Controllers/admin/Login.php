<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

//Photonic Class
use App\Http\Controllers\home\GlobalController;
use App\Services\CommonService;
use App\Services\pattern\Edcode;

class Login extends GlobalController{
  public $admin_type = 'admin';

  public function __construct(){
    parent::__construct();
    $this->data['admin_type']= $this->admin_type;
    $this->data['google_auth'] = config('extra.shop.admin_google_auth');

    if(!defined('__PUBLIC__')) define('__PUBLIC__', '/public/static/admin');
    if(!defined('__UPLOAD__')) define('__UPLOAD__', '/public/static/index');
  }

  public function index(Request $request){
    $admin_info = Db::table('admin_info')->find(1);
    $this->data['admin_info'] = CommonService::objectToArray($admin_info);
    return view('admin.login.login',['data'=>$this->data]);
  }

  public function loginAction(Request $request){
    $admin_info = Db::table('admin_info')->find(1);		
    $this->data['admin_info'] = CommonService::objectToArray($admin_info);

    $account = $request->post('account');
    $password = $request->post('password');
    $auth = $request->post('auth');
    $jump_uri = $request->post('jump_uri') ?? '';

    $rules = [
      'account'  => 'required',
      'password' => 'required'
    ];

    $msg = [
      'account.required' => '帳號不得為空',
      'password.required' => '密碼不得為空'
    ];

    $data = [
      'account'  => $account,
      'password' => $password
    ];

    $validate = Validator::make($data, $rules, $msg);

    if ($validate->fails()) {
      $this->error($validate->errors()->first());
    }

    if($this->admin_type=='admin'){ /*平台管理員*/
      $where = [
        'account' => $account,
      ];
      $adminData = Db::table('admin')->where($where)->first();
      $password_column = 'password';
    }else{ /*供應商*/
      $where = [
        'email' => $account,
        'user_type' => 1,
      ];
      $adminData = Db::connection('main_db')->table('account')->where($where)->first();
      if($adminData){ $adminData['permission'] = 'no'; }
      $password_column = 'pwd';
    }
    // dd($adminData);
    $adminData = CommonService::objectToArray($adminData);
    if($adminData){
      if($adminData[$password_column] == md5($password)){
        if($this->admin_type=='admin'){ /*平台管理員*/
          if ($this->data['google_auth'] == 1) {
            include_once(ROOT_PATH.'app/Services/ThirdParty/GoogleAuthenticator.php');
            $ga = new \PHPGangsta_GoogleAuthenticator();
            $checkResult = $ga->verifyCode('AJVROPCJEJYGCIAG', $auth, 2); // 2 = 2*30sec clock tolerance
            if (!$checkResult) {
              $this->error('驗證碼錯誤');
            }
          }
        }
        $adminData['admin_type'] = $this->admin_type;
        session()->put($this->admin_type, $adminData);

        $Edcode = new Edcode("");
        $jump_uri = $jump_uri ? $Edcode->safe_b64decode($jump_uri) : url('index/index');

        $this->success('登入成功', $jump_uri);
      }else{
        $this->error('帳號或密碼錯誤');
      }
    }else{
      $this->error('帳號或密碼錯誤');
    }
  }

  public function logout(Request $request){
    session()->remove($this->admin_type);
    $this->redirect(url('login/index'));
  }
}