<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\admin\MainController;
use App\Http\Requests\NavigationMenuRequest;
use App\Services\Admin\NavigationMenuService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class Navigation extends MainController
{
    protected $navigationMenuService;

    public function __construct(NavigationMenuService $navigationMenuService)
    {
        parent::__construct();
        $this->navigationMenuService = $navigationMenuService;
    }

    /**
     * 顯示選單列表
     */
    public function index(): View
    {
        $menus = $this->navigationMenuService->getAllMenus();
        $statistics = $this->navigationMenuService->getMenuStatistics();

        $this->data['menus'] = $menus;
        $this->data['statistics'] = $statistics;
        $this->data['navigationActive'] = 'active';
        $this->data['homeCollapse'] = 'show';
        return view('admin.navigation.index', ['data' => $this->data]);
    }

    /**
     * 顯示新增選單表單
     */
    public function create(): View
    {
        $this->data['navigationActive'] = 'active';
        $this->data['homeCollapse'] = 'show';
        return view('admin.navigation.create', ['data' => $this->data]);
    }

    /**
     * 儲存新選單
     */
    public function store(NavigationMenuRequest $request): RedirectResponse
    {
        try {
            $validated = $request->validated();
            $this->navigationMenuService->createMenu($validated);

            return redirect()->route('admin.navigation.index')
                ->with('success', '選單已成功建立！');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', '建立選單失敗：' . $e->getMessage());
        }
    }

    /**
     * 顯示編輯選單表單
     */
    public function edit($id)
    {
        try {
            $menu = $this->navigationMenuService->getMenuById($id);

            $this->data['menu'] = $menu;
            $this->data['navigationActive'] = 'active';
            $this->data['homeCollapse'] = 'show';
            return view('admin.navigation.edit', ['data' => $this->data]);
        } catch (\Exception $e) {
            return redirect()->route('admin.navigation.index')
                ->with('error', '找不到指定的選單');
        }
    }

    /**
     * 更新選單
     */
    public function update(NavigationMenuRequest $request, $id): RedirectResponse
    {
        try {
            $validated = $request->validated();
            $this->navigationMenuService->updateMenu($id, $validated);

            return redirect()->route('admin.navigation.index')
                ->with('success', '選單已成功更新！');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', '更新選單失敗：' . $e->getMessage());
        }
    }

    /**
     * 刪除選單
     */
    public function destroy($id): RedirectResponse
    {
        try {
            $this->navigationMenuService->deleteMenu($id);

            return redirect()->route('admin.navigation.index')
                ->with('success', '選單已成功刪除！');
        } catch (\Exception $e) {
            return redirect()->route('admin.navigation.index')
                ->with('error', '刪除選單失敗：' . $e->getMessage());
        }
    }

    /**
     * AJAX 更新排序
     */
    public function updateOrder(Request $request): JsonResponse
    {
        try {
            // 驗證排序資料
            $request->validate([
                'orders' => 'required|array',
                'orders.*.id' => 'required|integer|exists:navigation_menus,id',
                'orders.*.sort_order' => 'required|integer|min:1'
            ]);

            $this->navigationMenuService->updateMenuOrders($request->orders);

            return response()->json([
                'success' => true,
                'message' => '排序已成功更新！'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '排序更新失敗：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: 取得選單資料
     */
    public function apiIndex(): JsonResponse
    {
        try {
            $menus = $this->navigationMenuService->getAllMenus();

            return response()->json([
                'success' => true,
                'data' => $menus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: 取得前台格式化的選單資料
     */
    public function apiFrontendMenus(): JsonResponse
    {
        try {
            $menus = $this->navigationMenuService->formatMenusForFrontend();

            return response()->json([
                'success' => true,
                'data' => $menus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
