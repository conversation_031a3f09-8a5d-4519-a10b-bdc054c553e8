<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Http\Session;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\CommonService;
use App\Http\Controllers\home\GlobalController;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\Edcode;
use App\Services\pattern\HelperService;

class MainController extends GlobalController
{
    protected $admin;
    protected $show_list;
    protected $close_function;
    protected $my_distributor_id;
    public $admin_type = 'admin';
    public $distributor_id_where_sql = 'distributor_id="0"'; /*供應商篩選語法*/

    public function __construct()
    {
        header("Access-Control-Allow-Origin: *");

        parent::__construct();

        if (!defined('__PUBLIC__')) define('__PUBLIC__', '/public/static/admin');
        if (!defined('__UPLOAD__')) define('__UPLOAD__', '/public/static/index');

        if (!defined('UPLOAD_PATH')) {
            define('UPLOAD_PATH', ROOT_PATH . 'public' . DS . 'static' . DS . 'index' . DS . 'upload');
        }
        $this->data['notification_pubkey'] = config('extra.notification.PUBKEY');

        //auth system
        $this->auth();

        $route = app('request')->route()->getAction();
        $route = explode('Controllers\\', $route['controller'])[1];

        $a = explode('@', $route)[1];
        $module_and_controller = explode('@', $route)[0];
        $m = explode('\\', $module_and_controller)[0];
        $c = explode('\\', $module_and_controller)[1];
        $c = Str::snake($c);
        $sec_active = strtolower($c) . '_' . strtolower($a);

        $m_adjust = str_replace('orderdistribution', 'order', $m);
        $m_adjust = str_replace('distribution', 'admin', $m_adjust);
        $m_adjust = Str::snake($m_adjust);
        $backstage_menu = Db::table('backstage_menu_second')->select('name', 'backstage_menu_id', 'url')->whereRaw('url like "/%' . $m_adjust . '/' . $c . '/%"')->first();
        $backstage_menu = CommonService::objectToArray($backstage_menu);
        // dump('url like "/%'.$m_adjust.'/'.$c.'/%"'); dump($backstage_menu);
        if (!$backstage_menu) {
            $top_active = '';
        } else {
            $top_active = $backstage_menu['backstage_menu_id'];
            if (preg_match('/\/order\//i ', $backstage_menu['url'])) {
                $sec_active = 'order_' . $sec_active;
            }
        }
        // dump($sec_active, $top_active);

        if (in_array($sec_active, ['consumption_pay_list']) !== false) {
            $top_active = 5;
        } else if (in_array($sec_active, ['admin_edit']) !== false) {
            $top_active = 5;
        } else if (in_array($sec_active, ['admin_system_email', 'admin_admin_info']) !== false) {
            $top_active = 8;
        } else if (in_array($sec_active, ['admin_point_set']) !== false) {
            $top_active = 3;
        } else if (in_array($sec_active, ['order_member_product_view_edit']) !== false) {
            $sec_active = 'order_member_product_view';
        } else if (in_array(strtolower($c), ['typeinfo', 'productinfo', 'product', 'branch']) !== false) {
            $top_active = 2;
            if (in_array($sec_active, ['productinfo_allcreate', 'productinfo_edit', 'productinfo_show_position_portion', 'productinfo_edit_fields']) !== false) {
                $sec_active = 'all_index';
            }
        } else if (in_array($sec_active, ['index_spepriceproduct', 'indexad_index']) !== false) {
            $top_active = 1;
            $sec_active = 'index_index';
        } else if (in_array($sec_active, ['act_edit']) !== false) {
            $sec_active = 'act_index';
        } else if (in_array($sec_active, ['discount_edit']) !== false) {
            $sec_active = 'discount_index';
        } else if (in_array($sec_active, ['addprice_edit']) !== false) {
            $sec_active = 'addprice_index';
        } else if (in_array($sec_active, ['coupondirect_edit']) !== false) {
            $sec_active = 'coupondirect_index';
        } else if (in_array($sec_active, ['coupon_create', 'coupon_show']) !== false) {
            $sec_active = 'coupon_index';
        } else if (in_array($sec_active, ['product_index', 'typeinfo_index', 'branch_index']) !== false) {
            $sec_active = 'layertree_tree';
        } else if (in_array($sec_active, ['kol_index', 'kol_salelist', 'kol_sale_detail']) !== false) {
            $sec_active = 'kol_index';
        } else if (in_array($sec_active, ['consumption_exchange_list']) !== false) {
            $sec_active = 'consumption_exchange';
        } else if (in_array($sec_active, ['consumption_draw_list']) !== false) {
            $sec_active = 'consumption_draw';
        } else if (in_array($sec_active, ['admin_edit']) !== false) {
            $sec_active = 'admin_edit';
        } else if (in_array($sec_active, ['orderctrl_index', 'order_pick_index', 'orderctrl_trash']) !== false) {
            $top_active = 7;
            if (in_array($sec_active, ['orderctrl_index']) !== false) {
                $sec_active = 'order_order_ctrl_index';
            } else if (in_array($sec_active, ['order_pick_index']) !== false) {
                $sec_active = 'order_pick_index';
            } else if (in_array($sec_active, ['orderctrl_trash']) !== false) {
                $sec_active = 'order_order_ctrl_trash';
            }
        } else if (in_array($sec_active, ['order_index_index']) !== false) {
            $status = request()->get('status');
            if ($status == null) {
                $sec_active = 'order_index_index_';
            } else {
                $sec_active = 'order_index_index_' . $status;
            }
        }
        // dump($top_active);dump($sec_active);exit;
        $this->data['top_active'] = $top_active;
        $this->data['sec_active'] = $sec_active;

        if (!empty(request()->file())) {
            $type_error = '';
            foreach (request()->file() as $v => $s) {
                $type = $s->getClientMimeType();
                //echo request()->file()[$v]["type"];exit;
                if ($type == '') {
                    continue;
                }
                if (in_array($type, [
                    "image/jpeg",
                    "image/jpg",
                    "image/png",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "application/vnd.ms-excel",
                    "text/xml",
                ])) {
                    $type_error = 'ok';
                }
                if ($type == "application/octet-stream") {
                    $type_error = 'no';
                }
                if ($type_error != 'ok') {
                    $this->error('操作失敗：無效檔案類型');
                }
            }
        }

        /***當前使用者開啟/關閉功能***/
        /*當前登入者功能*/
        if (($this->admin['admin_type'] ?? '') == 'admin') { /*平台管理者登入*/
            $user_function_setting = DB::table('admin')->select('purview')->whereRaw("id = '" . $this->admin['id'] . "'")->first();
            $user_function_setting = CommonService::objectToArray($user_function_setting);

            if (empty($user_function_setting) == false) {
                if (empty($user_function_setting['purview']) == false) {
                    $user_function_setting = json_decode($user_function_setting['purview'], true);
                }
            }
        } else { /*供應商功能*/
            /*寫固定*/
            $user_function_setting = json_decode('{"10":["21","84","85","86","87","89"],"1":["1"],"6":["17"],"4":["18","15","16","11","12","76"],"2":["58"],"5":["23","59","69"],"3":["65","60"],"8":["22","24","57","56","63","62","27","64"]}', true);
        }
        $function_result = HelperService::arrange_use_function($user_function_setting, ($this->admin['admin_type'] ?? ''), ($this->admin['permission'] ?? '[]'));
        $this->show_list = $function_result['show_list'];
        $this->data['show_list'] = $this->show_list;
        $this->data['show_list_group'] = $function_result['show_list_group'];
        $this->data['close_function'] = $function_result['close_function'];
        $this->close_function = $function_result['close_function'];
        /***當前使用者開啟/關閉選單***/

        $this->data['admin_type'] = $this->admin_type;
        if ($this->admin_type != 'admin') {
            $adminType = session()->get($this->admin_type);
            if (empty($adminType) == true) {
                $this->my_distributor_id = 0;
            } else {
                $this->my_distributor_id =  max(intval($adminType['id']), 0);
            }
        } else {
            $this->my_distributor_id = 0;
        }
        $this->data['my_distributor_id'] = $this->my_distributor_id;
    }

    public function auth()
    {
        $route = app('request')->route()->getAction();
        $route = explode('Controllers\\', $route['controller'])[1];

        $a = strtolower(explode('@', $route)[1]);
        $module_and_controller = explode('@', $route)[0];
        $m = explode('\\', $module_and_controller)[0];
        if ($m == 'distribution') {
            $m = 'distAdmin';
        }
        $c = explode('\\', $module_and_controller)[1];
        if (in_array($c . '/' . $a, ['examination/start_roll_call', 'examination/qrcode_roll_call'])) {
            return;
        }

        $this->admin = session()->get($this->admin_type);

        $get = request()->query();

        if (isset($get['acc']) && isset($get['pwd'])) {
            $admin = DB::table($this->admin_type)->where('account', $get['acc'])->where('password', $get['pwd'])->get();
            $admin = CommonService::objectToArray($admin);
            if ($admin) {
                $this->admin = $admin[0];
                $this->admin['admin_type'] = $this->admin_type;
                session()->put($this->admin_type, $this->admin);
            }
        }
        if ($this->admin == null) {
            $Edcode = new Edcode("");
            $jump_uri = url('Login/index') . '?jump_uri=' . $Edcode->safe_b64encode($_SERVER['REQUEST_URI']);
            $this->redirect($jump_uri);
        }
        if ($this->admin_type == 'distribution') {
            if (config('control.control_platform') == 1) {
                $MemberInstance = new MemberInstance($this->admin['id'] ?? 0);
                $user_data = $MemberInstance->get_user_data_distributor();
                if (!$user_data) {
                    $this->error('請先登入，或請確認帳號有供應商資格');
                }
            } else {
                $this->error('查無此頁');
            }
        }

        // dd($this->admin);
        $this->data[$this->admin_type] = $this->admin;
    }

    public function dumpException(\Exception $e)
    {
        if (config('app.debug')) {
            $this->data['waitSecond'] = 5;
            $this->error(Lang::get('操作失敗') . '：' . $e->getMessage());
        } else {
            $this->data['waitSecond'] = 1;
            $this->error(Lang::get('操作失敗'));
        }
    }

    public function dumpError($errorMessage)
    {
        if (config('app.debug')) {
            $this->data['waitSecond'] = 5;
        } else {
            $this->data['waitSecond'] = 1;
        }
        $this->error('操作失敗：' . $errorMessage);
    }

    // 自動更改排序
    public function auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where = false)
    {
        if (!$table) $this->error('操作失敗：請提供更改的資料表');
        if (!$column) $this->error('操作失敗：請提供更改的欄位');
        if (!$primary_key) $this->error('操作失敗：請提供改目標資料表的主鍵');
        if (!$primary_value) $this->error('操作失敗：請提供改目標資料表的主鍵值');

        $order_num = $order_num ? $order_num : 0; // 未提供排序，預設為0

        if ($filter_where !== false) {
            $filter_where = $filter_where ? $filter_where : 'true = true';
        } else {
            $filter_where = 'true = true';
        }

        // 利用篩選條件檢查此次設定的排序 是否已被設定 且是 別人
        $order_num_isset = DB::table($table)->whereRaw($filter_where . ' and ' . $column . ' = ' . $order_num . ' and ' . $primary_key . ' != ' . $primary_value)->get();
        if (count($order_num_isset) > 0) {
            // 被設定走了，開始自動修改排序

            // 利用篩選條件找出要一起檢查排序的資料
            $rows =    DB::table($table)->whereRaw($filter_where)->get();
            $rows = CommonService::objectToArray($rows);
            foreach ($rows as $key => $value) {
                // 如果該資料排序等於或大於此次設定的排序
                if ($value[$column] >= $order_num) {
                    $new_order = $value[$column] + 1; // 排序自動+1
                    DB::table($table)
                        ->whereRaw($primary_key . ' = ' . $value[$primary_key])
                        ->update([$column => $new_order]);
                }
            }
        }

        // 根據目標篩選，修改目標資料排序
        DB::table($table)->whereRaw($primary_key . ' = ' . $primary_value)->update([$column => $order_num]);
    }

    /*供應商篩選*/
    public function get_distributor_id_where_sql()
    {
        if ($this->admin_type != 'admin') {
            $this->distributor_id_where_sql = 'distributor_id ="' . session()->get($this->admin_type)['id'] . '"';
        }
    }

    /*檢查控制權*/
    public function check_controll($table, $id, $configDb = '', $primary_key = 'id')
    {
        try {
            DB::connection($configDb)->table($table)->column('distributor_id'); /*測試是否有user_id欄位*/
            $has_column_distributor_id = true;
        } catch (\Exception $e) {
            $has_column_distributor_id = false;
        }

        if ($has_column_distributor_id) {
            $target = Db::connection($configDb)->table($table)->where($primary_key, $id);
            if ($this->admin_type == 'distribution') {
                $target = $target->whereRaw($this->distributor_id_where_sql);
            }
            $rowData = $target->first();
            return boolval($rowData);
        } else {
            return true; /*無user_id欄位一律視為有權限*/
        }
    }
}
