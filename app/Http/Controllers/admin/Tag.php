<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Tag extends MainController {
  private $DBTextConnecter;
  const ShowType = 'tag';

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('frontend_data_name');
  }

  public function tag(Request $request) {
    $item = Db::table('frontend_data_name')->whereRaw('show_type = "'.self::ShowType.'"')->orderByRaw('id asc')->get();
    $this->data['item'] = CommonService::objectToArray($item);
    
    return view('admin.tag.tag',['data'=>$this->data]);
  }

  public function update_tag(Request $request) {
    $newData = $request->post();
    // dump($newData);exit;
    foreach ($newData['item'] as $key => $value) {
      $update = [
        'name' => $value
      ];
      Db::table('frontend_data_name')->whereRaw('id = '.$key)->update($update);
    }
    $this->success('更新成功');
  }
}