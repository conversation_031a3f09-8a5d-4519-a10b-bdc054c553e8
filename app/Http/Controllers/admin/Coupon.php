<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Coupon extends MainController {
  private $tableName;
  private $poolTableName;
  private $DBTextConnecter;
  private $DBFileConnecter;

  private $COUPON_TYPE;
  private $TRANSFER_TYPE;
  private $ONLINE;
  private $AREA;

  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
    $this->COUPON_TYPE = [Lang::get('虛擬卷'), Lang::get('實體卷')];
    $this->TRANSFER_TYPE = [Lang::get('不可以'), Lang::get('可以')];
    $this->ONLINE = [Lang::get('隱藏'), Lang::get('顯示')];
    $this->AREA = [Lang::get('全體'), Lang::get('單一')];
    $this->tableName = 'coupon';
    $this->poolTableName = 'coupon_pool';
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->tableName);
    $this->DBFileConnecter = DBFileConnecter::withTableName($this->tableName);
  }

  public function index(Request $request) {	
    $searchKey = $request->get('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $this->data['searchKey'] = $searchKey;
    $start = $request->get('start') ?? '1970-01-01';
    $end = $request->get('end') ?? '9999-01-01';

    $rowData = Db::table($this->tableName)
                  ->where(function($query)use($searchKey){
                    $query->orWhere('title', 'like', '%'.$searchKey.'%')
                          ->orWhere('number', 'like', '%'.$searchKey.'%');
                  })
                  ->where(function($query)use($start,$end){
                    $timestamp_s = strtotime($start);
                    $timestamp_e = strtotime($end.' +1Day');
                    /*活動時間區間與搜尋時間區間重疊*/
                    $query->orWhereRaw("start >= '$timestamp_s' AND start < '$timestamp_e'")
                          ->orWhereRaw("end >= '$timestamp_s' AND end < '$timestamp_e'")
                          ->orWhereRaw("start <= '$timestamp_s' AND end > '$timestamp_e'")
                          ->orWhereRaw("start >= '$timestamp_s' AND end < '$timestamp_e'");
                  })
                  ->whereRaw($this->distributor_id_where_sql)
                  ->orderByRaw('id desc')
                  ->paginate(self::PER_PAGE_ROWS)
                  ->appends([
                    'searchKey' => $searchKey,
                    'start' => $start,
                    'end' => $end,
                  ]);
    $rowDataItem = $rowData->items();
    if (empty($rowDataItem) == false) {
      foreach ($rowDataItem as $key =>$value) {
        $rowDataItem[$key]->sellCount = Db::table($this->poolTableName)
                                          ->whereNotNull('login_time')
                                          ->whereNotNull('owner')
                                          ->whereRaw('coupon_id = ' . $value->id)
                                          ->count();

        $rowDataItem[$key]->useCount = Db::table($this->poolTableName)
                                          ->whereNotNull('use_time')
                                          ->whereNotNull('owner')
                                          ->whereRaw('coupon_id = ' . $value->id)
                                          ->count();
      }
    }

    $this->data['rowDataItems'] = $rowDataItem;		
    $this->data['coupon'] = $rowData;

    return view('admin.coupon.member-discount',['data'=>$this->data]);
  }

  public function create(Request $request) {
    $productinfo = Db::table('productinfo')
                      ->select('productinfo.title AS title','productinfo.id AS id')
                      ->whereRaw('
                        productinfo.online < 2 AND
                        productinfo.has_price = 1
                      ')
                      ->whereRaw($this->distributor_id_where_sql)
                      ->orderByRaw('id desc')->get();
    $this->data['productinfo'] = CommonService::objectToArray($productinfo);

    return view('admin.coupon.member-discount-new',['data'=>$this->data]);
  }
  public function doCreate(Request $request) {
    $width = 600; $height = 600;
    $newData = $request->post();
    unset($newData['_token']);
    if($this->admin_type=='distribution'){
      $newData['distributor_id'] = session()->get($this->admin_type)['id'];
    }
    // dump($newData);exit;
    try{
      if($newData['title'] == ''){ throw new \Exception(Lang::get('請輸入標題')); }
      if($newData['limit_num'] == ''){ throw new \Exception(Lang::get('請輸入領取上限')); }
      if($newData['start'] == ''){ throw new \Exception(Lang::get('請選擇開始時間')); }
      if($newData['end'] == ''){ throw new \Exception(Lang::get('請選擇結束時間')); }
      if($newData['discount'] == ''){ throw new \Exception(Lang::get('請輸入折扣金額')); }
      if($newData['num'] == ''){ throw new \Exception(Lang::get('請輸入生成數量')); }

      $count = Db::table('coupon')->whereRaw('number like "'.config('extra.shop.subDeparment').'C'.date('Ymd').'%"')->orderByRaw('id desc')->first();
      $count = CommonService::objectToArray($count);
      $count = $count ? intval(substr($count['number'],-3)) + 1 : 1;
      if($count < 10){
        $count = '00' . $count;
      }else if($count < 100){
        $count = '0' . $count;
      }

      $newData['number'] = config('extra.shop.subDeparment') . 'C' . date('Ymd') . $count;
      $newData['start'] = strtotime($newData['start']);
      $newData['end'] = strtotime($newData['end']);
      $this->DBTextConnecter->setDataArray($newData);

      $mainId = $this->DBTextConnecter->createTextRow();
      $image = $request->file('image');

      if($image){
        $DBFileConnecter = new DBFileConnecter();
        $newData['pic'] = 
        $DBFileConnecter->fixedFileUp($image, 'coupon_' . $mainId, $width, $height);
        $newData['id'] = $mainId;
        $this->DBTextConnecter->setDataArray($newData);
        $this->DBTextConnecter->upTextRow();
      }

      //pool
      $pool = array();
      for ($i=0; $i < $newData['num']; $i++) {
        array_push($pool,[
          'number' => config('extra.shop.subDeparment') . 
                      'CP' . $mainId . 
                      chr(mt_rand(65, 90)) . 
                      chr(mt_rand(65, 90)) . 
                      $i . 
                      chr(mt_rand(65, 90)) . 
                      chr(mt_rand(65, 90)),
          'coupon_id' => $mainId,
        ]);
      }

      Db::table($this->poolTableName)->insert($pool);
    }catch (\Exception $e) {
      $this->dumpException($e);
    }

    $this->success(Lang::get('操作成功'), url('/admin/Coupon/index'));
  }

  public function show(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->tableName, $id)){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $singleData = Db::table($this->tableName)->find($id);
    $singleData = CommonService::objectToArray($singleData);
    if($singleData == null){
      $this->error(Lang::get('資料有誤'));
    }

    $singleData['pool'] = Db::table($this->poolTableName)
                            ->whereNotNull('login_time')
                            ->whereNotNull('owner')
                            ->whereRaw('coupon_id = ' . $singleData['id'])
                            ->get();
    $singleData['pool'] = CommonService::objectToArray($singleData['pool']);
    $singleData['pool'] = array_map(function ($value) {
      $owner = Db::connection('main_db')
                  ->table('account')
                  ->select('number')	
                  ->find($value['owner']);
      $value['owner'] = CommonService::objectToArray($owner);
      return $value;
    }, $singleData['pool']);

    $singleData['sellCount'] = Db::table($this->poolTableName)
                                ->whereNotNull('login_time')
                                ->whereNotNull('owner')
                                ->whereRaw('coupon_id = ' . $singleData['id'])
                                ->count();

    $singleData['useCount'] = Db::table($this->poolTableName)
                                ->whereNotNull('use_time')
                                ->whereNotNull('owner')
                                ->whereRaw('coupon_id = ' . $singleData['id'])
                                ->count();

    $singleData['type'] = $this->COUPON_TYPE[$singleData['type']];
    $singleData['transfer'] = $this->TRANSFER_TYPE[$singleData['transfer']];
    $singleData['text1_online'] = $this->ONLINE[$singleData['text1_online']];
    $singleData['text2_online'] = $this->ONLINE[$singleData['text2_online']];
    $singleData['text3_online'] = $this->ONLINE[$singleData['text3_online']];
    $singleData['text4_online'] = $this->ONLINE[$singleData['text4_online']];

    if ($singleData['area']) {
      $area_id = Db::table('productinfo')->find($singleData['area_id']);
      $area_id = CommonService::objectToArray($area_id);

      if (!$singleData['area_id']) {
        $singleData['area_id'] = Lang::get('已經找不到商品了，請關閉優惠卷');
      } else {
        $singleData['area_id'] = $area_id['title'];
      }

    }else{
      $singleData['area_id'] = '';
    }

    $singleData['area'] = $this->AREA[$singleData['area']];
    $this->data['singleData'] = $singleData;

    return view('admin.coupon.show',['data'=>$this->data]);
  }
  public function dumpUsedExcel(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->tableName, $id)){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $singleData = Db::table($this->tableName)->find($id);
    $singleData = CommonService::objectToArray($singleData);
    if($singleData == null){
      $this->error(Lang::get('資料有誤'));
    }

    $singleData['pool'] = Db::table($this->poolTableName)
                            ->whereNotNull('use_time')
                            ->whereRaw('coupon_id = ' . $singleData['id'])
                            ->get();
    $singleData['pool'] = CommonService::objectToArray($singleData['pool']);
    $singleData['pool'] = array_map(function ($value) {
      $owner = Db::connection('main_db')
                  ->table('account')
                  ->select('number')	
                  ->find($value['owner']);
      $value['owner'] = CommonService::objectToArray($owner);
      return $value;
    }, $singleData['pool']);

    $singleData['sellCount'] = Db::table($this->poolTableName)
                                  ->whereNotNull('login_time')
                                  ->whereRaw('coupon_id = ' . $singleData['id'])
                                  ->count();

    $singleData['useCount'] = Db::table($this->poolTableName)
                                  ->whereNotNull('use_time')
                                  ->whereRaw('coupon_id = ' . $singleData['id'])
                                  ->count();

    $singleData['type'] = $this->COUPON_TYPE[$singleData['type']];
    $singleData['transfer'] = $this->TRANSFER_TYPE[$singleData['transfer']];

    $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->setCellValue('A1', Lang::get('名稱'));
    $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('B1', $singleData['title']);
    $objPHPExcel->getActiveSheet()->setCellValue('C1', Lang::get('編號'));
    $objPHPExcel->getActiveSheet()->getStyle('C1')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('D1', $singleData['number']);
    $objPHPExcel->getActiveSheet()->setCellValue('A2', Lang::get('開始日期'));
    $objPHPExcel->getActiveSheet()->getStyle('A2')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('B2', date('Y-m-d', $singleData['start']));
    $objPHPExcel->getActiveSheet()->setCellValue('C2', Lang::get('結束日期'));
    $objPHPExcel->getActiveSheet()->getStyle('C2')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('D2', date('Y-m-d', $singleData['end']));
    $objPHPExcel->getActiveSheet()->setCellValue('A3', Lang::get('折扣方式'));
    $objPHPExcel->getActiveSheet()->getStyle('A3')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('B3', Lang::get('扣').config('extra.shop.dollar_symbol').$singleData['discount']);
    $objPHPExcel->getActiveSheet()->setCellValue('C3', Lang::get('使用限制'));
    $objPHPExcel->getActiveSheet()->getStyle('C3')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('D3', Lang::get('滿額').config('extra.shop.dollar_symbol').$singleData['coupon_condition']);
    $objPHPExcel->getActiveSheet()->setCellValue('A4', Lang::get('可否轉移'));
    $objPHPExcel->getActiveSheet()->getStyle('A4')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('B4', $singleData['transfer']);
    $objPHPExcel->getActiveSheet()->setCellValue('C4', Lang::get('生成類型'));
    $objPHPExcel->getActiveSheet()->getStyle('C4')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('D4', $singleData['type']);
    $objPHPExcel->getActiveSheet()->setCellValue('A5', Lang::get('生產張數'));
    $objPHPExcel->getActiveSheet()->getStyle('A5')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('B5', $singleData['num']);
    $objPHPExcel->getActiveSheet()->setCellValue('C5', Lang::get('已領取張數'));
    $objPHPExcel->getActiveSheet()->getStyle('C5')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('D5', $singleData['sellCount']);
    $objPHPExcel->getActiveSheet()->setCellValue('E5', Lang::get('已使用張數'));
    $objPHPExcel->getActiveSheet()->getStyle('E5')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('F5', $singleData['useCount']);

    $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(16);
    $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(16);
    $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(16);
    $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(16);
    $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(16);
    $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(16);

    $objPHPExcel->getActiveSheet()->setCellValue('A7', Lang::get('優惠券代碼'));
    $objPHPExcel->getActiveSheet()->getStyle('A7')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('B7', Lang::get('領取日期'));
    $objPHPExcel->getActiveSheet()->getStyle('B7')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('C7', Lang::get('領取會員'));
    $objPHPExcel->getActiveSheet()->getStyle('C7')->getFont()->setBold(true);
    $objPHPExcel->getActiveSheet()->setCellValue('D7', Lang::get('使用日期'));
    $objPHPExcel->getActiveSheet()->getStyle('D7')->getFont()->setBold(true);

    $row = 8;
    foreach ($singleData['pool'] as $value) {
      $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, $value['number']);
      $objPHPExcel->getActiveSheet()->setCellValue('B' . $row, date('Y-m-d', $value['login_time']));
      $objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $value['owner']['number']);
      $value['use_time'] = $value['use_time'] ? date('Y-m-d', $value['use_time']) : Lang::get('未使用');
      $objPHPExcel->getActiveSheet()->setCellValue('D' . $row, $value['use_time']);
      $row++;
    }

    $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
    $filename = $singleData['number'] . " - " . $singleData['title'] . " ".Lang::get('使用報表').".xls";
    ob_end_clean();
    ob_start();
    header("Content-type: application/force-download");
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $PHPExcelWriter->save('php://output');
  }
  public function dumpPoolExcel(Request $request){
    $id = $request->get('id');
    if(!parent::check_controll($this->tableName, $id)){
      $this->error(Lang::get('您無法操作此項目'));
    }
    $singleData = Db::table($this->tableName)->find($id);
    $singleData = CommonService::objectToArray($singleData);
    if($singleData == null){
      $this->error(Lang::get('資料有誤'));
    }

    $singleData['pool'] = Db::table($this->poolTableName)
                ->whereRaw('coupon_id = ' . $singleData['id'])
                ->get();
    $singleData['pool'] = CommonService::objectToArray($singleData['pool']);

    $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(16);

    $row = 1;
    foreach ($singleData['pool'] as $value) {
      $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, $value['number']);
      $row++;
    }

    $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
    $filename = $singleData['number'] . " - " . $singleData['title'] . " ".Lang::get('編號列表').".xls";
    ob_end_clean();
    ob_start();
    header("Content-type: application/force-download");
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $PHPExcelWriter->save('php://output');
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->tableName, $id)){
      $this->error(Lang::get('您無法操作此項目'));
    }
    try{
      Db::table($this->tableName)->delete($id);
      Db::table($this->poolTableName)->where('coupon_id', $id)->delete();
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success(Lang::get('操作成功'), '/admin/Coupon/index');
  }
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll($this->tableName, $id)){
            throw new \Exception(Lang::get('您無法操作此項目'));
          }
        }
        Db::table($this->tableName)->whereIn('id', $idList)->delete();
        Db::table($this->poolTableName)->whereIn('coupon_id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success(Lang::get('操作成功'), '/admin/Coupon/index');
  }
  /*AJAX*/
  public function cellCtrl(Request $request) {
    try{
      $updateData = $request->post();
      if(!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])){
        throw new \Exception(Lang::get('您無法操作此項目'));
      }

      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'

      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }
}