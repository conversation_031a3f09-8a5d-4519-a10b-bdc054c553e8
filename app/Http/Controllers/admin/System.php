<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;

class System extends MainController{
  public function index(Request $request){
    $system_intro = Db::table('system_intro')->where('system_id',1)->get();
    $system_intro = CommonService::objectToArray($system_intro);
    $this->data['system_intro'] = $system_intro[0];
    return view('admin.system.index',['data'=>$this->data]);
  }

  public function update(Request $request){	
    $data = $request->post();
    unset($data['_token']);
    Db::table('system_intro')->where('system_id',1)->update($data);
    $this->success('更新成功');
  }
}