<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;
use App\Services\pattern\MemberInstance;

class Layertree extends MainController{
  private $DBproduct;

  public function __construct() {
    parent::__construct();
    $this->DBproduct = DBTextConnecter::withTableName('product');
  }

  public function tree(Request $request) {
    return view('admin.layertree.tree',['data'=>$this->data]);
  }

  public function get_distributors(Request $request){
    if($this->admin_type!='admin' || config('control.control_platform')==0){ /*非管理者 或 不平台化*/
      $distributors = [['id'=>0, 'name'=>'自己', 'shop_name'=>'自己']];
    }else{
      $request_obj = Request::create('', 'GET', [
        'user_type' => 1, 	/*供應商*/
      ]);
      // dump($request_obj);exit;
      $distributors = MemberInstance::search_member(1, $request_obj, 'a.id, a.name, a.shop_name')['rowData'];
      array_unshift($distributors, ['id'=>0, 'name'=>'平台', 'shop_name'=>'平台']);
    }
    // dump($distributors);exit;
    return $distributors;
  }

  public function get_product_tree(Request $request){
    $distributor_id_where = '';
    if($this->admin_type=='distribution'){ /*供應商限看自己的*/
      $distributor_id_where = $this->distributor_id_where_sql;
    }
    else if($this->admin_type=='admin'){ /*若為管理者，可看指定供應商*/
      if(!empty($request->get('distributor_id'))){
        if($request->get('distributor_id')!=='' && $request->get('distributor_id')!=='-1'){
          $distributor_id_where = 'distributor_id="'.$request->get('distributor_id').'"';
        }
      }
    }
    // dump($distributor_id_where);exit;

    $product = Db::table('product')->select('id', 'title', 'order_id', 'online', 'recommend', 'show_on_nav');
    if (empty($distributor_id_where) == false) {
      $product = $product->whereRaw($distributor_id_where);
    }
    $product = $product->orderByRaw('order_id asc, id asc')->get();
    $product=CommonService::objectToArray($product);

    foreach ($product as $key => $value) {
      //第0層 館
      $product[$key]['product_num'] = $this->count_products($value['id'], 0, 0);
      $product[$key]['content'] = $this->next_layer_product($value['id'], 0);
    }

    // dump($product);
    return $product;
  }
  private function next_layer_product($parent_id, $branch_id){
    $next_product = Db::table('typeinfo')->whereRaw('parent_id ="'.$parent_id.'" and branch_id ="'.$branch_id.'"');
    /*供應商限看自己的*/
    if($this->admin_type=='distribution'){
      $next_product = $next_product->whereRaw($this->distributor_id_where_sql);
    }
    $next_product = $next_product->orderByRaw('order_id asc, id asc')->get();
    $next_product=CommonService::objectToArray($next_product);

    if (count($next_product) > 0) {
      foreach ($next_product as $key => $value) {
        //處理下一階層
        $next_product[$key]['product_num'] = $this->count_products($parent_id, $value['id'], -1);;
        $next_product[$key]['content'] = $this->next_layer_product($parent_id,  $value['id']);
      }
    }
    return $next_product;
  }
  private function count_products($prev_id, $branch_id, $parent_id){
    $db_obj = Db::table('productinfo');
    /*供應商限看自己的*/
    if($this->admin_type=='distribution'){
      $db_obj = $db_obj->whereRaw($this->distributor_id_where_sql);
    }

    if($parent_id = -1){
      $product_num = $db_obj->whereRaw('final_array like \'%"prev_id":"'.$prev_id.'","branch_id":"'.$branch_id.'"%\'')->count();
    }else{
      $product_num = $db_obj->whereRaw('final_array like \'%"prev_id":"'.$prev_id.'","branch_id":"'.$branch_id.'","parent_id":"'.$parent_id.'"%\'')->count();
    }
    return $product_num;
  }
}