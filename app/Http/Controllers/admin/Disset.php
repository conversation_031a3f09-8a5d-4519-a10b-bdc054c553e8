<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;

class Disset extends MainController
{
  private $tableName;
  public function __construct() {
    parent::__construct();
    $this->tableName = 'discount';
  }

  public function index(Request $request) {
    $dis = Db::table($this->tableName)->whereRaw($this->distributor_id_where_sql)->orderByRaw('number asc')->get();
    $this->data['dis'] = CommonService::objectToArray($dis);
    return view('admin.disset.index',['data'=>$this->data]);
  }

  public function edit(Request $request) {
    $type = $request->post('type');
    $id = $request->post('id');

    switch ($type) {
      case "add":
        $data['name'] = $request->post('name');
        $data['number'] = $request->post('number');
        /*供應商*/
        if($this->admin_type=='distribution'){
          $data['distributor_id'] = session()->get($this->admin_type)['id'];
        }
        if(Db::table($this->tableName)->insert($data)){
          echo 'success';
        }else{
          echo '新增失敗';
        }
        break;

      case "delete":
        if(!parent::check_controll($this->tableName, $id)){
          echo '您無法操作此項目';
        }
        if(Db::table($this->tableName)->where('id',$id)->delete()){
          echo 'success';
        }else{
          echo '失敗';
        }
        break;
    }
  }
}