<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Http\Controllers\admin\Act;
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Coupondirect extends Act
{
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  private $tableName;
  private $DBTextConnecter;
  public function __construct() {
    parent::__construct('coupon_direct', 'coupon_direct_product');
    $this->tableName = 'coupon_direct';
    $this->associated_tableName = 'coupon_direct_product';
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->tableName);
  }

  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $this->data['searchKey'] = $searchKey;
    $rowData = Db::table($this->tableName)
                  ->where(function($query) use($searchKey){
                    $query->orWhere([
                      'name' => ['like', '%'.$searchKey.'%'],
                      'number' => ['like', '%'.$searchKey.'%']
                    ]);
                  })
                  ->whereRaw($this->distributor_id_where_sql)
                  ->orderByRaw('id desc')
                  ->paginate(self::PER_PAGE_ROWS)
                  ->appends([
                    'searchKey' => $searchKey
                  ]);
    // echo $sql = Db::table($this->tableName)->getLastSql();
    $this->data['start'] = date('Y-m-d');
    $this->data['end'] = date('Y-m-d');
    $this->data['act'] = $rowData;
    return view('admin.coupondirect.act',['data'=>$this->data]);
  }
  /*列表頁相關功能api*/
  public function getActList(Request $request){
    $type = $request->post('type');
    $searchKey = $request->post('searchKey');
    $start = $request->post('start');
    $end = $request->post('end');
    $id = $request->post('id');
    // var_dump($searchKey);
    if($type == 'keyword' && !empty($searchKey)){
      $list = Db::table($this->tableName)
                ->where(function($query) use($searchKey){
                  $query->orWhere([
                    'name' => ['like', '%'.$searchKey.'%'],
                    'number' => ['like', '%'.$searchKey.'%'],
                    'user_code' => ['like', '%'.$searchKey.'%'],
                  ]);
                })
                ->whereRaw($this->distributor_id_where_sql);
      $search = $searchKey;
    }else if($type == 'date'){
      $form=Db::table($this->tableName);
      $list = $form->where(function($query)use($start,$end){
                      $query->orWhere([ /*搜尋的時間區包含開始或結束時間*/
                        'start' => ['between', [strtotime($start), strtotime($end)]],
                        'end' => ['between', [strtotime($start), strtotime($end)]]
                      ])
                      ->orWhere(function($query)use($start,$end){ /*搜尋的時間區間在開始結束時間內*/
                        $query->where([
                          'start' => ['elt', strtotime($start)],
                          'end' => ['egt', strtotime($end)]
                        ]);
                      })
                      ->orWhere(function($query)use($start,$end){ /*搜尋的開始時間大於開始時間，且被設定為無結束時間*/
                        $query->where([
                          'start' => ['elt', strtotime($start)],
                          'end' => ['eq', '-28800']
                        ]);
                      });
                    })
                    ->whereRaw($this->distributor_id_where_sql);
            // dump($form->getLastSql());
      $search = $start."~".$start;
    }else{
      $list = Db::table($this->tableName)
                ->whereRaw($this->distributor_id_where_sql);
      $search = "";
    }

    if ($id) {
      $list = $list->where('id', $id);
    }

    $list = $list->orderBy('id', 'desc')->get();
    $list = CommonService::objectToArray($list);

    foreach($list as $liKey => $liValue){
      $list[$liKey]['start'] = date("Y-m-d", $liValue['start']);

      if ($list[$liKey]['end'] == -28800){
        $list[$liKey]['end'] = Lang::get('無時間');
      }else{
        $list[$liKey]['end'] = date("Y-m-d", $liValue['end']);
      }
    }

    $retData = ['actList' => $list, 'search'=>$search];
    return $retData;
  }
  // public function changeOnline(Request $request){}
  // public function delAct(Request $request){}
  public function doCreate(Request $request) {
    $newData['name']      = $request->post('name');
    if(!$newData['name']){ $this->error(Lang::get('請輸入標題'));}        
    
    $newData['user_code'] = $request->post('user_code');
    if(!$newData['user_code']){ $this->error(Lang::get('請輸入輸入代碼')); }
    $repeat = Db::table($this->tableName)->where('user_code', $newData['user_code'])->get();
    if(empty($repeat) == true){ $this->error(Lang::get('輸入代碼重複')); }
    
    $newData['start']     = strtotime($request->post('start_time'));
    if(!$newData['start']){ $this->error(Lang::get('請選擇開始時間')); }
    $newData['end']       = $request->post('noEndTime')!='true' ? strtotime($request->post('end_time')) : -28800;
    if(!$newData['end']){ $this->error(Lang::get('請選擇結束時間')); }
    $newData['type']      = $request->post('type');
    if(!$newData['type']){ $this->error(Lang::get('請輸選擇優惠方式')); }

    $count = $this->getNumber();
    $newData['number'] = config('extra.shop.subDeparment') . 'T' . date('Ymd') . $count;
    $new_id = parent::doCreate_db($newData);
    $this->success(Lang::get('操作成功'), url('Coupondirect/edit') . '?id=' . $new_id);
  }

  public function edit(Request $request){
    return parent::edit($request);
  }
  public function update(Request $request) {
    try{
      $end = $request->post('noEndTime')!='true' ? strtotime($request->post('end_time')) : -28800;
      $updateData = [
        'id' => $request->post('id'),
        'name' => $request->post('name'),
        'user_code' => $request->post('user_code'),
        'limit_num' => $request->post('limit_num') ? $request->post('limit_num') : NULL,
        'content' => $request->post('content'),
        'start' => strtotime($request->post('start_time')),
        'end' => $end,
        'type' => $request->post('type'),
        // 'online' => 1
      ];
      if(!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])){
        throw new \Exception(Lang::get('您無法編輯此項目'), 1);
      }

      if(!$updateData['name']){ throw new \Exception(Lang::get('請輸入標題')); }
      if(!$updateData['user_code']){ throw new \Exception(Lang::get('請輸入輸入代碼')); }
      $repeat = Db::table($this->tableName)->where('user_code', $updateData['user_code'])
                        ->whereRaw('id!="'.$updateData['id'].'"')
                        ->get();
      if(empty($repeat) == true){ throw new \Exception(Lang::get('輸入代碼重複')); }
      if(!$updateData['start']){ throw new \Exception(Lang::get('請選擇開始時間')); }
      if(!$updateData['end']){ throw new \Exception(Lang::get('請選擇結束時間')); }
      if(!$updateData['type']){ throw new \Exception(Lang::get('請輸選擇優惠方式')); }

      $online1 = $request->post('online1');
      $updateData['online1'] = $online1=='true' || $online1==1 ? 1 : 0;
      $updateData['condition1'] = $request->post('condition1');
      $updateData['discount1'] = $request->post('discount1');
      $online2 = $request->post('online2');
      $updateData['online2'] = $online2=='true' || $online2==1 ? 1 : 0;
      $updateData['condition2'] = $request->post('condition2');
      $updateData['discount2'] = $request->post('discount2');
      $online3 = $request->post('online3');
      $updateData['online3'] = $online3=='true' || $online3==1 ? 1 : 0;
      $updateData['condition3'] = $request->post('condition3');
      $updateData['discount3'] = $request->post('discount3');

      // 上傳圖片(base64)
      $img_base64 = $request->post('img_base64');
      if($img_base64){
        // dump($img_base64);exit;
        $path = CommonService::uploadFile('/public/static/index/upload', $img_base64, 'coupondirect_pic_'.$updateData['id']);
        $path = explode('upload', $path);
        if(count($path)==2){
          $path = '/upload'.$path[1];
          $updateData['img'] = $path;
        }
      }
      // dd($updateData);
      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();

      // 上傳圖片
      $img = $request->file('img');
      if($img){
        $DBFileConnecter = DBFileConnecter::withTableName($this->tableName);
        $DBFileConnecter->setFileArray([
          'img' => $img
        ]);
        $DBFileConnecter->setPrivateKeyId($request->post('id'));
        $DBFileConnecter->upFileRow();
      }

    } catch (\Exception $e) {
      $this->dumpException($e);
    }
    $this->success(Lang::get('操作成功'));
  }

  // public function getCount(Request $request){}

  private function getNumber($code='T'){
    return parent::getNumber($code);
  }

  // 取得可加入優惠券的商品
  public function getCouponDirectProd(Request $request){
    $id = $request->post('cateId');
    $first = $request->post('first');

    /*排除自己已選擇的商品*/
    $coupon_id = $request->post('coupon_id');
    $self_product = Db::table($this->associated_tableName.' as ap')->where('ap.coupon_id', $coupon_id)->get();
    $self_product = CommonService::objectToArray($self_product);
    $not_in_query = [];
    foreach ($self_product as $key => $value) {
      array_push($not_in_query, $value['prod_id']);
    }
    $not_in_query = $not_in_query ? 'pi.id not in ('.implode(',', $not_in_query).')' : '1=1';
    // dump($not_in_query);exit;
    
    if($first){
      //是第一階
      $productinfo = Db::table('productinf as pi');
      if($id){
        $productinfo = $productinfo->whereRaw("final_array like '%\"prev_id\":\"".$id."\"%\"parent_id\":\"0\"%'");
      }
      $productinfo = $productinfo->where($not_in_query)
                                ->whereRaw($this->distributor_id_where_sql)
                                ->groupBy('pi.id')
                                ->orderByRaw('pi.order_id, pi.id desc')->get();            
    }else{
      $productinfo = Db::table('productinfo as pi');
      if($id){
        $productinfo = $productinfo->whereRaw("final_array like '%\"parent_id\":\"".$id."\"%'");
      }               
      $productinfo = $productinfo->whereRaw($not_in_query)
                                ->whereRaw($this->distributor_id_where_sql)
                                ->groupBy('pi.id')
                                ->orderByRaw('pi.order_id, pi.id desc')->get();
    }
    if(empty($productinfo)){
      $productinfo = Db::table('productinfo as pi');
      if($id){
        $productinfo = $productinfo->whereRaw("final_array like '%\"branch_id\": \"".$id."\"%' ");
      } 
      $productinfo = $productinfo->whereRaw($not_in_query)
                                ->whereRaw($this->distributor_id_where_sql)
                                ->groupBy('pi.id')
                                ->orderByRaw('pi.order_id, pi.id desc')->get();
    }
    $productinfo = CommonService::objectToArray($productinfo);
    foreach($productinfo as $k => $v){
      $pic1 = json_decode($v['pic'],true);
      if($pic1 == null){
        $productinfo[$k]['pic1'] = "";
      }else{
        $productinfo[$k]['pic1'] = $pic1[0];
      }
      
    }
      
    //echo Db::table('productinfo')->getLastSql();
    $data = ['cateId' => $id,'productinfo' => $productinfo];
    return $data;
  }
  // public function getActProd(Request $request){}
  // public function insertAct(Request $request){}
  // private function add_act_prouduct($insertGetId, $actData){}
  // public function delActProd(Request $request){}
}

