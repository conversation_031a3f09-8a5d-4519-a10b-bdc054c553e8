<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\BonusSettingHelper;

class Bonusmodel extends MainController {
    public function index(Request $request){
        return view('admin.bonusmodel.index',['data'=>$this->data]);
    }

    public function get_data(Request $request){
        $get_detail = $request->all();
        return BonusSettingHelper::get_bonus_models($get_detail);
    }

    public function save_data(Request $request){
        $post_detail = $request->post('detail');
        // dd($post_detail);
        try {
            $detail_id = BonusSettingHelper::save_bonus_model($post_detail);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success([
            'id' => $detail_id,
            'msg' => Lang::get('操作成功'),
        ]);
    }

    public function delete_data(Request $request){
        $id = $request->post('id');
        // dd($id);
        try {
            $delete_result = BonusSettingHelper::delete_bonus_model($id);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success(Lang::get('操作成功'));
    }
    
}