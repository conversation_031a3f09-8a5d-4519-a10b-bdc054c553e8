<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\MemberInstance;
use App\Http\Controllers\home\Cart;

class Consumption extends MainController{
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;
  const DEFAULT_LIMIT_PRCIE = 999999999;

  private $fieldsTable;
  private $commentsTable;
  public function __construct()
  {
    parent::__construct();
  }

  /*消費累積兌換*/
    /*列表*/
    public function exchange(Request $request){
      $searchOnline = !empty($request->get('searchOnline')) ? $request->get('searchOnline') : '-1';
      $this->data['searchOnline'] = $searchOnline;
      $searchKey = !empty($request->get('searchKey')) ? trim($request->get('searchKey')) : '';
      $this->data['searchKey'] = $searchKey;


      $datas = Db::table('consumption_exchange')->whereRaw($this->distributor_id_where_sql);
      if($searchOnline!='-1'){
        $datas = $datas->where('online', $searchOnline);
      }
      if($searchKey!=''){
        $datas = $datas->where(function ($query) use ($searchKey) {
          $query->where('name', 'like', "%".$searchKey."%")
                ->orWhere('name', 'like', "%".$searchKey."%");
        });
      }

      $datas = $datas->orderByRaw('price asc, id desc')->get();
      $datas = CommonService::objectToArray($datas);
      foreach ($datas as $key => $value) {
        $datas[$key]['winner'] = Db::table('consumption_exchange_record')->where('exchange_id', $value['id'])->count();
      }
      $this->data['datas'] = $datas;

      return view('admin.consumption.exchange',['data'=>$this->data]);
    }
    /*新增、修改*/
    public function exchange_save(Request $request){
      $data = [
        'name' => !empty($request->post('name')) ? $request->post('name') : "",
        'price' => !empty($request->post('price')) ? $request->post('price') : "",
        'online' => !empty($request->post('online')) ? $request->post('online') : 1,
      ];

      if(array_key_exists('id', $request->post()) == false){$this->error('儲存失敗');}
      $id = $request->post('id');

      if(!$data['name']){$this->error('請輸入贈品說明');}
      if($data['price']===""){$this->error('請輸入需求累積消費');}

      if($id=='0'){ /*新增*/
        if($this->admin_type=='distribution'){
          $data['distributor_id'] = session()->get($this->admin_type)['id'];
        }
        $id = DB::table('consumption_exchange')->insertGetId($data);
      }
      else{ /*編輯*/
        if(!parent::check_controll('consumption_exchange', $id)){
          $this->error('您無法操作此項目');
        }
        DB::table('consumption_exchange')->where('id', $id)->update($data);
      }

      // 處理圖片
      if(!empty($request->post('pic'))){
        $image_base64 = !empty($request->post('pic')) ? $request->post('pic') : "";
        if( substr($image_base64, 0, 4) == 'data' ){
          $path_to_file = CommonService::uploadFile('/public/static/index/upload', $image_base64, 'exchange_'.$id);
          $path_to_file = explode("/", $path_to_file);
          $data['pic'] = 'upload/'. end($path_to_file);
          DB::table('consumption_exchange')->where('id', $id)->update($data);
        }
      }

      $this->success('儲存成功');
    }
    /*刪除*/
    public function exchange_delete(Request $request){
      if(empty($request->post('id'))){$this->error('刪除失敗');}
      if(!parent::check_controll('consumption_exchange', $request->post('id'))){
        $this->error('您無法操作此項目');
      }
      DB::table('consumption_exchange')->where('id', $request->post('id'))->delete();
      $this->success('刪除成功');
    }

    /*展示中獎名單*/
    public function exchange_list(Request $request){
      $exchange_id = !empty($request->get('exchange_id')) ? $request->get('exchange_id') : '';
      $this->data['exchange_id'] = $exchange_id;
      $searchKey = !empty($request->get('searchKey')) ? trim($request->get('searchKey')) : '';
      $this->data['searchKey'] = $searchKey;
      $s_ex_date = !empty($request->get('s_ex_date')) ? trim($request->get('s_ex_date')) : '';
      $this->data['s_ex_date'] = $s_ex_date;
      $e_ex_date = !empty($request->get('e_ex_date')) ? trim($request->get('e_ex_date')) : '';
      $this->data['e_ex_date'] = $e_ex_date;

      $page = !empty($request->get('page')) ? trim($request->get('page')) : '1';
      $this->data['page'] = $page;


      $datas = Db::table('consumption_exchange_record')->whereRaw($this->distributor_id_where_sql);
      if($exchange_id!=''){
        $datas = $datas->where('exchange_id', $exchange_id);
      }
      if($searchKey!=''){
        $datas = $datas->where(function ($query) use ($searchKey) {
          $query->where('gift_name', 'like', "%".$searchKey."%")
                ->orWhere('gift_name', 'like', "%".$searchKey."%");
          /*會員搜尋*/
          $user_id_sql = MemberInstance::user_id_sql(['searchKey' =>$searchKey]);
          if($user_id_sql){
            $query->orWhere($user_id_sql);
          }
        });
      }

      if($s_ex_date!=''){
        $datas = $datas->whereRaw('ex_date >='. strtotime($s_ex_date));
      }
      if($e_ex_date!=''){
        $datas = $datas->whereRaw('ex_date <='. strtotime($e_ex_date.' + 1Day'));
      }

      $MemberInstance = new MemberInstance(0);
      $datas = $datas->orderByRaw('id desc')
                    ->paginate(self::PER_PAGE_ROWS)
                    ->appends([
                      'exchange_id' => $exchange_id,
                      'searchKey' => $searchKey,
                      's_ex_date' => $s_ex_date,
                      'e_ex_date' => $e_ex_date,
                    ]);
      if (empty($datas) == false) {
        foreach ($datas->items() as $key => $item) {
          $datas->items()[$key]->index = ($page - 1) * self::PER_PAGE_ROWS + ($key + 1);
          $datas->items()[$key]->ex_date = $item->ex_date ? date('Y-m-d H:i', $item->ex_date) : "";

          /*取得會員資料*/
          $MemberInstance->change_user_id($item->user_id);
          $datas->items()[$key]->user = $MemberInstance->get_user_data();
        }
      }

      $this->data['datas'] = $datas;

      $consumption_exchanges = Db::table('consumption_exchange')->whereRaw($this->distributor_id_where_sql)->orderByRaw('price asc, id desc')->get();
      $this->data['consumption_exchanges'] = CommonService::objectToArray($consumption_exchanges);

      return view('admin.consumption.exchange_list',['data'=>$this->data]);
    }

  /*消費抽抽樂*/
    /*列表*/
    public function draw(Request $request){
      $searchOnline = !empty($request->get('searchOnline')) ? $request->get('searchOnline') : '-1';
      $this->data['searchOnline'] = $searchOnline;
      $searchKey = !empty($request->get('searchKey')) ? trim($request->get('searchKey')) : '';
      $this->data['searchKey'] = $searchKey;

      $datas = Db::table('consumption_draw')->whereRaw($this->distributor_id_where_sql);
      if($searchOnline!='-1'){
        $datas = $datas->where('online', $searchOnline);
      }
      if($searchKey!=''){
        $datas = $datas->where(function ($query) use ($searchKey) {
          $query->where('name', 'like', "%".$searchKey."%")
                ->orWhere('name', 'like', "%".$searchKey."%");
        });
      }

      $datas = $datas->orderByRaw('ratio asc, id desc')->get();
              // ->paginate(self::PER_PAGE_ROWS)
              // ->appends([
              //     'searchOnline' => $searchOnline,
              //     'searchKey' => $searchKey,
              // ]);

      $datas= CommonService::objectToArray($datas);
      foreach ($datas as $key => $value) {
        $datas[$key]['winner'] = Db::table('consumption_draw_record')->where('draw_id', $value['id'])->count();
      }
      $this->data['datas'] = $datas;

      // 滿多少元抽一次
      $limit_price = Db::table('consumption_draw_limit')->whereRaw($this->distributor_id_where_sql)->first();
      $limit_price = CommonService::objectToArray($limit_price);
      if($limit_price){
        $limit_price = $limit_price['price'];
      }else{
        $limit_price = self::DEFAULT_LIMIT_PRCIE;
      }
      $this->data['limit_price'] = $limit_price;

      return view('admin.consumption.draw',['data'=>$this->data]);
    }
    /*新增、修改*/
    public function draw_save(Request $request){
      $data = [
        'name' => !empty($request->post('name')) ? $request->post('name') : "",
        'ratio' => !empty($request->post('ratio')) ? $request->post('ratio') : "",
        'online' => !empty($request->post('online')) ? $request->post('online') : 1,
      ];

      if(array_key_exists('id', $request->post()) == false){$this->error('儲存失敗');}
      $id = $request->post('id');

      if(!$data['name']){$this->error('請輸入獎品說明');}
      if($data['ratio']===""){$this->error('請輸入數量');}

      if($id=='0'){ /*新增*/
        if($this->admin_type=='distribution'){
          $data['distributor_id'] = session()->get($this->admin_type)['id'];
        }
        $id = DB::table('consumption_draw')->insertGetId($data);
      }
      else{ /*編輯*/
        if(!parent::check_controll('consumption_draw', $id)){
          $this->error('您無法操作此項目');
        }
        DB::table('consumption_draw')->where('id', $id)->update($data);
      }

      // 處理圖片
      if(!empty($request->post('pic'))){
        $image_base64 = !empty($request->post('pic')) ? $request->post('pic') : "";
        if( substr($image_base64, 0, 4) == 'data' ){
          $path_to_file = CommonService::uploadFile('/public/static/index/upload', $image_base64, 'draw_'.$id);
          $path_to_file = explode("/", $path_to_file);
          $data['pic'] = 'upload/'. end($path_to_file);
          DB::table('consumption_draw')->where('id', $id)->update($data);
        }
      }

      $this->success('儲存成功');
    }
    /*刪除*/
    public function draw_delete(Request $request){
      if(array_key_exists('id', $request->post()) == false){$this->error('刪除失敗');}
      if(!parent::check_controll('consumption_draw', $request->post('id'))){
        $this->error('您無法操作此項目');
      }
      DB::table('consumption_draw')->where('id', $request->post('id'))->delete();
      $this->success('刪除成功');
    }
    /*修改滿多少元抽一次*/
    public function draw_limit_save(Request $request){
      $data = [
        'price' => !empty($request->post('price')) ? $request->post('price') : "",
      ];
      if($data['price']==="" || $data['price']==0){$this->error('請輸金額');}

      if($this->admin_type=='distribution'){
        $consumption_draw_limit = DB::table('consumption_draw_limit')->whereRaw($this->distributor_id_where_sql)->first();
        $consumption_draw_limit = CommonService::objectToArray($consumption_draw_limit);
        if($consumption_draw_limit){
          DB::table('consumption_draw_limit')->where('id', $consumption_draw_limit['id'])->update($data);
        }else{
          $data['distributor_id'] = session()->get($this->admin_type)['id'];
          DB::table('consumption_draw_limit')->where('id', $consumption_draw_limit['id'])->insert($data);
        }

      }else{
        DB::table('consumption_draw_limit')->where('id', 1)->update($data);
      }
      $this->success('儲存成功');
    }

    /*展示中獎名單*/
    public function draw_list(Request $request){
      $draw_id = !empty($request->get('draw_id')) ? $request->get('draw_id') : '';
      $this->data['draw_id'] = $draw_id;
      $searchKey = !empty($request->get('searchKey')) ? trim($request->get('searchKey')) : '';
      $this->data['searchKey'] = $searchKey;
      $s_date = !empty($request->get('s_date')) ? trim($request->get('s_date')) : '';
      $this->data['s_date'] = $s_date;
      $e_date = !empty($request->get('e_date')) ? trim($request->get('e_date')) : '';
      $this->data['e_date'] = $e_date;
      $s_ex_date = !empty($request->get('s_ex_date')) ? trim($request->get('s_ex_date')) : '';
      $this->data['s_ex_date'] = $s_ex_date;
      $e_ex_date = !empty($request->get('e_ex_date')) ? trim($request->get('e_ex_date')) : '';
      $this->data['e_ex_date'] = $e_ex_date;

      $page = !empty($request->get('page')) ? trim($request->get('page')) : '1';
      $this->data['page'] = $page;

      $datas = Db::table('consumption_draw_record')->whereRaw($this->distributor_id_where_sql);
      if($draw_id!=''){
        $datas = $datas->where('draw_id', $draw_id);
      }
      if($searchKey!=''){
        $datas = $datas->where(function ($query) use ($searchKey) {
          $query->whereRaw("gift_name like '%$searchKey%'")
                ->orWhere('gift_name', 'like', "%".$searchKey."%");
          /*會員搜尋*/
          $user_id_sql = MemberInstance::user_id_sql(['searchKey' =>$searchKey]);
          if($user_id_sql){
            $query->orWhere($user_id_sql);
          }
        });
      }

      if($s_date!=''){
        $datas = $datas->where('createdate >='. strtotime($s_date));
      }
      if($e_date!=''){
        $datas = $datas->where('createdate <='. strtotime($e_date.' + 1Day'));
      }
      if($s_ex_date!=''){
        $datas = $datas->where('ex_date >='. strtotime($s_ex_date));
      }
      if($e_ex_date!=''){
        $datas = $datas->where('ex_date <='. strtotime($e_ex_date.' + 1Day'));
      }

      $MemberInstance = new MemberInstance(0);
      $datas = $datas->orderByRaw('id desc')
              ->paginate(
                self::PER_PAGE_ROWS
              )->appends([
                'draw_id' => $draw_id,
                'searchKey' => $searchKey,
                's_date' => $s_date,
                'e_date' => $e_date,
                's_ex_date' => $s_ex_date,
                'e_ex_date' => $e_ex_date,
              ]);

      if (empty($datas->items()) == false) {
        foreach ($datas->items() as $key => $item) {
          $datas->items()[$key]->index = ($page - 1) * self::PER_PAGE_ROWS + ($key + 1);
          $datas->items()[$key]->createdate = $item->createdate ? date('Y-m-d H:i', $item->createdate) : "";
          $datas->items()[$key]->ex_date = $item->ex_date ? date('Y-m-d H:i', $item->ex_date) : "";

          /*取得會員資料*/
          $MemberInstance->change_user_id($item->user_id);
          $datas->items()[$key]->user = $MemberInstance->get_user_data();
        }
      }

      // dump($datas->items());
      $this->data['datas'] = $datas;

      $consumption_draws = Db::table('consumption_draw')->whereRaw($this->distributor_id_where_sql)->orderByRaw('ratio asc, id desc')->get();
      $consumption_draws = CommonService::objectToArray($consumption_draws);

      $this->data['consumption_draws'] = $consumption_draws;

      return view('admin.consumption.draw_list',['data'=>$this->data]);
    }


  /*掃碼付款管理*/
    /*列表*/
    public function pay_list(Request $request){
      $searchKey = !empty($request->get('searchKey')) ? trim($request->get('searchKey')) : '';
      $this->data['searchKey'] = $searchKey;

      $datas = Db::table('consumption_pay_record as cpr')
                  ->select('cpr.*','cpr.datetime as f_datetime')
                  //->select('cpr.*','FROM_UNIXTIME(cpr.datetime) as f_datetime')
                  ->where('cpr.audit', 0)
                  ->whereRaw($this->distributor_id_where_sql);
      if($searchKey!=''){
        $datas = $datas->where(function ($query) use ($searchKey) {
          $query->where('name', 'like', "%".$searchKey."%")
                ->orWhere('name', 'like', "%".$searchKey."%");
        });
      }

      $datas = $datas->orderByRaw('datetime desc , id desc')->get();
      $datas = CommonService::objectToArray($datas);
      foreach ($datas as $key => $value) {
        $datas[$key]['user'] = Db::connection('main_db')->table('account')->find($value['user_id']);
      }
      $this->data['datas'] = $datas;

      return view('admin.consumption.pay_list',['data'=>$this->data]);
    }
    /*修改(審核通過)*/
    public function pay_list_save(Request $request){
      $id = !empty($request->post('id')) ? $request->post('id') : '0';
      if(!parent::check_controll('consumption_pay_record', $id)){
        $this->error('您無法操作此項目');
      }
      $consumption_pay_record = DB::table('consumption_pay_record')->find($id);
      $consumption_pay_record = CommonService::objectToArray($consumption_pay_record);
      if(!$consumption_pay_record){$this->error('無此付款資料');}

      $data = [
        'audit' => !empty($request->post('audit')) ? $request->post('audit') : "",
      ];

      if($data['audit']===""){$this->error('請設定審核狀態');}

      if($data['audit']=='1'){ /*審核通過*/
        // 依照付款內容建立訂單
        $Cart = new Cart(request());
        $insertData = [
          'distributor_id' => $consumption_pay_record['distributor_id'],
          'user_id' => $consumption_pay_record['user_id'],
          'order_number' => $Cart->create_order_number(),
          'create_time' => time(),
          'over_time' => $Cart->get_eff_dateline(),
          "payment" => "貨到付款",
          "transport" => "到店取貨",
          "transport_location" => "現場購物(掃碼付款)",
          "transport_location_name"	=> "現場購物(掃碼付款)",
          "transport_location_phone"	=> "",
          "transport_location_tele"	=> "",
          "transport_location_textarea"	=> "",
          "transport_email"	=> "",
          'product' => json_encode([
            [
              "name"=>"掃碼付款",
              "price"=>$consumption_pay_record['price'],
              "num"=>1,
              "total"=>$consumption_pay_record['price'],
            ]
          ], JSON_UNESCAPED_UNICODE),
          "add_point" => 0,
          "total" => $consumption_pay_record['price'],
          "discount"	=> "[]",
          "freediscount" => 0,
          "receipts_state" => 1,
          "transport_state" =>1,
          "uniform_numbers"	=> "",
          "company_title"	=> "",
          'ps' => "掃碼付款",
          "status" => "Complete",
        ];
        $order_id = Db::connection('main_db')->table('orderform')->insertGetId($insertData);

        if( empty(config('control.close_function_current')['消費抽抽樂']) ){
          // 處理抽抽樂優惠
          $MemberInstance = new MemberInstance($consumption_pay_record['user_id']);
          $MemberInstance->set_lucky_draw($consumption_pay_record['price'], $id, $order_id);
        }
      }

      DB::table('consumption_pay_record')->where('id', $id)->update($data);
      $this->success('付款資料已審核通過');
    }
    /*刪除*/
    public function pay_list_delete(Request $request){
      if(empty($request->post('id'))){$this->error('刪除失敗');}
      if(!parent::check_controll('consumption_pay_record', $request->post('id'))){
        $this->error('您無法操作此項目');
      }
      DB::table('consumption_pay_record')->where('id', $request->post('id'))->delete();
      $this->success('刪除成功');
    }
}