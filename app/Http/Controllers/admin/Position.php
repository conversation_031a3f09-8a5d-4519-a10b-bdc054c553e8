<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;

class Position extends MainController{
  private $tableName;

  public function __construct() {
    parent::__construct();
    $this->tableName = 'position';
  }

  public function index(Request $request) {
    $dis = Db::table($this->tableName)->whereRaw($this->distributor_id_where_sql)->orderByRaw('name asc, id desc')->get();
    $dis = CommonService::objectToArray($dis);

    array_walk($dis, function($item,$key)use(&$dis){
      $dis[$key]['used_num'] = Db::table('position_portion')->whereRaw("position_id = '".$item['id']."' and product_id != '0'")->count();
    });
    $this->data['dis'] = $dis;
    return view('admin.position.position',['data'=>$this->data]);
  }

  public function edit(Request $request) {
    // dump($request->post());exit;
    $type = $request->post('type');
    $id = $request->post('id');
    $use_pos = Db::table('position_portion')->whereRaw("position_id = '".$id."' and product_id != '0'")->count();

    if($type == 'delete' ){
      if($use_pos > 0){
        return '已使用'.$use_pos.'個位置，請先清除';
      }
    }
    switch ($type) {
      case "add":
        $data['name'] = $request->post('name');
        $data['number'] = $request->post('number') ?? 0;
        $data['max'] = $request->post('max');
        /*供應商*/
        if($this->admin_type=='distribution'){
          $data['distributor_id'] = session()->get($this->admin_type)['id'];
        }
        if(Db::table($this->tableName)->insert($data)){
          return 'success';
        }else{
          return '新增失敗';
        }
        break;

      case "delete":
        if(!parent::check_controll($this->tableName, $id)){
          return '您無法操作此項目';
        }
        if(Db::table($this->tableName)->where('id',$id)->delete()){
          return 'success';
        }else{
          return '失敗';
        }
        break;		

      case "update":
        if(!parent::check_controll($this->tableName, $id)){
          return '您無法操作此項目';
        }
        $data['number'] = $request->post('number');
        $data['max'] = $request->post('max');
        // dump($data);
        $ori_position = Db::table($this->tableName)->select('number','max')->find($id);
        $ori_position = CommonService::objectToArray($ori_position);
        // dump($ori_position);
        if($use_pos > 0){
          return '已使用的庫存區不可修改上限';
          break;
        }else{
          if($ori_position['number'] > $data['number']  && $ori_position['max'] !=1){
            return '不可縮減數量';
            break;
          }
          if($ori_position['max'] == 1 && $data['max'] ==0){
            return '無上限不可調整回有上限';
            break;
          }
        }

        if(Db::table($this->tableName)->where('id',$id)->update($data)){
          return 'success';
        }else{
          return '無須更新資料';
        }
        break;
    }
  }
}