<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
//Photonic Class
use App\Services\CommonService;
use controllerInterface\resources\SoleType;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Stronghold extends MainController{
  private $DBTextConnecter;
  private $DBTextConnecter2;
  private $resTableName;
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('typeinfo_str');
    $this->DBTextConnecter2 = DBTextConnecter::withTableName('stronghold');
    $this->resTableName = 'typeinfo_str';
  }

  public function index(Request $request){
    $id=$request->get('id');
    $stronghold = Db::table('stronghold')->orderByRaw('order_id asc, id desc')->get();
    $stronghold = CommonService::objectToArray($stronghold);
    $this->data['stronghold'] = $stronghold;

    if($id==""){
      if($stronghold){ $id = $stronghold[0]['id']; }
    }
    $distrtitle = Db::table('stronghold')->select('id','title')->where('id',$id)->orderByRaw('order_id asc, id desc')->get();
    $distrtitle = CommonService::objectToArray($distrtitle);
    if($distrtitle){
      $distrtitle = $distrtitle[0]['title'];
    }else{
      $distrtitle = "";
      $id = "";
    }
    $this->data['distrtitle'] = $distrtitle;
    $this->data['parent_id'] = $id;

    $searchKey = trim($request->get('searchKey') ?? '');
    $this->data['searchKey'] = $searchKey;
    $typeinfo_str = Db::table($this->resTableName)
                    ->whereRaw("title LIKE '%$searchKey%' and  parent_id = '".$id."' ")
                    ->orderBy('orders', 'asc')
                    ->orderByRaw('time desc')
                    ->paginate(self::PER_PAGE_ROWS)
                    ->appends([
                        'searchKey' => $searchKey
                    ]);
    $this->data['typeinfo_str'] = $typeinfo_str;
    return view('admin.stronghold.index',['data'=>$this->data]);
  }
  
  public function doCreate(Request $request){
    $width = 700; $height = 475;
    $newData = $request->post();
    unset($newData['id']);
    unset($newData['json_sub_pics']);
    unset($newData['_token']);
    try{
      $this->DBTextConnecter->setDataArray($newData);
      $mainId = $this->DBTextConnecter->createTextRow();
      $newData['id'] = $mainId;

      /*處理大圖*/
      $image = $request->file('image');
      if($image){   
        $DBFileConnecter = new DBFileConnecter();
        $newData['pic'] = $DBFileConnecter->fixedFileUp($image, 'stronghold_' . $mainId, $width, $height);
      }

      /*處理附圖*/
      $sub_pics = $request->file('sub_pics');
      // dump($sub_pics);
      if($sub_pics){
        $DBFileConnecter = new DBFileConnecter();
        foreach ($sub_pics as $key => $value) {
        $sub_pics[$key] = $DBFileConnecter->fixedFileUp($value, 'stronghold_'.$mainId.'_sub'.$key.'_'.time());
        }
        // dump($sub_pics);
        $newData['sub_pics'] = json_encode($sub_pics, JSON_UNESCAPED_UNICODE);
      }

      /*更新圖片資料*/
      if($image || $sub_pics){
        $this->DBTextConnecter->setDataArray($newData);
        $this->DBTextConnecter->upTextRow();
      }     	

      // 自動更新排序
      if( isset($newData['orders']) ){
        $table = $this->resTableName;
        $column = 'orders';
        $order_num = $newData['orders'];
        $primary_key = 'id';
        $primary_value = $mainId;
        $filter_where = 'parent_id='.$newData['parent_id'];
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
        unset($newData['orders']);
      }

      ob_clean();
      echo('<h1>上傳成功</h1>');die();
    }catch (\Exception $e){
      ob_clean();
      echo($e->getMessage());die();
    }
  }

  public function update(Request $request){
    $width = 700; $height = 475;
    $newData = $request->post();
    // dump($newData);
    unset($newData['_token']);

    try{
      /* 處理主圖 */
      $image = $request->file('image');
      if($image){
        $DBFileConnecter = new DBFileConnecter();
        $newData['pic'] = $DBFileConnecter->fixedFileUp($image, 'stronghold_' . $newData['id'], $width, $height);
      }

      /* 處理附圖 */
      $sub_pics = json_decode($newData['json_sub_pics']);
      $sub_pics_files = $request->file('sub_pics');
      // dump($sub_pics_files);
      if($sub_pics_files){
        $DBFileConnecter = new DBFileConnecter();
        $sub_count = 0;
        foreach ($sub_pics as $key => $value) {
          if( substr($value, 0, 7) != "/upload" && $sub_pics_files[$sub_count]){
            $sub_pics[$key] = $DBFileConnecter->fixedFileUp($sub_pics_files[$sub_count], 'stronghold_'.$newData['id'].'_sub'.$key.'_'.time());
            $sub_count+=1;
          }
        }
      }
      // dump($sub_pics);
      $newData['sub_pics'] = json_encode($sub_pics, JSON_UNESCAPED_UNICODE);
      unset($newData['json_sub_pics']);

      /*更新資料*/
      // dump($newData);exit;
      $this->DBTextConnecter->setDataArray($newData);
      $this->DBTextConnecter->upTextRow();

      // 自動更新排序
      if( isset($newData['orders']) ){
        $table = $this->resTableName;
        $column = 'orders';
        $order_num = $newData['orders'];
        $primary_key = 'id';
        $primary_value = $newData['id'];
        $filter_where = 'parent_id='.$newData['parent_id'];
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
        unset($newData['orders']);
      }

      ob_clean();
      echo('<h1>上傳成功</h1>');die();
    }catch (\Exception $e){
      ob_clean();
      echo($e->getMessage());die();
    }
  }

  public function delete(Request $request){
    $id = $request->get('id');
    $parent_id = $request->get('parent_id');

    try{
        Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
        $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  public function multiDelete(Request $request){
    $idList = $request->post('id');
    try{
        if ($idList) {
            $idList = json_decode($idList);dd($idList);
            DB::table($this->resTableName)->whereIn('id', $idList)->delete();
        }
    } catch (\Exception $e){
        $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  /*AJAX*/
  public function cellCtrl(Request $request){
    try{
      $updateData = $request->post();
      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function dodelete(Request $request) {
    $id = $request->get('id');
    $url = $request->get('url');

    $deleteData = Db::table('typeinfo_str')->select('id')->where('parent_id', $id)->get();

    if (sizeof($deleteData)) {
      $this->error('內部還有經銷據點，請先清空');
    }
    Db::table('stronghold')->delete($id);
    $this->success('刪除成功', $url);
  }

  /*AJAX*/
  public function create(Request $request) {
    try{
      $updateData = $request->post();
      $this->DBTextConnecter2->setDataArray($updateData);
      $id = $this->DBTextConnecter2->createTextRow();

      // 自動更新排序
      $table = 'stronghold';
      $column = 'order_id';
      $order_num = 0;
      $primary_key = 'id';
      $primary_value = $id;
      parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);

      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function updatetitle(Request $request){
    $newData = $request->post();
    $cate = Db::table('stronghold')->whereRaw('id ='.$newData['id'])->update(['title' =>$newData['title']]);
    
    if($cate == null){
      $this->error('商品不存在');
    }
    $this->success('更新成功');
  }
}
