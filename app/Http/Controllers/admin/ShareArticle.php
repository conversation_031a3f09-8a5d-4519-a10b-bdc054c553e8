<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
//photonicClass
use App\Services\CommonService;
use App\Services\pattern\MemberArticle;

class ShareArticle extends MainController{
  public function __construct(Request $request) {
    parent::__construct();
  }

  public function index(Request $request) {
    return view('admin.share_article.index', ['data'=>$this->data]);
  }
  public function get_share_article(){
    $request = request();
    $search = $request->post();
    $data_page = MemberArticle::get_share_article($search);

    $user_ids = array_merge([-1], array_map(function($item){ return  $item['user_id']; }, $data_page['db_data']));
    $users = Db::connection('main_db')->table('account')->selectRaw('id, number, name, phone')->whereIn('id', $user_ids)->get()->keyBy('id');
    
    unset($search['count_of_items']);
    $data_all = MemberArticle::get_share_article($search);   
    
    return [
      'records_show' => $data_page['db_data'],
      'records_total' => count($data_all['db_data']),
      'users' => $users,
    ];
  }
  public function save_share_article(){
    $request = request();
    $data = $request->post();
    $data['id'] = $data['id'] ?? 0;
    if($data['id']==0){ /*新增*/
      $this->error(Lang::get('無此操作'));
    }
    else{ /*編輯*/
      try {
        MemberArticle::edit_article([
          'id' => $data['id'] ?? '',
          'name' => $data['name'] ?? '',
          'show' => $data['show'] ?? '',
          'img' => $data['img'] ?? '',
          'content' => $data['content'] ?? '',
          'orders' => $data['orders'] ?? '',
        ]);
      } catch (\Throwable $th) {
        $this->error($th->getMessage());
      }
    }
    $this->success(Lang::get('操作成功'));
  }
  public function delete_share_article(){
    $request = request();
    $id = $request->post('id');
    try {
      MemberArticle::delete_share_article($id);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success(Lang::get('操作成功'));
  }
}