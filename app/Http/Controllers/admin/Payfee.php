<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Http\Controllers\admin\CartMethod;
use App\Services\CommonService;

class Payfee extends CartMethod{
  public const DB_TABLE_NAME = 'pay_fee';

  public function __construct() {
    parent::__construct('pay_fee');
  }

  public function index(Request $request, $view_html=''){
    return parent::index($request, 'admin.cartmethod.payfee');
  }

  /*覆寫 不允許刪除、新增*/
  public function delete(Request $request) {}
  public function multiDelete(Request $request) {}
  public function doCreate(Request $request) {}

  public static function get_payment_name($payment_id=''){
    $pay_fee_dict = self::get_pay_fee_dict();
    $payment_name = isset($pay_fee_dict['k_'.$payment_id]) ? $pay_fee_dict['k_'.$payment_id]['name'] : '';
    return $payment_name;
  }
  public static function get_pay_fee_dict($ajax=false){
    $pay_fee_dict = [];
    $pay_fee = Db::table(self::DB_TABLE_NAME)->get();
    $pay_fee=CommonService::objectToArray($pay_fee);
    foreach ($pay_fee as $value) {
      $pay_fee_dict['k_'.$value['id']] = $value;
    }
    // dump($pay_fee_dict);exit;
    if($ajax){
      $pay_fee_dict = json_encode($pay_fee_dict, JSON_UNESCAPED_UNICODE);
    }
    return $pay_fee_dict;
  }
}