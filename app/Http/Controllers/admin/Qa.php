<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
//Photonic Class
use App\Services\DBtool\DBTextConnecter;

class Qa extends MainController{
  private $DBTextConnecter;
  private $resTableName;
  const PER_PAGE_ROWS = 20;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('qa');
    $this->resTableName = 'qa';
  }

  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $this->data['searchKey'] = $searchKey;
    $category = $request->get('category') ?? '';
    $category = trim($category);
    $this->data['category'] = $category;
    
    $qa = Db::table($this->resTableName)
      ->whereRaw("q LIKE '%$searchKey%' OR a LIKE '%$searchKey%'");

    if($category){
    $qa = $qa->where("category", $category);
    }

    $qa = $qa->orderByRaw('order_id')
      ->paginate(self::PER_PAGE_ROWS)
      ->appends([
        'searchKey' => $searchKey
      ]);

    $this->data['qa'] = $qa;
    return view('admin.qa.qa',['data'=>$this->data]);
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    try{
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        Db::table($this->resTableName)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
  
  /*AJAX*/
  public function cellCtrl(Request $request) {
    try{
      $updateData = $request->post();

      // 自動更新排序
      if( isset($updateData['order_id']) ){
        $table = $this->resTableName;
        $column = 'order_id';
        $order_num = $updateData['order_id'];
        $primary_key = 'id';
        $primary_value = $updateData['id'];
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
        unset($updateData['order_id']);
      }

      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function doCreate(Request $request) {
    $newData = $request->post();
    try{
      $this->DBTextConnecter->setDataArray($newData);
      $id = $this->DBTextConnecter->createTextRow();

      // 自動更新排序
      $table = $this->resTableName;
      $column = 'order_id';
      $order_num = 0;
      $primary_key = 'id';
      $primary_value = $id;
      parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);

      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function update(Request $request) {
    $newData = $request->post();
    try{
      $this->DBTextConnecter->setDataArray($newData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }
}