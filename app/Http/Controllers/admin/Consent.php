<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\DBtool\DBTextConnecter;

use App\Services\CommonService;

class Consent extends MainController{
  private $DBTextConnecter;
  private $fixedResourcesRowId = 1;

  public function __construct(){
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('consent');
    
  }

  public function index(Request $request){
    $consent = Db::table('consent')->find($this->fixedResourcesRowId);
    $this->data['consent'] = CommonService::objectToArray($consent);

    $this->data['index_to_db_column'] = [
      'member',
      'point',
      'coupon',
      'shopping',
      'other',
      'privacy',
      'examination',
      'g_process',
      'examinee',
    ];  
    $this->data['index_to_db_column_json'] = json_encode($this->data['index_to_db_column']);

    return view('admin.consent.consent',['data'=>$this->data]);
  }
  
  /*AJAX*/
  public function cellCtrl(Request $request){
    try{
      $updateData = $request->post();
      
      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
      return $outputData;
    }
    return $outputData;
  }
}