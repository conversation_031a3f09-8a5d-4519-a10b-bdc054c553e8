<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Act extends MainController
{
  const ACT_TYPE = 1;
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  private $tableName;
  private $DBTextConnecter;
  protected $associated_tableName;
  protected $associated_column;
  protected $act_type;
  public function __construct($tableName='act', $associated_tableName='act_product') {
    parent::__construct();
    $this->tableName = $tableName;
    $this->associated_tableName = $associated_tableName;
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->tableName);
    $this->act_type = self::ACT_TYPE;

    if($this->associated_tableName=='act_product'){
      $this->associated_column = 'act_id';
    }else if($this->associated_tableName=='coupon_direct_product'){
      $this->associated_column = 'coupon_id';
    }else{
      $this->associated_column = '';
    }
  }

  public function index(Request $request) {
    return view('admin.act.act',['data'=>$this->data]);
  }
  /*列表頁相關功能api*/
  public function getActList(Request $request){
    $type = $request->post('type');
    $searchKey = $request->post('searchKey');
    $start = $request->post('start');
    $end = $request->post('end');
    $id = $request->post('id');

    // var_dump($searchKey);
    if($type == 'keyword' && !empty($searchKey)){
      $list = Db::table($this->tableName)
                ->where('act_type',$this->act_type)
                ->where(function($query) use($searchKey){
                  $query->orWhere([
                    'name' => ['like', '%'.$searchKey.'%'],
                    'number' => ['like', '%'.$searchKey.'%']
                  ]);
                });
      $search = $searchKey;
    }else if($type == 'date'){
      $form=Db::table($this->tableName);
      $list = $form->where('act_type',$this->act_type) /*立馬省*/
                  ->where(function($query)use($start,$end){
                    $query->orWhere([ /*搜尋的時間區包含開始或結束時間*/
                      'start' => ['between', [strtotime($start), strtotime($end)]],
                      'end' => ['between', [strtotime($start), strtotime($end)]]
                    ])
                    ->orWhere(function($query)use($start,$end){ /*搜尋的時間區間在開始結束時間內*/
                      $query->where([
                        'start' => ['elt', strtotime($start)],
                        'end' => ['egt', strtotime($end)]
                      ]);
                    })
                    ->orWhere(function($query)use($start,$end){ /*搜尋的開始時間大於開始時間，且被設定為無結束時間*/
                      $query->where([
                        'start' => ['elt', strtotime($start)],
                        'end' => ['eq', '-28800']
                      ]);
                    });
                  });
      // dump($form->orderByRaw('id', 'desc')->select()->getLastSql());
      $search = $start."~".$start;
    }else{
      $list = Db::table($this->tableName)->where('act_type',$this->act_type);
      $search = "";
    }
    if($id){
      $list = $list->whereRaw('id="'.$id.'"');
    }
    $list = $list->whereRaw($this->distributor_id_where_sql)->orderBy('id', 'desc')->get();
    $list = CommonService::objectToArray($list);

    foreach($list as $liKey => $liValue){
      $list[$liKey]['start'] = date("Y-m-d", $liValue['start']);

      if ($list[$liKey]['end'] == -28800){
        $list[$liKey]['end'] = Lang::get('無時間');
      }else{
        $list[$liKey]['end'] = date("Y-m-d", $liValue['end']);
      }
    }
    $retData = ['actList' => $list, 'search'=>$search];
    return $retData;
  }
  public function changeOnline(Request $request){
    $itemData = $request->post();
    if(!parent::check_controll($this->DBTextConnecter->getTableName(), $itemData['id'])){
      $this->error(Lang::get('您無法編輯此項目'));
    }
    $online =  ($itemData['online'] == 'true') ? 1 : 0;
    Db::table($this->tableName)->where('id',$itemData['id'])->update(['online' => $online]);
    $this->success(Lang::get('操作成功'));
  }
  public function delAct(Request $request){
    $itemData = $request->post();
    if(!parent::check_controll($this->DBTextConnecter->getTableName(), $itemData['id'])){
      $this->error(Lang::get('您無法編輯此項目'));
    }
    $delId = Db::table($this->tableName)->where('id',$itemData['id'])->delete();

    if($this->associated_column){
      $delProd = Db::table($this->associated_tableName)->where($this->associated_column,$itemData['id'])->delete();
    }
    $this->success(Lang::get('操作成功'));
  }
  public function doCreate(Request $request) {
    $newData['act_type']  = $this->act_type; /*活動優惠*/
    $count = $this->getNumber();
    $newData['number'] = config('extra.shop.subDeparment') . 'A' . date('Ymd') . $count;
    $new_id = $this->doCreate_db($request, $newData);
    $this->success(Lang::get('操作成功'), url('Act/edit') . '?' . http_build_query(['id'=>$new_id]));
  }
  private function doCreate_db($request, $newData=[]) {
    // dump($request->post());
    $newData['name']      = $request->post('name');
    if(!$newData['name']){ $this->error(Lang::get('請輸入標題')); }
    $newData['start']     = strtotime($request->post('start_time'));
    if(!$newData['start']){ $this->error(Lang::get('請選擇開始時間')); }
    $newData['end']       = $request->post('noEndTime')!='true' ? strtotime($request->post('end_time')) : -28800;
    if(!$newData['end']){ $this->error(Lang::get('請選擇結束時間')); }
    $newData['type']      = $request->post('type');
    if(!$newData['type']){ $this->error(Lang::get('請輸選擇優惠方式')); }

    $newData['content']   = $request->post('content');
    $newData['online']    = 1;

    if($this->admin_type=='distribution'){
      $newData['distributor_id'] = session()->get($this->admin_type)['id'];
    }

    $newData['online1'] = $request->post('online1') ? 1 : 0;
    $newData['condition1'] = $request->post('condition1');
    $newData['discount1'] = $request->post('discount1');
    $newData['online2'] = $request->post('online2') ? 1 : 0;
    $newData['condition2'] = $request->post('condition2');
    $newData['discount2'] = $request->post('discount2');
    $newData['online3'] = $request->post('online3') ? 1 : 0;
    $newData['condition3'] = $request->post('condition3');
    $newData['discount3'] = $request->post('discount3');

    //dd($newData);
    $this->DBTextConnecter->setDataArray($newData);
    $new_id = $this->DBTextConnecter->createTextRow();
    return $new_id;
  }
  private function getNumber($code='A'){
    $count = Db::table($this->tableName)
                ->whereRaw('number LIKE "'.config('extra.shop.subDeparment').$code.date('Ymd').'%"')
                ->orderByRaw('id desc')->first();
    $count = CommonService::objectToArray($count);

    $count = $count ? intval(substr($count['number'],-3)) + 1 : 1;
    if($count < 10){
      $count = '00' . $count;
    }else if($count < 100){
      $count = '0' . $count;
    }
    return $count;
  }

  public function edit(Request $request) {
    $id = $request->get('id');
    $class = class_basename($this);
    if(!parent::check_controll($this->tableName, $id)){
      $this->error(Lang::get('您無法編輯此項目'));
    }
    $this->data['actId'] = $id;
    // $routeArray = app('request')->route()->getAction();
    // $controllerAction = class_basename($routeArray['controller']);
    // list($controller, $action) = explode('@', $controllerAction);
    // $view=;
    return view('admin.'.$class.'.act-edit',['data'=>$this->data]);
  }
  /*更新活動資料*/
  public function update(Request $request) {
    try{
      $end = $request->post('noEndTime')!='true' ? strtotime($request->post('end_time')) : -28800;
      $newData = [
        'id' => $request->post('id'),
        'name' => $request->post('name'),
        // 'ps' => $request->post('ps'),
        'content' => $request->post('content'),
        'start' => strtotime($request->post('start_time')),
        'end' => $end,
        'location' => $request->post('location'),
        'type' => $request->post('type'),
        // 'online' => 1
      ];
       
      if(!parent::check_controll($this->DBTextConnecter->getTableName(), $newData['id'])){
        throw new \Exception(Lang::get('您無法編輯此項目'), 1);
      }

      if(!$newData['name']){ return ['code'=>1,'msg'=>Lang::get('請輸入標題'),'url'=>'/']; }
      if(!$newData['start']){ return ['code'=>1,'msg'=>Lang::get('請選擇開始時間'),'url'=>'/']; }
      if(!$newData['end']){ return ['code'=>1,'msg'=>Lang::get('請選擇結束時間'),'url'=>'/']; }
      if(!$newData['type']){ return ['code'=>1,'msg'=>Lang::get('請輸選擇優惠方式'),'url'=>'/']; }
      
      $online1 = $request->post('online1');
      $newData['online1'] = $online1=='true' || $online1==1 ? 1 : 0;
      $newData['condition1'] = $request->post('condition1');
      $newData['discount1'] = $request->post('discount1');
      $online2 = $request->post('online2');
      $newData['online2'] = $online2=='true' || $online2==1 ? 1 : 0;
      $newData['condition2'] = $request->post('condition2');
      $newData['discount2'] = $request->post('discount2');
      $online3 = $request->post('online3');
      $newData['online3'] = $online3=='true' || $online3==1 ? 1 : 0;
      $newData['condition3'] = $request->post('condition3');
      $newData['discount3'] = $request->post('discount3');
      
      // 上傳圖片(base64)
      $img_base64 = $request->post('img_base64');
      if($img_base64){
        // dump($img_base64);exit;
        $path = CommonService::uploadFile('/public/static/index/upload', $img_base64, 'act_pic_'.$newData['id']);
        $path = explode('upload', $path);
        if(count($path)==2){
          $path = '/upload'.$path[1];
          $newData['img'] = $path;
        }
      }
      // dump($newData);exit;
      $this->DBTextConnecter->setDataArray($newData);
      $this->DBTextConnecter->upTextRow();
    } catch (\Exception $e) {
      $this->dumpException($e);
    }
    return ['code'=>1,'msg'=>Lang::get('操作成功'),'url'=>'/'];
  }

  public function getCount(Request $request){
    try{
      $act_count = Db::table($this->tableName)
                      ->where('act_type',$this->act_type)
                      ->whereRaw("online = '1' AND 
                        ( 
                          (
                            start <= '".time()."' AND 
                            end >= '".time()."' 
                          ) OR 
                          (
                            start <= '".time()."' AND end = '-28800'
                          ) 
                        )
                      ")->count();
      $outputData = [
        'status' => true,
        'message' => $act_count
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
      return $outputData;
    }
    return $outputData;
  }

  /*讀取活動資料及已套用商品*/
  public function getActProd(Request $request){
    $postData = $request->post();
    $actId = $postData['actId'];
    $actInfo = Db::table($this->tableName)->where('id',$actId)->first();
    $actInfo = CommonService::objectToArray($actInfo);
    
    if($this->admin_type=='distribution'){
      if($actInfo['distributor_id']!=session()->get($this->admin_type)['id']){
        $retData = [
          'status'  => 200,
          'actInfo' => [],
          'actProd' => [],
        ];
        return $retData;
      }
    }

    $actInfo['start'] = date('Y-m-d',$actInfo['start']+8*3600);
    if ($actInfo['end'] == -28800){
      $actInfo['end'] = '';
    }else{
      $actInfo['end'] = date('Y-m-d',$actInfo['end']+8*3600);
    }

    if($this->associated_column){
      $actProd = Db::table($this->associated_tableName.' as ap')
            ->join('productinfo as pi','ap.prod_id','pi.id')
            ->where('ap.'.$this->associated_column,$actId)->get();
      $actProd = CommonService::objectToArray($actProd);
      foreach($actProd as $k => $v){
        $pic1 = json_decode($v['pic'],true);
        if($pic1==null){
          $actProd[$k]['pic1'] = "";
        }else{
          $actProd[$k]['pic1'] = $pic1[0];
        }
        
      }
    }else{
      $actProd = [];
    }

    $retData = [
      'status'  => 200,
      'actInfo' => $actInfo,
      'actProd' => $actProd,
    ];
    return $retData;
  }
  /*加入活動商品*/
  public function insertAct(Request $request){
    $postData   = $request->post();
    $actData    = $postData['actData'];
    $actId      = $postData['actId'];

    if(!parent::check_controll($this->DBTextConnecter->getTableName(), $actId)){
      $this->error(Lang::get('您無法編輯此項目'), 1);
    }

    $result = $this->add_act_prouduct($actId, $actData, $this->admin_type);
    
    $echoData = [
      'status'    => 200,
      'actData'   => $result['actData'],
      'actProd'   => $result['actProd'],
    ];
    $this->success(Lang::get('操作成功'));
  }
  private function add_act_prouduct($insertGetId, $actData){
    if(!$this->associated_column){ return ['actData'=>[], 'actProd'=>[]]; }
    $createSeries   = false;
    $createCate     = false;
    $actSeries  = [];
    $actCate    = [];
    $actProd    = [];
    
    $series = isset($actData['series']) ? $actData['series'] : [];
    
    foreach($series as $serKey => $serValue){
      if (isset($serValue['select'])){
        if($serValue['select'] == 'true'){
          $createSeries = true;
          $seriesProd = DB::table('product')->alias('prod')
                                            ->join('typeinfo ti','prod.id = ti.parent_id')
                                            ->join('productinfo pi','ti.id = pi.parent_id')
                                            ->join($this->associated_tableName.' ap','pi.id=ap.prod_id','LEFT')
                                            ->where('prod.id',$serValue['id'])
                                            ->where('ap.act_prod_id is null')->get();
          foreach($seriesProd as $spKey => $spValue){
            $actSeries[] = $spValue;
          }
        }//if
      }//if
    }//foreach

    $cate = isset($actData['cate']) ? $actData['cate'] : [];
    foreach($cate as $cateKey => $cateValue){
      if (isset($cateValue['select'])){
        if($cateValue['select'] == 'true'){
          $createCate = true;
          $cateProd = DB::table('typeinfo')->alias('ti')
                                          ->join('productinfo pi','ti.id = pi.parent_id')
                                          ->join($this->associated_tableName.' ap','pi.id=ap.prod_id','LEFT')
                                          ->where('ti.id',$cateValue['id'])
                                          ->where('ap.act_prod_id is null')->get();
          // echo $sql = Db::table('typeinfo')->getLastSql();
          foreach($cateProd as $cpKey => $cpValue){
            $actCate[] = $cpValue;
          }
        }//if
      }//if
    }//foreach

    if ($createSeries){
      foreach ($actSeries as $asKey => $asValue){
        $actProd[] = [
          $this->associated_column    => $insertGetId,
          'prod_id'   => $asValue['id']
        ];
      }//foreach
    }elseif($createCate){
      foreach ($actCate as $acKey => $acValue){
        $actProd[] = [
          $this->associated_column    => $insertGetId,
          'prod_id'   => $acValue['id']
        ];
      }//foreach
    }elseif(isset($actData['cateProd'])){
      foreach ($actData['cateProd'] as $cateProdKey => $cateProdValue){
        if (isset($cateProdValue['select'])){
          if($cateProdValue['select'] == 'true'){
            $actProd[] = [
              $this->associated_column    => $insertGetId,
              'prod_id'   => $cateProdValue['id']
            ];
          }//if
        }//if
      }//foreach
    }//else

    $actProd_yours = [];
    foreach ($actProd as $key => $value) {
      $productinfo = Db::table('productinfo')->find($value['prod_id']);
      $productinfo = CommonService::objectToArray($productinfo);
      if(!$productinfo){ continue; }
      if($this->admin_type=='distribution'){
        if($productinfo['distributor_id']==session()->get($this->admin_type)['id']){
          array_push($actProd_yours, $value);
        }
      }else{
        if($productinfo['distributor_id']==0){
          array_push($actProd_yours, $value);
        }
      }
    }
    //dd($actProd_yours);exit;
    Db::table($this->associated_tableName)->insert($actProd_yours);

    return ['actData' => $actData, 'actProd' => $actProd];
  }
  
  /*刪除活動商品*/
  public function delActProd(Request $request){
    if(!$this->associated_column){ return ['status'=>500, 'delProd'=>[]]; }

    $postData = $request->post();
    $actId = $postData['actId'];
    $delProd = $postData['cateProd'];
    foreach($delProd as $dpKey => $dpValue){
      if (isset($dpValue['select'])) {
        if ($dpValue['select'] == 'true') {
          $act_product = Db::table($this->associated_tableName)->where('prod_id',$dpValue['id'])->first();
          $act_product = CommonService::objectToArray($act_product);
          if($act_product){
            if(!parent::check_controll($this->tableName, $act_product[$this->associated_column])){
              $this->error(Lang::get('您無法編輯此項目'));
            }
          }
        }
      }
    }
    foreach($delProd as $dpKey => $dpValue){
      if (isset($dpValue['select'])) {
        if ($dpValue['select'] == 'true') {
          Db::table($this->associated_tableName)
            ->where($this->associated_column,$actId)
            ->where('prod_id',$dpValue['id'])
            ->delete();
        }
      }
    }

    $retData = [
      'status'  => 200,
      'delProd' => $delProd,
    ];
    return ['code'=>1,'msg'=>Lang::get('操作成功'),'url'=>'/'];
  }
}