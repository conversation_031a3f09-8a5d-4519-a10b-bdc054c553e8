<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class About extends MainController {

  private $DBTextConnecter;
  private $fixedResourcesRowId = 1;

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('about_story');
  }

  public function index(Request $request) {
    $about_story = Db::table('about_story')->find($this->fixedResourcesRowId);
    $this->data['about_story'] = CommonService::objectToArray($about_story);
    return view('admin.about.about',['data'=>$this->data]);
  }

  public function update(Request $request) {
    $newData = $request->post();
    unset($newData['_token']);
    $newData['id'] = $this->fixedResourcesRowId;
    $picNameList = ['image_left_top', 'image_right_top', 'image_right_bottom'];
    $picSizeList = [
      ['width' => 200, 'height' => 200], 
      ['width' => 382, 'height' => 255], 
      ['width' => 380, 'height' => 254]
    ];

    try{
      $DBFileConnecter = new DBFileConnecter();
      for($i = 1; $i <= 3; $i++){
        $image = $request->file('image' . $i);
        if($image){
          $imageName = 'about_' . $picNameList[$i-1];
          $newDataColumnName = $picNameList[$i-1];

          $newData[$newDataColumnName] = $DBFileConnecter->fixedFileUp(
            $image, 
            $imageName, 
            $picSizeList[$i-1]['width'], 
            $picSizeList[$i-1]['height']
          );
        }
      }
      $this->DBTextConnecter->setDataArray($newData);
      $this->DBTextConnecter->upTextRow();
    } catch (\Exception $e) {
      $this->dumpException($e);
    }
    $this->success('更新成功');
  }
}