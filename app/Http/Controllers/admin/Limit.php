<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Http\Controllers\admin\Productinfo;

class Limit extends MainController
{
  const PER_PAGE_ROWS = 20;
  const SIMPLE_MODE_PAGINATE = false;

  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $this->data['searchKey'] = $searchKey;

    $kol_id = $request->get('kol_id') ?? '-1';
    $this->data['kol_id'] = $kol_id;
    if($kol_id == -1){ // 不依搜尋網紅
      $kol_search = ' ( kp.is_using=1 OR kp.kol_id is null)';
    }else if($kol_id == 0){ // 搜尋無網紅
      $kol_search = ' ( (kp.kol_id =0 AND kp.is_using=1) OR kp.kol_id is null )';
    }else{ // 搜尋某網紅的
      $kol_search = ' kp.kol_id ='.$kol_id.' AND kp.is_using=1';
    }

    $orderGet = $request->get('orderGet');
    $order = "productinfo_create_time desc, productinfo_title asc, productinfo_type_num asc";
    if(!($orderGet == '' || $orderGet == null)){
      switch ($orderGet) {
        case 'num':
          $order = 'productinfo_type_num desc';
          break;
        case 'create_time':
          $order = 'productinfo.create_time desc';
          break;
      }
    }

    $view_way = $request->get('view_way') ? $request->get('view_way') : 'limit';
    if($view_way == 'limit'){
      $view_way_where = 'productinfo_type.num <= productinfo_type.limit_num';
    }elseif($view_way == 'all'){
      $view_way_where = 'true';
    }else{
      $view_way_where = 'false';
    }
    $this->data['view_way'] = $view_way;

    $limit_num = Db::table('productinfo')
                    ->select('productinfo.title AS productinfo_title', 
                          'productinfo.create_time AS productinfo_create_time', 
                          'productinfo.ISBN AS productinfo_ISBN', 
                          'productinfo.id AS productinfo_id', 
                          'productinfo.r_repeat AS r_repeat',
                          'productinfo_type.id AS type_id',
                          'productinfo_type.title AS productinfo_type_title',
                          'productinfo_type.num AS productinfo_type_num',
                          'productinfo_type.limit_num',
                          'kol.kol_name')
                    ->whereRaw($view_way_where."
                      AND (
                        productinfo_type.title like  '%" . $searchKey . "%' OR 
                        productinfo.ISBN like  '%" . $searchKey . "%' OR 
                        productinfo.title like  '%" . $searchKey . "%'
                      )
                      AND 
                      productinfo_type.online = 1 AND
                      ".$kol_search
                    )
                    ->whereRaw($this->distributor_id_where_sql)
                    ->join('productinfo_type','productinfo_type.product_id','productinfo.id')
                    ->leftJoin('kol_productinfo as kp', 'kp.productinfo_id','productinfo.id')
                    ->leftJoin('kol','kol.id','kp.kol_id')
                    ->orderByRaw($order)
                    ->paginate(self::PER_PAGE_ROWS)
                    ->appends([
                      'view_way' => $view_way,
                      'kol_id' => $kol_id,
                      'searchKey' => $searchKey,
                      'orderGet' => $orderGet,
                    ]);
    $limit_num_items = $limit_num->items();
    $limit_num_items = CommonService::objectToArray($limit_num_items);
    array_walk($limit_num_items, function($item,$key)use(&$limit_num_items){
      $result = Db::table('position_portion')->whereRaw("product_id = '".$item['productinfo_id']."' and productinfo_type = '".$item['type_id']."' and num!=0");

      if($item['r_repeat']==0){ // 依實體編碼
        $limit_num_items[$key]['productinfo_type_total']=$result->count();
      }else{ // 依品項編碼
        if (empty($result->first()) == false) {
          $limit_num_items[$key]['productinfo_type_total']=$result->first()->num;
        } else {
          $limit_num_items[$key]['productinfo_type_total']=0;
        }
      }
    });
    // dump($limit_num_items);exit;
    $this->data['limit_num_items'] = $limit_num_items;
    $this->data['limit_num'] = $limit_num;

    $kol = Db::table('kol')->orderByRaw('id desc')->get();
    $this->data['kol'] =  CommonService::objectToArray($kol);

    return view('admin.limit.warning',['data'=>$this->data]);
  }

  public function getCount(Request $request){
    try{
      $totalCount = Db::table('productinfo')
              ->select('productinfo.title AS productinfo_title', 
              'productinfo.create_time AS productinfo_create_time', 
              'productinfo.product_id AS productinfo_product_id', 
              'productinfo.id AS productinfo_id', 
              'productinfo_type.title AS productinfo_type_title',
              'productinfo_type.num AS productinfo_type_num')
              ->whereRaw("productinfo_type.num <=  productinfo_type.limit_num AND
                  productinfo_type.online = 1")
              //->whereRaw($this->distributor_id_where_sql)
              ->leftjoin('productinfo_type','productinfo_type.product_id','productinfo.id')
              ->count();
              
      $outputData = [
        'status' => true,
        'message' => $totalCount
      ];
    }catch (\Exception $e){

      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
      return $outputData;
    }

    return $outputData;
  }

  /*修改庫存數量*/
  public function change_type_num(Request $request){
    $type_id = $request->get('type_id')??'';
    $new_type_num = $request->get('new_type_num')??'';

    if($type_id=='') $this->error('未提供品項');
    if($new_type_num=='') $this->error('未提供數量');

    $productinfo_type = Db::table('productinfo_type')->find($type_id);
    if(!$productinfo_type) $this->error('無此品項');
    $productinfo = Db::table('productinfo')->find($productinfo_type->product_id);
    $productinfo = CommonService::objectToArray($productinfo);
    if(!$productinfo) $this->error('無此商品');

    if(!parent::check_controll('productinfo', $productinfo['id'])){
      $this->error('您無法操作此項目');
    }

    /*取得商品編輯品項的格式資料*/
    $items = Productinfo::get_product_types($productinfo['id']);
    foreach ($items as $key => $value) {
      if($value['id']==$type_id){ /*找到目標品項*/
        if(isset(config('control.close_function_current')["存放位置管理"])){ /*無使用庫存編碼*/
          $items[$key]['online_num'] = $new_type_num;
        }
        else{  /*有使用庫存編碼*/
          $items[$key]['num'] = $new_type_num;
        }
        break;
      }
    }
    // dump($items);exit;
    $items = json_encode($items);
    $Productinfo = new Productinfo($request);
    $result = $Productinfo->deal_with_productinfo_type($request, $productinfo['id'], $productinfo['r_repeat'], $items, '[]');
    $this->success('修改成功');
  }
}