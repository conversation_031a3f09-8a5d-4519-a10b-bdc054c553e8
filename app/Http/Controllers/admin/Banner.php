<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Http\Controllers\admin\Template\TemplateArticle;
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Banner extends TemplateArticle
{
  public function __construct(){
    parent::__construct(
      $controller_name    = "Banner",
      $table_name         = "frontend_menu_name",
      $pic_width          = 1920, 
      $pic_height         = 616
    );
    $this->controllerName   = $controller_name;
    $this->resTableName     = $table_name;
  }

  public function index(Request $request){
    $searchKey = $request->get('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $this->data['searchKey'] = $searchKey;
    $where = "name LIKE '%$searchKey%'";
    
    $activity = Db::table($this->resTableName);

    $no_in_ids = [9]; /*預設排除：消費功能*/
    foreach(config('control.close_function_current') as $key => $value){
      if($key=="關於我們"){
        array_push($no_in_ids, 1);
      }
      else if($key=="新增商品"){
        array_push($no_in_ids, 2);
      }
      else if($key=="有感體驗"){
        array_push($no_in_ids, 3);
      }
      else if($key=="活動專區"){
        array_push($no_in_ids, 4);
      }
      else if($key=="常見問題"){
        array_push($no_in_ids, 5);
      }
      else if($key=="經銷據點"){
        array_push($no_in_ids, 6);
      }
      else if($key=="最新消息"){
        array_push($no_in_ids, 7);
      }
      else if($key=="找貨回函"){
        array_push($no_in_ids, 8);
      }
    }
    if($no_in_ids){
      $where.= ' and id not in ('.implode(',', $no_in_ids).')';
    }
    // dump($where);exit;

    $activity = $activity->whereRaw($where)->orderByRaw('id asc')->get();
    $this->data[$this->resTableName] = $activity;

    /*用於取得前台某選單的文字*/
    $this->data['table_name'] = $this->resTableName;

    return view('admin.banner.index',['data'=>$this->data]);
  }

  public function cellCtrl(Request $request){
    if(!empty($request->post('name'))){
      $id = $request->post('id');
      $frontend_menu_name = Db::table($this->resTableName)->where('id',$id)->first();
      if($frontend_menu_name){
        Db::table('backstage_menu_second')
          ->whereRaw('id="'.$frontend_menu_name->backstage_menu_second_id.'"')
          ->update(['show_name'=>$request->post('name')]);
      }
    }
    return parent::cellCtrl($request);
  }
}