<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;

class Shippingfeetag extends MainController{
  private $DBTextConnecter;
  private $resTableName;
  const PER_PAGE_ROWS = 20;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('shipping_fee_tag');
    $this->resTableName = 'shipping_fee_tag';
  }

  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $this->data['searchKey'] = $searchKey;
    $shipping_fee = Db::table($this->resTableName)
                      ->whereRaw("name LIKE '%$searchKey%'")
                      ->whereRaw($this->distributor_id_where_sql)
                      ->orderBy('order_id')
                      ->simplePaginate(
                        self::PER_PAGE_ROWS
                      )->appends([
                        'searchKey' => $searchKey,
                      ]);

    $this->data['shipping_fee'] = $shipping_fee;
    return view('admin.shippingfeetag.index',['data'=>$this->data]);
  }

  public function doCreate(Request $request) {
    $newData = $request->post();
    try{
      if($this->admin_type=='distribution'){
        $newData['distributor_id'] = session()->get($this->admin_type)['id'];
      }

      $this->DBTextConnecter->setDataArray($newData);
      $id = $this->DBTextConnecter->createTextRow();

      // 自動更新排序
      $table = $this->resTableName;
      $column = 'order_id';
      $order_num = 0;
      $primary_key = 'id';
      $primary_value = $id;
      parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $this->distributor_id_where_sql);

      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function update(Request $request) {
    $updateData = $request->post();
    if(!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])){
      $this->error('您無法操作此項目');
    }

    try{
      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }
  /*AJAX*/
  public function cellCtrl(Request $request) {
    try{
      $updateData = $request->post();

      if(!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])){
        throw new \Exception("您無法編輯此項目", 1);
      }

      // 自動更新排序
      if( isset($updateData['order_id']) ){
        $table = $this->resTableName;
        $column = 'order_id';
        $order_num = $updateData['order_id'];
        $primary_key = 'id';
        $primary_value = $updateData['id'];
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
        unset($updateData['order_id']);
      }

      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->resTableName, $id)){
      $this->error('您無法操作此項目');
    }
    try{
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll($this->resTableName, $id)){
            throw new \Exception('您無法操作此項目', 1);
          }
        }
        Db::table($this->resTableName)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
}