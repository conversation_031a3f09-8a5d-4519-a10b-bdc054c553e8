<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Seo extends MainController {
  private $DBTextConnecter;
  const FIXED_RESOURCES_ID = 1;

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('seo');
  }

  public function edit(Request $request) {
    $seo = Db::table('seo')->find(self::FIXED_RESOURCES_ID);

    $this->data['seo'] = CommonService::objectToArray($seo);
    return view('admin.seo.index',['data'=>$this->data]);
  }

  public function edit_social(Request $request) {
    $seo = Db::table('seo')->find(self::FIXED_RESOURCES_ID);
    $seo = CommonService::objectToArray($seo);

    $this->data['seo'] = $seo;
    return view('admin.seo.index_social',['data'=>$this->data]);
  }

  public function edit_advance(Request $request) {
    $seo = Db::table('seo')->find(self::FIXED_RESOURCES_ID);
    $seo = CommonService::objectToArray($seo);
    try{
      $robots = fopen(base_path() . "/public/robots.txt", "r");
      $seo['robot'] =  fread($robots,filesize(base_path() . "/public/robots.txt"));
      fclose($robots);
    }catch (\Exception $e){
      $seo['robot'] = "";
    }
    $this->data['seo'] = $seo;
    return view('admin.seo.index_advance',['data'=>$this->data]);
  }

  public function update(Request $request) {
    $newData = $request->post();
    unset($newData['_token']);

    $this->DBTextConnecter->setDataArray($newData);
    $this->DBTextConnecter->upTextRow();
  
    $result = self::success(Lang::get('更新成功'), '/admin/seo/edit');        
    return view('redirecttemp',$result);
  }

  public function update_social(Request $request) {
    $newData = $request->post();
    unset($newData['_token']);

    $image = $request->file('fb_img');
    if($image){
      if(isset($_FILES['fb_img'])){
        $file = $_FILES['fb_img'];
        $ext = pathinfo($file['name'], PATHINFO_EXTENSION);            
        $new_file= base_path()."/public/upload/seo_fb_img_" . $newData['id'] . '_' . time() .'.'.$ext;
        $doupload=move_uploaded_file($file['tmp_name'], $new_file);
        $newData['fb_img'] = "/public/upload/seo_fb_img_" . $newData['id'] . '_' . time() . '.'.$ext;
      }
    }

    $this->DBTextConnecter->setDataArray($newData);
    $this->DBTextConnecter->upTextRow();

    $result = self::success(Lang::get('更新成功'), '/admin/seo/edit_social');        
    return view('redirecttemp',$result);
  }

  public function update_advance(Request $request) {
    $newData = $request->post();
    unset($newData['_token']);
    $robots = fopen(base_path() . "/public/robots.txt", "w");
    fwrite($robots, $newData['robot']);
    fclose($robots);
    unset($newData['robot']);

    $DBFileConnecter = new DBFileConnecter();
    $image = $request->file('fb_img');
    if($image){
      $newData['fb_img'] = $DBFileConnecter->fixedFileUp(
        $image, 
        'seo_fb_img', 
        1200, 
        630
      );
    }

    $DBFileConnecter->setUploadPath(base_path() . '/public');
    $map = $request->file('map');
    if($map){
      $newData['map'] = $DBFileConnecter->fixedFileUp(
        $map, 
        'sitemap'
      );
      $newData['map'] = explode('/', $newData['map'])[2];
      $newData['map'] = explode('?', $newData['map'])[0];
    }

    $this->DBTextConnecter->setDataArray($newData);
    $this->DBTextConnecter->upTextRow();
    $result = self::success(Lang::get('更新成功'), '/admin/seo/edit_advance');        
    return view('redirecttemp',$result);
  }
}