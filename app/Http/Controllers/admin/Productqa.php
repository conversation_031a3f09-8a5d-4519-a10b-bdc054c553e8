<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Http\Controllers\home\PublicController;
use App\Services\CommonService;

class Productqa extends MainController{
  private $resTableName;
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
    $this->resTableName = 'product_qa';
  }

  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';

    $this->data['searchKey'] = $searchKey;
    $qa = Db::connection('main_db')->table($this->resTableName .' as pq')
                                  ->join('account as acnt','pq.uid','acnt.id')
                                  ->whereRaw("
                                    pq.prod_q LIKE '%$searchKey%' OR 
                                    pq.prod_a LIKE '%$searchKey%' OR 
                                    pq.prod_addr LIKE '%$searchKey%' OR 
                                    acnt.name LIKE '%$searchKey%'
                                  ")
                                  ->whereRaw($this->distributor_id_where_sql)
                                  ->orderByRaw('prod_qa_id desc')
                                  ->paginate(self::PER_PAGE_ROWS)
                                  ->appends([
                                    'searchKey' => $searchKey
                                  ]);
    if (empty($qa->items()) == false) {
      foreach ($qa->items() as $key => $value) {
        if ($value->prod_a) {
          $qa->items()[$key]->new = false;
        } else {
          $qa->items()[$key]->new = true;
        }
      }
    }
    $this->data['qa'] = $qa;

    $qa_json = json_encode($qa->items(), JSON_UNESCAPED_UNICODE);
    $qa_json = str_replace("\t", "", $qa_json);
    $this->data['qa_json'] = $qa_json;

    return view('admin.productqa.index',['data'=>$this->data]);
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->resTableName, $id, 'main_db', 'prod_qa_id')){
      $this->error('您無法操作此項目');
    }
    // exit;
    try{
      Db::connection('main_db')->table($this->resTableName)->where('prod_qa_id',$id)->delete();
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll($this->resTableName, $id, 'main_db', 'prod_qa_id')){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
        Db::connection('main_db')->table($this->resTableName)->whereIn('prod_qa_id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  /*AJAX*/
  public function update(Request $request) {
    $newData = $request->post();
    if(!parent::check_controll($this->resTableName, $newData['prod_qa_id'], 'main_db', 'prod_qa_id')){
      $this->error("您無法編輯此項目");
    }

    $updData = [
      'prod_a'    => $newData['prod_a'],
      'a_datetime'=> date("Y-m-d H:i:s")
    ];
    $productQa = Db::connection('main_db')->table($this->resTableName)->where('prod_qa_id',$newData['prod_qa_id'])->first();
    $productQa = CommonService::objectToArray($productQa);
    Db::connection('main_db')->table($this->resTableName)->where('prod_qa_id',$newData['prod_qa_id'])->update($updData);
    $user = Db::connection('main_db')->table('account')->where('id',$newData['uid'])->first();
    $user = CommonService::objectToArray($user);
    ////////////
    /// mail
    ////////////
    $globalMailData = PublicController::getMailData($productQa['site_name']);
    $message  = "
      <html>
        <head></head>
        <body>
          <div>
            親愛的顧客您好:<br>
            <br>
            您的商品提問已被回覆<br>
            回覆內容：".$newData['prod_a']."<br>
            產品網址：".$request->server('REQUEST_SCHEME') . '://' . $request->server('HTTP_HOST') . '/' . $productQa['prod_addr']."
          </div>
          <div>
            ". $globalMailData['system_email']['product_qa'] ."
          </div>
          <div style='color:red;'>
            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
          </div>
        </body>
      </html>
    ";
    PublicController::Mail_Send($message,'client',$user['email'],"產品問答回覆",$config_db=$productQa['site_name']);

    return $newData;
  }
}