<?php
namespace App\Http\Controllers\admin\Template;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Http\Controllers\admin\MainController;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class TemplateArticle extends MainController
{
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  public $controllerName;
  public $DBTextConnecter;
  public $resTableName;
  public $pic_width;
  public $pic_height;
  private $has_column_user_id;

  //this resources's frontend use Single Page Web, some CURD method is useless
  public function edit(Request $request){}
  public function create(Request $request){}

  public function __construct(
    $controller_name,
    $table_name,
    $pic_width = 700, 
    $pic_height = 475
  ){
    parent::__construct();
    $this->controllerName   = $controller_name;
    $this->DBTextConnecter  = DBTextConnecter::withTableName($table_name);
    $this->resTableName     = $table_name;
    $this->pic_width        = $pic_width;
    $this->pic_height       = $pic_height;

    try {
      Db::table($this->resTableName)->column('user_id'); /*測試是否有user_id欄位*/
      $this->has_column_user_id = true;
    } catch (\Exception $e) {
      $this->has_column_user_id = false;
    }
  }

  public function index(Request $request){
    $searchKey = $request->get('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $this->data['searchKey'] = $searchKey;
    $activity = Db::table($this->resTableName);
    
    if($this->has_column_user_id){
      $activity = $activity->whereRaw($this->distributor_id_where_sql);
    }

    $activity = $activity->whereRaw("title LIKE '%$searchKey%'")
          ->orderByRaw('orders asc, time desc')
          ->paginate(self::PER_PAGE_ROWS)
          ->appends([
            'searchKey' => $searchKey
          ]);
  
    $this->data[$this->resTableName] = $activity;

    /*用於取得前台某選單的文字*/
    $this->data['table_name'] = $this->resTableName;

    return view("admin.".$this->resTableName.".index",['data'=>$this->data]);
  }

  public function doCreate(Request $request, $finish=true){
    $newData = $request->post();
    unset($newData['id']);
    unset($newData['_token']);
    /*供應商*/
    if($this->admin_type=='distribution'){
      if($this->has_column_user_id){
        $newData['distributor_id'] = session()->get($this->admin_type)['id'];
      }
    }
    try{
      /*創建資料*/
      $this->DBTextConnecter->setDataArray($newData);
      $mainId = $this->DBTextConnecter->createTextRow();

      /*處理圖片*/
      $image = $request->file('image');
      if($image){
        $DBFileConnecter = new DBFileConnecter();
        $newData['pic'] = $DBFileConnecter->fixedFileUp($image, $this->resTableName.'_'.$mainId, $this->pic_width, $this->pic_height);

        $newData['id'] = $mainId;

        /*更新圖片欄位*/
        $this->DBTextConnecter->setDataArray($newData);
        $this->DBTextConnecter->upTextRow();
      }

      if( isset($newData['orders']) ){
        // 自動更新排序
        $table = $this->resTableName;
        $column = 'orders';
        $order_num = $newData['orders'];
        $primary_key = 'id';
        $primary_value = $mainId;
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
        unset($newData['orders']);
      }

      if($finish){
        ob_clean();
        echo('<h1>上傳成功</h1>');die();
      }else{
        return $mainId;
      }
    }
    catch (\Exception $e){
      ob_clean();
      echo($e->getMessage());die();
    }
  }

  public function update(Request $request, $finish=true){
    $newData = $request->post();
    unset($newData['_token']);
    try{
      if(!parent::check_controll($this->resTableName, $newData['id'])){
        throw new \Exception("您無法編輯此項目", 1);
      }

      /*處理圖片*/
      $image = $request->file('image');
      if($image){
        $DBFileConnecter = new DBFileConnecter();
        $newData['pic'] = $DBFileConnecter->fixedFileUp($image, $this->resTableName.'_'.$newData['id'], $this->pic_width, $this->pic_height);
      }

      /*更新資料*/
      $this->DBTextConnecter->setDataArray($newData);
      $this->DBTextConnecter->upTextRow();

      if( isset($newData['orders']) ){
        // 自動更新排序
        $table = $this->resTableName;
        $column = 'orders';
        $order_num = $newData['orders'];
        $primary_key = 'id';
        $primary_value = $newData['id'];
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value);
        unset($newData['orders']);
      }
      
      if($finish){
        ob_clean();
        echo('<h1>上傳成功</h1>');die();
      }else{
        return $newData['id'];
      }
    }
    catch (\Exception $e){
      ob_clean();
      echo($e->getMessage());die();
    }
  }

  public function delete(Request $request){
    $id = $request->get('id');
    try{
      if(!parent::check_controll($this->resTableName, $id)){
        throw new \Exception("您無法編輯此項目", 1);
      }
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('刪除成功');
  }

  public function multiDelete(Request $request){
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll($this->resTableName, $id)){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
        Db::table($this->resTableName)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('刪除成功');
  }

  /*AJAX*/
  public function cellCtrl(Request $request){
    try{
      $updateData = $request->post();
      if(!parent::check_controll($this->resTableName, $updateData['id'])){
        throw new \Exception("您無法編輯此項目", 1);
      }

      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }

    return $outputData;
  }
}