<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Http\Controllers\admin\Act;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Discount extends Act
{
  const ACT_TYPE = 2;
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  private $tableName;
  private $DBTextConnecter;
  protected $act_type;
  public function __construct() {
    parent::__construct();
    $this->tableName = 'act';
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->tableName);
    $this->act_type = self::ACT_TYPE;
  }

  public function index(Request $request) {
    return view('admin.discount.act',['data'=>$this->data]);
  }
  /*列表頁相關功能api*/
  public function getActList(Request $request){
    return parent::getActList();
  }
  // public function changeOnline(Request $request){}
  // public function delAct(Request $request){}
  public function doCreate(Request $request) {
    $newData['act_type']  = $this->act_type; /*立馬省*/
    $count = $this->getNumber();
    $newData['number'] = config('extra.shop.subDeparment') . 'D' . date('Ymd') . $count;
    $new_id = parent::doCreate_db($newData);
    $this->success('新增成功', url('/admin/Discount/edit').'?'.http_build_query(['id'=>$new_id, 'class'=>class_basename($this)]));
  }

  public function edit(Request $request){
    return parent::edit();
  }

  // public function update(Request $request){}
  // public function getCount(Request $request){}

  public function getNumber($code='D'){
    return parent::getNumber($code);
  }

  // public function getActProd(Request $request){}
  // public function insertAct(Request $request){}
  // private function add_act_prouduct($insertGetId, $actData){}
  // public function delActProd(Request $request){}
}
