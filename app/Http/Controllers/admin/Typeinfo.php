<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Typeinfo extends MainController{
  private $resTableName;
  private $DBTextConnecter;
  private $DBFileConnecter;

  public function __construct() {
    parent::__construct();
    $this->resTableName = 'typeinfo';
    $this->DBTextConnecter = DBTextConnecter::withTableName('typeinfo');
    $this->DBFileConnecter = DBFileConnecter::withTableName('typeinfo');
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->DBTextConnecter->getTableName(), $id)){
      $this->error('您無法操作此項目');
    }

    $produc_in_layer = Db::table('productinfo')->select('id')->whereRaw("final_array like '%".'"branch_id":"'.$id.'"'."%'")->get();
    $layer_in_layer = Db::table('typeinfo')->select('id')->whereRaw("branch_id =".$id)->get();

    if (sizeof($produc_in_layer)) {
      $this->error('此分類還有商品，請先清光');
    }else if(sizeof($layer_in_layer)){
      $this->error('此分類下還有分類，請先清光');
    }
    Db::table('typeinfo')->delete($id);
    $this->success('刪除成功');
  }

  /*iframe*/
  /*those method will cover old image by DB table id*/
  public function create(Request $request) {
    $width = 40; $height = 40;
    $updateData = $request->post();
    unset($updateData['id']);
    unset($updateData['image_del']);
    $image = $request->file('image');
    try{
      if($image){
        $updateData['pic'] = $this->DBFileConnecter->fixedFileUp($image, 'typeinfo'.md5(rand().time()), $width, $height);
      }
    }catch (\Exception $e){
      // $this->error('上傳圖片有誤');
      echo('<h1>上傳圖片有誤</h1>');die();
    }

    if($this->admin_type=='distribution'){
      $updateData['distributor_id'] =session()->get($this->admin_type)['id'];
    }

    unset($updateData['_token']);

    if (empty($updateData['webtype_keywords']) == true) {
      $updateData['webtype_keywords'] = '';
    }
    if (empty($updateData['webtype_description']) == true) {
      $updateData['webtype_description'] = '';
    }

    $this->DBTextConnecter->setDataArray($updateData);
    $id = $this->DBTextConnecter->createTextRow();

    // 自動更新排序
    if( isset($updateData['order_id']) ){
      $table = $this->resTableName;
      $column = 'order_id';
      $order_num = $updateData['order_id'];
      $primary_key = 'id';
      $primary_value = $id;
      $filter_where = 'parent_id='.$updateData['parent_id'].' AND branch_id='.$updateData['branch_id'].' AND '.$this->distributor_id_where_sql;
      parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
      unset($updateData['order_id']);
    }
    
    $this->success('新增成功');
  }

  public function update(Request $request) {
    $width = 40; $height = 40;
    $updateData = $request->post();

    if(!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])){
      $this->error('您無法操作此項目');
    }

    // 自動更新排序
    if( isset($updateData['order_id']) ){
      $table = $this->resTableName;
      $column = 'order_id';
      $order_num = $updateData['order_id'];
      $primary_key = 'id';
      $primary_value = $updateData['id'];
      $filter_where = 'parent_id='.$updateData['parent_id'].' AND branch_id='.$updateData['branch_id'].' AND '.$this->distributor_id_where_sql;
      parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
      unset($updateData['order_id']);
    }

    $image_del = isset($updateData['image_del']) ? $updateData['image_del'] : 0;
    unset($updateData['image_del']);
    if($image_del==1){
      $updateData['pic'] = "";
    }else{
      $image = $request->file('image');
      if($image){
        try {
          $updateData['pic'] = $this->DBFileConnecter->fixedFileUp($image, 'typeinfo_' . $updateData['id'], $width, $height);
        } catch (\Exception $e) {
          // $this->error('更新圖片有誤');
          echo('<h1>更新圖片有誤</h1>');die();
        }
      }

    }

    unset($updateData['_token']);

    $this->DBTextConnecter->setDataArray($updateData);
    $this->DBTextConnecter->upTextRow();
    
    $this->success('更新成功');
  }
  /*AJAX*/
  public function cellCtrl(Request $request) {
    try{
      $updateData = $request->post();

      if(!parent::check_controll($this->DBTextConnecter->getTableName(), $updateData['id'])){
        throw new \Exception("您無法編輯此項目", 1);
      }

      // 自動更新排序
      if( isset($updateData['order_id']) ){
        $table = $this->resTableName;
        $column = 'order_id';
        $order_num = $updateData['order_id'];
        $primary_key = 'id';
        $primary_value = $updateData['id'];
        $filter_where = 'parent_id='.$updateData['parent_id'].' AND branch_id='.$updateData['branch_id'].' AND '.$this->distributor_id_where_sql;
        parent::auto_change_orders($table, $column, $order_num, $primary_key, $primary_value, $filter_where);
        unset($updateData['order_id']);
      }

      $this->DBTextConnecter->setDataArray($updateData);
      $this->DBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => 'success'
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage()
      ];
    }
    return $outputData;
  }
}