<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//Photonic Class
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\pattern\MemberInstance;

class Contact extends MainController{
  const TYPE_TABLE_NAME = 'contact';

  private $DBTextConnecter;
  private $typeDBTextConnecter;
  private $DBTextConnecter_record;
  private $resTableName;
  private $typeTableName;
  const PER_PAGE_ROWS = 10;
  const SIMPLE_MODE_PAGINATE = false;

  //this resources cannet edit and create
  public function edit(Request $request){}
  public function create(Request $request){}
  public function doCreate(Request $request){}
  public function update(Request $request){}

  public function __construct() {
    parent::__construct();
    $this->DBTextConnecter = DBTextConnecter::withTableName('contact_log');
    $this->DBTextConnecter_record = DBTextConnecter::withTableName('contact_log_record');
    $this->typeDBTextConnecter = DBTextConnecter::withTableName('contact');
    $this->resTableName = 'contact_log';
    $this->typeTableName = self::TYPE_TABLE_NAME;
  }

  public function index(Request $request) {
    $searchKey = $request->get('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $this->data['searchKey'] = $searchKey;
    $start = $request->get('start') ?? '1970-01-01';
    $end = $request->get('end') ?? '9999-01-01';
    $timestamp_s = strtotime($start);
    $timestamp_e = strtotime($end.' +1Day');

    $contact_log = DB::table($this->resTableName)
                      ->whereRaw("(
                        type LIKE '%$searchKey%' or 
                        phone LIKE '%$searchKey%' or 
                        homephone LIKE '%$searchKey%' or 
                        name LIKE '%$searchKey%' or 
                        email LIKE '%$searchKey%' or 
                        order_number  LIKE '%$searchKey%' 
                      )")
                      ->whereRaw("UNIX_TIMESTAMP(`time`) >= $timestamp_s AND UNIX_TIMESTAMP(`time`) < $timestamp_e")
                      ->whereRaw($this->distributor_id_where_sql);
      $status = $request->get('status') ?? '';
      if($status!=''){
        $contact_log = $contact_log->where('status', $status);
      }
      $contact_log = $contact_log->orderByRaw('id desc')
                                ->paginate(self::PER_PAGE_ROWS)
                                ->appends([
                                  'searchKey' => $searchKey,
                                  'start' => $start,
                                  'end' => $end,
                                  'status' => $status,
                                ]);
    $this->data['contact_log'] = $contact_log;

    $contact_type = self::get_contact_type($this->distributor_id_where_sql);
    $this->data['contact_type'] =  $contact_type;
    
    $this->data['empty'] = '沒有數據';
    return view('admin.contact.index',['data'=>$this->data]);
  }
  public static function get_contact_type($where=''){
    $contact_type = Db::table(self::TYPE_TABLE_NAME);
    if($where){
      $contact_type = $contact_type->whereRaw($where);
    }
    $contact_type = $contact_type->first();
    $contact_type = CommonService::objectToArray($contact_type);
    $contact_type = $contact_type ? $contact_type['contact_type'] : "其他";
    return $contact_type;
  }

  public function contact_log_record(Request $request){
    $contact_log_id = $request->post('contact_log_id');
    $contact_log_record = Db::table('contact_log_record')->whereRaw('contact_log_id="'.$contact_log_id.'"')->orderByRaw('id asc')->get();
    $contact_log_record = CommonService::objectToArray($contact_log_record);
    foreach ($contact_log_record as $key => $value) {
      $contact_log_record[$key]['datetime_format'] = date('Y-m-d H:i', $value['datetime']);
      if($value['admin_type']=='admin'){
        $admin= Db::table('admin')->where('id', $value['admin_id'])->first();
        $admin_name = $admin->name ?? '';
      }else if($value['admin_type']=='distribution'){
        $MemberInstance = new MemberInstance($value['admin_id']);
        $userD = $MemberInstance->get_user_data();
        $admin_name = $userD['name'] ?? '';
      }else{
        $admin_name = '';
      }
      $contact_log_record[$key]['admin_name'] = $admin_name;
    }
    return $contact_log_record;
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->resTableName, $id)){
      $this->error('您無法操作此項目');
    }
    try{
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('刪除成功', '/admin/Contact/index');
  }
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll($this->resTableName, $id)){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
        Db::table($this->resTableName)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  /*更改status和新增回覆，直接對JQ post下tp5看得懂得更新指令*/
  public function status(Request $request) {
    $update = $request->post();
    $remessage = $update['remessage'] ?? '';
    if(!$remessage){
      $this->error(Lang::get('資料不完整'));
    }
    try{
      if(!parent::check_controll($this->DBTextConnecter->getTableName(), $update['id'])){
        throw new \Exception("您無法編輯此項目", 1);
      }

      /*添加留言紀錄*/
      $admin_type = $this->admin_type;
      $record = [
        'contact_log_id' => $update['id'],
        'message' => $remessage,
        'admin_type' => $admin_type,
        'admin_id' => $this->$admin_type['id'],
        'datetime' => time(),
      ];
      // dump($record);exit;
      $this->DBTextConnecter_record->setDataArray($record);
      $this->DBTextConnecter_record->createTextRow();

      /*更新回函表*/
      $this->DBTextConnecter->setDataArray($update);
      $this->DBTextConnecter->upTextRow();
    }catch (\Exception $e){
      $this->error($e->getMessage());
    }
    $this->success(Lang::get('操作成功'));
  }

  /*更改type，直接對JQ post下tp5看得懂得更新指令*/
  public function type(Request $request) {
    $update = $request->post();
    if($this->admin_type=='distribution'){
      $contact_type = Db::table($this->typeTableName)->whereRaw($this->distributor_id_where_sql)->first();
      $contact_type = CommonService::objectToArray($contact_type);

      if($contact_type){ /*編輯*/
        $update['id'] = $contact_type['id'];
      }else{ /*新增*/
        unset($update['id']);
        $update['distributor_id'] = session()->get($this->admin_type)['id'];
        $this->typeDBTextConnecter->setDataArray($update);
        $this->typeDBTextConnecter->createTextRow();
        $outputData = [
          'status' => true,
          'message' => '更改成功',
        ];
        return $outputData;
      }
    }

    try{
      $this->typeDBTextConnecter->setDataArray($update);
      $this->typeDBTextConnecter->upTextRow();
      $outputData = [
        'status' => true,
        'message' => '更改成功',
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage(),
      ];
      return $outputData;
    }
    return $outputData;
  }

  public function getCount(Request $request){
    try{
      $unprocess = Db::table($this->resTableName)->whereRaw($this->distributor_id_where_sql)->where('status',0)->count();
      $count = Db::table($this->resTableName)->whereRaw($this->distributor_id_where_sql)->count();
      $outputData = [
        'status' => true,
        'message' => array($unprocess,$count),
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage(),
      ];
      return $outputData;
    }
    return $outputData;
  }
}
