<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
//photonicClass
use App\Services\CommonService;
use App\Services\pattern\TaskHelper;

class Task extends MainController{
  public function __construct(Request $request) {
    parent::__construct();
  }

  public function index(Request $request) {
    return view('admin.task.index', ['data'=>$this->data]);
  }

  public function get_data(Request $request){
    $get_detail = $request->all();
    $data_page = TaskHelper::get_tasks($get_detail);

    unset($get_detail['count_of_items']);
    $data = TaskHelper::get_tasks($get_detail);
    $data_page['total_num'] = count($data['db_data']);
    $data_page['type_options'] = TaskHelper::$task_type;
    return $data_page;
  }

  public function save_data(Request $request){
    $post_detail = $request->post('detail');
    $detail_data = [
      'id' => $post_detail['id']??null,
      'name' => $post_detail['name']??'',
      'type' => $post_detail['type']??'',
      'time_s' => $post_detail['time_s']??'',
      'time_e' => $post_detail['time_e']??'',
      'msg' => $post_detail['msg']??'',
      'bonus_column1' => $post_detail['bonus_column1']??'',
    ];
    // dd($detail_data);
    try {
      $detail_id = TaskHelper::save_tasks($detail_data);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success([
      'id' => $detail_id,
      'msg' => Lang::get('操作成功'),
    ]);
  }

  public function delete_data(Request $request){
    $id = $request->post('id');
    // dd($id);
    try {
      $delete_result = TaskHelper::delete_tasks($id);
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
    }
    $this->success(Lang::get('操作成功'));
  }
}