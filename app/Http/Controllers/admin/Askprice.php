<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\pattern\AskpriceHelper;
use App\Services\pattern\MemberInstance;

class Askprice extends MainController{
  private $DBTextConnecter;
  private $resTableName;
  private $resTableName_record;
  private $DBTextConnecter_record;
  //this resources cannet edit and create
  public function edit(Request $request){}
  public function create(Request $request){}
  public function doCreate(Request $request){}
  public function update(Request $request){}

  public function __construct() {
    parent::__construct();
    $this->resTableName = 'askprice';
    $this->DBTextConnecter = DBTextConnecter::withTableName($this->resTableName);
    $this->resTableName_record = 'askprice_record';
    $this->DBTextConnecter_record = DBTextConnecter::withTableName($this->resTableName_record);
  }

  public function index(Request $request) {
    return view('admin.askprice.index',['data'=>$this->data]);
  }
  public function getList(Request $request){
    $AskpriceHelper = new AskpriceHelper();
    $result = $AskpriceHelper->getList($this->distributor_id_where_sql,'',$request);
    return $result;
  }
  public function getOne(Request $request){
    $AskpriceHelper = new AskpriceHelper();
    $result = $AskpriceHelper->getOne($this->distributor_id_where_sql);
    return $result;
  }

  public function delete(Request $request) {
    $id = $request->get('id');
    if(!parent::check_controll($this->resTableName, $id)){
      $this->error('您無法操作此項目');
    }
    try{
      Db::table($this->resTableName)->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功',url('Askprice/index'));
  }
  public function multiDelete(Request $request) {
    $idList = $request->post('id');
    try{
      if ($idList) {
        $idList = json_decode($idList);
        foreach ($idList as $id) {
          if(!parent::check_controll($this->resTableName, $id)){
            throw new \Exception("您無法編輯此項目", 1);
          }
        }
        Db::table($this->resTableName)->whereIn('id', $idList)->delete();
      }
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');
  }

  /*更改askprice_record紀錄*/
  public function status(Request $request) {
    $update = $request->post();
    $record = Db::table($this->resTableName_record)->find($update['id']);
    $record = CommonService::objectToArray($record);
    if(!$record){ $this->error('項目不存在'); }
    $main = Db::table($this->resTableName)->find($record['askprice_id']);
    $main = CommonService::objectToArray($main);
    if(!$main){ $this->error('項目不存在'); }
    
    try{
      if(isset($update['agree'])){
        if($update['agree']==1){
          $price_final = $update['price_final'] ?? '';
          if(!$price_final){ throw new \Exception("請設定回覆價格", 1); }

          $expired_date = $update['expired_date'] ?? '';
          if(!$expired_date){ throw new \Exception("請設定購買期限", 1); }
        }
      }
      if(!parent::check_controll($this->resTableName_record, $record['askprice_id'])){
        throw new \Exception("您無法編輯此項目", 1);
      }
      $update['status'] = 1;
      $update['response_time'] = time();
      // dump($update);exit;
      $this->DBTextConnecter_record->setDataArray($update);
      $this->DBTextConnecter_record->upTextRow();
    }catch (\Exception $e){
      $this->error($e->getMessage());
    }

    $MemberInstance = new MemberInstance($main['user_id']);
    $user = $MemberInstance->get_user_data();

    $globalMailData = HelperService::getMailData();
    $show_product_name = $main['product_name'];
    $show_product_name .= $main['product_type_name'] ? ('-'.$main['product_type_name']) : '';
    $price_final = isset($update['price_final']) ? $update['price_final'] : '無';
    $expired_date = isset($update['expired_date']) ? $update['expired_date'] : '無';
    $mailBody = "
      <html>
        <head></head>
        <body>
          <div>
            親愛的<u>&nbsp;&nbsp;".$user['name']."&nbsp;&nbsp;</u>，您好，<br>
            我們已收到您的來信，並儘速處理您的問題，<br>
            請您耐心等候。<br><br>
            您所的詢價內容如下：<br>
            <br>
            詢問商品：".$show_product_name."<br>
            詢問數量：".$record['num']."<br>
            回覆價格：".$price_final."<br>
            購買期限：".$expired_date."<br>
            回覆內容：<br>".str_replace("\n", '<br>', $update['response'])."<br>
            <br>
          </div>
          <center></center>
          <div style='color:red;'>
            ≡ 此信件為系統自動發送，請勿直接回覆 ≡
          </div>
        </body>
      </html>
    ";
    $mail_return = HelperService::Mail_Send($mailBody,'client',$user['email'], "再次詢價回函表");

    $this->success('更改成功');
  }

  public function getCount(Request $request){
    try{
      $unprocess = Db::table($this->resTableName)->join('askprice_record as ar', 'ar.id','askprice_record_id')
                                                ->whereRaw($this->distributor_id_where_sql)
                                                ->whereRaw("ar.status='0'")->count();
      $count = Db::table($this->resTableName)->whereRaw($this->distributor_id_where_sql)->count();
      $outputData = [
        'status' => true,
        'message' => array($unprocess,$count),
      ];
    }catch (\Exception $e){
      $outputData = [
        'status' => false,
        'message' => $e->getMessage(),
      ];
      return $outputData;
    }
    return $outputData;
  }
}
