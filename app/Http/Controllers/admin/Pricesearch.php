<?php
namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
//Photonic Class
use App\Http\Controllers\admin\Template\TemplateArticle;
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class Pricesearch extends TemplateArticle{
  public function __construct(){
    parent::__construct(
      $controller_name    = "Pricesearch",
      $table_name         = "product_price_search",
      $pic_width          = 0, 
      $pic_height         = 0
    );
    $this->controllerName   = $controller_name;
    $this->resTableName     = $table_name;
  }

  public function index(Request $request){
    $searchKey = $request->get('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $this->data['searchKey'] = $searchKey;
    $where = "title LIKE '%$searchKey%'";
    
    $activity = Db::table($this->resTableName);
    // dump($where);exit;

    $activity = $activity->whereRaw($where)->whereRaw($this->distributor_id_where_sql)->orderByRaw('orders asc, id asc')->get();
    $this->data['pricesearch'] = CommonService::objectToArray($activity);

    /*用於取得前台某選單的文字*/
    $this->data['table_name'] = $this->resTableName;

    return view('admin.pricesearch.index',['data'=>$this->data]);
  }
}