<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

use App\Services\CommonService;
use App\Services\pattern\MemberInstance;

class Admin extends MainController{
  public function member_discount(Request $request){
    $fisrt_buy = Db::connection('main_db')->table("vip_type")->whereRaw("id = 0")->first();
    $this->data['fisrt_buy'] = CommonService::objectToArray($fisrt_buy);

    $vip_type = MemberInstance::get_vip_types()['db_data'];
    $this->data['vip_type'] = CommonService::objectToArray($vip_type);

    return view('order.admin.member_discount',['data'=>$this->data]);
  }

  public function add_vip_type(Request $request){
    $insert = $request->post();
    unset($insert['_token']);
    
    $this->check_data($insert);

    try{
      foreach ($insert as $key => $value) {
        if($value===null){ unset($insert[$key]); }
      }
      Db::connection('main_db')->table("vip_type")->insert($insert);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('新增成功');
  }
  public function update_vip_type(Request $request){
    $id = $request->get('id');

    $update = $request->post();
    unset($update['id']);
    unset($update['_token']);
    
    $this->check_data($update);

    try{
      Db::connection('main_db')->table("vip_type")->where("id", $id)->update($update);
    } catch (\Exception $e){
      $this->dumpException($e);
    }

    $this->success('更新成功');
  }
  private function check_data($send_data){
    $rule = [
      'type'  	=> 'required',
      'vip_name'	=> 'required',
      'discount' 	=> 'required',
      'dividend_month_weighted' => 'required|numeric|min:0',
      'burn_cv' => 'required|numeric|min:0',
      'discount_ratio' => 'required|numeric|between:0,100',
      'liability_gv' => 'required|numeric|min:0',
    ];
    $msg = [
      'type.required' => '請選擇折扣方式',
      'vip_name.required' => '名稱不得為空',
      'discount.required' => '優惠值不得為空',
      'dividend_month_weighted.required' => '月分紅加權不得為空',
      'dividend_month_weighted.min' => '月分紅加權需大於等於0',
      'burn_cv.required' => '會員級別燒傷金額不得為空',
      'burn_cv.min' => '會員級別燒傷金額趴數需大於0',
      'discount_ratio.required' => '消費圓滿點數抵扣消費比率不得為空',
      'discount_ratio.between' => '消費圓滿點數抵扣消費比率趴數需介於0~100之間',
      'liability_gv.required' => '個人點數責任額(GV)不得為空',
      'liability_gv.min' => '個人點數責任額(GV)需大於等於0',
    ];
    $data = [
      'type'  	=> $send_data['type'],
      'vip_name'  => $send_data['vip_name'],
      'discount'	=> $send_data['discount'],
      'dividend_month_weighted'	=> $send_data['dividend_month_weighted'],
      'burn_cv'	=> $send_data['burn_cv'],
      'discount_ratio'	=> $send_data['discount_ratio'],
      'liability_gv'	=> $send_data['liability_gv'],
    ];
    $validate=Validator::make($data ,$rule, $msg);
    if($validate->fails()){
      $this->error($validate->errors()->first());
    }
  }

  public function del_vip_type(Request $request){
    $id = $request->get('id');
    if($id=="0") $this->error("不可刪除");
    try{
      Db::connection('main_db')->table("vip_type")->delete($id);
    } catch (\Exception $e){
      $this->dumpException($e);
    }
    $this->success('刪除成功');	
  }
}