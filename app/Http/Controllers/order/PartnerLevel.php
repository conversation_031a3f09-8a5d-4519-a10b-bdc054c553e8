<?php

namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\pattern\MemberInstance;

class PartnerLevel extends MainController
{
    public function index(Request $request)
    {
        return view('order.partner_level.index', ['data' => $this->data]);
    }

    public function get_data(Request $request)
    {
        $get_detail = $request->all();
        return MemberInstance::get_partner_levels($get_detail);
    }

    public function save_data(Request $request)
    {
        $post_detail = $request->post('detail');
        // dd($post_detail);
        try {
            $detail_id = MemberInstance::save_partner_level($post_detail);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success([
            'id' => $detail_id,
            'msg' => Lang::get('操作成功'),
        ]);
    }

    public function delete_data(Request $request)
    {
        $id = $request->post('id');
        // dd($id);
        try {
            $delete_result = MemberInstance::delete_partner_level($id);
        } catch (\Throwable $th) {
            $this->error($th->getMessage());
        }
        $this->success(Lang::get('操作成功'));
    }
}
