<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;
//photonicClass
use App\Services\CommonService;
use App\Services\pattern\OrderHelper;
use App\Http\Controllers\admin\Payfee;

class Supplier extends MainController{
  public function __construct(Request $request) {
    parent::__construct();
    $this->tableName = 'orderform';
    $this->coupon_tableName = 'coupon_pool';
  }

  public function orders(Request $request) {
    $this->data['distributor_id_search'] = -1;

    /*付款方式下拉選*/
    $payments = [];
    $pay_fee_dict = Payfee::get_pay_fee_dict();
    $this->data['pay_fee_dict'] = $pay_fee_dict;
    foreach ($pay_fee_dict as $key => $value) {
      if($value['online']==1){
        array_push($payments, ['id'=>$value['id'], 'name'=>$value['name'], 'sys_status'=>$value['sys_status']]);
      }
    }
    // dump($payments);exit;
    $this->data['payments'] = $payments;

    /*運法下拉選*/
    $transports = DB::connection('main_db')->table($this->tableName)->select('transport')->groupBy('transport')->get();
    $this->data['transports'] = $transports;

    return view('order.supplier.orders', ['data'=>$this->data]);
  }

  public function get_orderforms(Request $request){
    return $this->get_product_data($request, true);
  }
  public function excel(Request $request){
    $data = $this->get_product_data($request, false)['rowDataItem'];
    // dump($data);

    $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->setCellValue('A1', '訂單日期/編號');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', '商品名稱');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', '供應商姓名');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', '供應商會員編號');
    $objPHPExcel->getActiveSheet()->setCellValue('E1', '回饋方式');
    $objPHPExcel->getActiveSheet()->setCellValue('F1', '供應商回饋金額(美金)');
    $objPHPExcel->getActiveSheet()->setCellValue('G1', '供應商回饋日期');

    // 放入資料
    $row = 2;
    foreach ($data as $value) {
      $objPHPExcel->getActiveSheet()->setCellValue('A'.$row, $value->order_number);
      $objPHPExcel->getActiveSheet()->setCellValue('B'.$row, $value->name);
      $objPHPExcel->getActiveSheet()->setCellValue('C'.$row, $value->supplier_name);
      $objPHPExcel->getActiveSheet()->setCellValue('D'.$row, $value->supplier_number);
      $objPHPExcel->getActiveSheet()->setCellValue('E'.$row, $value->supplier_bonus_name);
      $objPHPExcel->getActiveSheet()->setCellValue('F'.$row, $value->price_supplier);
      $objPHPExcel->getActiveSheet()->setCellValue('G'.$row, $value->do_award_supplier_time_f);
      $row++;
    }

    // 下載檔案
    $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
    $filename = "供應商回饋列表.xlsx";
    // ob_end_clean();
    ob_start();
    header("Content-type: application/force-download");
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $PHPExcelWriter->save('php://output');
  }
  public function group_excel(Request $request){
    $data = $this->get_product_data($request, false)['rowDataItem'];
    // dump($data);
    
    $data_group = [];
    foreach ($data as $value) {
      if(!isset($data_group[$value->distributor_id])){
        $data_group[$value->distributor_id] = (object)[
          'supplier_name' => $value->supplier_name,
          'supplier_number' => $value->supplier_number,
          'bonus' => [],
        ];
      }
      $supplier_bonus_name = $value->supplier_bonus_name;
      if(!isset($data_group[$value->distributor_id]->bonus[$supplier_bonus_name])){
        $data_group[$value->distributor_id]->bonus[$supplier_bonus_name] = 0;
      }
      $data_group[$value->distributor_id]->bonus[$supplier_bonus_name] += $value->price_supplier;
    }

    $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->setCellValue('A1', '供應商姓名');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', '供應商會員編號');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', '回饋方式');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', '供應商回饋金額(美金)');

    // 放入資料
    $row = 2;
    foreach ($data_group as $value) {
      ksort($value->bonus);
      foreach ($value->bonus as $supplier_bonus_name => $supplier_bonus_num) {
        $objPHPExcel->getActiveSheet()->setCellValue('A'.$row, $value->supplier_name);
        $objPHPExcel->getActiveSheet()->setCellValue('B'.$row, $value->supplier_number);
        $objPHPExcel->getActiveSheet()->setCellValue('C'.$row, $supplier_bonus_name);
        $objPHPExcel->getActiveSheet()->setCellValue('D'.$row, $supplier_bonus_num);
        $row++;
      }
    }

    // 下載檔案
    $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
    $filename = "供應商商品金額總計.xlsx";
    // ob_end_clean();
    ob_start();
    header("Content-type: application/force-download");
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $PHPExcelWriter->save('php://output');
  }
  public function pay_batch(Request $request){
    $post_data = $request->all();
    $post_data['do_award_supplier_time'] = '0'; /*商品未標記已處理供應商回饋*/
    $post_data['supplier_bonus'] = '2';         /*商品回饋方式為現金*/
    $request_obj  = new Request($post_data);   
    $data = $this->get_product_data($request_obj, false)['rowDataItem'];
    if(count($data)==0){
      $this->error('無項目需處理或已處理過');
    }

    $ids = [];
    foreach ($data as $value) {
      array_push($ids, $value->id);
    }
    Db::connection('main_db')->table('orderform_product')->whereIn('id', $ids)->update(['do_award_supplier_time' => time()]);
    $this->success('操作成功');
  }

  private function get_product_data(Request $request, $need_page){
    /*依篩選撈取完成、所有供應商、已處理回饋 的訂單*/
    $post_data = $request->all();
    $post_data['order_ship_status'] = '5';
    $post_data['do_award_time'] = '-1';
    $request_obj  = new Request($post_data);
    $rowData = OrderHelper::get_orders($request_obj, $this->admin_type, $this->my_distributor_id, 'Complete', false);
    $order_is = [-1];
    $order_obj = [];
    foreach ($rowData as $value) {
      $order_obj[$value['id']] = $value;
      array_push($order_is, $value['id']);
    }
    // dump($order_is);exit;
    
    /*依搜尋篩選撈取訂單商品(全部)*/
    $param = $request->all();

    $supplier_text = trim($request->get('supplier_text')??'');
    if($supplier_text){
      $param['distributor_ids'] = [-1];
      /*篩選會員*/
      $data = DB::connection('main_db')->table('account')
                                      ->where(function($query)use($supplier_text){
                                        return $query->orWhere('name', 'LIKE', '%'.$supplier_text.'%')
                                                    ->orWhere('number', 'LIKE', '%'.$supplier_text.'%')
                                                    ->orWhere('phone', 'LIKE', '%'.$supplier_text.'%');
                                      })->get()->toArray();
      // dump($data);exit;
      foreach ($data as $value) {
        array_push($param['distributor_ids'], $value->id);
      }
      // dump($param['distributor_ids']);exit;
    }
    $param['distributor_id'] = '-2'; /*商品有指定供應商*/
    $param['has_price_supplier'] = '1'; /*商品供應商回饋金額大於0*/
    $param['do_award_supplier_time'] = $request->get('do_award_supplier_time');
    $param['do_award_supplier_time_s'] = $request->get('do_award_supplier_time_s');
    $param['do_award_supplier_time_e'] = $request->get('do_award_supplier_time_e');
    $param['order_query'] = 'id desc';
    $param['need_page'] = false;
    $data['total_orderform'] = OrderHelper::get_orderform_products($order_is, '-2', $param);

    if($need_page){ /*需要分頁*/
      /*依搜尋篩選撈取訂單商品(分頁)*/
      $param['need_page'] = true;
      $rowData = OrderHelper::get_orderform_products($order_is, '-2', $param);
      $data['CurrentPage'] = $rowData->currentPage();
      $data['listRows'] = $rowData->perPage();
      $data['lastPage'] = $rowData->lastPage();
      $rowDataItem = $rowData->items();
    }else{
      $rowDataItem = $data['total_orderform'];
    }

    $user_ids = [-1];
    foreach ($rowDataItem as $value) {
      $value = (object)$value;
      array_push($user_ids, $value->distributor_id);
    }
    $user_collection = Db::connection('main_db')->table('account')->whereIn('id', $user_ids)->get();
    $db_id_to_user = $user_collection->keyBy('id')->toArray();

    $rowDataItem = array_map(function ($value) use ($order_obj, $db_id_to_user){
      $value = (object)$value;
      $value->order_number = $order_obj[$value->orderform_id]['order_number'];
      $value->do_award_supplier_time_f = $value->do_award_supplier_time ? date('Y-m-d H:i', $value->do_award_supplier_time) : '';
      if($value->supplier_bonus==1){
        $value->supplier_bonus_name = '增值積分';
      }else if($value->supplier_bonus==2){
        $value->supplier_bonus_name = '現金';
      }else{
        $value->supplier_bonus_name = '';
      }
      $value->supplier_name = $db_id_to_user[$value->distributor_id]->name ?? '';
      $value->supplier_number = $db_id_to_user[$value->distributor_id]->number ?? '';
      return $value;
    }, $rowDataItem);
    $data['rowDataItem'] = $rowDataItem;
  
    return $data;
  }
}