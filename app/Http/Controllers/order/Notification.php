<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use app\Http\Controllers\order\MainController;
use App\Services\pattern\NotificationHelper;

class Notification extends MainController{
  public function __construct() {
    parent::__construct();
  }

  public function send_notification(Request $request){
    $ids = request()->post('ids') ?? '[]';
    $title = request()->post('title') ?? '';
    $msg = request()->post('msg') ?? '';
    $open_url = request()->post('open_url') ?? '';
    if($ids=='[]') {$this->error('請選擇推播對象');}
    $ids = json_decode($ids) ? json_decode($ids) : [];
    if(!$title) {$this->error('請輸入推播標題');}
    if(!$msg) {$this->error('請輸入推播內容');}

    $subscription = Db::connection('main_db')->table('subscription')->where('user_id in ('.implode(',', $ids).')')->get();
    foreach ($ids as $key => $id) {
      $result = NotificationHelper::send($id, $title, $msg, $open_url);
      // dump($result);
    }
    $this->success('發送完成');
  }
}