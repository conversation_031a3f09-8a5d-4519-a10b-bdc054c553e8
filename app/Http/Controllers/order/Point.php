<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\PointRecords;
use App\Services\pattern\BonusHelper;

class Point extends MainController{
  public function cash(Request $request){
    return view('order.point.cash',['data'=> $this->data]);
  }
  public function get_cash_record_data(Request $request){
    $params = $request->post();
    
    /*依需求搜尋紀錄*/
    $MemberInstance = new MemberInstance(0);
    $records_show = $MemberInstance->get_cash_record_data($params, true);
    
    /*找出符合搜尋紀錄的全部數量*/
    unset($params['count_of_items']);
    $records_all= $MemberInstance->get_cash_record_data($params, true);
    $records_total = count($records_all);

    return [
      'records_show' => $records_show,
      'records_total' => $records_total,
    ];
  }
  public function pay_cash(Request $request){
    $params = $request->post();
    $params['paid'] = 1; /*未給付*/
    
    /*依需求搜尋紀錄*/
    $MemberInstance = new MemberInstance(0);
    $records_all = $MemberInstance->get_cash_record_data($params, false);
    if(count($records_all)==0){
      $this->error('無項目需處理或已處理過');
    }
    $ids = [-1];
    foreach ($records_all as $value) {
      array_push($ids, $value['id']);
    }
    DB::connection('main_db')->table('points_to_cash_record')->whereIn('id', $ids)->update([
      'time_pay' => time(),
    ]);
    $this->success(Lang::get('操作成功'));
  }
  public function cash_excel(Request $request){
    $params = $request->all();

    $MemberInstance = new MemberInstance(0);
    $records_all = $MemberInstance->get_cash_record_data($params, true);
    // dump($records_all);exit;

    $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->setCellValue('A1', '日期');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', '會員編號');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', '姓名');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', '幣別');
    $objPHPExcel->getActiveSheet()->setCellValue('E1', '金額');
    $objPHPExcel->getActiveSheet()->setCellValue('F1', '給付日期');

    // 放入資料
    $row = 2;
    foreach ($records_all as $value) {
      $objPHPExcel->getActiveSheet()->setCellValue('A'.$row, $value['time_create_f']);
      $objPHPExcel->getActiveSheet()->setCellValue('B'.$row, $value['number']);
      $objPHPExcel->getActiveSheet()->setCellValue('C'.$row, $value['name']);
      $objPHPExcel->getActiveSheet()->setCellValue('D'.$row, $value['currency']);
      $objPHPExcel->getActiveSheet()->setCellValue('E'.$row, $value['num']);
      $objPHPExcel->getActiveSheet()->setCellValue('F'.$row, $value['time_pay'] ? $value['time_pay_f'] : '');
      $row++;
    }

    // 下載檔案
    $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
    $filename = "積分提現列表.xlsx";
    // ob_end_clean();
    ob_start();
    header("Content-type: application/force-download");
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $PHPExcelWriter->save('php://output');
  }
  public function cash_group_excel(Request $request){
    $params = $request->all();

    $MemberInstance = new MemberInstance(0);
    $records_all = $MemberInstance->get_cash_record_data($params, true);
    // dump($records_all);exit;
    $data_group = [];
    foreach ($records_all as $value) {
      if(!isset($data_group[$value['user_id']])){
        $data_group[$value['user_id']] = [
          'name' => $value['name'],
          'number' => $value['number'],
          'cash' => [],
        ];
      }
      $currency = $value['currency'];
      if(!isset($data_group[$value['user_id']]['cash'][$currency])){
        $data_group[$value['user_id']]['cash'][$currency] = 0;
      }
      $data_group[$value['user_id']]['cash'][$currency] += $value['num'];
    }
    // dump($data_group);exit;

    $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->setCellValue('A1', '會員編號');
    $objPHPExcel->getActiveSheet()->setCellValue('B1', '姓名');
    $objPHPExcel->getActiveSheet()->setCellValue('C1', '幣別');
    $objPHPExcel->getActiveSheet()->setCellValue('D1', '金額');

    // 放入資料
    $row = 2;
    foreach ($data_group as $value) {
      ksort($value['cash']);
      foreach ($value['cash'] as $currency => $cash_num) {
        $objPHPExcel->getActiveSheet()->setCellValue('A'.$row, $value['number']);
        $objPHPExcel->getActiveSheet()->setCellValue('B'.$row, $value['name']);
        $objPHPExcel->getActiveSheet()->setCellValue('C'.$row, $currency);
        $objPHPExcel->getActiveSheet()->setCellValue('D'.$row, $cash_num);
        $row++;
      }
    }

    // 下載檔案
    $PHPExcelWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($objPHPExcel);
    $filename = "積分提現會員總計.xlsx";
    // ob_end_clean();
    ob_start();
    header("Content-type: application/force-download");
    header("Content-Disposition: attachment; filename=\"$filename\"");
    $PHPExcelWriter->save('php://output');
  }

  public function point_increasable_record(Request $request){
    return view('order.point.point_increasable_record',['data'=> $this->data]);
  }
  public function get_point_increasable_data(Request $request){
    $params = $request->post();
    
    /*依需求搜尋紀錄*/
    $PointRecords = new PointRecords(0);
    $records_show = $PointRecords->get_records_increasable($params, true);
    
    /*找出符合搜尋紀錄的全部數量*/
    unset($params['count_of_items']);
    $records_all= $PointRecords->get_records_increasable($params, true);
    $records_total = count($records_all);

    return [
      'records_show' => $records_show,
      'records_total' => $records_total,
    ];
  }
  public function transfer_point_increasable(Request $request){
    $from = $request->get('from');
    $to = $request->get('to');
    $num = $request->get('num');
    $msg = $request->get('msg') ?? '管理員轉移增值積分';

    $PointRecords = new PointRecords(0);
    try {
      $PointRecords->transfer_point_increasable($from, $to, $num, $msg);
    } catch (\Throwable $th) {
      // throw $th;
      $this->error($th->getMessage());
    }
    $this->success(Lang::get('操作成功'));
  }

  public function points_record(Request $request){
    return view('order.point.points_record',['data'=> $this->data]);
  }
  public function get_point_data(Request $request){
    $params = $request->post();
    
    /*依需求搜尋紀錄*/
    $PointRecords = new PointRecords(0);
		$records_show = $PointRecords->get_records($params, true);
    
    /*找出符合搜尋紀錄的全部數量*/
    unset($params['count_of_items']);
		$records_all= $PointRecords->get_records($params, true);
    $records_total = count($records_all);

    return [
      'records_show' => $records_show,
      'records_total' => $records_total,
    ];
  }

  public function point_limit_record(Request $request){
    return view('order.point.point_limit_record',['data'=> $this->data]);
  }
  public function get_point_limit_data(Request $request){
    $params = $request->post();
    
    /*依需求搜尋紀錄*/
    $MemberInstance = new MemberInstance(0);
    $records_show = $MemberInstance->get_increasing_limit_records($params, true);
    
    /*找出符合搜尋紀錄的全部數量*/
    unset($params['count_of_items']);
    $records_all= $MemberInstance->get_increasing_limit_records($params, true);
    $records_total = count($records_all);

    $limit_type_to_definition = BonusHelper::$limit_type_to_definition;

    return [
      'records_show' => $records_show,
      'records_total' => $records_total,
      'limit_type_to_definition' => $limit_type_to_definition
    ];
  }
}