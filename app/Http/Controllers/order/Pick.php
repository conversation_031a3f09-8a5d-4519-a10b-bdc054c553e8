<?php
namespace App\Http\Controllers\order;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

use App\Services\CommonService;

class Pick extends MainController{
  const ROW_PER_PAGE = 10;
    const SIMPLE_MODE_PAGINATE = false;

  public function __construct() {
    parent::__construct();
  }

  public function index(Request $request){
    return view('order.pick.index',['data'=>$this->data]);
  }

  public function history(Request $request) {
    return view('order.pick.history',['data'=>$this->data]);
  }

  public function get_history(Request $request) {
    $input = $request->query();

    if ($input['location'] == 'index') {
      return $this->get_index_data($input);
    } else if ($input['location'] == 'history') {
      return $this->get_history_data($input);
    }
  }

  private function get_index_data($input) {
    $index_data_all = $this->get_irows($input, false);
    $data['total_history'] = $index_data_all;

    $index_data = $this->get_irows($input, true);
    $data['CurrentPage'] = $index_data->currentPage();
    $data['listRows'] = $index_data->perPage();
    $data['lastPage'] = $index_data->lastPage();

    $index_data_item = $index_data->items();
    $index_data_item = array_map(function ($arr) {
      $arr->title = $arr->title;
      $arr->type_title = $arr->type_title;
      $arr->type_id = $arr->type_id;
      return $arr;
    }, $index_data_item);
    $data['history_data_item'] = $index_data_item;

    return $data;
  }

  private function get_irows($conditions, $need_page=true) {
    $order_type = isset($conditions['order']) ? $conditions['order'] : 'desc';

    if (isset(config('control.close_function_current')['存放位置管理']) == true) {
      $history_data = DB::table('picked_history AS ph')
                        ->leftJoin('productinfo AS pi', 'ph.product_id', 'pi.id')
                        ->leftJoin('productinfo_type AS pt', 'ph.productinfo_type', 'pt.id')
                        ->selectRaw('pi.ISBN, pi.title, pt.title AS type_title, pt.num AS real_stock, SUM(ph.num) AS count, pt.id AS type_id')
                        ->where('ph.deal_position', 0)
                        ->groupBy('ph.product_id')
                        ->groupBy('ph.productinfo_type')
                        ->orderByRaw("SUM(ph.num) $order_type");
    } else {
      $history_data = DB::table('picked_history AS ph')
                        ->leftJoin('productinfo AS pi', 'ph.product_id', 'pi.id')
                        ->leftJoin('productinfo_type AS pt', 'ph.productinfo_type', 'pt.id')
                        ->leftJoin('position_portion AS pp', 'ph.position_id', 'pp.position_id')
                        ->selectRaw('pi.ISBN, ph.p_code, pi.title, pt.title AS type_title, pp.num AS real_stock, SUM(ph.num) AS count, pt.id AS type_id')
                        ->where('ph.deal_position', 0)
                        ->whereRaw('pp.product_id = ph.product_id')
                        ->whereRaw('pp.productinfo_type = ph.productinfo_type')
                        ->groupBy('ph.p_code')
                        ->groupBy('ph.product_id')
                        ->groupBy('ph.productinfo_type')
                        ->orderByRaw("SUM(ph.num) $order_type");
    }

    $row_per_page = isset($conditions['row_per_page']) ? $conditions['row_per_page'] : self::ROW_PER_PAGE;

    if ($need_page == true) {
      $history = $history_data
                ->paginate($row_per_page)
                ->appends($conditions);
    } else {
      $history = $history_data->get();
      $history = CommonService::objectToArray($history);
    }

    return $history;
  }

  private function get_history_data($input) {
    $history_data_all = $this->get_hrows($input, false);
    $data['total_history'] = $history_data_all;

    $history_data = $this->get_hrows($input, true);
    $data['CurrentPage'] = $history_data->currentPage();
    $data['listRows'] = $history_data->total();
    $data['lastPage'] = $history_data->lastPage();

    $history_data_item = $history_data->items();
    $history_data_item = array_map(function ($arr) {
      $arr->datetime = date('Y/m/d H:i:s', strtotime($arr->datetime));
      $arr->title = $arr->title;
      $arr->type_title = $arr->type_title;

      return $arr;
    }, $history_data_item);
    $data['history_data_item'] = $history_data_item;

    return $data;
  }

  private function get_hrows($conditions, $need_page=true) {
    $order_type = isset($conditions['order']) ? $conditions['order'] : 'desc';

    $history_data = DB::table('picked_history AS ph')
                      ->selectRaw('SUM(ph.num) as count, ph.p_code, ph.datetime, ph.product_id, ph.productinfo_type, ph.position_id, ph.position_number, pi.ISBN, pi.title, pt.title as type_title, pt.num as online_stock')
                      ->leftJoin('productinfo AS pi', 'ph.product_id', '=', 'pi.id')
                      ->leftJoin('productinfo_type AS pt', 'ph.productinfo_type', '=', 'pt.id')
                      ->where("ph.deal_position", 1)
                      ->groupByRaw('ph.p_code, ph.product_id, ph.productinfo_type, ph.datetime')
                      ->orderByRaw("ph.datetime $order_type");

    $row_per_page = isset($conditions['row_per_page']) ? $conditions['row_per_page'] : self::ROW_PER_PAGE;

    if ($need_page == true) {
      $history = $history_data
                  ->paginate($row_per_page)
                  ->appends($conditions);
    } else {
      $history = $history_data->get();
      $history = CommonService::objectToArray($history);
    }

    return $history;
  }
}