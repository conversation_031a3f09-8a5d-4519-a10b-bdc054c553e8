<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class NavigationMenuRequest extends FormRequest
{
    /**
     * 判斷使用者是否有權限執行此請求
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 取得驗證規則
     */
    public function rules(): array
    {
        $menuId = $this->route('navigation') ?? $this->route('id');

        return [
            'menu_key' => [
                'required',
                'alpha_dash',
                'max:50',
                Rule::unique('navigation_menus', 'menu_key')->ignore($menuId)
            ],
            'menu_name' => [
                'required',
                'string',
                'max:255'
            ],
            'menu_url' => [
                'required',
                'string',
                'max:255'
            ],
            'sort_order' => [
                'required',
                'integer',
                'min:0',
                'max:9999'
            ],
            'status' => [
                'required',
                'in:0,1'
            ],
            'target' => [
                'required',
                'in:_self,_blank'
            ]
        ];
    }

    /**
     * 取得自訂驗證錯誤訊息
     */
    public function messages(): array
    {
        return [
            'menu_key.required' => '選單識別碼為必填項目',
            'menu_key.alpha_dash' => '選單識別碼只能包含字母、數字、底線和連字符',
            'menu_key.unique' => '選單識別碼已存在',
            'menu_key.max' => '選單識別碼不能超過 50 個字元',
            'menu_name.required' => '選單名稱為必填項目',
            'menu_name.string' => '選單名稱必須是字串',
            'menu_name.max' => '選單名稱不能超過 255 個字元',
            'menu_url.required' => '選單連結為必填項目',
            'menu_url.string' => '選單連結必須是字串',
            'menu_url.max' => '選單連結不能超過 255 個字元',
            'sort_order.required' => '排序為必填項目',
            'sort_order.integer' => '排序必須是整數',
            'sort_order.min' => '排序不能小於 0',
            'sort_order.max' => '排序不能大於 9999',
            'status.required' => '狀態為必填項目',
            'status.in' => '狀態值無效',
            'target.required' => '開啟方式為必填項目',
            'target.in' => '開啟方式無效',
        ];
    }
}
