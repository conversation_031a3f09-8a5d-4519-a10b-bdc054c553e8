<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Response;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            // return route('login');

            // if($request->ajax()){
                /*回傳錯誤json資料*/
                $response = Response::make([
                    'errorcode' => 401,
                    'data' => [],
                    'message' => 'Unauthorized',
                ], 200, []);
                throw new HttpResponseException($response);
            // }else{
            //     /*回傳錯誤頁面*/
            //     abort(401, 'Unauthorized');
            // }
        }
    }
}
