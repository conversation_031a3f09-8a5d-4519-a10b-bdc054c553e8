<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SoldCountService;

class UpdateSoldCounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'product:update-sold-counts {--product-id= : 更新特定商品ID的已售出數量}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新商品的已售出數量';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $productId = $this->option('product-id');
        
        if ($productId) {
            // 更新特定商品
            $this->info("正在更新商品 ID: {$productId} 的已售出數量...");
            
            if (SoldCountService::updateActualSoldCount($productId)) {
                $info = SoldCountService::getSoldCountInfo($productId);
                $this->info("更新成功！");
                $this->info("起始數值: {$info['base_count']}");
                $this->info("實際數量: {$info['actual_count']}");
                $this->info("顯示數量: {$info['display_count']} ({$info['formatted_count']})");
            } else {
                $this->error("更新失敗！商品 ID: {$productId} 不存在。");
                return 1;
            }
        } else {
            // 更新所有商品
            $this->info('正在更新所有商品的已售出數量...');
            
            $updatedCount = SoldCountService::updateAllSoldCounts();
            
            $this->info("更新完成！共更新了 {$updatedCount} 個商品的已售出數量。");
        }
        
        return 0;
    }
}
