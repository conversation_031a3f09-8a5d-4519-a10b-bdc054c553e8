<?php

if (!function_exists('U')) {
    /**
     * Generate a new URL.
     *
     * @param  string|null  $path
     * @param  mixed  $parameters
     * @param  bool|null  $secure
     * @return string
     */
    function U($path = null, $parameters = [], $secure = null)
    {
        return url($path, $parameters, $secure);
    }
}
if (!function_exists('URL')) {
    /**
     * Generate a new URL.
     *
     * @param  string|null  $path
     * @param  mixed  $parameters
     * @param  bool|null  $secure
     * @return string
     */
    function URL($path = null, $parameters = [], $secure = null)
    {
        return url($path, $parameters, $secure);
    }
}