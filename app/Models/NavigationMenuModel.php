<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class NavigationMenuModel extends Model
{
    protected $table = 'navigation_menus';
    protected $primaryKey = 'id';
    protected $fillable = [
        'menu_key',
        'menu_name',
        'menu_url',
        'sort_order',
        'status',
        'target'
    ];

    /**
     * 作用域：只取啟用的選單
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * 作用域：按排序取得
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }
}
