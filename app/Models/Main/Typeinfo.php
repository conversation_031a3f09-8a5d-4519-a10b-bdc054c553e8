<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;

class Typeinfo extends Model
{
    //protected $connection= 'main_db';
    protected $table = 'typeinfo';
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id','distributor_id','parent_id','branch_id','title','pic',
        'start','end','limit_num','order_id','webtype_keywords','webtype_description','online'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        
    ];
}
