<?php

namespace App\Models\Main;

use Illuminate\Database\Eloquent\Model;

class Excel extends Model
{
    protected $connection= 'main_db';
    protected $table = 'excel';
    //protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id','value1','value2'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        
    ];
}
