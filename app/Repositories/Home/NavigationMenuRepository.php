<?php

namespace App\Repositories\Home;

use App\Models\NavigationMenuModel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NavigationMenuRepository
{
    protected $model;

    public function __construct(NavigationMenuModel $model)
    {
        $this->model = $model;
    }

    /**
     * 取得所有選單（按排序）
     */
    public function getAllOrdered(): Collection
    {
        return $this->model->ordered()->get();
    }

    /**
     * 取得啟用的選單（按排序）
     */
    public function getActiveOrdered(): Collection
    {
        return $this->model->active()->ordered()->get();
    }

    /**
     * 根據 ID 取得選單
     */
    public function findById(int $id): ?NavigationMenuModel
    {
        return $this->model->find($id);
    }

    /**
     * 根據 ID 取得選單（找不到時拋出例外）
     */
    public function findByIdOrFail(int $id): NavigationMenuModel
    {
        return $this->model->findOrFail($id);
    }

    /**
     * 根據 menu_key 取得選單
     */
    public function findByMenuKey(string $menuKey): ?NavigationMenuModel
    {
        return $this->model->where('menu_key', $menuKey)->first();
    }

    /**
     * 建立新選單
     */
    public function create(array $data): NavigationMenuModel
    {
        return $this->model->create($data);
    }

    /**
     * 更新選單
     */
    public function update(NavigationMenuModel $menu, array $data): bool
    {
        return $menu->update($data);
    }

    /**
     * 刪除選單
     */
    public function delete(NavigationMenuModel $menu): bool
    {
        return $menu->delete();
    }

    /**
     * 更新單個選單的排序
     */
    public function updateSortOrder(int $id, int $sortOrder): bool
    {
        return $this->model->where('id', $id)->update(['sort_order' => $sortOrder]);
    }

    /**
     * 批量更新排序
     */
    public function updateMultipleSortOrders(array $orders): bool
    {
        try {
            DB::beginTransaction();
            foreach ($orders as $order) {
                $this->updateSortOrder($order['id'], $order['sort_order']);
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            Log::info("navigation error at NavigationMenuRepository@updateMultipleSortOrders", ['message' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 檢查 menu_key 是否已存在（排除特定 ID）
     */
    public function menuKeyExists(string $menuKey, ?int $excludeId = null): bool
    {
        $query = $this->model->where('menu_key', $menuKey);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }
}
