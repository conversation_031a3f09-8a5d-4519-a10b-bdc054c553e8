<?php

namespace App\Repositories\Home;

use Illuminate\Support\Facades\DB;

class ProductPriceSearchRepository
{
    /**
     * 取得價格搜尋選項
     *
     * @param int $distributor_id: 經銷商ID
     * @return void
     */
    public function getProductPriceSearchs($distributor_id = 0)
    {
        return DB::table('product_price_search')
            ->whereRaw('online=1')
            ->whereRaw('distributor_id="' . $distributor_id . '"')
            ->orderByRaw('orders asc, id asc')->get();
    }
}
