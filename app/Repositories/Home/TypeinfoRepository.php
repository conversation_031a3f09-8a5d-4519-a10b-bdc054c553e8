<?php

namespace App\Repositories\Home;

use Illuminate\Support\Facades\DB;

class TypeinfoRepository
{
    /**
     * 取得指定 parent_id 的子分類
     * @param int $parent_id
     * @return \Illuminate\Support\Collection
     */
    public function getSubTypeinfoByParentId($parent_id)
    {
        return DB::table('typeinfo')
            ->select('typeinfo.title', 'typeinfo.id')
            ->whereRaw("typeinfo.parent_id = {$parent_id}
                        AND typeinfo.branch_id = 0
                        AND typeinfo.online = 1
                        AND (
                          typeinfo.end <= 0 OR
                          (typeinfo.start < " . time() . " AND typeinfo.end > " . time() . ")
                        )
                    ")
            ->orderByRaw('typeinfo.order_id')->get();
    }
    /**
     * 依 id 取得 typeinfo
     * @param int $id
     * @return object|null
     */
    public function getTypeinfoById($id)
    {
        return DB::table('typeinfo')
            ->select('title', 'id', 'parent_id', 'branch_id', 'webtype_keywords', 'webtype_description', 'online')
            ->whereRaw(" (
                    typeinfo.end <= 0
                    OR
                    (typeinfo.start < " . time() . " AND typeinfo.end > " . time() . ")
                    )")
            ->where('id', $id)
            ->first();
    }
}
