<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
        // 自訂 Guard
        Auth::viaRequest('custom-basic', function ($request) {
            $username = $request->getUser();
            $password = $request->getPassword();
    
            // 查詢自訂資料表
            $user = \DB::connection('main_db')->table('basic_auth')->where('username', $username)->first();
            if ($user && \Hash::check($password, $user->password)) {
                return $user;
            }
    
            return null;
        });
    }
}
