<?php
namespace App\Providers;

use Illuminate\Translation\TranslationServiceProvider as BaseTranslationServiceProvider;
use Illuminate\Translation\Translator;
use App\Services\CustomFileLoader;

class CustomTranslationServiceProvider extends BaseTranslationServiceProvider
{
    protected function registerLoader()
    {
        $this->app->singleton('translation.loader', function ($app) {
            return new CustomFileLoader($app['files'], $app['path.lang']);
        });
    }
}
