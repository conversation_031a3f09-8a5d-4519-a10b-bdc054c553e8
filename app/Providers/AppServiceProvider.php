<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\CustomUrlGenerator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('url', function ($app) {
            $url = new CustomUrlGenerator($app['router']->getRoutes(), $app['request']);
            if (app()->environment('production')) { // 線上環境強制使用HTTPS協議
                $url->forceScheme('https');
            }
            return $url;
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
