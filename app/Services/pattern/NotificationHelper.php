<?php
namespace App\Services\pattern;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Minishlink\WebPush\WebPush;
use Minishlink\WebPush\Subscription;

class NotificationHelper
{
  public static function send($user_id, $title='', $msg='', $open_url=''){
    $subscription = DB::connection('main_db')->table('subscription')->where('user_id="'.$user_id.'"')->get();
    if(!$subscription){ return; }
    if(!$title){ return; }
    if(!$msg){ return; }
    $payload = [
      'notification_id' => time(),
      'title' => $title,
      'msg' => $msg,
      'open_url' => $open_url,
    ];

    $result = null;
    foreach ($subscription as $key => $value) {
      $subscription_data = [
        'contentEncoding'	=> 'aes128gcm',
        'endpoint'				=> $value['endpoint'],
        'expirationTime'	=> $value['expirationTime'],
        'keys'			=> [
          'auth'		=> $value['auth'],
          'p256dh'	=> $value['p256dh']
        ]
      ];
      $result = self::do_send($subscription_data, $payload);
      // dump($result);
    }
    return $result;
  }

  public static function do_send($subscription_data, $payload){
    $subscription = Subscription::create($subscription_data);

    $auth = array(
      'VAPID' => array(
        'subject' 	=> 'https://github.com/Minishlink/web-push-php-example/',
        'publicKey'	=> config('extra.notification.PUBKEY'),
        'privateKey'=> config('extra.notification.PRIKEY'),
      ),
    );
    // dump($auth);exit;
    if(!isset($payload['notification_id'])){
        $payload['notification_id'] = CommonService::geraHash(32);
      }

    $webPush = new WebPush($auth);
    $report = $webPush->sendOneNotification(
      $subscription,
      json_encode($payload, JSON_UNESCAPED_UNICODE)
    );

    // handle eventual errors here, and remove the subscription from your server if it is expired
    $endpoint = $report->getRequest()->getUri()->__toString();

    if ($report->isSuccess()) {
        return "[v] Message sent successfully for subscription {$endpoint}.";
    } else {
        return "[x] Message failed to sent for subscription {$endpoint}: {$report->getReason()}";
    }
  }
}