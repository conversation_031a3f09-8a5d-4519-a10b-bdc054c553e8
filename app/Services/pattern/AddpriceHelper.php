<?php
namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;

//Photonic Class
// use App\Services\DBtool\DBTextConnecter;

class AddpriceHelper
{   
  const PER_PAGE_ROWS = 20;

  /*依時間取得加價購資料*/
  static public function getListByDate($start, $end, $frontEnd=false){
    $start_st = strtotime($start);
    $end_st = strtotime($end);
    $db = DB::table('addprice');
    $list = $db->orWhere(function($query)use($start_st,$end_st){ /*搜尋的時間區包含開始或結束時間*/
                  $query->whereRaw('UNIX_TIMESTAMP(start_time) BETWEEN ? AND ?', [$start_st, $end_st])
                        ->whereRaw('UNIX_TIMESTAMP(end_time) BETWEEN ? AND ?', [$start_st, $end_st]);
                })
                ->orWhere(function($query)use($start_st,$end_st){ /*搜尋的時間區間在開始結束時間內*/
                  $query->whereRaw('UNIX_TIMESTAMP(start_time) <= ?', [$start_st])
                        ->whereRaw('UNIX_TIMESTAMP(end_time) >= ?', [$end_st]);
                })
                ->orWhere(function($query)use($start_st,$end_st){ /*搜尋的開始時間大於開始時間，且被設定為無結束時間*/
                  $query->whereRaw('UNIX_TIMESTAMP(start_time) <= ?', [$start_st])
                        ->where('end_time', '=', '0000-00-00 00:00:00');
                });
    if($frontEnd){ $list->having('online',1); }
    // dump($db->getLastSql());exit;
    return $list;
  }
}
