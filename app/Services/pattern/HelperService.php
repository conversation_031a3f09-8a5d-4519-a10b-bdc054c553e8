<?php

namespace App\Services\pattern;

/***
 * Edcode
 * 對密碼加密類
 *
 * 對密碼加密閉免被有心人盜取
 *
 ***/

use App\Http\Controllers\admin\Excel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use Database\Seeders\Shop_admin\ExcelSeeder;
use App\Services\CommonService;

class HelperService
{
    public static function getSeoData($config_db = "")
    {
        $seo = DB::connection($config_db)->table('seo')->find(1);
        return CommonService::objectToArray($seo);
    }
    public static function getMailData($config_db = "")
    {
        $seo = self::getSeoData($config_db);
        $system_email = DB::table('system_email')->where("id", 1)->find(1);
        $system_email = CommonService::objectToArray($system_email);
        $return_email = DB::table('admin')->select('email')->whereNotNull('email')->get();
        $return_email = CommonService::objectToArray($return_email);

        $globalMailData = [
            'mailHost'         =>    config('extra.mail.host'),
            'mailUsername'     =>    config('extra.mail.username'),
            'mailPassword'     =>    config('extra.mail.password'),
            'mailSubject'     =>    $seo['title'] . ' ' . Lang::get('系統信箱'),
            'mailFrom'         =>    config('extra.mail.username'),
            'mailFromName'     =>    $seo['title'],
            'system_email'     => $system_email,
            'return_email'   => $return_email,
        ];
        return $globalMailData;
    }
    public static function Mail_Send($Body = '', $type = 'admin', $client_email = '', $subject = NULL, $config_db = "")
    {
        $globalMailData = self::getMailData($config_db);
        // dump($globalMailData);
        if (!$globalMailData['mailPassword']) {
            return false;
        }
        $mail = new PHPMailer();
        $mail->IsSMTP();
        $mail->Host = $globalMailData['mailHost'];
        $mail->Port = config('extra.mail.port');
        $mail->SMTPAuth = true;
        $mail->SMTPSecure = config('extra.mail.ssl');
        $mail->CharSet = "UTF-8";
        $mail->Encoding = "base64";
        $mail->Username = $globalMailData['mailUsername'];
        $mail->Password = $globalMailData['mailPassword'];
        $mail->Subject = $globalMailData['mailSubject'] . ' ' . $subject;
        $mail->From = $globalMailData['mailFrom'];
        $mail->SMTPAutoTLS = false;
        $mail->FromName = $globalMailData['mailFromName'];
        $mail->Body = $Body;
        $mail->IsHTML(true);
        // dump($mail);

        switch ($type) {
            case 'client':
                $mail->AddAddress($client_email);
                break;

            case 'admin':
                foreach ($globalMailData['return_email'] as $k => $v) {
                    $mail->AddAddress($v['email']);
                }
                break;
        }

        if ($mail->Send()) {
            return true;
        } else {
            // dump($mail->ErrorInfo);
            // exit;//檢查錯誤
            return false;
        }
    }

    /*發送請求*/
    public static function http_request($url, $data = null, $headers = null)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (! empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            curl_setopt($curl, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
            ]);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        if ($headers) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }

    // 文字星號隱藏
    public static function hidestr($string, $start = 0, $length = 0, $re = '*')
    {
        if (empty($string)) return false;
        $strarr = array();
        $mb_strlen = mb_strlen($string);
        while ($mb_strlen) { //循环把字符串变为数组
            $strarr[] = mb_substr($string, 0, 1, 'utf8');
            $string = mb_substr($string, 1, $mb_strlen, 'utf8');
            $mb_strlen = mb_strlen($string);
        }
        $strlen = count($strarr);
        $begin  = $start >= 0 ? $start : ($strlen - abs($start));
        $end    = $last   = $strlen - 1;
        if ($length > 0) {
            $end  = $begin + $length - 1;
        } elseif ($length < 0) {
            $end -= abs($length);
        }
        for ($i = $begin; $i <= $end; $i++) {
            $strarr[$i] = $re;
        }
        if ($begin >= $last || $end > $last) return false;
        return implode('', $strarr);
    }

    /*回傳並命名系統控制功能起停用狀態*/
    public static function get_excel_function()
    {
        // 智障吔..service provider 會在每次請求時都執行一次..綁資料庫怎麼migration怎麼seeder??
        $excel = $default_excel_data = DB::connection('main_db')->table('excel')->orderBy('id', 'asc')->get();

        $excel_function = [];
        $excel_function['control_auto_logout'] = $excel[4]['value1'];           /* 自動登出 */

        $excel_function['control_pre_buy'] = $excel[0]['value1'];                         /* 商品可否設定超額購買 */
        $excel_function['control_card_pay'] = $excel[6]['value1'];                         /* 商品可否設定刷卡 */
        $excel_function['control_product_paying'] = $excel[20]['value1'];     /* 商品可否設定付款方法 */
        $excel_function['control_register'] = $excel[13]['value1'];                     /* 是否啟用報名功能 */
        $excel_function['control_product_shipping'] = $excel[14]['value1'];      /* 商品可否設定運法 */
        $excel_function['control_time_limit_prod'] = $excel[8]['value1'];          /* 首頁是否顯示限時搶購 */
        $excel_function['control_index_edm'] = $excel[10]['value1'];                    /* 首頁是否顯示EDM */
        $excel_function['control_sepc_price'] = $excel[9]['value1'];                    /* 是否使用特價商品(不限數量標籤) */
        $excel_function['control_copy_product'] = $excel[11]['value1'];       /* 後台複製商品 */

        $excel_function['control_point_duration'] = $excel[1]['value1'];        /* 點數到期週期(年) */
        $excel_function['control_point_duration_date'] = $excel[1]['value2']; /* 點數到期日期 */

        $excel_function['thirdpart_money'] = $excel[15]['value1'];                        /* 是否啟用第三方金流 */
        $excel_function['thirdpart_logistic'] = $excel[16]['value1'];                    /* 是否啟用第三方物流 */
        $excel_function['thirdpart_invoice'] = $excel[17]['value1'];              /* 是否啟用第三方發票 */

        $excel_function['control_FirstBuyDiscount'] = $excel[18]['value1'];   /* 首購優惠 */
        $excel_function['control_VipDiscount'] = $excel[19]['value1'];        /* VIP等級 */
        $excel_function['control_platform'] = $excel[21]['value1'];           /* 是否平台化 */
        $excel_function['control_img_quantity'] = $excel[2]['value1'];        /* 商品照片數量 */
        $excel_function['control_prod_type_layer'] = $excel[3]['value1'];     /* 商品階層品項 */
        $excel_function['control_upload_film'] = $excel[5]['value1'];         /* 商品圖是否可以放影片 */
        $excel_function['control_prod_edm'] = $excel[7]['value1'];            /* 商品是否顯示EDM */
        $excel_function['control_social_share'] = $excel[12]['value1'];       /* 商品是否可單一商品社群分享 */
        $excel_function['control_down_line'] = $excel[22]['value1'];          /* 是否使用招募會員 */

        $excel_function['invoice_style_text'] = [                             /* 發票開立方式選項 */
            "1" => '個人實體紙本發票',
            "2" => '個人電子郵件寄送發票',
            "3" => '個人共通性載具',
            "4" => '公司戶發票',
            "5" => '捐贈',
        ];
        // dd($excel_function);
        return $excel_function;
    }

    /*依管理者帳號回傳有使用的功能*/
    public static function get_frontend_user_use_function()
    {

        $user_function_setting_current = DB::table('admin')->select('purview')->where('permission', 'current')->first()->purview;
        $user_function_setting_current = json_decode($user_function_setting_current, true);
        $function_result_current = self::arrange_use_function($user_function_setting_current);
        return $function_result_current;
    }

    /**
     * 取出後台選單
     */
    public static function arrange_use_function($user_function_setting, $admin_type = '', $permission = 'no')
    {
        $close_function = [];
        $close_desk = [];
        $close_desk_admin = [];

        /*總可用功能(current)*/
        $current_Block = DB::table('admin')->select('purview')->where('permission', 'current')->first()->purview;
        $current_Block = json_decode($current_Block, true);

        $first_list = DB::table('backstage_menu')->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
        $first_list = CommonService::objectToArray($first_list);
        $second_list = DB::table('backstage_menu_second')->orderBy('sort', 'asc')->orderBy('id', 'asc')->get();
        $second_list = CommonService::objectToArray($second_list);

        $show_list = [];
        foreach ($first_list as $k => $v) {
            $show_list[$v['id']]['id'] = $v['id'];
            $show_list[$v['id']]['name'] = $v['name'];
            $show_list[$v['id']]['title'] = $v['title'];
        }
        foreach ($second_list as $k => $v) {
            $v['front_desk'] = $v['front_desk'] ?? '';
            $v['url_show'] = $v['url_show'] ?? '';
            if (empty($show_list[$v['backstage_menu_id']]['sub'])) {
                $show_list[$v['backstage_menu_id']]['sub'] = [];
            }
            if ($admin_type == 'distribution' && $v['target'] == '_parent') { /*供應商登入替換網址*/
                $v['url_show'] = str_replace('/admin/', '/' . $admin_type . '/', $v['url']);
                $v['url_show'] = str_replace('/order/', '/order' . $admin_type . '/', $v['url_show']);
                $v['url_show'] = url($v['url_show']);
            } else {
                $v['url_show'] = $v['url'];
                $v['url_show'] = url($v['url']);
            }
            $ck = 0;
            if (!empty($user_function_setting[$v['backstage_menu_id']])) {
                foreach ($user_function_setting[$v['backstage_menu_id']] as $bk => $bv) {
                    if ($v['id'] == $bv) {
                        $close_function[$v['name']] = 1; //紀錄被關閉的功能
                        $close_desk[strtolower($v['front_desk'])] = 1;
                        $close_desk_admin[strtolower($v['url_show'])] = 1;
                        $ck = 1;
                        // break;
                    }
                }
            }

            $need_hide_functions = false;
            if ($admin_type != 'admin') {
                $need_hide_functions = true;
            } else if ($permission == 'no') { /*一般管理者*/
                $need_hide_functions = true;
            }
            if ($need_hide_functions) {
                if (!empty($current_Block[$v['backstage_menu_id']])) { /*一般管理者*/
                    foreach ($current_Block[$v['backstage_menu_id']] as $bk => $bv) {
                        if ($v['id'] == $bv) {
                            $close_function[$v['name']] = 1; //紀錄被關閉的功能
                            $close_desk[strtolower($v['front_desk'])] = 1;
                            $close_desk_admin[strtolower($v['url_show'])] = 1;
                            $ck = 1;
                        }
                    }
                }
            }
            if ($ck == 0) {
                array_push($show_list[$v['backstage_menu_id']]['sub'], $v);
            }
        }
        foreach ($show_list as $k => $v) {
            if (count($v['sub']) == 0) {
                unset($show_list[$k]);
            }
        }
        // dump($show_list);exit();
        $result['show_list'] = $show_list;

        $show_list_group = [];
        foreach ($show_list as $key => $value) {
            if (!isset($show_list_group[$value['title']])) {
                $show_list_group[$value['title']] = ['title' => $value['title'], 'show_list' => []];
            }
            array_push($show_list_group[$value['title']]['show_list'], $value);
        }
        // dump($show_list_group);exit;
        $result['show_list_group'] = $show_list_group;
        $result['close_function'] = $close_function;
        $result['close_desk'] = $close_desk;
        $result['close_desk_admin'] = $close_desk_admin;
        return $result;
    }

    public static function zip32_api($addr)
    {
        preg_match('/^(\d)+/', $addr, $zip); /*找出字串最前方的數字*/
        if ($zip) {
            return $zip[0]; /*回傳自己填寫的郵遞區號*/
        } else {
            preg_match('/(?!\d).*$/', $addr, $words); /*找出去除字串最前方的數字後的字串*/
            if ($words) {
                $addr = $words[0];
            }
        }
        // dump($addr);

        $url = "https://zip5.5432.tw/zip5json.py?adrs=" . $addr;

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        $output = json_decode(curl_exec($curl));
        curl_close($curl);

        return $output->zipcode;
    }
}
