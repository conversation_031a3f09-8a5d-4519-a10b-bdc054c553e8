<?php
namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;

//Photonic Class
// use App\Services\DBtool\DBTextConnecter;
use App\Services\CommonService;

class AskpriceHelper
{   
  const PER_PAGE_ROWS = 20;
  private $resTableName;
  private $resTableName_record;
  // private $DBTextConnecter;
  // private $DBTextConnecter_record;

  public function __construct() {
    $this->resTableName = 'askprice';
    $this->resTableName_record = 'askprice_record';
    // $this->DBTextConnecter = DBTextConnecter::withTableName($this->resTableName);
    // $this->DBTextConnecter_record = DBTextConnecter::withTableName($this->resTableName_record);
  }

  public function getList($other_where='', $need_history=false){
    $type = request()->post('type');
    $searchKey = request()->post('searchKey') ?? '';
    $searchKey = trim($searchKey);
    $start = request()->post('start');
    $end = request()->post('end');
    $status = request()->post('status') ?? '';
    $page = request()->post('page') ?? 1;
    // var_dump($searchKey);
    $list = DB::table($this->resTableName.' as a')
              ->select('a.*',
                  'ar.ask','ar.ask_time','ar.response','ar.response_time','ar.price','ar.price_final','ar.num','ar.expired_date','ar.status','ar.agree','ar.bought')
              ->join( $this->resTableName_record.' as ar', 'ar.id','a.askprice_record_id');
    if($type == 'keyword' && !empty($searchKey)){
      $list = $list->where(function($query) use($searchKey){
        $query->orWhere([
          'a.name' => ['like', '%'.$searchKey.'%'],
          'a.phone' => ['like', '%'.$searchKey.'%'],
          'a.email' => ['like', '%'.$searchKey.'%'],
          'a.product_name' => ['like', '%'.$searchKey.'%'],
          'a.product_type_name' => ['like', '%'.$searchKey.'%'],
        ]);
      });
      $search = $searchKey;
    }else if($type == 'date'){
      $list = $list->where(function($query)use($start,$end){
        $query->orWhere([ /*搜尋的時間區包含開始或結束時間*/
          'a.create_time' => ['between', [strtotime($start), strtotime($end.' + 1Day')]],
        ]);
      }); 
      $search = $start."~".$start;
    }else{
      $search = "";
    }
    if($status!==''){
      $list = $list->where('ar.status', $status);
    }
    if($other_where!=''){
      $list = $list->whereRaw($other_where);
    }
    $list = $list->orderBy('a.id', 'desc')->get();

    $s_index = $page-1>= 0 ? ($page-1) * self::PER_PAGE_ROWS : 0;
    $contacts = array_slice(CommonService::objectToArray($list), $s_index, self::PER_PAGE_ROWS);
    foreach ($contacts as $key => $value) {
      $productinfo = DB::table('productinfo')->find($value['product_id']);
      $productinfo = CommonService::objectToArray($productinf);
      $pic = $productinfo ? json_decode($productinfo['pic'], true) : [];
      $contacts[$key]['product_pic'] = $pic ? $pic[0] : '';
      $contacts[$key]['create_time'] = date('Y-m-d H:i', $value['create_time']);
      $contacts[$key]['ask_time'] = date('Y-m-d H:i', $value['ask_time']);
      $contacts[$key]['response_time'] = $value['response_time'] ? date('Y-m-d H:i', $value['response_time']) : '';

      if($need_history){
        $detail = $this->getOne_by_main_id($value['id']);
        $contacts[$key]['history'] = $detail['history'];
      }
    }

    $total = count($list);
    /*計算總頁數*/
    if($total==0){
      $last_page = 1;
    }else{
      $last_page = (Int)($total / self::PER_PAGE_ROWS);
      $last_page += $total % self::PER_PAGE_ROWS !=0 ?  + 1 : 0;
    }

    $retData = ['contacts' => $contacts, 'search'=>$search, 'last_page'=>$last_page, 'total'=>$total];
    return $retData;
  }
  public function getOne($other_where=''){
    $contact_id = request()->post('contact_id') ?? '';
    return $this->getOne_by_main_id($contact_id, $other_where);
  }
  public function getOne_by_main_id($contact_id='', $other_where=''){  
    $main = DB::table($this->resTableName.' as a')->where('a.id', $contact_id);
    if($other_where){
      $main = $main->whereRaw($other_where);
    }
    $main = $main->first();
    $main = CommonService::objectToArray($main);
    if(!$main){ return [
      'main' => null,
      'current' => null,
      'history' => [],
    ]; }
    $productinfo = DB::table('productinfo')->find($main['product_id']);
    $productinfo = CommonService::objectToArray($productinfo);
    $pic = $productinfo ? json_decode($productinfo['pic'], true) : [];
    $main['product_pic'] = $pic ? $pic[0] : '';
    $main['create_time'] = date('Y-m-d H:i', $main['create_time']);

    $current = DB::table($this->resTableName_record)->find($main['askprice_record_id']);
    $current = CommonService::objectToArray($current);
    if(!$current){ return [
      'main' => null,
      'current' => null,
      'history' => [],
    ]; }
    $current['ask_time'] = date('Y-m-d H:i', $current['ask_time']);
    $current['response_time'] = $current['response_time'] ? date('Y-m-d H:i', $current['response_time']) : '';


    $history = DB::table($this->resTableName_record)->whereRaw('id !="'.$main['askprice_record_id'].'"')
                                                    ->where('askprice_id', $contact_id)
                                                    ->orderByRaw('id desc')->get();
    $history = CommonService::objectToArray($history);
    foreach ($history as $key => $value) {
      $history[$key]['ask_time'] = date('Y-m-d H:i', $value['ask_time']);
      $history[$key]['response_time'] = $value['response_time'] ? date('Y-m-d H:i', $value['response_time']) : '';
    }

    return [
      'main' => $main,
      'current' => $current,
      'history' => $history,
    ];
  }
}
