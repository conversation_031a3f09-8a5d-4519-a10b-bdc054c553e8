<?php
namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\DBtool\DBFileConnecter;

class MemberArticle
{
  static public $tableName = 'member_article';
  static public $share_article_img_path = '/public/static/index/upload/share_article';

  /*取得任務資料*/
  static public function get_share_article(array $params=[], bool $id_as_key=false){
    $db_data = DB::table(self::$tableName);
    $db_data->selectRaw('*, FROM_UNIXTIME(create_time) AS create_time_f');

    if(isset($params['id'])){ $db_data->where('id', $params['id']); }
    if(isset($params['user_id'])){ $db_data->where('user_id', $params['user_id']); }
    if(isset($params['front_show'])){ 
      if($params['front_show']==1){ /*前台顯示*/
        $db_data->where('show_status', 1)->where('show', 1);
      }else if($params['front_show']==2){ /*前台隱藏*/
        $db_data->where(function($query){
          return $query->orWhere('show_status', 0)->orWhere('show', 0);
        });
      }
    }
    if(isset($params['name']) && $params['name']!=''){ 
      $db_data->where('name', 'LIKE', '%'.$params['name'].'%');
    }
    if(isset($params['member_key']) && $params['member_key']!=''){ 
      $users = Db::connection('main_db')->table('account')->select('id')
                                        ->where(function($query)use($params){
                                          return $query->orWhere('name', 'like', '%'.$params['member_key'].'%')
                                                       ->orWhere('number', 'like', '%'.$params['member_key'].'%')
                                                       ->orWhere('phone', 'like', '%'.$params['member_key'].'%');
                                        })
                                        ->get()->toArray();
      $user_ids = array_merge([-1], array_map(function($item){ return  $item->id; }, $users));
      $db_data->whereIn('user_id', $user_ids);
    }
    
  
    /*分頁*/
    if(isset($params['count_of_items'])){ /*有傳入一頁數量*/
      $index_and_length = CommonService::get_model_limit_index_and_length($params['count_of_items'], $params['page'] ?? 1);
      if($index_and_length[1]){ /*需依分頁篩選*/
        $db_data->offset($index_and_length[0])->limit($index_and_length[1]);
      }
    }

    $db_data = $db_data->orderBy('orders', 'asc')->orderBy('create_time', 'desc')->get();
    $db_data = CommonService::objectToArray($db_data);

    if($id_as_key){
      $temp_data = [];
      foreach ($db_data as $value) {
        $temp_data[$value['id']] = $value;
      }
      $db_data = $temp_data;
    }
    return ['db_data'=>$db_data];
  }

  static public function create_article($newData){
    if(!($newData['name']??'')){
      throw new \Exception(Lang::get('請輸入文章名稱'));
    }
    if(($newData['show_status']??'')==''){
      throw new \Exception(Lang::get('請選擇文章狀態'));
    }
    if(!($newData['img']??'')){
      throw new \Exception(Lang::get('請選擇文章圖片'));
    }
    if(!($newData['content']??'')){
      throw new \Exception(Lang::get('請輸入文章內容'));
    }else{
      /*過濾文章內容*/
      $config = \HTMLPurifier_Config::createDefault();
      $purifier = new \HTMLPurifier($config);
      $newData['content'] = $purifier->purify($newData['content']);
    }
    $newData['show'] = 1; /*預設管理員控制狀態為 顯示*/
    $newData['orders'] = 0; /*預設管理員控制排序為 0*/
    $newData['create_time'] = time();
    $newData['img'] = CommonService::uploadFile(self::$share_article_img_path, $newData['img']);

    $DBTextConnecter = DBTextConnecter::withTableName(self::$tableName);
    $DBTextConnecter->setDataArray($newData);
    $new_id = $DBTextConnecter->createTextRow();
    return $new_id;
  }

  static public function check_article_auth($article_id, $user_id){
    $data = self::get_share_article([
      'id' => $article_id,
      'user_id' => $user_id,
    ]);
    if(count($data['db_data'])==0){
      throw new \Exception(Lang::get('無權限操作'));
    }
  }
  static public function edit_article($saveData){
    unset($saveData['create_time']);
    if(isset($saveData['id']) && $saveData['id']==''){
      throw new \Exception(Lang::get('未設定id'));
    }
    if(isset($saveData['name']) && $saveData['name']==''){
      throw new \Exception(Lang::get('請輸入文章名稱'));
    }
    if(isset($saveData['show_status']) && $saveData['show_status']==''){
      throw new \Exception(Lang::get('請選擇文章狀態'));
    }
    if(isset($saveData['show']) && $saveData['show']==''){
      throw new \Exception(Lang::get('請選擇狀態'));
    }
    if(isset($saveData['orders']) && $saveData['orders']==''){
      throw new \Exception(Lang::get('請輸入排序'));
    }
    if(isset($saveData['img'])){
      if($saveData['img']==''){
        throw new \Exception(Lang::get('請選擇文章圖片'));
      }else if(substr($saveData['img'], 0, 4)=='data'){
        $saveData['img'] = CommonService::uploadFile(self::$share_article_img_path, $saveData['img']);
      }
    }
    if(isset($saveData['content'])){
      if($saveData['content']==''){
        throw new \Exception(Lang::get('請輸入文章內容'));
      }else{
        /*過濾文章內容*/
        $config = \HTMLPurifier_Config::createDefault();
        $purifier = new \HTMLPurifier($config);
        $saveData['content'] = $purifier->purify($saveData['content']);
      }
    }

    $DBTextConnecter = DBTextConnecter::withTableName(self::$tableName);
    $DBTextConnecter->setDataArray($saveData);
    $DBTextConnecter->upTextRow();
    return $saveData['id'];
  }
  static public function delete_share_article($article_id){
    $db_data = DB::table(self::$tableName);
    $db_data->where('id', $article_id)->delete();
  }

  static public function report_article($ip, $article_id, $note){
    if(!$ip){ throw new \Exception(Lang::get('資料有誤')); }
    if(!$article_id){ throw new \Exception(Lang::get('資料有誤')); }
    if(!$note){ throw new \Exception(Lang::get('請輸入檢舉說明')); }

    $article = Db::table('member_article')->where('id', $article_id)->first();
    if(!$article){
      throw new \Exception(Lang::get('無此文章'));
    }

    $has_record = Db::table('member_article_report')
                    ->where('ip', $ip)
                    ->where('article_id', $article_id)
                    ->where('create_time', '>=', strtotime(date('Y-m-d').' -7Days'))
                    ->first();
    if($has_record){
      throw new \Exception(Lang::get('7日內已檢舉過此文章'));
    }

    $contact_letter_admin = '親愛的管理員您好：<br>';
    $contact_letter_admin.= '系統收到使用者對以下文章的檢舉：<br>';
    $article_url = url('index/ShareArticle/detail').'?id='.$article->id;
    $contact_letter_admin.= '文章標題：'.$article->name.'<br>';
    $contact_letter_admin.= '系統編號：'.$article->id.'<br>';
    $contact_letter_admin.= '前台網址：<a href="'.$article_url.'" target="_blank">'.$article_url.'</a><br>';
    $contact_letter_admin.= '檢舉說明：<br>'.str_replace("\n", "<br>", $note);
    $contact_letter_admin.= '<br><br>若該文章確實有疑義，請至後台進行下架或刪除<br><br>';
    $mailBody = "
      <html>
        <head></head>
        <body>
          <div>
            ".$contact_letter_admin."
          </div>
          <div style='color:red;'>
            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
          </div>
        </body>
      </html>
    ";
    $subject = '檢舉 會員文章分享';
    // echo($mailBody);exit;
    HelperService::Mail_Send($mailBody, 'admin', '', $subject, '');

    Db::table('member_article_report')->insert([
      'ip' => $ip,
      'article_id' => $article_id,
      'note' => $note,
      'create_time' => time(),
    ]);
  }

  static public function get_interact_status($params){
    $record = Db::table('member_article_interact')->orderBy('id', 'desc');
    if($params['visitorId'] ?? ''){
      $record->where('visitorId', $params['visitorId']);
    }
    if($params['article_id'] ?? ''){
      $record->where('article_id', $params['article_id']);
    }
    if($params['act_type'] ?? ''){
      $record->where('act_type', $params['act_type']);
    }
    if($params['create_time_s'] ?? ''){
      $record->where('create_time', '>=', $params['create_time_s']);
    }
    if($params['create_time_e'] ?? ''){
      $record->where('create_time', '<', $params['create_time_e']);
    }

    if($params['sum_value'] ?? ''){
      $record->groupBy('article_id')->selectRaw('article_id, SUM(`value`) AS sum_value');
    }
    
    return $record->get()->toArray();
  }

  static public function interact($visitorId, $article_id, $act_type){
    if(!$visitorId){ throw new \Exception(Lang::get('資料有誤')); }
    if(!$article_id){ throw new \Exception(Lang::get('資料有誤')); }
    if(!$act_type){ throw new \Exception(Lang::get('資料有誤')); }

    $article = Db::table('member_article')->where('id', $article_id)->first();
    if(!$article){
      throw new \Exception(Lang::get('無此文章'));
    }

    $value = 0;
    $check_record = Db::table('member_article_interact')
                      ->orderBy('id', 'desc')
                      ->where('visitorId', $visitorId)
                      ->where('article_id', $article_id)
                      ->where('act_type', $act_type);
    if($act_type==1){ /*瀏覽次數*/
      /*1天內沒瀏覽紀錄*/
      $has_record = $check_record->where('create_time', '>=', strtotime(date('Y-m-d').' -1Days'))->first();
      if(!$has_record){ $value = 1; } /*瀏覽次數+1*/
    }else if($act_type==2){ /*喜歡or取消喜歡文章*/
      $has_record = $check_record->first();
      if(($has_record->value??-1)==-1){ /*沒紀錄 或 紀錄為取消*/
        $value = 1; /*設定為喜歡*/
      }else{
        $value = -1; /*設定為取消喜歡*/
      }
    }else{
      throw new \Exception(Lang::get('無此互動類型'));
    }

    if($value==0){
      throw new \Exception(Lang::get('無資料需要修改'));
    }else{
      Db::table('member_article_interact')->insert([
        'visitorId' => $visitorId,
        'article_id' => $article_id,
        'act_type' => $act_type,
        'create_time' => time(),
        'value' => $value,
      ]);
    }
    return $value;
  }

  /*根據互動類型、任務開始時間、任務結束時間 統計文章所屬會員的累計互動數量*/
  static public function calculate_record($act_type, $task_time_s, $task_time_e){
    $interact_cal = self::get_interact_status([
      'act_type' => $act_type,
      'create_time_s' => strtotime($task_time_s),
      'create_time_e' => strtotime($task_time_e.' +1Day'),
      'sum_value' => 1,
    ]);
    // dump($interact_cal);
    $article_ids = array_map(function($vo){return $vo->article_id;}, $interact_cal);
    // dump($article_ids);
    $article_to_user = DB::table('member_article')->whereIn('id', $article_ids)->pluck('user_id', 'id')->toArray();
    // dump($article_to_user);
    $user_cal = [];
    foreach ($interact_cal as $interact_data) {
      $user_id = $article_to_user[$interact_data->article_id] ?? 0;
      if(!isset($user_cal[$user_id])){ $user_cal[$user_id]=0; }
      $user_cal[$user_id] += $interact_data->sum_value;
    }
    // dump($user_cal);exit;
    return $user_cal;
  }
}