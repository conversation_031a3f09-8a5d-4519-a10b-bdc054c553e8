<?php
namespace App\Services\pattern;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Route;

//Photonic Class
use App\Services\pattern\HelperService;
use App\Services\pattern\MemberInstance;
use App\Http\Controllers\home\PublicController;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;

//QrCode 套件
use chillerlan\QRCode\QRCode;

class ExaminationHelper
{
    public static $types_need_limit = ['text', 'textarea', 'number', 'file'];
    public static $types_need_option = ['radio', 'radio_box', 'checkbox', 'checkbox_time', 'checkbox_box', 'select'];
    public static $types_need_checked = ['radio', 'radio_box', 'checkbox', 'checkbox_time', 'checkbox_box'];
    public static $weekday = ['日', '一', '二', '三', '四', '五', '六'];

    static public function get_fields_type_par(){
        return [
            'types_need_limit' => self::$types_need_limit,
            'types_need_option' => self::$types_need_option,
            'types_need_checked' => self::$types_need_checked,
        ];
    }

    /*取得 判斷你有沒有權限看報名者資料的where篩選語法*/
    static public function get_yours_where($email, $backstage=false){
        if($backstage){ /*呼叫端為後台*/
            if(session()->get('admin.id')){ /*確定有登入後台，可看全部*/
                $yours_where = "true = true";
            }
            else if(session()->get('roll_call')){ /*有準備點名，可看全部*/
                $yours_where = "true = true";
            }
        }else{
            if($email){ /*有輸入信箱，依信箱查詢*/
                $yours_where = "email = '".$email."'";
            }
            else if(session()->get('user.id')){ /*有登入會員，可看此會員及同session_id的*/
                $yours_where = "(
                ( user_id='".session()->get('user.id')."' AND user_id!=0 ) OR 
                ( session_id='".session_id()."' AND order_id=0 )
                )";
            }
            else{ /*其他，僅可看同session_id的*/
                $yours_where = "session_id = '".session_id()."'";
            }
        }
        // dd($yours_where);
        return $yours_where;
    }
    /*取得排除取消訂單的sql where 篩選語法*/
    static public function get_not_include_cancel_order_where(){
        $not_include_cancel_order = '';
        $order_id_cancel = [];
        $order_cancel = DB::connection('main_db')->table('orderform')->select('id')->whereRaw("status not in ('New', 'Complete')")->get();
        $order_cancel = CommonService::objectToArray($order_cancel);
        if(count($order_cancel)>0){
            foreach ($order_cancel as $key => $value) {
                array_push($order_id_cancel, $value['id']);
            }
            // dump($order_id_cancel);
            $not_include_cancel_order = "ex.order_id not in (".join(",",$order_id_cancel).")";
        }

        return $not_include_cancel_order;
    }
    /*依商品品項id(、報名者id、訂單id) 取得自己填寫的報名資料 (預設取得 不看品項、不看報名者、未成立訂單 的報名資料)*/
    static public function get_myself_type_examinee_info($type_id=0, $examinee_id=0, $order_id=null, $email="", $post_data=[]){
        $examinee_info = DB::table('examinee_info as ex');

        /*取得 判斷你有沒有權限看報名者資料的where篩選語法*/
        $yours_where = self::get_yours_where($email, $post_data['backstage'] ?? false);
        $examinee_info = $examinee_info->whereRaw($yours_where);
        if($examinee_id!=0){ /*有設定報名者id*/
            $examinee_info = $examinee_info->where('id', $examinee_id);
        }

        // 搜尋訂單id (null->不搜尋、 -1->已成立的, 0->未成立的, 其他->指定訂單)
        if($order_id==-1){
            $examinee_info = $examinee_info->whereRaw('order_id != 0');
        }
        else if($order_id!==null){
            $examinee_info = $examinee_info->where('order_id', $order_id);
        }

        // 處理搜尋篩選
        $not_search_type_id = true;
        if(isset($post_data['search_cond'])){
            $search_cond = $post_data['search_cond'];
            // dump($search_cond);

            /*訂單編號查詢*/
            if( isset($search_cond['order_number']) ){
                if($search_cond['order_number']){
                    $order_search_where = '';
                    $order_ids = [];
                    $orderform = DB::connection('main_db')->table('orderform')->select('id')->where("order_number", 'LIKE', '%'.$search_cond['order_number'].'%')->get();
                    $orderform = CommonService::objectToArray($orderform);
                    if(count($orderform)>0){
                        foreach ($orderform as $key => $value) {
                            array_push($order_ids, $value['id']);
                        }
                        // dump($order_ids);
                        $order_search_where = "ex.order_id in (".join(",",$order_ids).")";
                    }else{
                        $order_search_where = "ex.order_id in (0)";
                    }
                    $examinee_info = $examinee_info->whereRaw($order_search_where);
                }
            }
            /*訂單繳費狀況查詢*/
            if( isset($search_cond['receipts_state']) ){
                if($search_cond['receipts_state']!==""){
                    $order_search_where = '';
                    $order_ids = [];
                    $orders = DB::connection('main_db')->table('orderform')->select('id')->where("receipts_state", $search_cond['receipts_state'])->get();                    
                    $orders = CommonService::objectToArray($orders);
                    if(count($orders)>0){
                        foreach ($orders as $key => $value) {
                            array_push($order_ids, $value['id']);
                        }
                        // dump($order_ids);
                        $order_search_where = "ex.order_id in (".join(",",$order_ids).")";
                    }else{
                        $order_search_where = "ex.order_id in (0)";
                    }
                    $examinee_info = $examinee_info->whereRaw($order_search_where);
                }
            }

            /*報名狀態*/
            if( isset($search_cond['cancel_status']) ){
                if($search_cond['cancel_status']!=""){
                    $examinee_info = $examinee_info->where('cancel', $search_cond['cancel_status']);
                }
            }

            /*候補狀態*/
            if( isset($search_cond['reg_status']) ){
                if($search_cond['reg_status']!==""){
                    if($search_cond['reg_status']=="-1"){
                        $examinee_info = $examinee_info->whereIn('reg_status', [1,2]);
                    }
                    else{
                        $examinee_info = $examinee_info->where('reg_status', $search_cond['reg_status']);
                    }
                }
            }

            /*商品查詢*/
            if( isset($search_cond['prod_id']) ){
                if($search_cond['prod_id']!=""){
                    $examinee_info = $examinee_info->where('prod_id', $search_cond['prod_id']);
                }
            }
            /*商品品項查詢*/
            if( isset($search_cond['type_id']) ){
                if($search_cond['type_id']!=""){
                    $examinee_info = $examinee_info->whereRaw('type_id REGEXP "'.$search_cond['type_id'].'_?.*"');
                    $not_search_type_id = false;
                }
            }

            /*報名欄位查詢*/
            if( isset($search_cond['register_data']) ){
                // dump($search_cond);
                // $option_join_type = 'AND';
                $option_join_type = 'OR';

                $register_data = (Array)json_decode($search_cond['register_data']);
                $register_data_where = "";
                foreach ($register_data as $key => $value) {
                    if($value!="" && $value!=[]){
                        if( is_array($value) ){ /*選項類型*/
                            $register_data_where .= '(';
                            foreach ($value as $vk => $vv) {
                                if($vv!=""){
                                    $register_data_where .= '(JSON_SEARCH(register_data,\'all\',\''.$vv.'%\') IS NOT NULL)';
                                    //$vv = json_encode($vv, JSON_UNESCAPED_UNICODE);
                                    //$register_data_where .= 'JSON_CONTAINS(register_data->\'$.'.$key.'\', \'['.$vv.']\', \'$\')';
                                }else{
                                    $register_data_where .= '(
                                        register_data like \'%"' .$key. '":[]%\' OR
                                        NOT JSON_CONTAINS_PATH(register_data, \'one\', \'$.'.$key.'\') 
                                    )';
                                }
                                $register_data_where .= $option_join_type.' ';
                            }
                            $register_data_where .= ' '.($option_join_type=='AND' ? 'true' : 'false').'  ) AND ';
                        }
                        else if (is_object($value)) { /*檔案類型*/
                            if($value->file_name){
                                $str = CommonService::replace_str_to_do_sql_search($value->file_name);
                                $register_data_where .= 'register_data like \'%"' .$key. '":{"file_name":"%' .$str. '%","data"%\' AND ';
                            }
                        }
                        else{
                            $str = CommonService::replace_str_to_do_sql_search($value);
                            $register_data_where .= 'register_data like \'%"' .$key. '":"%' .$str. '%"%\' AND ';
                        }
                    }
                }
                $register_data_where .= ' true';
                // dd($register_data_where);
                if($register_data_where){
                    $examinee_info = $examinee_info->whereRaw($register_data_where);
                }
                // dd($register_data_where);
            }

            /*是否取消報名*/
            if( isset($search_cond['ex_cancel']) ){
                if($search_cond['ex_cancel']!=""){
                    $examinee_info = $examinee_info->whereRaw('ex.cancel', $search_cond['ex_cancel']);
                }
            }
        }

        if($type_id!=0 && $type_id!='' &&$not_search_type_id){ /*有設定商品品項id 且 無搜尋品項*/
            $examinee_info = $examinee_info->where('type_id', $type_id);
        }

        //排除取消訂單
        $not_include_cancel_order = self::get_not_include_cancel_order_where();
        if($not_include_cancel_order != ''){
            $examinee_info = $examinee_info->whereRaw($not_include_cancel_order);
        }

        $examinee_data = $examinee_info->orderByRaw('id asc')->get();
        // dd($examinee_info->getLastSql());
        $examinee_data = CommonService::objectToArray($examinee_data);
        foreach ($examinee_data as $key => $value) {
            $examinee_data[$key]['register_data'] = str_replace("\\n", "\\\\n", $value['register_data']);
        }
        // dd($examinee_data);

        return $examinee_data;
    }
    static public function get_myself_type_examinee_info_with_register_data($type_id, $order_id=null, $post_data=[]){
        /*依條件取得報名者們*/
        if(isset($post_data['search_cond'])){
            $type_id = isset($post_data['search_cond']['type_id']) ? $post_data['search_cond']['type_id'] : $type_id;
        }
        $examinees = self::get_myself_type_examinee_info($type_id, $examinee_id=0, $order_id, $email="", $post_data);
        foreach ($examinees as $k => $v) {
            $examinees[$k]['register_data'] = (Array)json_decode($v['register_data']);
        }
        return $examinees;
    }

    /*以品項id取得品項及商品資料*/
    static public function get_prodtype_and_prodtitle_by_type_id($type_id){
        $productinfo_type = DB::table('productinfo_type as py')
                                ->select('py.*', 'p.title as p_title', 'p.is_registrable', 'p.is_roll_call')
                                ->join('productinfo p', 'p.id = py.product_id')
                                ->where('py.id', $type_id)
                                ->first();
        return CommonService::objectToArray($productinfo_type);
    }
    /*依商品ID取得報名欄位*/
    static public function get_fields_by_prod_id($prod_id=""){
        if($prod_id==""){ return []; }
        $fields = DB::table('productinfo_register_fields')
                        ->where('prod_id', $prod_id)
                        ->where('online', 1)
                        ->orderByRaw('order_id asc, id desc')->get();
        $fields = CommonService::objectToArray($fields);
        foreach($fields as $key=>$value){
            $fields[$key]['name'] = "field_id_".$value['id'];
        }
        return $fields;
    }
    /* 取得報名者通用報名資料 "1":姓名, "2":email */
    static public function get_examinee_common_value($examinee_id, $target_colum="1"){
        $ex_name = Lang::get('報名者');
        $examinee_info = DB::table('examinee_info')->find($examinee_id);
        $examinee_info = CommonService::objectToArray($examinee_info);
        if(!$examinee_info){ return $ex_name; }

        // 找出報名的品項、商品
        $productinfo_type = self::get_prodtype_and_prodtitle_by_type_id($examinee_info['type_id']);

        /*取得商品的所有欄位*/
        $fields = self::get_fields_by_prod_id($examinee_info['prod_id']);
        $name_key = "name_key";
        foreach ($fields as $fk => $fv) {
            if($fv['fields_set_id']==$target_colum){
                $name_key = 'field_id_' . $fv['id'];
            }
        }

        $register_data = (Array)json_decode($examinee_info['register_data']);
        if( isset($register_data[$name_key]) ){ $ex_name = $register_data[$name_key]; };

        return $ex_name;
    }
    /*依欄位類型設定預設答案*/
    static public function set_examinee_default_ans($input_type){
        if( in_array($input_type, self::$types_need_checked) ){ /*選項類型*/
            $ans = [];
        }
        elseif( in_array($input_type, ['file']) ){ /*檔案類型*/
            $ans = (object)["file_name"=>"", "data"=>""];
        }
        else{ /*文字類型*/
            $ans = "";
        }
        
        return $ans;
    }
    /*取得點名Qr code*/
    static public function get_roll_code_img($info_type_with_prod, $examinee_id){
        $roll_code_img = "";
        // dump($info_type_with_prod);exit;
        if($info_type_with_prod['is_registrable']==1 && $info_type_with_prod['is_roll_call']==1){
            /*請求QRcode圖片*/
            $path = '/public/uploads/qrcode';
            $fileName = $examinee_id.'_'.CommonService::geraHash(8);
            $img_url = request()->server('REQUEST_SCHEME').'://'.request()->server('HTTP_HOST').$path.'/'.$fileName.'.png';
            // dump($img_url);exit;
            $qrCode = new QRCode();
            $qrCodeImage = $qrCode->render(request()->server('REQUEST_SCHEME').'://'.request()->server('HTTP_HOST').'/admin/examination/qrcode_roll_call?ex_id='.md5($info_type_with_prod['product_id'].'_'.$examinee_id));
            // dump($qrCodeImage);exit;
            CommonService::uploadFile($path, $qrCodeImage, $fileName);
            $roll_code_img = '點名QR code：<br><img src="'.$img_url.'"><br>';
        }

        return $roll_code_img;
    }
    /*依欄位類型及答案回傳顯示html*/
    public static function show_text_ans_by_field_type($input_type, $ans){
        if( in_array($input_type, self::$types_need_checked) ){ /*選項類型*/
            $ans = implode(',', $ans);
        }
        elseif( in_array($input_type, ['file']) ){ /*檔案類型*/
            $ans = '<a href="'.request()->server('REQUEST_SCHEME').'://'.request()->server('HTTP_HOST').'/'.$ans->data.'" target="_blank">'. $ans->file_name .'</a>';
        }
        else{ /*文字類型*/
            $ans = $ans;
        }
        return $ans;
    }

    /*寄送完成報名信*/
    static public function send_complete_order_email($examinee_id){
        $examinee_info = DB::table('examinee_info')->find($examinee_id);
        $examinee_info = CommonService::objectToArray($examinee_info);
        if(!$examinee_info){ return; }

        // 找出報名的品項、商品
        $productinfo_type = self::get_prodtype_and_prodtitle_by_type_id($examinee_info['type_id']);

        /*取得報名者姓名*/
        $ex_name = self::get_examinee_common_value($examinee_id, "1"); 

        $register_letter = Lang::get('menu.報名成功信消費者');
        $register_letter = str_replace("{ex_name}", $ex_name, $register_letter);
        $register_letter = str_replace("{show_product_name}", $productinfo_type['p_title']." ".$productinfo_type['title'], $register_letter);
        $register_letter = str_replace("{roll_code_img}", self::get_roll_code_img($productinfo_type, $examinee_id), $register_letter);
        // $globalMailData = PublicController::getMailData();
        $mailBody = "
            <html>
                <head></head>
                <body>
                    <div>
                        ".$register_letter."
                    </div>
                    <div style='color:red;'>
                        ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
                    </div>
                </body>
            </html>
        ";
        // dump($mailBody);exit;
        $mail_return = PublicController::Mail_Send($mailBody,'client', $examinee_info['email'], Lang::get('報名成功通知'));
    }
    /*寄送填寫特殊欄位提醒信*/
    static public function send_special_email($examinee_id){
        $examinee_info = DB::table('examinee_info')->find($examinee_id);
        $examinee_info = CommonService::objectToArray($examinee_info);
        if(!$examinee_info){ return; }
        $saved_register_data = $examinee_info ? (Array)json_decode($examinee_info['register_data']) : [];

        // 找出報名的品項、商品
        $productinfo_type = self::get_prodtype_and_prodtitle_by_type_id($examinee_info['type_id']);

        /*取得商品的所有欄位*/
        $register_fields = self::get_fields_by_prod_id($examinee_info['prod_id']);
        // dump($register_fields);

        $special = [];
        foreach ($register_fields as $key => $value) {
            $ans_key = 'field_id_'.$value['id'];
            if($value['special']==1){ /*是特殊欄位*/
                if(isset($saved_register_data[$ans_key])){ /*有設定此欄位(刪除資料依就會保留欄位，只是答案空白)*/
                    $default_ans = self::set_examinee_default_ans($value['type']);
                    if($saved_register_data[$ans_key]!=$default_ans){ /*代表內容有填寫*/
                        array_push($special, $value['title']."：".self::show_text_ans_by_field_type($value['type'], $saved_register_data[$ans_key]));
                    }
                }
            }
            if($value['fields_set_id']==1){ /*紀錄報名者姓名*/
                $ex_name = $saved_register_data[$ans_key];
            }
        }
        // dump($special);
        if($special){
            // $globalMailData = PublicController::getMailData();
            $special_columns_letter = Lang::get('menu.特殊欄位信消費者');
            $special_columns_letter = str_replace("{ex_name}", $ex_name, $special_columns_letter);
            $special_columns_letter = str_replace("{show_product_name}", $productinfo_type['p_title']." ".$productinfo_type['title'], $special_columns_letter);
            $special_text = '';
            foreach ($special as $value){
                $value = str_replace("\n", "<br>", $value);
                $special_text .= $value."<br>";
            }
            $special_columns_letter = str_replace("{special_text}", $special_text, $special_columns_letter);
            $mailBody = "
                <html>
                <head></head>
                <body>
                    <div>
                    ".$special_columns_letter."
                    </div>
                    <div style='color:red;'>
                    ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
                    </div>
                </body>
                </html>
            ";
            // dump($mailBody);exit;
            $mail_return = PublicController::Mail_Send($mailBody,'admin','', Lang::get('特殊欄位提醒'));
        }
    }
    /*寄送報名資料修改提醒信(內容差異)*/
    static public function send_update_email($examinee_id, $ans_data){
        // dump($examinee_id); dump($ans_data);
        $examinee_info = DB::table('examinee_info')->find($examinee_id);
        $examinee_info = CommonService::objectToArray($examinee_info);
        if(!$examinee_info){ return; }
        $saved_register_data = $examinee_info ? (Array)json_decode($examinee_info['register_data']) : [];
        // dump($saved_register_data);

        // 找出報名的品項、商品
        $productinfo_type = self::get_prodtype_and_prodtitle_by_type_id($examinee_info['type_id']);

        /*取得商品的所有欄位*/
        $register_fields = self::get_fields_by_prod_id($examinee_info['prod_id']);
        // dump($register_fields);

        $change_data = [];
        $ex_name = "";
        foreach ($register_fields as $key => $value) {
            $ans_key = 'field_id_'.$value['id'];
            if(isset($ans_data[$ans_key])){ /*有設定此欄位(刪除資料依就會保留欄位，只是答案空白)*/
                $changed = false;
                if( !isset($saved_register_data[$ans_key]) ){ /*代表後來新添加的欄位*/
                    $changed = true;
                }
                else if($ans_data[$ans_key]!=$saved_register_data[$ans_key]){ /*代表內容有修改*/
                    $changed = true;
                }
                if($changed){ /*有記錄到資料變動*/
                    array_push($change_data, $value['title']."：".self::show_text_ans_by_field_type($value['type'], $ans_data[$ans_key]));
                }
            }
            if($value['fields_set_id']==1){ /*紀錄報名者姓名*/
                $ex_name = $ans_data[$ans_key];
            }
        }
        // dump($change_data);exit;
        if($change_data){
            $register_change_letter = Lang::get('menu.報名資料修改信消費者');
            $register_change_letter = str_replace("{ex_name}", $ex_name, $register_change_letter);
            $register_change_letter = str_replace("{date_time}", date("Y-m-d H:i"), $register_change_letter);
            $register_change_letter = str_replace("{show_product_name}", $productinfo_type['p_title']." ".$productinfo_type['title'], $register_change_letter);
            $change_data_text = '';
            foreach ($change_data as $value){
                $value = str_replace("\n", "<br>", $value);
                $change_data_text .= $value."<br>";
            }
            $register_change_letter = str_replace("{change_data_text}", $change_data_text, $register_change_letter);
            
            // $globalMailData = PublicController::getMailData();
            $mailBody = "
                <html>
                    <head></head>
                    <body>
                        <div>
                            ".$register_change_letter."
                        </div>
                        <div style='color:red;'>
                            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')." ≡
                        </div>
                    </body>
                </html>
            ";
            // dump($mailBody);exit;
            $mail_return = PublicController::Mail_Send($mailBody,'admin','', Lang::get('報名資料修改'));
        }
    }

    /*新增/修改報名資料*/
    static public function examinee_save($backstage=false, $user_id=0, $post_data=[]){
        // dump($post_data);exit;
        $type_product_id = isset($post_data['type_product_id']) ? $post_data['type_product_id'] : "";
        $type_id = isset($post_data['type_id']) ? $post_data['type_id'] : "";
        $examinee_id = isset($post_data['examinee_id']) ? $post_data['examinee_id'] : "0";
        if($type_product_id==""){ throw new \Exception(Lang::get('資料不完整')); }
        if($type_id==""){ throw new \Exception(Lang::get('資料不完整')); }
        
        $register_data = isset($post_data['register_data']) ? $post_data['register_data'] : [];
        if(empty($register_data)){ throw new \Exception(Lang::get('資料不完整')); }
        // dump($register_data);

        /*取得已記錄的資料*/
        $saved_register_data = DB::table('examinee_info')->find($examinee_id);
        $saved_register_data = $saved_register_data ? (Array)json_decode($saved_register_data['register_data']) : [];

        $productinfo = DB::table('productinfo')->whereRaw('id="'.$type_product_id.'"')->first();
        $productinfo = CommonService::objectToArray($productinfo);
        if(!$productinfo){ throw new \Exception(Lang::get('資料有誤')); }
        if($productinfo['register_data_change_limit']){
            if(date('Y-m-d') > $productinfo['register_data_change_limit']){
                throw new \Exception(Lang::get('超出期限'));
            }
        }

        /*取得商品的所有欄位*/
        $register_fields = ExaminationHelper::get_fields_by_prod_id($type_product_id);
        $ans_data = [];
        $email = "";
        foreach ($register_fields as $key => $value) {
            /*設定問題答案*/
            $ans_key = 'field_id_'.$value['id'];
            if(isset($register_data[$ans_key])){ /*有輸入的話，依使用者輸入*/
                if($value['type']=='file'){
                    $register_data[$ans_key] = (object)$register_data[$ans_key];
                    $image_base64 = $register_data[$ans_key]->data;
                    if($image_base64=='delete'){
                        $register_data[$ans_key] = ExaminationHelper::set_examinee_default_ans($value['type']);
                    }
                    else if( mb_substr($image_base64, 0, 7)!="/public" && $image_base64!='' ){
                        $register_data[$ans_key]->data = CommonService::uploadFile('/public/static/index/upload/examinee', $image_base64);
                    }
                    else if(isset($saved_register_data[$ans_key])){ /*有紀錄的話，依紀錄*/
                        $register_data[$ans_key] = $saved_register_data[$ans_key];
                    }
                }
                $ans = $register_data[$ans_key];
            }
            else{ /*沒輸入的話，依預設*/
                $ans = ExaminationHelper::set_examinee_default_ans($value['type']);
            }

            /*檢查必填*/
            if($value['type']=='file' && $value['required']==1 && ($ans->file_name=="" || $ans->data=="")){
                throw new \Exception(Lang::get('請輸入必填欄位')."：".$value['title']);
            }
            if($value['required']==1 && ($ans=="" || $ans==[])){
                throw new \Exception(Lang::get('請輸入必填欄位')."：".$value['title']);
            }

            /*檢查格式*/
            if( in_array($value['type'], ExaminationHelper::$types_need_checked) ){ /*選項類型*/
                $value['options'] = $value['options'] ? json_decode($value['options']) : [];
                if($ans){
                    if(is_array($ans)){
                        foreach ($ans as $ans_v) {
                            if( !in_array($ans_v, $value['options']) ){
                                throw new \Exception(Lang::get('格式有誤')."：".$value['title']);
                            }
                        }
                    }else{
                        if( !in_array($ans, $value['options']) ){
                            throw new \Exception(Lang::get('格式有誤')."：".$value['title']);
                        }
                    }
                }
            }
            else if( in_array($value['type'], ['file']) ){ /*檔案類型*/
                if($value['limit'] && $ans->data){
                    $file_type = explode('.', $ans->file_name);
                    $file_type = end($file_type);
                    if( !preg_match("/$file_type/", $value['limit']) ){
                        throw new \Exception(Lang::get('格式有誤')."：".$value['title']);
                    }
                }
            }
            else{ /*文字類型*/
                if($value['limit'] && $ans){
                    $pattern = $value['limit'];
                    if( !preg_match("/$pattern/", $ans) ){
                        throw new \Exception(Lang::get('格式有誤')."：".$value['title']);
                    }
                }
            }

            $ans_data[ $ans_key ] = $ans;

            // 額外設定name欄位供後續查詢使用
            if($value['fields_set_id']=='1'){
                $name = $register_data[$ans_key];
            }
            // 額外設定email欄位供後續查詢使用(改為手機了)
            if($value['fields_set_id']=='2'){
                $email = $register_data[$ans_key];
            }
        }
        // dump($ans_data); exit;

        if($name==""){
            throw new \Exception(Lang::get('資料不完整'));
        }
        if($email==""){
            throw new \Exception(Lang::get('資料不完整'));
        }
        
        $data = [
            'prod_id' => $type_product_id,
            'type_id' => $type_id,
            'order_id' => 0,
            'user_id' => session('user.id') ? session('user.id') : 0,
            'name' => $name,
            'email' => $email,
            'session_id' => session_id(),
            'register_data' => json_encode($ans_data, JSON_UNESCAPED_UNICODE),  
        ];
        // dump($data);exit;

        if($examinee_id=='0'){ /*新增*/
            $repeat_info = DB::table('examinee_info')->whereRaw('
                prod_id="'.$data['prod_id'].'" AND 
                type_id="'.$data['type_id'].'" AND 
                name="'.$data['name'].'" AND 
                email="'.$data['email'].'"
            ')->first();
            if($repeat_info){
                throw new \Exception(Lang::get('資料重複'));
            }

            /*檢查是否超出上限*/
            $count = count( ExaminationHelper::get_myself_type_examinee_info($type_id, $examinee_id=0, $order_id=0, "", ['backstage'=>$backstage]) );
            $Proposal = Proposal::withTeamMembersAndRequire(
                ['GetCartData'],
                ['user_id' => $user_id]
            );
            $Proposal = MemberFactory::createNextMember($Proposal);
            $cartData = json_decode($Proposal->projectData['data'], true);
            if((Int)$count >= $cartData[$type_id]){
                throw new \Exception(Lang::get('超出上限'));
            }

            $insertGetId = DB::table('examinee_info')->insertGetId($data);
            $data['examinee_id'] = $insertGetId;
        }
        else{ /*編輯*/
            $email = isset($post_data['email']) ? $post_data['email'] : "";
            $my_examinee_info = ExaminationHelper::get_myself_type_examinee_info($type_id, $examinee_id, $order_id=null, $email, ['backstage'=>$backstage]);
            if(count($my_examinee_info)==0){ throw new \Exception(Lang::get('資料有誤')); }
            if($my_examinee_info[0]['cancel']=='1'){ throw new \Exception(Lang::get('此報名已取消')); }

            unset($data['prod_id']);
            unset($data['type_id']);
            unset($data['order_id']);
            if($my_examinee_info[0]['user_id']!=0){unset($data['user_id']);}
            if($my_examinee_info[0]['order_id']!=0){ self::send_update_email($examinee_id, $ans_data); }
            DB::table('examinee_info')->where('id', $examinee_id)->update($data);
            $data['examinee_id'] = $examinee_id;
        }
        return $data;
    }

    static public function create_examinee_list_table_view($data = [], $type_id, $order_id=null, $edit_able=false, $post_data=[]){
        $data = json_decode(json_encode($data), true);
        $data['types_need_limit'] = self::$types_need_limit;
        $data['types_need_option'] = self::$types_need_option;
        $data['types_need_checked'] = self::$types_need_checked;
    
        if($type_id==""){ throw new \Exception(Lang::get('資料不完整')); }
        // $type_id = explode('_', $type_id)[0];

        $productinfo_type = DB::table('productinfo_type')->find($type_id);
        $productinfo_type = CommonService::objectToArray($productinfo_type);
        if(!$productinfo_type){ throw new \Exception(Lang::get('無資料')); }
        $info_id = $productinfo_type['product_id'];
        $productinfo = DB::table('productinfo')->find($info_id);
        $data['productinfo'] = CommonService::objectToArray($productinfo);
        
        $user_id = session('user.id') ? session('user.id') : 0;

        /*依條件取得報名者們*/
        if(isset($post_data['search_cond'])){
            $type_id = isset($post_data['search_cond']['type_id']) ? $post_data['search_cond']['type_id'] : $type_id;
        }
        $examinees = self::get_myself_type_examinee_info($type_id, $examinee_id=0, $order_id, '', $post_data);
        // dd($examinees);
        foreach ($examinees as $k => $v) {
            $examinees[$k]['register_data'] = (Array)json_decode($v['register_data']);
        }
        $data['examinees'] = $examinees;

        /*取得商品報名欄位*/
        $register_fields = self::get_fields_by_prod_id($info_id);
        $data['register_fields'] = $register_fields;

        $examinees = self::get_myself_type_examinee_info_with_register_data($type_id, $order_id, $post_data);
        $data['examinees'] = $examinees;
        
        $data['edit_able'] = $edit_able;

        $data['request_module'] = Route::current()->action['module'] ?? 'index';

        return view('home.examination.examinee_list_table', ['data'=>$data]);
    }
    static public function create_examinee_list_graph_view($data = [], $type_id, $order_id=null, $hide_user=false, $post_data=[]){
        $data = json_decode(json_encode($data), true);
        if($type_id==""){ throw new \Exception(Lang::get('資料不完整')); }

        $data['rand'] = CommonService::geraHash(10);

        $type_id = explode('_', $type_id)[0];
        $productinfo_type = DB::table('productinfo_type')->find($type_id);
        $productinfo_type = CommonService::objectToArray($productinfo_type);
        if(!$productinfo_type){ throw new \Exception(Lang::get('資料有誤')); }
        $info_id = $productinfo_type['product_id'];
        $productinfo = DB::table('productinfo')->find($info_id);
        $productinfo = CommonService::objectToArray($productinfo);
        if(!$productinfo){ throw new \Exception(Lang::get('資料有誤')); }

        /*取得商品報名欄位*/
        $register_fields = self::get_fields_by_prod_id($info_id);
        $data['register_fields'] = $register_fields;

        $graph_datas = [];
        $option_questions = [];
        $question_prefix = 'field_id_';
        foreach ($register_fields as $key => $value) {
            if(in_array($value['type'], self::$types_need_option)){ //只統計選項類欄位, 
                $praph_data = [];
                $praph_data['title'] = $value['title'];
                $praph_data['options'] = $value['options'];
                $options = json_decode($value['options']);
                $praph_data['options_decode'] = $options;
                $praph_data['dates'] = [];
                foreach ($options as $option) {
                    array_push($praph_data['dates'], [
                        'value' => 0,
                        'name' => $option,
                        'users' => [],
                    ]);
                }
                array_push($option_questions, $value['id']);
                $graph_datas[$question_prefix.$value['id']] = $praph_data;
            }
        }
        // dd($graph_datas);
        // dump($option_questions);

        $examinees = self::get_myself_type_examinee_info_with_register_data($type_id, $order_id, $post_data);
        $data['examinees'] = $examinees;
        // dd($examinees);
        foreach ($examinees as $key_e => $examinee) {
            foreach ($examinee['register_data'] as $key_q => $anss) {
                $question_id = explode('_', $key_q);
                $question_id = end($question_id);
                if(in_array($question_id, $option_questions)){
                    if(!is_array($anss)){ $anss = [$anss]; } /*為select欄位例外處理*/
                    foreach ($anss as $ans) {
                        $index = array_search($ans, $graph_datas[$question_prefix.$question_id]['options_decode']);
                        if($index!==false){
                            $graph_datas[$question_prefix.$question_id]['dates'][$index]['value'] += 1;
                            $user_name = $hide_user ? HelperService::hidestr($examinee['name'], 1, -1, 'O') : $examinee['name'];
                            array_push($graph_datas[$question_prefix.$question_id]['dates'][$index]['users'], $user_name);
                        }
                    }
                }
            }
        }
        // dd($graph_datas);
        $data['graph_datas'] = $graph_datas;
        return view('home.examination.examinee_list_graph', ['data'=>$data]);

        /*結合成總表*/
        // $graph_data_all = ['title'=>'', 'options'=>"", 'options_decode'=>[], 'dates'=>[], 'users'=>[]];
        // foreach ($graph_datas as $k_gd => $v_gd) {
        //     foreach ($v_gd['options_decode'] as $k_o => $v_o) {
        //         array_push($graph_data_all['options_decode'], $v_gd['title'].' '.$v_o);
        //     }
        //     foreach ($v_gd['dates'] as $k_d => $v_d) {
        //         array_push($graph_data_all['dates'], $v_d);
        //     }
        // }
        // $graph_data_all['options'] = json_encode($graph_data_all['options_decode'], JSON_UNESCAPED_UNICODE);
        // // dump($graph_data_all);exit;
        // $data['graph_datas'] = [$graph_data_all];
        // return view('home.examination.examinee_list_graph', ['data'=>$data]);
    }
    static public function create_examinee_panel_view(
        View $View, 
        $post_data=[], 
        $user_id=0, 
        $view_path='home.examination.examinee_panel'
    ){
        $examinee_panelView = clone $View;
        $data = [];
        $data['types_need_limit'] = self::$types_need_limit;
        $data['types_need_option'] = self::$types_need_option;
        $data['types_need_checked'] = self::$types_need_checked;

        $email = isset($post_data['email']) ? $post_data['email'] : "";
        /*取得 判斷你有沒有權限看報名者資料的where篩選語法*/
        if(session('admin')){
            $backstage = true;
        }else{
            $backstage = false;
        }
        $yours_where = ExaminationHelper::get_yours_where($email, $post_data['backstage']??false);

        $examinee_id = isset($post_data['examinee_id']) ? $post_data['examinee_id'] : 0;
        
        $type_id = isset($post_data['type_id']) ? $post_data['type_id'] : 0;
        if($type_id==0){ throw new \Exception(Lang::get('資料不完整')); }

        $productinfo_type = DB::table('productinfo_type')->find($type_id);
        $productinfo_type = CommonService::objectToArray($productinfo_type);
        if(!$productinfo_type){ throw new \Exception(Lang::get('資料有誤')); }
        $type_product_id = $productinfo_type['product_id'];

        $productinfo = DB::table('productinfo')->find($type_product_id);
        $productinfo = CommonService::objectToArray($productinfo);
        if(!$productinfo){ throw new \Exception(Lang::get('資料有誤')); }
        
        $title = $productinfo['title'];
        $title .= $productinfo_type['title'] ? "-".$productinfo_type['title'] : "" ;
        
        $data['examinee_id'] = $examinee_id;
        $data['type_id'] = $type_id;
        $data['type_product_id'] = $type_product_id;
        $data['title'] = $title;

        /*取得填寫資料*/
        $register_data = [];
        if($examinee_id!=0){
            $examinee_info = DB::table('examinee_info')->whereRaw($yours_where)->find($examinee_id);
            $examinee_info = CommonService::objectToArray($examinee_info);
            if($examinee_info){ $register_data = $examinee_info['register_data'] ? (Array)json_decode($examinee_info['register_data']) : []; }
        }
        // dump($register_data);

        /*初始化時間表格值*/
        $time_start = "24:00";
        $time_end = "00:00";
        $time_pattern = "/^((((0[0-9])|(1[0-9])|(2[0-3])):[03]0)|(24:00))$/i";

        $register_fields = self::get_fields_by_prod_id($type_product_id);   /*取得商品報名欄位*/
        foreach ($register_fields as $key => $value) {
            /*答案併入問題中*/
            $ans_key = 'field_id_'.$value['id'];
            if( isset($register_data[$ans_key]) ){ /*有紀錄的話，依紀錄*/
                $register_fields[$key]['ans'] = $register_data[$ans_key];
            }
            else{ /*沒紀錄的話，依預設*/
                $register_fields[$key]['ans'] = self::set_examinee_default_ans($value['type']);
            }

            /*處理選項*/
            $register_fields[$key]['options'] = isset($value['options']) ? json_decode($value['options']) : [];

            /*找出全時間選的起訖時間*/
            if($value['type']=='checkbox_time'){
                foreach ($register_fields[$key]['options'] as $o_k => $o_v) {
                    $o_v_s = explode('~', $o_v);
                    if(count($o_v_s)!=2){
                        continue;
                    }
                    
                    if(preg_match($time_pattern, $o_v_s[0]) && /*開始時間符合格式*/
                        preg_match($time_pattern, $o_v_s[1]) && /*結束時間符合格式*/
                        $o_v_s[0] < $o_v_s[1]                   /*開始時間小於於結束時間*/
                    ) {
                        if($o_v_s[0] < $time_start){ $time_start = $o_v_s[0]; } 
                        if($o_v_s[1] > $time_end){ $time_end = $o_v_s[1]; }
                    }
                }
            }
        }

        /*製作時間表格起訖區間*/
        if(explode(':', $time_start)[1]=='30'){ /*開始時間帶有30分鐘*/
            $time_start = explode(':', $time_start)[0].':00'; /*往前30分鐘*/
        }
        if(explode(':', $time_end)[1]=='30'){ /*結束時間帶有30分鐘*/
            $time_end = date('H:i', strtotime('2022-01-01 '.explode(':', $time_end)[0].':00 + 1Hour')); /*往後30分鐘*/
        }
        // dump($time_start.'~'.$time_end);

        $time_area_data = [];
        $add_time_data = $time_start;
        while ($add_time_data <= $time_end){
            array_push($time_area_data, $add_time_data);
            $add_time_data = date('H:i', strtotime('2022-01-01 '.$add_time_data.' + 1Hour'));
        }
        // dump($time_area_data);
        $data['time_area_data'] = $time_area_data;

        /*計算時間表格選項的開始位置，區間高度*/
        $checkbox_time_count = 0;
        foreach ($register_fields as $key => $value) {
            /*找出全時間選的起訖時間*/
            if($value['type']=='checkbox_time'){
                /*修改標題*/
                $title_s = explode('/', $value['title']);
                if(count($title_s)!=3){ continue; }
                if(!checkdate( $title_s[1], $title_s[2], $title_s[0])){ continue; }
                $weekday_index = date('w', strtotime($value['title']));
                $register_fields[$key]['title_format'] = date('m/d', strtotime($value['title'])).'<br>('.ExaminationHelper::$weekday[$weekday_index].')';

                /*有作用的時間選*/
                $checkbox_time_count += 1;
                foreach ($register_fields[$key]['options'] as $o_k => $o_v) {
                    $o_v_s = explode('~', $o_v);
                    if(count($o_v_s)!=2){ continue; }
                    if(preg_match($time_pattern, $o_v_s[0]) && /*開始時間符合格式*/
                        preg_match($time_pattern, $o_v_s[1]) && /*結束時間符合格式*/
                        $o_v_s[0] < $o_v_s[1]                   /*開始時間小於於結束時間*/
                    ) {
                        /*開始位置*/
                        $time_diff = date_diff(date_create(date("Y-m-d")." ".$o_v_s[0]), date_create(date("Y-m-d")." ".$time_start));
                        $time_duration = $time_diff->h + round($time_diff->i / 60, 2);
                        $register_fields[$key]['options_start'][$o_k] = $time_duration;
                        
                        /*區間高度*/
                        $time_diff = date_diff(date_create(date("Y-m-d")." ".$o_v_s[0]), date_create(date("Y-m-d")." ".$o_v_s[1]));
                        $time_duration = $time_diff->h + round($time_diff->i / 60, 2);
                        $register_fields[$key]['options_duration'][$o_k] = $time_duration;
                    }
                }
            }
        }
        // dump($register_fields);exit;
        $data['checkbox_time_count'] = $checkbox_time_count;
        $data['register_fields'] = $register_fields;

        $MemberInstance = new MemberInstance($user_id);
        $user_data = $MemberInstance->get_user_data();
        $home = $user_data ? $user_data['home'] : '';
        $data['home'] = $home;

        $consent = DB::table('consent')->first();
        $consent = CommonService::objectToArray($consent);
        $data['consent'] = $consent['examinee'];
        
        $data['request_module'] = Route::current()->action['module'] ?? 'index';
    
        return view($view_path, ['data'=>$data]);
    }
}