<?php
namespace App\Services\pattern\simpleFactory\discountFactory;

use Illuminate\Support\Facades\DB;
 /*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is new
 * @depend: (1)thinkPHP5.x think\Db static class
 *
*/
class noneDiscount extends discount
{
    public function getDiscountAndTotal($OrderData) {
        return [
            'total' => $this->total,
            'discount' => '[]'
        ];
    }
}

