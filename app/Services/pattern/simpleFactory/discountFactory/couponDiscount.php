<?php
namespace App\Services\pattern\simpleFactory\discountFactory;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;
use App\Services\pattern\recursiveCorrdination\discountRC\Proposal as DiscountProposal;
use App\Services\pattern\recursiveCorrdination\discountRC\MemberFactory as DiscountMemberFactory;
/*
*
* @author: MazeR
* @email: <EMAIL>
* @lastUpdate: Nov 08 2017
* @Description: orderClass that status is new
* @depend: (1)thinkPHP5.x think\Db static class
*
*/
class couponDiscount extends discount
{
  public function getDiscountAndTotal($OrderData) {
    $DiscountAndTotal = $this->getDiscount();
    return [
      'total' => $DiscountAndTotal['total'],
      'discount' => urldecode(json_encode($DiscountAndTotal['discount']))
    ];
  }

  private function getDiscount(){
    $user_id = Session('user.id');
    $use_coupon = DB::table('coupon_pool AS cp')
                    ->select(
                      'cp.id AS coupon_pool_id', 
                      'coupon.discount AS discount', 
                      'coupon.title AS coupon_title',
                      'coupon.id AS coupon_id'
                    )
                    ->join('coupon', 'cp.coupon_id','coupon.id')
                    ->where('cp.id', $this->discountId)
                    ->where('cp.owner', $user_id)
                    ->whereNull('cp.use_time')
                    ->first();
    $use_coupon = CommonService::objectToArray($use_coupon);
    if(count($use_coupon)<1){
      return [
        'total' => $this->total,
        'discount' => [],
      ];
    }

    /*檢查購物買內容是否可套用優惠券*/
    $Proposal = Proposal::withTeamMembersAndRequire(
      ['GetCartData'],
      [
        'cart_session' => 'cart',
        'user_id' => $user_id,
      ]
    );
    $Proposal = MemberFactory::createNextMember($Proposal);
    $cartData = $Proposal->getCartArray_with_info();
    /*檢查購買商品是否可使用優惠*/
    $DiscountProposal = DiscountProposal::withTeamMembersAndRequire(
        ['CouponCheck'],
        [
          'user_id' => $user_id,
          'cartData' => $cartData
        ]
      );
    $DiscountProposal = DiscountMemberFactory::createNextMember($DiscountProposal);
    $has_this_coupon = false; /*預設優惠券不存在*/
    foreach ($DiscountProposal->projectData["coupon"] as $key => $coupon) {
      if($use_coupon['coupon_id']==$coupon['coupon_id']){
        $has_this_coupon = true; /*使用的優惠券存在*/
        break;
      }
    }
    if(!$has_this_coupon){
      return [
        'total' => $this->total,
        'discount' => [],
      ];
    }
    
    DB::table('coupon_pool')->where('id', $this->discountId)->update(['use_time'=>time()]);
    return [
      'total' => $this->total - $use_coupon['discount'],
      'discount' => [
        [
          'type' => urlencode(Lang::get('會員優惠券')),
          'name' => urlencode($use_coupon['coupon_title']),
          'count' => urlencode(Lang::get('扣') . config('extra.shop.dollar_symbol') . $use_coupon['discount']),
          'coupon_pool_id' => $use_coupon['coupon_pool_id']
        ]
      ],
    ];
  }
}
