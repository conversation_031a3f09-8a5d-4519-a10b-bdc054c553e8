<?php
namespace App\Services\pattern\simpleFactory\discountFactory;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;
use App\Services\pattern\recursiveCorrdination\cartRC\MemberFactory;

class directcouponDiscount extends discount
{
  public function getDiscountAndTotal($OrderData) {
    $DiscountAndTotal = $this->getDiscount($OrderData['directcoupon_code']);
    return [
      'total' => $DiscountAndTotal['total'],
      'discount' => urldecode(json_encode($DiscountAndTotal['discount']))
    ];
  }

  private function getDiscount($user_code){
    $coupondirectDiscount = self::get_discount($user_code);
    if($coupondirectDiscount['status']=="1"){
      return [
        'discount' => [
          [
            'type' => urlencode(Lang::get('活動優惠券')),
            'name' => urlencode($coupondirectDiscount['name']),
            'count' => urlencode(Lang::get('扣') . config('extra.shop.dollar_symbol') . $coupondirectDiscount['discount']),
            'coupon_id' => $coupondirectDiscount['id']
          ]
        ],
        'total' => $this->total - $coupondirectDiscount['discount']
      ];
    }else{
      return ['discount' => [], 'total' => $this->total];
    }
  }

  /*依照提供的折扣碼回傳目前購物車內折扣的結果*/
  public static function get_discount($user_code=false){
    $user_id = session('user.id');
    if ($user_id === null) {
      return ['status' => 0, 'discount' => 0, 'msg' => Lang::get('請先登入會員'), 'user_code' => $user_code];
    }
    if(empty($user_code) == true) {
      return ['status' => 0, 'discount' => 0, 'msg' => Lang::get('請輸入優惠券代碼'), 'user_code' => $user_code];
    }

    /*找出適用的優惠券*/
    $coupon_direct = DB::table('coupon_direct')->where('user_code', $user_code)
                                              ->where('online', 1)
                                              ->where('start', "<=", time())
                                              ->where(function ($query) {
                                                $query->orWhere('end', -28800)
                                                    ->orWhere('end',  ">=", strtotime(date('Y-m-d')));
                                              })
                                              ->first();
    $coupon_direct = CommonService::objectToArray($coupon_direct);
    if (empty($coupon_direct) == true) {
      return ['status' => 0, 'discount' => 0, 'msg' => Lang::get('無資料'), 'user_code' => $user_code];
    }

    /*檢查是否已使用過*/
    $coupon_direct_record = DB::table('coupon_direct_record')->where('coupon_id', $coupon_direct['id'])->where('user_id', $user_id)->get();

    if ($coupon_direct['limit_num'] !== null && $coupon_direct['limit_num'] !== '') {
      if ($coupon_direct['limit_num'] <= count($coupon_direct_record)) {
        return ['status' => 0, 'discount' => 0, 'msg' => Lang::get('超出使用次數上限')];
      }
    }

    $coupon_product = DB::table('coupon_direct_product')->where('coupon_id', $coupon_direct['id'])->get();
    $total = 0; // 優惠券商品總金額
    $count = 0; // 優惠券商品總件數

    /* 取得購物車商品 */
    $Proposal = Proposal::withTeamMembersAndRequire(
      ['GetCartData'],
      ['user_id' => $user_id]
    );
    $Proposal = MemberFactory::createNextMember($Proposal);
    foreach (json_decode($Proposal->projectData['data'], true) as $key => $value) {
      $singleData = Proposal::get_singleData($key, 'online'); /* 取得商品資料 */

      if ($singleData['key_type'] == 'normal' || substr($singleData['key_type'], 0, 3) == 'kol') { /*一般商品 或 網紅商品 */
        foreach ($coupon_product as $k => $v) {
          if ($v->prod_id == $singleData['type_product_id']) { // 如果屬於優惠券商品
            $total += $value * $singleData['countPrice']; // 優惠商品金額+
            $count += $value; // 優惠商品數量+

            break;
          }
        }
      }
    }

    // 計算優惠金額
    $discountReault = self::discountCycle($coupon_direct, $total, $count);

    if ($discountReault['disTotal'] < 0) { // 如果折扣後金額為負，要調整成為0
      $discountReault['discount'] += $discountReault['disTotal'];
      $discountReault['disTotal'] = 0;
    }

    if ($discountReault['discount'] == 0) {
      return ['status' => 0, 'discount' => 0, 'user_code' => $user_code, 'msg' => Lang::get('資格不符')];
    }

    if ($coupon_direct['end'] == -28800) {
      $coupon_end = '無使用期限';
    } else {
      $coupon_end = '將於' . date('Y/m/d', $coupon_direct['end']) . '到期';
    }

    return ['status' => 1, 'id' => $coupon_direct['id'], 'name' => $coupon_direct['name'], 'discount' => $discountReault['discount'], 'user_code' => $user_code, 'end' => $coupon_end];
  }
  private static function discountCycle($act, $total, $count){
    switch($act['type']){
      case 1: // 滿幾元打幾折
        if(
          ($total >= $act['condition3']) &&
          ($act['condition3']>0) && 
          ($act['online3']==1)
        ){
          $disTotal = round($total*$act['discount3']);
        }elseif (
          ($total >= $act['condition2']) &&
          ($act['condition2']>0) && 
          ($act['online2']==1)
        ){
          $disTotal = round($total*$act['discount2']);
        }elseif (
          ($total >= $act['condition1']) &&
          ($act['condition1']>0) && 
          ($act['online1']==1)
        ){
          $disTotal = round($total*$act['discount1']);
        }else{
          $disTotal = $total;
        }
        $discount = $total-$disTotal;
        break;

      case 2: // 滿幾元扣幾元
        $discount = 0;
        if(
          ($total >= $act['condition3']) &&
          ($act['condition3']>0) && 
          ($act['online3']==1)
        ){
          $discountReault_discount = $act['condition3']>0 ? self::discountCycle($act, $total-$act['condition3'], $count)['discount'] : 0;
          $discount += $act['discount3'] + $discountReault_discount;
        }elseif (
          ($total >= $act['condition2']) &&
          ($act['condition2']>0) && 
          ($act['online2']==1)
        ){
          $discountReault_discount = $act['condition2']>0 ? self::discountCycle($act, $total-$act['condition2'], $count)['discount'] : 0;
          $discount += $act['discount2'] + $discountReault_discount;
        }elseif (
          ($total >= $act['condition1']) &&
          ($act['condition1']>0) && 
          ($act['online1']==1)
        ){
          $discountReault_discount = $act['condition1']>0 ? self::discountCycle($act, $total-$act['condition1'], $count)['discount'] : 0;
          $discount += $act['discount1'] + $discountReault_discount;
        }else{
          $discount += 0;
        }
        $disTotal = round($total-$discount);
        break;

      case 3: // 滿幾件打幾折
        if(
          ($count >= $act['condition3']) &&
          ($act['condition3']>0) && 
          ($act['online3']==1)
        ){
          $disTotal = round($total*$act['discount3']);
        }elseif (
          ($count >= $act['condition2']) &&
          ($act['condition2']>0) && 
          ($act['online2']==1)
        ){
          $disTotal = round($total*$act['discount2']);
        }elseif (
          ($count >= $act['condition1']) &&
          ($act['condition1']>0) && 
          ($act['online1']==1)
        ){
          $disTotal = round($total*$act['discount1']);
        }else{
          $disTotal = $total;
        }
        $discount = $total-$disTotal;
        break;

      case 4: // 滿幾件扣幾元
        $discount = 0;
        if(
          ($count >= $act['condition3']) &&
          ($act['condition3']>0) && 
          ($act['online3']==1)
        ){
          $discountReault_discount = $act['condition3']>0 ? self::discountCycle($act, $total, $count-$act['condition3'])['discount'] : 0;
          $discount += $act['discount3'] + $discountReault_discount;
        }elseif (
          ($count >= $act['condition2']) &&
          ($act['condition2']>0) && 
          ($act['online2']==1)
        ){
          $discountReault_discount = $act['condition2']>0 ? self::discountCycle($act, $total, $count-$act['condition2'])['discount'] : 0;
          $discount += $act['discount2'] + $discountReault_discount;
        }elseif (
          ($count >= $act['condition1']) &&
          ($act['condition1']>0) && 
          ($act['online1']==1)
        ){
          $discountReault_discount = $act['condition1']>0 ? self::discountCycle($act, $total, $count-$act['condition1'])['discount'] : 0;
          $discount += $act['discount1'] + $discountReault_discount;
        }else{
          $discount += 0;
        }
        $disTotal = round($total-$discount);
        break;
    }
    return ['type'=>$act['type'],'disTotal'=>$disTotal,'discount'=>$discount];
  }
}
