<?php

namespace App\Services\pattern\simpleFactory\discountFactory;

use Illuminate\Support\Facades\DB;

 /*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: Abstract Class that all orderClass's parent
 * @depend: (1)thinkPHP5.x think\Db static class
 *
*/

abstract class discount
{

    protected $discountId;
    protected $total;

    public function __construct($discountId, $total) {
        $this->discountId = $discountId;
        $this->total = $total;
    }

    abstract public function getDiscountAndTotal($OrderData);

}
