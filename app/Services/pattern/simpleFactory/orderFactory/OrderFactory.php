<?php

namespace App\Services\pattern\simpleFactory\orderFactory;

use Illuminate\Support\Facades\DB;
use App\Services\CommonService;
/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: simpleStaticFactory that create class by reflaction database data
 * @depend: (1)thinkPHP5.x think\Db static class
 *
*/

class OrderFactory
{
    public static function createOrder($id, $tableName, $tableName2) {
        try{
            $order = DB::connection('main_db')->table($tableName)
                         ->select('status')
                         ->find($id);
        }catch(\Exception $e){
            throw new \RuntimeException('Db operating error：' . $e->getMessage());
        }
        if(!$order){
            throw new \UnexpectedValueException('Order not find');
        }
        $order = CommonService::objectToArray($order);
        $orderClassName = 'App\\Services\\pattern\\simpleFactory\\orderFactory\\' . $order['status'] . 'Order';
        return new $orderClassName($id, $tableName, $tableName2);
    }
}