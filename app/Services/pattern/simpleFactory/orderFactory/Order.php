<?php
namespace App\Services\pattern\simpleFactory\orderFactory;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\pattern\OrderHelper;
use App\Services\pattern\PointRecords;
use App\Services\pattern\MemberInstance;
use App\Http\Controllers\admin\Payfee;
/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: Abstract Class that all orderClass's parent
 * @depend: (1)thinkPHP5.x think\Db static class
 *
*/
abstract class Order extends Controller{
    protected $id;
    protected $config_db;
    protected $tableName;
    protected $coupon_tableName;
    protected $already2Next = false;
    protected $order_db = "main_db";

    public function __construct($id, $tableName, $coupon_tableName) {
        $this->id = $id;
        $this->tableName = $tableName;
        $this->coupon_tableName = $coupon_tableName;

        if($this->id){
            $orderform =DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
            $orderform = CommonService::objectToArray($orderform);
            $this->config_db = substr($orderform['order_number'],0,1).'_sub';
        }
    }

    abstract public function changeStatus2Next();

    /*回傳ID*/
    public function id(){
        return $this->id;
    }

    // 修改匯款碼
    public function setReportNumber($reportNumber, $user_id) {
        $id = request()->post('id');
        $order_data = DB::connection('main_db')->table('orderform')->find($id);
        $order_data = CommonService::objectToArray($order_data);
        if(!$order_data){
            return ['code'=>0,'msg'=>Lang::get('資料有誤'),'url'=>'/'];
        }
        if($order_data['user_id']!=0 && $order_data['user_id']!=$user_id){ // 訂單為會員訂單，且帳號不同
            return ['code'=>0,'msg'=>Lang::get('請先登入會員'),'url'=>'/'];
        }
        if($order_data['report']!=''){ // 已經回報過
            return ['code'=>0,'msg'=>Lang::get('已回報'),'url'=>'/'];
        }
        if($reportNumber == ''){
            return ['code'=>0,'msg'=>Lang::get('資料不完整'),'url'=>'/'];
        }

        DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->update(['report'=> $reportNumber]);

        // 寄送提醒信
        $report_code_letter = Lang::get('匯款回報信管理者');
        $report_code_letter = str_replace("{order_number}", $order_data['order_number'], $report_code_letter);
        $mailBody = "
            <html>
                <head></head>
                <body>
                    <div>
                        ".$report_code_letter."
                    </div>
                    <div style='color:red;'>
                        ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')."(".$order_data['id'].") ≡
                    </div>
                </body>
            </html>
        ";
        $distributor_id = $order_data['distributor_id'];
        if($distributor_id==0){
            $mail_return = HelperService::Mail_Send($mailBody,'admin','', Lang::get('匯款回報提醒'));
        }else{
            $MemberInstance = new MemberInstance($distributor_id);
            $user_data = $MemberInstance->get_user_data_distributor();
            if($user_data){
                $mail_return = HelperService::Mail_Send($mailBody,'client',$user_data['email'], Lang::get('匯款回報提醒'));
            }
        }
        return ['code'=>1,'msg'=>Lang::get('操作成功'),'url'=>'/'];
    }

    // 修改回報匯款狀態
    public function setReportState() {
        try{
            DB::connection($this->order_db)->table($this->tableName)
                ->where('id', $this->id)
                ->update(['report_check_time'=> time()]);

        }catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 更新收款狀態
    public function setReceiptsState($state) {
        $o = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
        $o = CommonService::objectToArray($o);
        if(!$o){return;}
        
        try{
            DB::connection($this->order_db)->table($this->tableName)
                ->where('id', $this->id)
                ->update(['receipts_state'=> $state]);
        }catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }

        if($state==1){
            // 寄送提醒信
            $order_paid_letter = Lang::get('確認付款信消費者');
            $order_paid_letter = str_replace("{order_number}", $o['order_number'], $order_paid_letter);
            $mailBody = "
                <html>
                    <head></head>
                    <body>
                        <div>
                            ".$order_paid_letter."
                        </div>
                        <div style='color:red;'>
                            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')."(".$o['id'].") ≡
                        </div>
                    </body>
                </html>
            ";
            $mail_return = HelperService::Mail_Send($mailBody,'client',$o['transport_email'],Lang::get('確認付款通知'));

            $order_paid_admin_letter = Lang::get('確認付款信管理者');
            $order_paid_admin_letter = str_replace("{order_number}", $o['order_number'], $order_paid_admin_letter);
            $mailBody = "
                <html>
                    <head></head>
                    <body>
                        <div>
                            ".$order_paid_admin_letter."
                        </div>
                        <div style='color:red;'>
                            ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')."(".$o['id'].") ≡
                        </div>
                    </body>
                </html>
            ";
            $mail_return = HelperService::Mail_Send($mailBody,'admin','',Lang::get('確認付款通知'));
        }
    }

    // 更新出貨狀態
    public function setTransportState($state) {
        try{
            $o = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
            $o = CommonService::objectToArray($o);

            if($state==1){ // 是改成「已出貨」
                if(in_array($o['status'], ['Complete'])){ /*檢查訂單是否已為「完成」*/
                    throw new \LogicException(Lang::get('已拋轉過完成'));
                }
                else if(in_array($o['status'], ['Cancel', 'Return'])){ /*檢查訂單是否已為「完成」*/
                    throw new \LogicException(Lang::get('請先恢復訂單'));
                }
                else if(in_array($o['status'], ['Pickable'])){ /*是否已在揀貨流程*/
                    if(empty(config('control.close_function_current')['揀貨列表'])){
                        throw new \LogicException(Lang::get('請先進行揀貨'));
                    }
                }
                else{ /*原本還是「未出貨」*/
                    // 派發優惠券
                    $this->change_coupon('send');
    
                    // 增加點數
                    if($o['add_point']>0){
                        $PointRecords = new PointRecords($o['user_id']);
                        $records = $PointRecords->add_records([
                            'msg'           => Lang::get('贈送點數').'('.Lang::get('完成訂單').')：'.$o['order_number'],
                            'points'        => $o['add_point'],
                            'belongs_time'  => time()
                        ]);
                    }
                    // 累積消費金額
                    DB::connection($this->order_db)->table('account')
                                                ->where('id', $o['user_id'])
                                                ->increment('total', $o['total']);
                }
            }

            // 修改訂單狀態
            DB::connection($this->order_db)->table($this->tableName)
                ->where('id', $this->id)
                ->update([
                    'transport_state' => $state,
                    'status' => 'Complete',
                    'transport_date' => date('Y-m-d'),
                ]);

        }catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 更新備註
    public function setPS($ps) {
        try{
            DB::connection($this->order_db)->table($this->tableName)
                ->where('id', $this->id)
                ->update(['ps'=>$ps]);

        }catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 退貨
    public function changeStatus2Return($reason) {
        try{
            // 收回優惠券
            $this->change_coupon('take_back');

            // 修改訂單狀態
            DB::connection($this->order_db)->table($this->tableName)
                ->where('id', $this->id)
                ->update([
                    'status' => 'Return',
                    'return_ps' => $reason,
                    'cancel_date'  => date('Y-m-d H:i:s',time()),
                ]);
        }
        catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }
    // 取消訂單
    public function changeStatus2Cancel($reason, $need_remind=true) {
        try{
            // 收回優惠券
            $this->change_coupon('take_back');
            
            // 修改訂單狀態
            DB::connection($this->order_db)->table($this->tableName)
                ->where('id', $this->id)
                ->update([
                    'status' => 'Cancel',
                    'cancel_ps' => $reason,
                    'cancel_date'  => date('Y-m-d H:i:s',time()),
                ]);

            if($need_remind){
                $o=DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->get();
                $o = CommonService::objectToArray($o[0]);
                
                $res_goods='';
                $result=(array)json_decode($o['product'], true);
                foreach ($result as $key => $value) {
                    $res_goods .=$value['name'];
                    if(!empty($result[$key+1])){
                        $res_goods .= '、';
                    }
                }
                
                /*取得寄信通用資料*/
                // dump($this->config_db);exit;
                $globalMailData = HelperService::getMailData($this->config_db);
                $payment_name = Payfee::get_payment_name($o['payment']);
                $order_cancel_letter = Lang::get('訂單取消信消費者');
                $Body = "
                    <html>
                        <head></head>
                        <body>
                            <div>
                                ".$order_cancel_letter."
                                ".Lang::get('訂單編號')."：".$o['order_number']."<br>
                                ".Lang::get('訂單時間')."：".date('Y/m/d H:i',$o['create_time'])."<br>
                                ".Lang::get('訂購商品')."：".$res_goods."<br>
                                ".Lang::get('訂單金額')."：".$o['total']."<br>
                                ".Lang::get('收件人')."：".$o['transport_location_name']."<br>
                                ".Lang::get('出貨地址')."：".$o['transport_location']."<br>
                                ".Lang::get('電子信箱')."：".$o['transport_email']."<br>
                                ".Lang::get('行動電話')."：".$o['transport_location_phone']."<br>
                                ".Lang::get('聯絡電話')."：".$o['transport_location_tele']."<br>
                                ".Lang::get('付款方式')."：".$payment_name."<br>
                                ".Lang::get('備註')."：".$o['transport_location_textarea']."<br>
                            </div>
                            <div>
                                ". $globalMailData['system_email']['order_cancel'] ."
                            </div>
                            <div style='color:red;'>
                                ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')."(".$o['id'].") ≡
                            </div>
                        </body>
                    </html>
                ";
                HelperService::Mail_Send($Body, $type='client', $o['transport_email'], Lang::get('訂單取消通知'), $this->config_db);
                
                /*通知管理者*/
                $order_cancel_admin_letter = Lang::get('訂單取消信管理者');
                $Body_admin = "
                    <html>
                        <head></head>
                        <body>
                            <div>
                                ".$order_cancel_admin_letter."
                                ".Lang::get('訂單編號')."：".$o['order_number']."<br>
                                ".Lang::get('訂單時間')."：".date('Y/m/d H:i',$o['create_time'])."<br>
                                ".Lang::get('訂購商品')."：".$res_goods."<br>
                                ".Lang::get('訂單金額')."：".$o['total']."<br>
                                ".Lang::get('收件人')."：".$o['transport_location_name']."<br>
                                ".Lang::get('出貨地址')."：".$o['transport_location']."<br>
                                ".Lang::get('電子信箱')."：".$o['transport_email']."<br>
                                ".Lang::get('行動電話')."：".$o['transport_location_phone']."<br>
                                ".Lang::get('聯絡電話')."：".$o['transport_location_tele']."<br>
                                ".Lang::get('付款方式')."：".$payment_name."<br>
                                ".Lang::get('備註')."：".$o['transport_location_textarea']."<br>
                            </div>
                            <div style='color:red;'>
                                ≡ ".Lang::get('此信件為系統自動發送，請勿直接回覆')."(".$o['id'].") ≡
                            </div>
                        </body>
                    </html>
                ";
                HelperService::Mail_Send($Body_admin, $type='admin', $email='', Lang::get('訂單取消通知'), $this->config_db);
            }
        }
        catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 恢復訂單
    public function changeStatus2Restore(){
        $this_order = DB::connection('main_db')->table($this->tableName)->find($this->id);
        $this_order = CommonService::objectToArray($this_order);
        
        /*扣回數量*/
        $return_num = json_decode($this_order['product'],true);
        foreach($return_num as $k => $v){/*先檢查還有沒有數量*/
        if(isset($v['type_id'])){
            $ck = DB::connection(config('A_sub'))->table('productinfo_type')->whereRaw("id = '".$v['type_id']."'")->select('num')->first()->num;
            if($ck - $v['num'] < 0){
                throw new \RuntimeException('庫存數量不足，此訂單無法恢復');
            }
        }
        }
        foreach($return_num as $k => $v){/*扣除數量*/
        if(isset($v['type_id'])){
            DB::connection('A_sub')->table('productinfo_type')->whereRaw("id = '".$v['type_id']."'")->decrement('num',$v['num']);
        }
        }
        /*扣回數量*/

        /*使用訂單所設優惠*/
        $user_point = DB::connection('main_db')->table('account')->select('point')->find($this_order['user_id'])->point ?? 0;
        $this_order['discount'] = json_decode($this_order['discount'], true);
        if($this_order['discount']){
        switch ($this_order['discount'][0]['type']) {
            case Lang::get('使用紅利點數'):
                if($user_point <= 0 || $user_point < $this_order['discount'][0]['dis']){
                    throw new \RuntimeException('會員點數不足');
                }

                $PointRecords = new PointRecords($this_order['user_id']);
                $records = $PointRecords->add_records([
                    'msg'           => '恢復訂單：'.$this_order['order_number'].'，使用紅利線上購物',
                    'points'        => $this_order['discount'][0]['dis'] * (-1),
                    'belongs_time'  => $this_order['create_time']
                ]);
                break;
        
            case Lang::get('會員優惠券'):
                $config_db = OrderHelper::get_shop_db_config($this_order['order_number']);
                $coupon_id = DB::connection($config_db)->table('coupon_pool')->find($this_order['discount'][0]['coupon_pool_id'])['coupon_id'];
                $coupon_pool_id = DB::connection($config_db)
                                    ->table('coupon_pool')
                                    ->whereRaw("coupon_id = ".$coupon_id." and login_time is not null and use_time is null and owner =".$this_order['user_id'])
                                    ->first();
                if($coupon_pool_id){
                    $coupon_pool_id = $coupon_pool_id['id'];
                    DB::connection($config_db)
                    ->table('coupon_pool')
                    ->where('id',$coupon_pool_id)
                    ->update(['use_time'=>time()]);
                    $this_order['discount'][0]['coupon_pool_id'] = $coupon_pool_id;
                    DB::connection('main_db')->table($this->tableName)->where('id', $this->id)->update(['discount'=>json_encode($this_order['discount'], JSON_UNESCAPED_UNICODE)]);
                }else{
                    throw new \RuntimeException('優惠券不足');
                }
            break;
        
            default:
                break;
        }
        }
        DB::connection('main_db')->table($this->tableName)->where('id', $this->id)->update(['status'=>'New']);
    }

    // 刪除訂單
    public function delete() {
        try{
            DB::connection($this->order_db)->table($this->tableName)->delete($this->id);
            DB::connection($this->order_db)->table('orderform_product')->where('orderform_id', $this->id)->delete();
            return true;
        } catch (\Exception $e){
            throw new \RuntimeException($e->getMessage());
        }
    }

    // 返回下單使用的優惠
    public function returndiscount() {
        $discount = DB::connection($this->order_db)->table('orderform')
                                                    ->select('*')
                                                    ->find($this->id);
        $discount = CommonService::objectToArray($discount);
        $discount['discount'] = json_decode($discount['discount'], true);
        if($discount['discount']){
            switch ($discount['discount'][0]['type']) {
                case Lang::get('使用紅利點數'):
                    $PointRecords = new PointRecords($discount['user_id']);
                    $records = $PointRecords->add_records([
                        'msg'           => Lang::get('返還使用點數').'('.Lang::get('取消訂單').')：'.$discount['order_number'],
                        'points'        => $discount['discount'][0]['dis'],
                        'belongs_time'  => $discount['create_time']
                    ]);
                    $PointRecords->set_point_expire(); /*扣除過期點數*/

                    break;
                case Lang::get('會員優惠券'):
                    DB::connection($this->config_db)->table('coupon_pool')
                                                    ->where('id', $discount['discount'][0]['coupon_pool_id'])
                                                    ->update(['use_time'=> null]);
                    break;
            }
        }
        return;
    }

    // 依傳入方法派送/收回優惠券
    public function change_coupon($mothod){
        
        $this_order = DB::connection($this->order_db)->table($this->tableName)->where('id', $this->id)->first();
        $this_order = CommonService::objectToArray($this_order);
        
        $product = OrderHelper::get_orderform_products([$this_order['id']]);
        
        $db_coupon_pool = DB::connection($this->config_db);
        
        $db_coupon_pool->beginTransaction();
        foreach ($product as $key => $value) {
            if(isset($value['key_type'])){
                if($value['key_type'] =='coupon'){
                    $copuon_id = explode('_', $value['type_id'])[0];
                    switch ($mothod) {
                        case 'send': /*贈送*/
                            $change_data = ['login_time' => time()]; /*設定領取時間*/
                            $cuopon_where = " coupon_id=".$copuon_id." and owner=".$this_order['user_id']." and use_time is null and login_time is null";
                            break;
                        
                        case 'take_back': /*收回*/
                            $change_data = ['login_time' => null, 'owner' => null];  /*清空領取時間、領取人id*/
                            $cuopon_where = " coupon_id=".$copuon_id." and owner=".$this_order['user_id']." and use_time is null and login_time is null";
                            break;

                        default:
                            $db_coupon_pool->rollback();
                            throw new \RuntimeException(Lang::get('優惠券不足'));
                            break;
                    }

                    $coupon_pool_ids = DB::connection($this->config_db)->table($this->coupon_tableName)
                                                                    ->select('id')
                                                                    ->where($cuopon_where)
                                                                    ->limit($value['num'])
                                                                    ->get();
                    $coupon_pool_ids = CommonService::objectToArray($coupon_pool_ids);
                    if(count($coupon_pool_ids) < $value['num']){
                        $db_coupon_pool->rollback();
                        throw new \RuntimeException(Lang::get('優惠券不足'));
                    }

                    $in_id = [];
                    foreach ($coupon_pool_ids as $key => $value) {
                        array_push($in_id, $value['id']);
                    }
                    if($in_id){
                        $in_id = '('.join(',', $in_id).')';
                        $db_coupon_pool->table($this->coupon_tableName)
                            ->where('id in '.$in_id)
                            ->update($change_data);
                    }
                }
            }
        }
        $db_coupon_pool->commit();
        
    }
}

