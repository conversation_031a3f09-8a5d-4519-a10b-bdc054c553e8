<?php
namespace App\Services\pattern\simpleFactory\orderFactory;

use Illuminate\Support\Facades\Lang;
/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is delete
 * @depend: none
 *
*/
class PickableOrder extends Order
{
    public function changeStatus2Next() {
        throw new \LogicException(Lang::get('訂單已待揀貨，請進行揀貨'));
    }
    public function changeStatus2Return($reason) {
        throw new \LogicException(Lang::get('訂單已待揀貨，不可取消'));
    }
    public function changeStatus2Cancel($reason, $need_remind=true) {
        throw new \LogicException(Lang::get('訂單已待揀貨，不可取消'));
    }
    public function changeStatus2Restore() {
        throw new \LogicException('PickableOrder can\'t be restored');
    }
}
