<?php

namespace App\Services\pattern\simpleFactory\orderFactory;

 /*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is cancel
 * @depend: none
 *
*/

class ReturnOrder extends Order
{
    public function changeStatus2Next() {
        throw new \LogicException('Cancel status without next status');
    }
    public function changeStatus2Return($reason) {
        throw new \LogicException('Cancel status without next status');
    }
    public function changeStatus2Cancel($reason, $need_remind=true) {
        throw new \LogicException('Cancel status without next status');
    }
    public function changeStatus2Restore() {
        parent::changeStatus2Restore();
    }
}
