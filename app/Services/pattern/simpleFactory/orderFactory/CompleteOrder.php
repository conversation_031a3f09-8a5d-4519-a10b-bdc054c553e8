<?php
namespace App\Services\pattern\simpleFactory\orderFactory;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\PointRecords;
/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is complete
 * @depend: none
 *
*/
class CompleteOrder extends Order
{
    public function changeStatus2Next() {
        throw new \LogicException(Lang::get('已拋轉過完成'));
    }

    public function changeStatus2Return($reason) {
        $this->returnPointAndPrice();
        parent::returndiscount();
        parent::changeStatus2Return($reason);
    }

    public function changeStatus2Cancel($reason, $need_remind=true) {
        // throw new \LogicException('CompleteOrder can\'t be canceled');
        $this->returnPointAndPrice();
        parent::returndiscount();
        parent::changeStatus2Cancel($reason, false);
    }

    public function changeStatus2Restore() {
        throw new \LogicException('CompleteOrder can\'t be restored');
    }

    private function returnPointAndPrice() {
        $owner = DB::connection($this->order_db)->table('orderform')
                                                ->select('*')
                                                ->find($this->id);
        $owner=CommonService::objectToArray($owner);
        $PointRecords = new PointRecords($owner['user_id']);
        $records = $PointRecords->add_records([
            'msg'           => Lang::get('扣除贈送點數').'('.Lang::get('取消訂單').')：'.$owner['order_number'],
            'points'        => $owner['add_point'] * (-1),
            'belongs_time'  => time()
        ]);
        $PointRecords->set_point_expire(); /*扣除過期點數*/
    }
}
