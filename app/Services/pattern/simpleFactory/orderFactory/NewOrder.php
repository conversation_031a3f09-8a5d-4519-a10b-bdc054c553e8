<?php
namespace App\Services\pattern\simpleFactory\orderFactory;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

use App\Services\CommonService;
use App\Services\pattern\PointRecords;
/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 08 2017
 * @Description: orderClass that status is new
 * @depend: (1)thinkPHP5.x think\Db static class
 *
*/
class NewOrder extends Order
{
    public function changeStatus2Next() {
        if($this->already2Next){
            throw new \LogicException('Status already change to next'); 
        }
        try{
            if(empty(config('control.close_function_current')['揀貨列表'])){
                throw new \LogicException(Lang::get('請先進行揀貨'));
            }
            $this->setTransportState(1);
            $this->already2Next = true;
        }catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage());
        }
    }

    public function changeStatus2Return($reason) {
        parent::returndiscount();
        parent::changeStatus2Return($reason);
    }

    public function changeStatus2Cancel($reason, $need_remind=true) {
        parent::returndiscount();
        parent::changeStatus2Cancel($reason);
    }

    public function changeStatus2Restore() {
        throw new \LogicException('NewOrder can\'t be restored');
    }
}

























