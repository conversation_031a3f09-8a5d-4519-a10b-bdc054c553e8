<?php
namespace App\Services\pattern;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//photonicClass
use App\Services\CommonService;
use App\Services\DBtool\DBTextConnecter;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\MemberArticle;

class TaskHelper
{
  static public $tableName = 'task';
  static public $tableName_login = 'task_login';
  static public $task_type = [ /*活動類型*/
    '1' => '加入會員',
    '2' => '推薦會員 ',
    '3' => '每日登入',
    '4' => '會員上傳瀏覽',
    '5' => '會員上傳互動',
  ];

  /*取得任務資料*/
  static public function get_tasks(array $params=[], bool $id_as_key=false){
    $db_data = DB::table(self::$tableName);
    $db_data->selectRaw('*');

    if(isset($params['id'])){ $db_data->where('id', $params['id']); }
    if(isset($params['type'])){ $db_data->where('type', $params['type']); }
    if(isset($params['name'])){ $db_data->where('name', 'like', '%'.$params['name'].'%'); }
    if(isset($params['using'])){
      $now_date = date('Y-m-d');
      if($params['using']=='1'){ /*進行中*/
        $db_data->where(function($query)use($now_date){
          return $query->orWhere(function($query2)use($now_date){
            return $query2->where('time_s', '')->where('time_e', '');
          })
          ->orWhere(function($query2)use($now_date){
            return $query2->where('time_s', '<=', $now_date)->where('time_e', '');
          })
          ->orWhere(function($query2)use($now_date){
            return $query2->where('time_s', '')->where('time_e', '>=', $now_date);
          })
          ->orWhere(function($query2)use($now_date){
            return $query2->where('time_s', '<=', $now_date)->where('time_e', '>=', $now_date);
          });
        });
      }else if($params['using']=='2'){ /*尚未開始*/
        $db_data->where('time_s', '>', $now_date);
      }else if($params['using']=='3'){ /*結束*/
        $db_data->where('time_e', '<', $now_date)
                ->where('time_e', '!=', '');
      }
    }
    if(isset($params['time_e'])){ $db_data->where('time_e', $params['time_e']); }
  
    /*分頁*/
    if(isset($params['count_of_items'])){ /*有傳入一頁數量*/
      $index_and_length = CommonService::get_model_limit_index_and_length($params['count_of_items'], $params['page'] ?? 1);
      if($index_and_length[1]){ /*需依分頁篩選*/
        $db_data->offset($index_and_length[0])->limit($index_and_length[1]);
      }
    }

    $db_data = $db_data->orderBy('time_s', 'asc')->orderBy('id', 'desc')->get();
    $db_data = CommonService::objectToArray($db_data);

    if($id_as_key){
      $temp_data = [];
      foreach ($db_data as $value) {
        $temp_data[$value['id']] = $value;
      }
      $db_data = $temp_data;
    }
    return ['db_data'=>$db_data];
  }
  /*儲存資料(根據傳入資料的id==0與否判斷為新增或編輯)*/
  static public function save_tasks($detail_data){
    if(!isset($detail_data['id'])){ 
      throw new \Exception(Lang::get('資料不完整'));
    }

    /*防呆檢查*/
    if(isset($detail_data['name'])){
      if($detail_data['name']==''){
        throw new \Exception('請輸入名稱');
      }
    }
    if(isset($detail_data['type'])){
      if(!in_array($detail_data['type'], array_keys(self::$task_type))){
        throw new \Exception('無此活動類型');
      }else if($detail_data['type']=='4'){/*會員上傳瀏覽*/ 
        if(!($detail_data['time_e']??'')){ throw new \Exception('請設定結束時間'); }
      }else if($detail_data['type']=='5'){/*會員上傳互動*/
        if(!($detail_data['time_e']??'')){ throw new \Exception('請設定結束時間'); }
      }
    }
    if(isset($detail_data['msg'])){
      if($detail_data['msg']==''){
        throw new \Exception('贈送記錄文字');
      }
    }
    if(isset($detail_data['bonus_column1'])){
      if($detail_data['bonus_column1']==''){
        throw new \Exception('獎勵設定有誤');
      }
    }

    // dd($detail_data);
    $DBTextConnecter = DBTextConnecter::withTableName(self::$tableName);
    if($detail_data['id']==0){ /*新增*/
      unset($detail_data['id']);
      $DBTextConnecter->setDataArray($detail_data);
      $id = $DBTextConnecter->createTextRow();
    }else{ /*編輯*/
      $DBTextConnecter->setDataArray($detail_data);
      $DBTextConnecter->upTextRow();
      $id = $detail_data['id'];
    }
    return $id;
  }
  /*刪除資料*/
  static public function delete_tasks($item_id){
    if(!$item_id){ 
      throw new \Exception(Lang::get('資料不完整'));
    }

    $db_data = DB::table(self::$tableName);
    $db_data = $db_data->where('id', $item_id);
    return $db_data->delete();
  }


  /*發送獎勵-加入會員(傳入新註冊會員id)*/
  static public function send_bonus_1($signup_user_id){
    $do_send = false;

    $MemberInstance = new MemberInstance($signup_user_id);
    $user_data = $MemberInstance->get_user_data();
    if($user_data){
      $create_time = time();
      $tasks = self::get_tasks(['type'=>1, 'using'=>1])['db_data'];
      foreach ($tasks as $task) {
        $do_send = true;
        $MemberInstance->add_increasing_limit_record(
          $task['bonus_column1'], 
          $task['msg'], 
          2, 
          5, 
          $create_time
        );
      }
    }
    return $do_send;
  }
  /*發送獎勵-推薦會員(傳入新註冊會員id)*/
  static public function send_bonus_2($signup_user_id){
    $do_send = false;

    $MemberInstance = new MemberInstance($signup_user_id);
    $user_data = $MemberInstance->get_user_data();
    /*找出推薦者*/
    $upline_user = $user_data['upline_user']??0;
    if($upline_user!=0){
      $MemberInstance->change_user_id($upline_user);
  
      $create_time = time();
      $tasks = self::get_tasks(['type'=>2, 'using'=>1])['db_data'];
      foreach ($tasks as $task) {
        $do_send = true;
        $MemberInstance->add_increasing_limit_record(
          $task['bonus_column1'], 
          $task['msg'], 
          2, 
          5, 
          $create_time
        );
      }
    }
    return $do_send;
  }
  /*發送獎勵-每日登入(傳入登入會員id)*/
  static public function send_bonus_3($login_user_id){
    $do_send = false;
    /*檢查登入紀錄*/
    $login = DB::table(self::$tableName_login)
                ->where('user_id', $login_user_id)
                ->where('time', '>', strtotime(date('Y-m-d')))
                ->where('time', '<=', strtotime(date('Y-m-d').' +1Day'))->get()->toArray();
    if(count($login)==0){
      $MemberInstance = new MemberInstance($login_user_id);

      $create_time = time();
      $tasks = self::get_tasks(['type'=>3, 'using'=>1])['db_data'];
      foreach ($tasks as $task) {
        $do_send = true;
        $MemberInstance->add_increasing_limit_record(
          $task['bonus_column1'], 
          $task['msg'], 
          2, 
          5, 
          $create_time
        );
      }
      if($do_send){
        /*添加登入紀錄*/
        DB::table(self::$tableName_login)->insert([
          'user_id' => $login_user_id,
          'time' => time(),
        ]);
      }
    }
    return $do_send;
  }
  /*發送獎勵-會員上傳瀏覽獎勵*/
  static public function send_bonus_4(){
    /*找出要處理的會員上傳瀏覽任務*/
    $tasks = self::get_tasks([
      'using' => '3', /*已結束*/
      'time_e' => date('Y-m-d', strtotime(date('Y-m-d').' -1Day')),/*結束日期在昨日*/
      'type' => 4,
    ])['db_data'];
    // dump($tasks);
    $create_time = time();
    $MemberInstance = new MemberInstance(0);
    foreach ($tasks as $task) {
      /*統計任務區間內文章瀏覽次數*/
      $user_cal = MemberArticle::calculate_record(1, $task['time_s'], $task['time_e']);
      // dump($user_cal);exit;
      foreach ($user_cal as $user_id => $num) {
        if($num>0){
          $MemberInstance->change_user_id($user_id);
          $MemberInstance->add_increasing_limit_record(
            ($num / (float)$task['bonus_column1']), 
            $task['msg'], 
            2, 
            5, 
            $create_time
          );
        }
      }
    }
  }
  /*發送獎勵-會員上傳互動獎勵*/
  static public function send_bonus_5(){
    /*找出要處理的會員上傳瀏覽任務*/
    $tasks = self::get_tasks([
      'using' => '3', /*已結束*/
      'time_e' => date('Y-m-d', strtotime(date('Y-m-d').' -1Day')),/*結束日期在昨日*/
      'type' => 5,
    ])['db_data'];
    // dump($tasks);
    $create_time = time();
    $MemberInstance = new MemberInstance(0);
    foreach ($tasks as $task) {
      /*統計任務區間內文章喜歡次數*/
      $user_cal = MemberArticle::calculate_record(2, $task['time_s'], $task['time_e']);
      // dump($user_cal);exit;
      foreach ($user_cal as $user_id => $num) {
        if($num>0){
          $MemberInstance->change_user_id($user_id);
          $MemberInstance->add_increasing_limit_record(
            ($num / (float)$task['bonus_column1']), 
            $task['msg'], 
            2, 
            5, 
            $create_time
          );
        }
      }
    }
  }
}