<?php
namespace App\Services\pattern\recursiveCorrdination\discountRC;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use App\Services\pattern\recursiveCorrdination\cartRC\Proposal;

class CouponCheck extends TeamMember {

  public static function createFactory($Proposal) {
    $instance = new self($Proposal);
    return $instance->participate();
  }

  public function participate() {
    $coupons = $this->getCoupon();
    $Total = $this->getTotal();
    $require = $this->Proposal->getRequire();

    /*購物車內的供應商們*/
    $distributors = [];
    // dump($require['cartData']);exit;
    foreach ($require['cartData'] as $key => $cartValue) {
      [$key_id, $key_type] = Proposal::get_prod_key_type($cartValue['type_id']);
      
      if(substr($key_type , 0, 8)=='askprice'){ /*如果有購買 詢價商品*/
        $this->Proposal->projectData['coupon'] = []; /*不可使用優惠券*/
        return $this->send2Next();
      }

      if(!in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)){ /*屬於 productinfo商品*/
        $productinfo = DB::table('productinfo')->whereRaw('id = "'.$cartValue['type_product_id'].'"')->first();
        $productinfo = CommonService::objectToArray($productinfo);
        if(!$productinfo){ continue; }
        /*紀錄購物車內的供應商們*/
        array_push($distributors, $productinfo['distributor_id']);
      }
    }
    // dump($distributors);exit;

    // 篩選可用的優惠券
    $coupons = array_filter($coupons, function ($coupon) use ($Total, $require, $distributors) {
      // 全館商品
      if (!$coupon['coupon_area']) {
        return (
          $coupon['coupon_condition'] <= $Total
          && in_array($coupon['coupon_distributor_id'], $distributors)
        );
      }

      // 單一商品
      return sizeof(array_filter($require['cartData'], function ($cartValue) use ($coupon, $Total, $distributors) {
        [$key_id, $key_type] = Proposal::get_prod_key_type($cartValue['type_id']);
        if(!in_array($key_type, Proposal::NOT_PRODUCTINFO_PRODTYPE)){ /*屬於 productinfo商品*/
          $productinfo = DB::table('productinfo')->whereRaw('id = "'.$cartValue['type_product_id'].'"')->first();
          $productinfo = CommonService::objectToArray($productinfo);
          if(!$productinfo){return [];}
          return (
            $coupon['coupon_condition'] <= $Total
            && $coupon['coupon_area_id'] == $productinfo['id']
            && in_array($coupon['coupon_distributor_id'], $distributors)
          );
        }else{
          return [];
        }
      }));
    });
    // 調整可用的優惠券的折抵金額
    $coupons = array_map(function ($coupon) use ($Total) {
      if($coupon['discount'] > $Total){
        $coupon['discount'] = $Total;
      }
      return $coupon;
    }, $coupons);
    $coupons = array_values($coupons);
    // dump($coupons);exit;
    $this->Proposal->projectData['coupon'] = $coupons;
    return $this->send2Next();
  }

  private function getCoupon() {
    $require = $this->Proposal->getRequire();
    $coupon_sql = $this->arrange_search_sql();
    $coupon = DB::table('coupon')
                ->select('coupon.id AS coupon_id', 
                    'coupon.distributor_id AS coupon_distributor_id', 
                    'coupon.area AS coupon_area', 
                    'coupon.area_id AS coupon_area_id', 
                    'coupon.coupon_condition AS coupon_condition', 
                    'coupon.discount AS discount', 
                    'coupon_pool.id AS coupon_pool_id', 
                    'coupon.title AS coupon_title')
                  ->join(
                    'coupon_pool', 
                    'coupon.id', '=', 'coupon_pool.coupon_id'
                  )->where([
                    'coupon_pool.owner' => $require['user_id']
                  ])
                  ->whereRaw($coupon_sql)
                  ->whereNull('coupon_pool.use_time')
                  ->whereNotNull('coupon_pool.login_time')
                  ->get();
    return CommonService::objectToArray($coupon);
  }
  static public function arrange_search_sql() {
    $check_time = strtotime(date('Y-m-d'));
    $query_sql = "
      coupon.online = 1 AND 
      (
        coupon.end <= 0 
        OR 
        (coupon.start <= " . $check_time . " AND coupon.end >= " . $check_time . ") 
      )
    ";
    return $query_sql;
  }

  private function getTotal() {
    $Total = 0;
    $require = $this->Proposal->getRequire();        
    array_walk($require['cartData'], function ($value) use (&$Total){
      $Total += (int)($value['countPrice']) * (int)$value['num'];
    });
    return $Total;
  }
}
