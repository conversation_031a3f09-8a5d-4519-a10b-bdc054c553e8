<?php
namespace App\Services\pattern\recursiveCorrdination\discountRC;

use Illuminate\Support\Facades\DB;

class Total extends TeamMember {
    public static function createFactory($Proposal) {
        $instance = new self($Proposal);
		return $instance->participate();
	}

    public function participate() {
        $Total = $this->getTotal();
        $this->Proposal->projectData['Total'] = $Total;
        return $this->send2Next();
    }

    private function getTotal() {
        $Total = 0;
        $require = $this->Proposal->getRequire();        
        array_walk($require['cartData'], function ($value) use (&$Total){
            $Total += ($value['countPrice']) * (int)$value['num'];
        });
        $Total = round($Total);
        return $Total;
    }
}

