<?php

namespace App\Services\pattern\recursiveCorrdination\discountRC;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;

class PointCheck extends TeamMember {
    public static function createFactory($Proposal) {
        $instance = new self($Proposal);
		return $instance->participate();
	}

    public function participate() {
        $points = $this->getPoints();
        $this->Proposal->projectData['points'] = $points;
        return $this->send2Next();
    }

    private function getPoints() {
        $require = $this->Proposal->getRequire();
        $point = DB::connection('main_db')
                ->table('account')
                ->select('point')
                ->where('id', $require['user_id'])
                ->get();
        $point = CommonService::objectToArray($point);
        if(empty($point)){ array_push($point, ['point'=>0]); }
        return $point;
    }
}
