<?php
namespace App\Services\pattern\recursiveCorrdination\discountRC;

use App\Services\CommonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Services\pattern\MemberInstance;

class ContributionDeduct extends TeamMember {

    public static function createFactory($Proposal) {
        $instance = new self($Proposal);
		return $instance->participate();
	}

    public function participate() {
        $require_data = $this->Proposal->getRequire();
        /*圓滿點數折抵上限*/
        $MemberInstance = new MemberInstance($require_data['user_id']);
        $member = $MemberInstance->get_user_data();
        $vip_type = $member['vip_type'] ?? 0;
        $discount_ratio = (Db::connection('main_db')->table("vip_type")->find($vip_type)->discount_ratio ?? 10) / 100;
        
        $cartData_deduct = [];
        foreach ($require_data['cartData'] as $value) {
            if($value['product_cate']==2){ /*消費商品才允許折抵*/
                $cartData_deduct[$value['type_id']] = floor($value['price_cv'] * $value['num']);
                $cartData_deduct_consumption[$value['type_id']] = floor($value['price_cv'] * $value['num'] * $discount_ratio);
            }else{
                $cartData_deduct[$value['type_id']] = 0;
                $cartData_deduct_consumption[$value['type_id']] = 0;
            }
        }
        // dd($cartData_deduct);
        $this->Proposal->projectData['ContributionDeduct'] = [
            'cartData_deduct' => $cartData_deduct, /*單項商品可折抵上限*/
            'cartData_deduct_consumption' => $cartData_deduct_consumption, /*單項商品消費圓滿點數可折抵上限*/
            'increasing_limit_invest' => $member['increasing_limit_invest'] ?? 0, /*功德圓滿點數*/
            'increasing_limit_consumption' => $member['increasing_limit_consumption'] ?? 0, /*消費圓滿點數*/
        ];
        return $this->send2Next();
    }
}