<?php
namespace App\Services\pattern\recursiveCorrdination\cartRC;

use App\Http\Controllers\home\GlobalController;

use Illuminate\Support\Facades\DB;

use App\Services\CommonService;
use App\Services\pattern\ExaminationHelper;
use App\Services\pattern\recursiveCorrdination\discountRC\ActCalculate;
use App\Services\pattern\AddpriceHelper;
use App\Services\pattern\AskpriceHelper;

class Proposal extends GlobalController
{
	public const NOT_PRODUCTINFO_PRODTYPE = ['coupon'];

	private $teamMembers;
	private $require;
	public $projectData;

    //overload construct by Static Factory Method Pattern
    private function __construct() {
    }
	public static function withTeamMembers(array $TeamMembers) {
		$instance = new self();
        $instance->teamMembers = $TeamMembers;
        return $instance;
	}
	public static function withTeamMembersAndRequire(array $TeamMembers, $require) {
		$instance = new self();
		$instance->teamMembers = $TeamMembers;
        $instance->require = $require;
        if(!isset($instance->require['cart_session'])){
        	$instance->require['cart_session'] = 'cart';
        }
        return $instance;
	}

	public function getNextMember() {
		return array_shift($this->teamMembers);
	}

	public function getTeamMembers() {
		return $this->teamMembers;
	}

	public function getTeamMemberCount() {
		return sizeof($this->teamMembers);
	}

	public function getRequire() {
		return $this->require;
	}

	public function getCartArray() {
		$cartArray = [];
		if(isset($this->projectData['data'])){
			$cartArray = json_decode($this->projectData['data'], true);
			if(!$cartArray){ $cartArray = []; }
		}
		return $cartArray;
	}
	public function getCartArray_with_info($buy_method='online') {
		$cartArray = $this->getCartArray();
		foreach ($cartArray as $cart_key => $num) {
			$cartArray[$cart_key] = self::get_singleData($cart_key, $buy_method); /* 取得商品資料 */
			$cartArray[$cart_key]['num'] = $num;
		}
		return $cartArray;
	}

	public static function get_prod_key_type($cart_type_id){
        $key_list = explode("_", $cart_type_id);
        $type_id_ori = $key_list[0];
        if( count($key_list)>1 ){
            $key_type = $key_list[1];
        }else{
            $key_type = 'normal';
        }
        return [$type_id_ori, $key_type];
    }
	public static function get_singleData($cart_key, $buy_method='online'){
        $user_id = session('user.id');
        [$type_id_ori, $key_type] = self::get_prod_key_type($cart_key);
        // dump($type_id_ori);dump($key_type);exit;
        switch($key_type){
            case 'normal': // 一般商品 或 某網紅的商品 或 加價購商品
            case substr($key_type , 0, 3)=='kol':
            case 'add':
            case substr($key_type , 0, 8)=='askprice':
                $singleData = DB::table('productinfo_type as type')
                                                        ->select('type.id AS type_id',
                                                                 'type.price AS type_price',
                                                                 'type.count AS type_count',
                                                                 'type.title AS type_title',
                                                                 'type.product_id AS type_product_id',
                                                                 'type.pic_index',
                                                                 'type.price_cv',
                                                                 'type.price_supplier',
                                                                 
                                                                 'info.title AS info_title',
                                                                 'info.distributor_id',
                                                                 'info.pic AS info_pic1',
                                                                 'info.card_pay AS card_pay',
                                                                 'info.is_registrable',
                                                                 'info.pay_type',
                                                                 'info.shipping_type',
                                                                 'info.shipping_fee_tag', 
                                                                 'info.product_cate',
                                                                 'info.bonus_model_id',
                                                                 'info.use_ad',
                                                                 'info.vip_type_reward',
                                                                 'info.vip_type_require'
                                                                )
                                                        ->join('productinfo as info', 'info.id', '=', 'type.product_id')
                                                        ->where('type.id', $type_id_ori)
                                                        ->first();
                $singleData = CommonService::objectToArray($singleData);
                $info_pics = json_decode($singleData['info_pic1'],true);
                $info_pic1 = '';
                if($info_pics){
                    $pic_index = 0;
                    if($singleData['pic_index'] >=1 && $singleData['pic_index'] <= count($info_pics)){
                        $pic_index = $singleData['pic_index']-1;
                    }
                    $info_pic1 = $info_pics[$pic_index];
                }
                $singleData['info_pic1'] = $info_pic1;

                /* 讀取報名欄位 */
                $fields = DB::table('productinfo_register_fields')->where('prod_id', $singleData['type_product_id'])->orderByRaw('order_id asc, id asc')->get();
                $singleData['fields'] = CommonService::objectToArray($fields);
                /* 讀取報名資料 (依品項、不管報名者id、報名未成立)*/
                $singleData['examinee'] = ExaminationHelper::get_myself_type_examinee_info($cart_key, $examinee_id=0, $order_id=0);

                // 修改type_id，讓購物車可以記錄到
                $singleData['type_id'] = $singleData['type_id'].'_'.$key_type;

                $singleData['countPrice'] = $singleData['type_count']; /*預設秀的價格是品項的售價*/
                if($key_type=='add'){ /*加價購修改價格*/
                    $addprice = self::get_addprice_products_by_cart();
                    if(isset($addprice['type'.$type_id_ori]) && empty(config('control.close_function_current')['加價購設定']) ){
                        $singleData['countPrice'] = $addprice['type'.$type_id_ori]['adp_dis'];
                    }
                }
                else if(substr($key_type , 0, 8)=='askprice'){ /*詢價修改價格*/
                    $askprice_id = str_replace('askprice', '', $key_type);
                    $AskpriceHelper = new AskpriceHelper();
                    $result = $AskpriceHelper->getOne_by_main_id($askprice_id, 'a.user_id ="'.$user_id.'"');
                    $main = $result['main'];
                    if(!$main){ break; }
                    $main_record = $result['current'];
                    if(!$main_record){ break; }
                    if($main_record['status'] != 1){ break; }
                    if($main_record['agree'] != 1){ break; }
                    if($main_record['expired_date']){
                        if(strtotime($main_record['expired_date'].' +1Day') < time()){ break; }
                    }
                    if($main_record['bought'] == 1){ break; }

                    $singleData['countPrice'] = $main_record['price_final'] / $main_record['num'];
                }
                else if($buy_method=='online'){ // 線上購物 要套用立馬省
                    $act_type_sql = ActCalculate::arrange_search_sql($act_type=2);
                    $freediscount = DB::table('act_product')
                                            ->join('act', 'act.id', '=', 'act_product.act_id')
                                            ->whereRaw($act_type_sql)
                                            ->whereRaw('act_product.prod_id = '.$singleData['type_product_id'])
                                            ->get();
                    $freediscount = CommonService::objectToArray($freediscount);
                    $singleData['countPrice'] = !empty($freediscount) ?  $singleData['type_count'] - $freediscount[0]['discount1'] : $singleData['type_count'];
                    $singleData['countPrice'] = $singleData['countPrice'] < 0 ? 0 : $singleData['countPrice'];
                }
                break;

            case 'coupon': // 優惠券商品
                $coupon = DB::table('coupon_pool')
                            ->whereNull('coupon_pool.owner')
                            ->select('coupon_pool.id AS coupon_pool_id',
                                        'coupon.price AS price',
                                        'coupon.title AS title',
                                        'coupon.distributor_id',
                                        'coupon.pic AS pic',
                                        'coupon.id AS coupon_id')
                            ->whereRaw('(
                                        coupon_pool.login_time < ' . (time() - 21600) . ' OR ' .'
                                        coupon_pool.login_time IS NULL
                                    )')
                            ->whereRaw('coupon.id = ' . $type_id_ori)
                            ->join('coupon', 'coupon_pool.coupon_id', '=', 'coupon.id')
                            ->first();
                $coupon = CommonService::objectToArray($coupon);
                $singleData = [
                    'type_title'        => '',
                    'type_price'        => $coupon['price'],
                    'type_count'        => $coupon['price'],
                    'countPrice'        => $coupon['price'],
                    'info_title'        => $coupon['title'],
                    'info_pic1'         => $coupon['pic'],
                    'distributor_id'    => $coupon['distributor_id'],
                    'type_id'           => $coupon['coupon_id'].'_'.$key_type,
                    'type_product_id'   => $coupon['coupon_id'],
                    // 'productId'          => $coupon['coupon_id'],
                    'card_pay'          => 1, // 預設都可刷卡
                    'is_registrable'    => 0, // 預設不用填報名資料
                    'pay_type'          => "",// 預設何種付款方法都可以
                    'shipping_type'     => "",// 預設何種運法都可以
                    'shipping_fee_tag'  => 0,// 預設運費標籤為0元
                    'product_cate'      => 0,
                    'bonus_model_id'    => 0,
                    'use_ad'            => 0,
                    'vip_type_reward'   => 0,
                    'vip_type_require'  => 0,
                    'price_cv'          => 0,
                    'price_supplier'    => 0,
                    'examinee'          => [],// 預設無報名資料
                ];
                break;

            default:
                break;
        }
        $singleData['type_id_ori'] = $type_id_ori;
        $singleData['key_type'] = $key_type;
        
        return $singleData;
    }
    static public function get_addprice_products_by_cart(){
        $Proposal = Proposal::withTeamMembersAndRequire(
            ['GetCartData'],
            [
                'cart_session' => 'cart',
                'user_id' => '-1',
            ]
        );
        $Proposal = MemberFactory::createNextMember($Proposal);
        $cart = $Proposal->getCartArray();
        // dump($cart);exit;

        $type_ids = []; /*紀錄總共買了那些品項，用於找出可加價購的商品*/
        foreach ($cart   as $cart_key => $num) {
            [$type_id_ori, $key_type] = Proposal::get_prod_key_type($cart_key);

            if($key_type=='normal' || substr($key_type, 0, 3)=='kol'){ /* 一般商品 或 網紅商品 可列入找尋加價購*/
                foreach (range(1,$num) as $v) {
                    array_push($type_ids, $type_id_ori);
                }
            }
        }
        // dump($type_ids);
        $addprice_group = $type_ids ? self::get_addprice_products($type_ids)['addprice_group'] : [];

        return $addprice_group;
    }
    static public function get_addprice_products($type_ids){
        if(!$type_ids) return ['addprice_type_group'=>[], 'addprice_group'=>[]];
        /*製作符合品項的搜尋條件*/
        $type_count=[];
        foreach ($type_ids as $value) {
            if( !isset($type_count[$value]) ){
                $type_count[$value] = 1;
            }else{
                $type_count[$value] += 1;
            }
        }
        $type_ids_where = $type_ids ? 'product_type_id in ('.implode(',', $type_ids).')' : "1=1";
        
        /*製作正在啟用中的加價購搜尋條件*/
        $addprices = AddpriceHelper::getListByDate($start=date('Y-m-d'),$end=date('Y-m-d'), $frontEnd=true)->get();
        $addprices = CommonService::objectToArray($addprices);
        $addprices_list=[];
        foreach ($addprices as $key => $value) {
            array_push($addprices_list, $value['id']);
        }
        $addprices_where = $addprices_list ? 'addprice_id in ('.implode(',', $addprices_list).')' : "1=0"; 

        /*找出 正在啟用中 且 條件商品是此品項的 全部條件商品*/
        $ruleProds = DB::table('addprice_rule')
                ->select('pt.*','pi.title as pi_title','pi.pic','product_type_id','addprice_id as addprice_id')
                ->join('productinfo_type as pt', 'pt.id','product_type_id')
                ->join('productinfo as pi','pi.id','pt.product_id')
                ->whereRaw('pt.online=1')
                ->whereRaw($addprices_where)
                ->whereRaw($type_ids_where)
                ->get();
        $ruleProds = CommonService::objectToArray($ruleProds);
        $addprice_type_group = []; /*跟據各品項整理可加價購商品*/
        $addprice_group = []; /*整理全部可購買加價購商品*/
        foreach ($ruleProds as $rp_k => $rp_v) {
            /*根據條件商品 找出對應加價購的優惠折扣、加價購商品*/
            $actProd = DB::table('addprice_product as adp_p')
                            ->select('pt.*','pi.title as pi_title','pi.pic','adp_p.limit_num as adp_p_num','adp.discount as adp_dis')
                            ->join('productinfo_type as pt', 'pt.id','adp_p.product_type_id')
                            ->join('productinfo as pi','pi.id','pt.product_id')
                            ->join('addprice as adp','adp.id','adp_p.addprice_id')
                            ->whereRaw('pt.online=1')
                            ->where('adp_p.addprice_id', $rp_v['addprice_id'])
                            ->get();
            $actProd = CommonService::objectToArray($actProd);
            $rp_v['pic'] = json_decode($rp_v['pic'],true);
            $rule_type_id = 'type'.$rp_v['id'];
            foreach ($actProd as $ap_k => $ap_v) {
                $product_type_id = 'type'.$ap_v['id'];
                $ap_v['cart_id'] = $ap_v['id'].'_add';
                $ap_v['pic'] = json_decode($ap_v['pic'],true);
                $ap_v['adp_p_num'] = ($ap_v['adp_p_num'] * $type_count[ $rp_v['product_type_id'] ]); /*依給的type_ids修正可加購數量*/
                $ap_v['adp_dis'] = round($ap_v['adp_dis'] * $ap_v['count']);
                /*整理addprice_type_group-----------------------------*/
                /*檢查是否已存在此加價購條件商品*/
                if( !isset($addprice_type_group[$rule_type_id]) ){ /*加入條件商品*/
                    $addprice_type_group[$rule_type_id] = [
                        'rule_info' => $rp_v,
                        'product'   => [],
                    ];
                }
                /*檢查是否已存在此加價購商品*/
                if( !isset($addprice_type_group[$rule_type_id]['product'][$product_type_id]) ){ /*不存在*/
                    $addprice_type_group[$rule_type_id]['product'][$product_type_id] = $ap_v; /*加入加價購商品*/

                }else{/*存在*/
                    $addprice_type_group[$rule_type_id]['product'][$product_type_id]['adp_p_num'] += $ap_v['adp_p_num']; /*購買上限數量相加*/
                    if($addprice_type_group[$rule_type_id]['product'][$product_type_id]['adp_dis']>$ap_v['adp_dis']){ /*檢查優惠折扣，取較小者*/
                        $addprice_type_group[$rule_type_id]['product'][$product_type_id]['adp_dis']=$ap_v['adp_dis'];
                    }
                }
                /*----------------------------------------------------*/

                /*整理addprice_group----------------------------------*/
                if( !isset($addprice_group[$product_type_id]) ){ /*加入條件商品*/
                    $addprice_group[$product_type_id] = $ap_v;
                }else{
                    $addprice_group[$product_type_id]['adp_p_num'] += $ap_v['adp_p_num']; /*購買上限數量相加*/
                    if($addprice_group[$product_type_id]['adp_dis']>$ap_v['adp_dis']){ /*檢查優惠折扣，取較小者*/
                        $addprice_group[$product_type_id]['adp_dis']=$ap_v['adp_dis'];
                    }
                }
                /*----------------------------------------------------*/
            }
        }

        return ['addprice_type_group'=>$addprice_type_group, 'addprice_group'=>$addprice_group];
    }

    public function createNextMember($Proposal) {
        $nextMember = $this->getNextMember();
        if($nextMember){

            $memberClassName = 'Services\\pattern\\cartRC\\' . $nextMember;
            return $memberClassName::createFactory($Proposal);

        }        
    }
}