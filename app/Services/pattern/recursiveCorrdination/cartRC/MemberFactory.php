<?php

namespace App\Services\pattern\recursiveCorrdination\cartRC;

/*
 *
 * @author: MazeR
 * @email: <EMAIL>
 * @lastUpdate: Nov 16 2017
 * @Description: simpleStaticFactory that create class by Proposal Class
 * @depend: Proposal Class
 *
*/

class MemberFactory
{
    public static function createNextMember($Proposal) {
        $nextMember = $Proposal->getNextMember();
        $memberClassName = 'App\\Services\\pattern\\recursiveCorrdination\\cartRC\\' . $nextMember;
        return $memberClassName::createFactory($Proposal);
    }
}