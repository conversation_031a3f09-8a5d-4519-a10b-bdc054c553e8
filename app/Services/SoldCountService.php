<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class SoldCountService
{
    /**
     * 計算商品的實際已售出數量
     * 只要下單即計算+1，不管之後有無取消或退貨
     * 
     * @param int $productId 商品ID
     * @return int 實際已售出數量
     */
    public static function calculateActualSoldCount($productId)
    {
        $soldCount = DB::connection('main_db')
            ->table('orderform_product')
            ->where('info_id', $productId)
            ->sum('num');
            
        return (int) $soldCount;
    }

    /**
     * 更新商品的實際已售出數量
     * 
     * @param int $productId 商品ID
     * @return bool 更新是否成功
     */
    public static function updateActualSoldCount($productId)
    {
        $actualCount = self::calculateActualSoldCount($productId);
        
        $updated = DB::table('productinfo')
            ->where('id', $productId)
            ->update(['sold_count_actual' => $actualCount]);
            
        // 更新顯示數量
        self::updateDisplaySoldCount($productId);
        
        return $updated > 0;
    }

    /**
     * 更新商品的顯示已售出數量
     * 顯示數量 = max(起始數值, 實際數量)
     * 
     * @param int $productId 商品ID
     * @return bool 更新是否成功
     */
    public static function updateDisplaySoldCount($productId)
    {
        $product = DB::table('productinfo')
            ->select('sold_count_base', 'sold_count_actual')
            ->where('id', $productId)
            ->first();
            
        if (!$product) {
            return false;
        }
        
        $displayCount = max($product->sold_count_base, $product->sold_count_actual);
        
        return DB::table('productinfo')
            ->where('id', $productId)
            ->update(['sold_count_display' => $displayCount]) > 0;
    }

    /**
     * 批量更新所有商品的已售出數量
     * 
     * @return int 更新的商品數量
     */
    public static function updateAllSoldCounts()
    {
        $products = DB::table('productinfo')
            ->select('id')
            ->get();
            
        $updatedCount = 0;
        foreach ($products as $product) {
            if (self::updateActualSoldCount($product->id)) {
                $updatedCount++;
            }
        }
        
        return $updatedCount;
    }

    /**
     * 格式化已售出數量顯示
     * 數字10000以內，不加千分位逗號
     * 數字超過10000時，顯示為萬為單位，例如1.1萬 or 99.8萬
     * 
     * @param int $count 已售出數量
     * @return string 格式化後的字串
     */
    public static function formatSoldCount($count)
    {
        if ($count <= 10000) {
            return (string) $count;
        }
        
        $wan = $count / 10000;
        
        // 如果是整數萬，直接顯示
        if ($wan == floor($wan)) {
            return floor($wan) . '萬';
        }
        
        // 保留一位小數
        return number_format($wan, 1) . '萬';
    }

    /**
     * 設定商品的起始已售出數值
     * 
     * @param int $productId 商品ID
     * @param int $baseCount 起始數值
     * @return bool 設定是否成功
     */
    public static function setBaseSoldCount($productId, $baseCount)
    {
        $updated = DB::table('productinfo')
            ->where('id', $productId)
            ->update(['sold_count_base' => (int) $baseCount]);
            
        // 更新顯示數量
        self::updateDisplaySoldCount($productId);
        
        return $updated > 0;
    }

    /**
     * 獲取商品的已售出數量資訊
     * 
     * @param int $productId 商品ID
     * @return array|null 包含所有已售出數量資訊的陣列
     */
    public static function getSoldCountInfo($productId)
    {
        $product = DB::table('productinfo')
            ->select('sold_count_base', 'sold_count_actual', 'sold_count_display')
            ->where('id', $productId)
            ->first();
            
        if (!$product) {
            return null;
        }
        
        return [
            'base_count' => $product->sold_count_base,
            'actual_count' => $product->sold_count_actual,
            'display_count' => $product->sold_count_display,
            'formatted_count' => self::formatSoldCount($product->sold_count_display)
        ];
    }

    /**
     * 當有新訂單時，更新相關商品的已售出數量
     * 
     * @param int $orderformId 訂單ID
     * @return int 更新的商品數量
     */
    public static function updateSoldCountsForOrder($orderformId)
    {
        $orderProducts = DB::connection('main_db')
            ->table('orderform_product')
            ->select('info_id')
            ->where('orderform_id', $orderformId)
            ->whereNotNull('info_id')
            ->distinct()
            ->get();
            
        $updatedCount = 0;
        foreach ($orderProducts as $product) {
            if (self::updateActualSoldCount($product->info_id)) {
                $updatedCount++;
            }
        }
        
        return $updatedCount;
    }
}
