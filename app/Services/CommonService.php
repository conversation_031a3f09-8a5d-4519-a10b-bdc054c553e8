<?php

namespace App\Services;

use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\DB;

class CommonService
{
    /**
     * --- 歷史遺毒 ---
     * array視覺上不是那麼友善..object不香嗎？..非要轉array來浪費記憶體很好玩嗎-.-
     */
    static public function objectToArray(&$object)
    {
        return @json_decode(json_encode($object, JSON_UNESCAPED_UNICODE), true);
    }

    // static public function param_filter($variable){
    //     if(is_array($variable)){
    //         foreach ($variable as $key => $value) {
    //             $variable[$key] = self::param_filter($value);
    //         }
    //     }else{
    //         try {
    //             $not_json = json_decode($variable) === NULL;
    //         } catch (\Exception $e) {
    //             $not_json = true;
    //         }
    //         $not_base64 = substr($variable, 0, 5) !='data:';
    //         if($not_json && $not_base64){
    //             if($variable == strip_tags($variable)){dd(request());
    //                 $module = request()->dispatch()['module'][0];
    //                 if($module!='admin'){
    //                     $variable = preg_replace('/[`;\'"]/i', '', $variable);
    //                 }
    //             }
    //         }
    //     }
    //     return $variable;
    // }

    static public function get_db_connect_by_lang_id($langId = null)
    {
        $langId = $langId ?? 1;
        $lang = DB::connection('main_db')->table('lang')->where('lang_id', $langId)->first();

        $sub_deparment = $lang ? $lang->sub_deparment : 'A';
        return $sub_deparment . '_sub';
    }

    // 從CamelCase轉換成snake_case
    static public function from_camel_case($input)
    {
        preg_match_all('!([A-Z][A-Z0-9]*(?=$|[A-Z][a-z0-9])|[A-Za-z][a-z0-9]+)!', $input, $matches);
        $ret = $matches[0];
        foreach ($ret as &$match) {
            $match = $match == strtoupper($match) ? strtolower($match) : lcfirst($match);
        }
        return implode('_', $ret);
    }
    // 应用公共文件
    static public function RECEIPTS_STATE($text)
    {
        $RECEIPTS_STATE = [
            Lang::get('未收款'),
            Lang::get('已收款'),
        ];
        $text = $RECEIPTS_STATE[$text];
        return $text;
    }

    static public function TRANSPORT_STATE($text)
    {
        $TRANSPORT_STATE = [
            Lang::get('未出貨'),
            Lang::get('已出貨'),
        ];
        $text = $TRANSPORT_STATE[$text];
        return $text;
    }

    static public function fat_index_text($text, $length)
    {
        if (mb_strlen($text, 'utf8') > $length) {
            return mb_substr($text, 0, $length, 'utf8') . '…';
        }
        return $text;
    }

    static public function fat_return_checked($text, $aim)
    {
        //2020/01/01
        if ($text == $aim) {
            return 'checked';
        }
    }

    static public function compare_return($text, $aim, $re, $reverse = false)
    {
        $check = $text == $aim;
        if ($reverse) {
            $check = !$check;
        }
        //2020/02/10
        if ($check) {
            return $re;
        } else {
            return '';
        }
    }

    static public function regidata_readonly($text)
    {
        //2020/09/09
        if ($text != '') {
            return 'readonly';
        } else {
            return '';
        }
    }

    /*回傳指定長度亂數*/
    static public function geraHash($qtd)
    {
        $Caracteres = 'ABCDEFGHIJKLMOPQRSTUVXWYZ0123456789';
        $QuantidadeCaracteres = strlen($Caracteres);
        $QuantidadeCaracteres--;
        $Hash = null;
        for ($x = 1; $x <= $qtd; $x++) {
            $Posicao = rand(0, $QuantidadeCaracteres);
            $Hash .= substr($Caracteres, $Posicao, 1);
        }
        return $Hash;
    }
    /*上傳base64檔案*/
    static public function uploadFile($path, $file, $fileName = "")
    {
        /*取得檔案附檔類型*/
        $getType = explode(";", $file)[0];
        $getType = explode("/", $getType)[1];

        /*設定檔案名稱*/
        if (!$fileName) {
            /*無給定*/
            $t = time();
            $gethash = self::geraHash(8);
            $fileName = $t . $gethash . '.' . $getType;
        } else {
            /*有給定*/
            $fileName = $fileName . '.' . $getType;
        }
        if ($getType == 'octet-stream') {
            return '';
        }

        /*上傳絕對目錄路徑*/
        $filePath = request()->server('DOCUMENT_ROOT') . $path . '/' . $fileName;
        // dump($filePath);exit();

        /*處理檔案、上傳主機*/
        $fileData = substr($file, strpos($file, ",") + 1);
        // dd($fileData);
        $decodedData = base64_decode($fileData);
        file_put_contents($filePath, $decodedData);

        /*回傳檔案相對路徑*/
        return $path . '/' . $fileName . '?' . rand(0, 10000000000);
    }

    /*修改輸入文字已符合sql篩選(主要用於json格式的文字比對)*/
    static public function replace_str_to_do_sql_search($str)
    {
        $str = str_replace("/", '\\\\\\\\/', $str);
        $str = str_replace("(", '\\\\(', $str);
        $str = str_replace(")", '\\\\)', $str);
        return $str;
    }

    static public function delimitate_string_back_index($str, $delimiter, $index = 0)
    {
        $arr = explode($delimiter, $str);
        return isset($arr[$index]) ? $arr[$index] : "";
    }

    /*合併訂單後台 函式 開始*/
    static public function returnRed($text, $aim)
    {
        //2020/01/01
        if ($text == $aim) {
            return 'red';
        }
    }
    static public function change_url($text)
    {
        $id = explode("?id=", $text);
        if (count($id) > 1) { /*前台商品網址*/
            $url = explode("/", $text);
            return $url[0] . '/admin/examination/examinee_list/id/' . $id[1];
        }

        $id = explode("/id/", $text);
        if (count($id) > 1) { /*後台商品網址*/
            $url = explode("/", $text);
            return $url[0] . '/admin/examination/examinee_list/id/' . $id[1];
        }

        return "";
    }

    static public function randomkeys($length)
    {
        $pattern = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $key = '';

        for ($i = 0; $i < $length; $i++) {
            $key .= $pattern[rand(0, 61)];
        }
        return $key;
    }
    /*合併訂單後台 函式 結束*/

    /*判斷不定量使用陣列內($elements)元素，是否能加出等於目標($target)的數*/
    static public function elements_able_to_sum_to_target($elements, $target)
    {
        rsort($elements);
        $success_num = [];
        foreach ($elements as $index => $element) {
            if ($target == $element) {
                return [$element];
            } else if ($target > $element) {
                $sub = array_slice($elements, $index + 1);
                $sub_result = self::elements_able_to_sum_to_target($sub, $target - $element);
                if ($sub_result) {
                    array_push($success_num, $element);
                    return array_merge($success_num, $sub_result);
                }
            }
        }
        return [];
    }

    /**
     * 依據傳入的一頁數量、當前頁數，回傳model的limit方法所需參數
     * @param string $count_of_page：一頁數量
     * @param string $current_page：當前頁數
     * @return array
     */
    public static function get_model_limit_index_and_length(string $count_of_page, string $current_page)
    {
        $index_and_length = [
            0, /*代表sql語法開始顯示的index位置*/
            0  /*代表限制顯示的數量，若為0，則應視為不用limit篩選*/
        ];

        $count_of_page = (int)$count_of_page;
        if ($count_of_page > 0) { /*一頁數量大於0*/
            $current_page = (int)$current_page;
            $current_page -= 1;
            if ($current_page < 0) {
                $current_page = 0;
            }
            $start_index = $current_page * $count_of_page;
            $index_and_length[0] = $start_index;
            $index_and_length[1] = $count_of_page;
        }
        return $index_and_length;
    }
}
