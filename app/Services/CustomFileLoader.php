<?php
namespace App\Services;

use Illuminate\Translation\FileLoader;
use Illuminate\Filesystem\Filesystem;

class CustomFileLoader extends FileLoader
{
    public function load($locale, $group, $namespace = null)
    {
        // 嘗試加載默認的翻譯文件
        $translations = parent::load($locale, $group, $namespace);
        // 如果沒有指定分組（即未使用 'menu.購物車' 格式），則在所有翻譯文件中搜尋
        if ($group === '*') {
            $files = $this->files->allFiles($this->path . '/' . $locale);
            foreach ($files as $file) {
                $groupTranslations = parent::load($locale, $file->getBasename('.php'));
                $translations = array_merge($translations, $groupTranslations);
            }
        }
        /*通用變數取代語言版文字*/
        foreach ($translations as $key => $value) {
            $translations[$key] = str_replace("{company_name}", config('extra.shop.company_name'), $translations[$key]);
            $translations[$key] = str_replace("{service_tel}", config('extra.shop.service_tel'), $translations[$key]);
            $translations[$key] = str_replace("{service_email}", config('extra.shop.service_email'), $translations[$key]);
        }
        
        return $translations;
    }
}
