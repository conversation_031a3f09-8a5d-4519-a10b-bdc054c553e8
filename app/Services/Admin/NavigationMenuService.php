<?php

namespace App\Services\Admin;

use App\Repositories\Home\NavigationMenuRepository;
use App\Models\NavigationMenuModel;
use Illuminate\Support\Collection;

use Illuminate\Validation\ValidationException;

class NavigationMenuService
{
    protected $navigationMenuRepository;

    public function __construct(NavigationMenuRepository $navigationMenuRepository)
    {
        $this->navigationMenuRepository = $navigationMenuRepository;
    }

    /**
     * 取得所有選單列表
     */
    public function getAllMenus(): Collection
    {
        return $this->navigationMenuRepository->getAllOrdered();
    }

    /**
     * 取得啟用的選單列表
     */
    public function getActiveMenus(): Collection
    {
        return $this->navigationMenuRepository->getActiveOrdered();
    }

    /**
     * 根據 ID 取得選單
     */
    public function getMenuById(int $id): NavigationMenuModel
    {
        return $this->navigationMenuRepository->findByIdOrFail($id);
    }

    /**
     * 建立新選單
     */
    public function createMenu(array $data): NavigationMenuModel
    {
        // 驗證業務邏輯
        $this->validateMenuData($data);

        return $this->navigationMenuRepository->create($data);
    }

    /**
     * 更新選單
     */
    public function updateMenu(int $id, array $data): NavigationMenuModel
    {
        $menu = $this->navigationMenuRepository->findByIdOrFail($id);

        // 驗證業務邏輯（排除當前選單的 ID）
        $this->validateMenuData($data, $id);

        $this->navigationMenuRepository->update($menu, $data);

        return $menu->fresh();
    }

    /**
     * 刪除選單
     */
    public function deleteMenu(int $id): bool
    {
        $menu = $this->navigationMenuRepository->findByIdOrFail($id);

        return $this->navigationMenuRepository->delete($menu);
    }

    /**
     * 更新選單排序
     */
    public function updateMenuOrders(array $orders): bool
    {
        return $this->navigationMenuRepository->updateMultipleSortOrders($orders);
    }

    /**
     * 驗證選單資料
     */
    private function validateMenuData(array $data, ?int $excludeId = null): void
    {
        // 檢查 menu_key 是否重複
        if (
            isset($data['menu_key']) &&
            $this->navigationMenuRepository->menuKeyExists($data['menu_key'], $excludeId)
        ) {
            throw ValidationException::withMessages([
                'menu_key' => ['選單識別碼已存在']
            ]);
        }

        // 檢查排序值是否有效
        if (isset($data['sort_order']) && $data['sort_order'] < 0) {
            throw ValidationException::withMessages([
                'sort_order' => ['排序值不能小於 0']
            ]);
        }

        // 檢查狀態值是否有效
        if (isset($data['status']) && !in_array($data['status'], [0, 1])) {
            throw ValidationException::withMessages([
                'status' => ['狀態值無效']
            ]);
        }

        // 檢查 target 值是否有效
        if (isset($data['target']) && !in_array($data['target'], ['_self', '_blank'])) {
            throw ValidationException::withMessages([
                'target' => ['開啟方式無效']
            ]);
        }
    }

    /**
     * 取得選單的統計資料
     */
    public function getMenuStatistics(): array
    {
        $allMenus = $this->navigationMenuRepository->getAllOrdered();

        return [
            'total' => $allMenus->count(),
            'active' => $allMenus->where('status', 1)->count(),
            'inactive' => $allMenus->where('status', 0)->count(),
        ];
    }

    /**
     * 格式化選單資料供前台使用
     */
    public function formatMenusForFrontend(): array
    {
        $menus = $this->getActiveMenus();

        return $menus->map(function ($menu) {
            return [
                'menu_key' => $menu->menu_key,
                'menu_name' => $menu->menu_name,
                'menu_url' => $menu->menu_url,
                'target' => $menu->target,
                'sort_order' => $menu->sort_order
            ];
        })->toArray();
    }
}
