@extends('admin.Public.aside')
@section('title')FB發佈圖文 - 後台設定@endsection
@section('content')
    <div id="content">
        <form action="{{url('seo/update_social')}}" method="post" enctype="multipart/form-data">
            @csrf
            <input type="hidden" name="id" value="1">
            <div class="block">
                <p class="title">FB發佈圖文</p>
                <p class="main-title text-center">FB關聯設定</p>
                <div class="admin-content width-70">
                    <div class="form-group"><label>貼在FB顯示公司名稱：</label><input type="text" id="fb_name" name="fb_name" value="{{$data['seo']['fb_name']}}"></div>
                    <div class="form-group"><label>標題：</label><input type="text" id="fb_title" name="fb_title" value="{{$data['seo']['fb_title']}}"></div>
                    <div class="form-group">
                        <label >描述：</label>
                        <textarea style="width: 100%;  height:100px;" type="text" id="fb_descr" name="fb_descr">{{$data['seo']['fb_descr']}}</textarea>
                        <p class="text-danger remark">*三十字以內最佳</p>
                    </div>
                    <div class="form-group">
                        <label>上傳圖片：</label>
                        <div class="position-relative">
                            <input  class="upl" style="position: absolute; border:2px solid #000; left:0; z-index:3;" type="file" id="fb_img" name="fb_img">
                            <div class="imgbox d-flex align-items-center justify-content-center">
                                <p style="position:absolute; left:45%; top:45%; z-index:1">點擊上傳圖片</p>
                                <img class="preview" src="{{$data['seo']['fb_img']}}">
                            </div>
                        </div>
                        <p class="text-danger remark">*圖片大小1200*630px</p>
                    </div>
                </div>
            </div>
            <div class="block">
                <p class="main-title text-center">Twitter關聯設定</p>
                <div class="admin-content width-70">
                    <div class="form-group"><label>貼在Twitter顯示公司名稱：</label><input type="text" id="twitter_name" name="twitter_name" value="{{$data['seo']['twitter_name']}}"></div>
                    <div class="form-group"><label>標題：</label><input type="text" id="twitter_title" name="twitter_title" value="{{$data['seo']['twitter_title']}}"></div>
                    <div class="form-group">
                        <label>描述：</label>
                        <textarea style="width: 100%;  height:100px;" type="text" id="twitter_descr" name="twitter_descr">{{$data['seo']['twitter_descr']}}</textarea>
                        <p class="text-danger remark">*三十字以內最佳</p>          
                    </div>
                </div>
            </div>
            <div class="block" style="text-align:center">
                <button class="btn sendbtn">儲存</button>
            </div>
        </form>
    </div>
@endsection
@section('ownJS')
    <script src="/public/static/admin/js/action.js"></script>
    <script>
        function format_float(num, pos)
        {
            var size = Math.pow(10, pos);
            return Math.round(num * size) / size;
        }

        function preview(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('.preview').attr('src', e.target.result);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        $("body").on("change", ".upl", function (){
            preview(this);
        })

        $('#down').click(function(){
            $('.robots').animate({height:'710px'},1000)
            $('#down').fadeOut()
            $('#up').fadeIn()
        })

        $('#up').click(function(){
            $('.robots').animate({height:'150px'},1000)
            $('#up').fadeOut()
            $('#down').fadeIn()
        })

        function openList() {
            $('#backstageListli').click();
        }
    </script>
@endsection