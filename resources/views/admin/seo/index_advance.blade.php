@extends('admin.Public.aside')
@section('title')進階SEO設定 - 後台設定@endsection
@section('content')
    <div id="content">
        <form action="{{url('seo/update_advance')}}" method="post" enctype="multipart/form-data">
            @csrf
            <input type="hidden" name="id" value="1">
            <h3 class="main-title text-center">進階SEO設定</h3>    
            <div class="mb-4 admin-content width-70 seo-content">
                <div class="form-group">
                    <label class="name">Google驗證：</label>
                    <div class="seo-cont">
                        <input type="text" class="w-100 p-1" id="verification" name="verification" value="{{$data['seo']['verification']}}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="name">Google分析追蹤碼：</label>
                    <div class="seo-cont">
                        <textarea class="w-100" style="position:relative;  height:200px;" type="text" id="trackgoogle" name="trackgoogle">{{$data['seo']['trackgoogle']}}</textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="name">Google再行銷碼：</label>
                    <div class="seo-cont">
                        <textarea class="w-100" style="position:relative; height:200px;" type="text" id="marketgoogle" name="marketgoogle">{{$data['seo']['marketgoogle']}}</textarea>
                    </div>
                </div>
                <div class="form-group">
                    <label class="name">Yahoo再行銷碼：</label>
                    <div class="seo-cont">
                        <textarea class="w-100" style="position:relative;  height:200px;" type="text" id="marketyahoo" name="marketyahoo" value="">{{$data['seo']['marketyahoo']}}</textarea>    
                    </div>
                </div>
                <div class="form-group">
                    <label class="name">隱藏描述詞：</label>
                    <div class="seo-cont">
                        <textarea class="w-100" style="position:relative;  height:200px;" type="text" id="display" name="display">{{$data['seo']['display']}}</textarea>
                    </div>    
                </div>
                <div class="form-group">
                    <label class="name">Robots：</label>
                    <div class="seo-cont">
                        <textarea class="w-100" style="position:relative;  height:200px;" type="text" id="robot" name="robot">{{$data['seo']['robot']}}</textarea>
                        <p style="position:absolute; color:red; top:0; right:55px; font-size:24px; font-weight:bold">
                            Robots 指令參考
                        </p>
                        <p id="down" style="position:absolute; color:red; bottom:0; right:0px; font-size:24px; font-weight:bold; cursor:pointer">
                            more <span style="font-size:18px;" class="bi bi-chevron-down"></span>
                        </p>       
                        <p class="robots" style="position:absolute; color:red; top:50px; right:0px; height:150px; overflow-y:auto">
                            允許所有的機器人：<br>
                            User-agent: *<br>
                            Disallow:<br><br>
                            另一寫法<br>
                            User-agent: *<br>
                            Allow:/<br><br>
                            僅允許特定的機器人：<br>
                                name_spider用真實名字代替）<br>
                                User-agent: name_spider<br>
                                Allow:<br><br>
                            攔截所有的機器人：<br>
                                User-agent: *<br>
                                Disallow: /<br><br>
                            禁止所有機器人造訪特定目錄：<br>
                                User-agent: *<br>
                                Disallow: /cgi-bin/<br>
                                Disallow: /images/<br>
                                Disallow: /tmp/<br>
                                Disallow: /private/<br><br>
                            僅禁止壞爬蟲造訪特定目錄<br>
                                BadBot用真實的名字代替）：<br>
                                User-agent: BadBot<br>
                                Disallow: /private/<br><br>
                            禁止所有機器人造訪特定檔案類型[2]：<br>
                                User-agent: *<br>
                                Disallow: /*.php$<br>
                                Disallow: /*.js$<br>
                                Disallow: /*.inc$<br>    
                                Disallow: /*.css$
                        </p>
                        <p id="up" style="position:absolute; color:red; bottom:0; right:0px; font-size:24px; font-weight:bold; cursor:pointer; display:none;">
                            more <span style="font-size:18px; top:4px" class="bi bi-chevron-up"></span>
                        </p> 
                    </div>
                </div>
                <div class="form-group">
                    <label class="name">Map：</label>
                    <div class="seo-cont">
                        <input class="w-100" type="file" id="map" name="map">
                    </div>
                </div>
                @if($data['seo']['map'] != '')
                    <div class="form-group">
                        <label class="name">現在的Map：</label>
                        <div class="seo-cont">
                            <a  download="{{$data['seo']['map']}}" href="/{{$data['seo']['map']}}">{{$data['seo']['map']}}</a>
                        </div>
                    </div>
                @endif
            </div>
            <div class="block" style="text-align:center">
                <button class="btn sendbtn">儲存</button>
            </div>
        </form>
    </div>
@endsection
@section('ownJS')
    <script src="/public/static/admin/js/action.js"></script>
    <script>
        function format_float(num, pos)
        {
            var size = Math.pow(10, pos);
            return Math.round(num * size) / size;
        }

        function preview(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('.preview').attr('src', e.target.result);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        $("body").on("change", ".upl", function (){
            preview(this);
        })

        $('#down').click(function(){
            $('.robots').animate({height:'710px'},1000)
            $('#down').fadeOut()
            $('#up').fadeIn()
        })

        $('#up').click(function(){
            $('.robots').animate({height:'150px'},1000)
            $('#up').fadeOut()
            $('#down').fadeIn()
        })

        function openList() {
            $('#backstageListli').click();
        }
    </script>
@endsection