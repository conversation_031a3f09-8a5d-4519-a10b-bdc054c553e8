@extends('admin.Public.aside')

@section('title')
SEO設定 - 後台設定
@endsection

@section('content')
    <div id="content">
        <form action="{{url('seo/update')}}" method="post" enctype="multipart/form-data">
            @csrf
            <input type="hidden" name="id" value="1">
            <div class="admin-content width-50 seo-content">
                <h4 class="main-title">SEO設定</h4>
                <div class="form-group">
                    <label class="name">網頁標題：</label><input type="text" id="title" class="form-control" name="title" value="{{$data['seo']['title']}}">
                    <span class="text-danger remark">*三十字以內最佳</span>
                </div>
                <div class="form-group">
                    <label class="name">關鍵字：</label><input type="text" id="seokey" class="form-control" name="seokey" value="{{$data['seo']['seokey']}}">
                    <span class="text-danger remark">*請用,號區格，十組內最佳</span>
                </div>
                <div class="form-group">
                    <label class="name">網頁描述：</label>
                    <textarea  rows="3" class="border w-100" type="text" id="descr" name="descr">{{$data['seo']['descr']}}</textarea>
                    <span class="text-danger remark">*兩百字以內最佳</span>
                </div>
            </div>
            <div class="block" style="text-align:center">
                <button class="btn sendbtn">儲存</button>
            </div>
        </form>
    </div>
@endsection
@section('ownJS')
    <script src="/public/static/admin/js/action.js"></script>
    <script>
        function format_float(num, pos)
        {
            var size = Math.pow(10, pos);
            return Math.round(num * size) / size;
        }

        function preview(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('.preview').attr('src', e.target.result);
                }

                reader.readAsDataURL(input.files[0]);
            }
        }
    
        $("body").on("change", ".upl", function (){
            preview(this);
        })

        $('#down').click(function(){
            $('.robots').animate({height:'710px'},1000)
            $('#down').fadeOut()
            $('#up').fadeIn()
        })

        $('#up').click(function(){
            $('.robots').animate({height:'150px'},1000)
            $('#up').fadeOut()
            $('#down').fadeIn()
        })

        function openList() {
            $('#backstageListli').click();
        }
    </script>
@endsection