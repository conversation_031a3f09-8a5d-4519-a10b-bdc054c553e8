  @extends('admin.Public.aside')
@section('title')參數設定區 > 存放位置管理@endsection

@section('content')
  <div id="content">
    <ul id="title" class="brand-menu">
      <li onclick="javascript:location.href='index'" ><a href="###">G功能應用項目</a></li>
      <li><a href="###">存放位置管理</a></li>
    </ul>

    <!--新增與編輯-->
    <div class="searchbox mb-2">
      <div class="search-position width-70">
        <input id="name" type="text" class="form-control" placeholder="文字輸入(位置代碼)"></input>
        <input id="number" type="number" class="form-control" placeholder="輸入上限">
        <input type="checkbox" id="max" name="max" value="1"><label  for="max">無上限</label>
        <button type="button" onclick="action('add',0)" class="ml-1 btn sendbtn">新增</button>
      </div>
    </div>

    <!--表格 開始-->
    <table class="table table-rwd table-mobile" >
      <thead>
        <tr>
          <th>位置代碼</th>
          <th>上限</th>
          <th>使用量</th>
          <th>查看商品</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        @if(empty($data['dis']) == true)
          <tr><td colspan=2>沒有數據<td></tr>
        @else
          @foreach($data['dis'] as $vo)
            <tr>
              <td data-th="位置代碼">
                {{$vo['name']}}
                <input type="hidden" id="name{{$vo['id']}}" value="{{$vo['name']}}"></input>
              </td>
              <td data-th="上限">
                <input id="number{{$vo['id']}}" value="{{$vo['number']}}"></input>
    
                @if($vo['max']=='1')
                <input style="margin-left: 20px;" type="checkbox" id="max{{$vo['id']}}" name="max" value="1" checked><label for="max{{$vo['id']}}">無上限</label>
                @else
                <input style="margin-left: 20px;" type="checkbox" id="max{{$vo['id']}}" name="max" value="1"><label for="max{{$vo['id']}}">無上限</label>		
                @endif
              </td>
              <td data-th="使用量">
                {{$vo['used_num']}}
              </td>
              <td data-th="查看商品">
                <a target="_blank" href="{{url('All/index')}}?position_id={{$vo['id']}}"><button class="btn whitebtn">查看</button></a>
              </td>
              <td data-th="操作">
                <button class="btn clearbtn" onclick="action('delete',{{$vo['id']}})">刪除</button>
                <button class="btn sendbtn" onclick="action('update',{{$vo['id']}})">修改</button>
              </td>              
            </tr>
          @endforeach
        @endif
      </tbody>
    </table>

    <!--表格 結束-->
    <div class="text-center">
    </div>
  </div>
@endsection

@section('ownJS')
  <script src="{{__PUBLIC__}}/js/action.js"></script>
  <script>
    function action(type,id){
      var name = $('#name').val();
      var number = parseInt($('#number').val());
      var max = $("#max:checked").val() ? $("#max:checked").val() : 0;

      if(type== 'update'){
        name = $('#name'+id).val();
        number = parseInt($('#number'+id).val());
        max = $("#max"+id+":checked").val() ? $("#max"+id+":checked").val() : 0;
      }

      if( (type == 'add'||type== 'update') && isNaN(number) && max !=1){
        alert('請輸入上限(限數字)');
        return 0;
      }
      if( (type== 'add'||type== 'update') && number <= 0 && max != 1) {
        alert("上限請輸入大於0的值");
        return 0;
      }

      if(type=='delete'){
        if(!confirm("確定刪除?")){ return 0; }
      }

      $.ajax({
        type:'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
        },
        data:{
          id:id,
          type:type,
          number:Boolean(number) ? number : 0,
          name:name,
          max:max,
        },
        url:"{{url('Position/edit')}}",
        datatype: 'json',
        success:function(res){
          if(res.trim()=='success'){
            location.reload();
          }else{
            alert(res.trim());
          }
        },
      });
    }
  </script>
@endsection