@extends('admin.Public.aside')
@section('title'){{Lang::get('優惠券管理')}} - {{Lang::get('編輯')}}@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('H行銷項目')}}</li>
            <li><a href="{{url('Coupon/index')}}">{{Lang::get('優惠券管理')}}</a></li>
            <li>{{Lang::get('編輯')}}</li>
        </ul>
        <a class="back btn sendbtn" href="{{url('Coupon/index')}}">
            <span class="bi bi-arrow-left"></span>
        </a>
        <div class="admin-content act width-70">
            <div class="col-lg-6">
                <div class="image-box">
                    @if($data['singleData']['pic'])
                    <img class="preview" name="image" class="upl" src="{{__UPLOAD__}}{{$data['singleData']['pic']}}"/>
                    @endif
                </div>
            </div>
            <div class="col-lg-6">
                <div class="item border-bottom">{{$data['singleData']['type']}}</div>
                <div class="item border-bottom">{{Lang::get('名稱')}}：{{$data['singleData']['title']}}</div>
                <div class="item border-bottom">{{Lang::get('編號')}}：{{$data['singleData']['number']}}</div>
                <div class="item border-bottom">{{Lang::get('領取上限')}}：{{$data['singleData']['limit_num']}}張</div>
                <div class="item">
                    <div class="name">{{Lang::get('簡介')}}</div>
                    <div class="border content" style="min-height: 200px;">
                        {!! str_replace("\n", "<br>", $data['singleData']['content']) !!}
                    </div>
                </div>
            </div>
        </div>
        <h3 class="main-title">{{Lang::get('優惠券設定')}}</h3>
        <div class="edit_form">
            <table class="table table-rwd" style="min-width: 992px;">
                <thead>
                    <tr>
                        <th>{{Lang::get('時間')}}</th>
                        <th>{{Lang::get('折扣方式')}}</th>
                        <th>{{Lang::get('使用限制')}}</th>
                        <th>{{Lang::get('可否轉移')}}</th>
                        <th>{{Lang::get('生產張數')}}</td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <p>
                                <span>{{Lang::get('開始日期')}}：{{date("Y-m-d",$data['singleData']['start'])}}</span>~
                                <span>{{Lang::get('結束日期')}}：{{date("Y-m-d",$data['singleData']['end'])}}</span>
                            </p>
                        </td>
                        <td><p>{{Lang::get('扣')}}{{config('extra.shop.dollar_symbol')}}{{$data['singleData']['discount']}}</p></td>
                        <td><p>{{Lang::get('滿額')}}{{config('extra.shop.dollar_symbol')}}{{$data['singleData']['coupon_condition']}}</p></td>
                        <td><p>{{$data['singleData']['transfer']}}</p></td>
                        <td>{{$data['singleData']['num']}}</td>
                    </tr>
                    <tr>
                        <td colspan="5">
                            {{Lang::get('適用商品設定')}} ：{{$data['singleData']['area']}} &nbsp;&nbsp;&nbsp;&nbsp;
                            {{$data['singleData']['area_id']}}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>    
        <div class="d-flex flex-wrap align-items-center mt-3 mb-3" >
            <div>
                <span>{{Lang::get('生產張數')}}：{{$data['singleData']['num']}}</span>
                <span>{{Lang::get('已領取張數')}}：{{$data['singleData']['sellCount']}}</span>
                <span>{{Lang::get('已使用張數')}}：{{$data['singleData']['useCount']}}</span>
            </div>
            <button class="btn sendbtn ml-2" onclick="location.href = '{{url('Coupon/dumpUsedExcel') . '?id=' . $data['singleData']['id']}}'">{{Lang::get('匯出EXCEL')}}</button>
            
        </div>
        <div class="edit_form">    
            <table class="table table-rwd" style="min-width: 992px;">
                <thead>
                    <tr>
                        <th>{{Lang::get('優惠券代碼')}}</th>
                        <th>{{Lang::get('領取日期')}}</th>
                        <th>{{Lang::get('領取會員')}}</th>
                        <th>{{Lang::get('使用日期')}}</th>
                        <th>{{Lang::get('結束日期')}}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['singleData']['pool'] as $vo)                            
                        <tr>
                            <td>{{$vo['number']}}</td>
                            <td>{{$vo['login_time']}}</td>
                            <td>{{$vo['owner']['number']}}</td>
                            @if($vo['use_time'])
                                <td>{{ date('Y-m-d', $vo['use_time'])}}</td>
                            @else
                                <td>{{Lang::get('未使用')}}</td>
                            @endif
                            <td>
                                {{ date('Y-m-d', $data['singleData']['end'])}}
                            </td>
                        </tr>
                    @endforeach
                    
                </tbody>
               @if($data['singleData']['type'] =='實體卷')
                    <button id="submitButton" 
                            class="btn sendbtn ml-2" 
                            onclick="location.href = '{{url('Coupon/dumpPoolExcel') . '?id=' . $data['singleData']['id']}}'"
                        >
                        {{Lang::get('編號列表')}}
                    </button>
                @endif
                @if($data['singleData']['type'] !='實體卷')
                    @include('admin.coupon.virtual')
                @endif
            </table>
        </div>
@endsection
@section('ownJS')
@endsection