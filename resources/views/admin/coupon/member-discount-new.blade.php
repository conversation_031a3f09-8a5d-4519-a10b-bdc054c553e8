@extends('admin.Public.aside')
@section('title'){{Lang::get('優惠券管理')}} - {{Lang::get('新增')}}@endsection
@section('css')@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('H行銷項目')}}</li>
            <li><a href="{{url('Coupon/index')}}">{{Lang::get('優惠券管理')}}</a></li>
            <li>{{Lang::get('新增')}}</li>
        </ul>
        <a class="back btn sendbtn" href="{{url('Coupon/index')}}">
            <span class="bi bi-arrow-left"></span>
        </a>
        <form name="productinfoForm" action="{{url('Coupon/doCreate')}}" method="post" enctype="multipart/form-data">
            @csrf
            <div class="admin-content act width-70">
                <div class="col-lg-6">
                    <div class="image-box">
                        <input type='file' ref="img" class="upl" name="image" accept="image/*">
                        <img class="preview" name="image"/>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="item">
                        <input type="radio" name="type" value="0" checked>{{Lang::get('虛擬卷')}}
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <input type="radio" name="type" value="1">{{Lang::get('實體卷')}}
                    </div>
                    <div class="item">
                        <div class="name"> {{Lang::get('名稱')}}：</div>
                        <input type="text" name="title" class="content" >
                    </div>
                    <div class="item">
                        <div class="name"> {{Lang::get('編號')}}：</div>
                        <div class="content" >{{Lang::get('自動生成，無需輸入')}}</div>
                    </div>
                    <div class="item">
                        <div class="name">{{Lang::get('領取上限')}}</div>
                        <div class="content">
                            <input type="number" name="limit_num" value="5" class="border-0 w-25">
                        </div>
                    </div>
                    <input type="hidden" name="price" value="0">
                    <div class="item">
                        <div class="name">{{Lang::get('簡介')}}</div>
                        <textarea name="content" id="editor" rows="3" class="w-100 border"></textarea>
                    </div>
                  
                </div>
            </div> 
            <div class="edit_form" >
                <h3 class="main-title">{{Lang::get('優惠券設定')}}</h3>
                <table class="table table-rwd" style="min-width: 992px;">
                    <thead>
                        <tr>
                            <th>{{Lang::get('時間')}}</th>
                            <th>{{Lang::get('折扣方式')}}</th>
                            <th>{{Lang::get('使用限制')}}</th>
                            <th>{{Lang::get('可否轉移')}}</th>
                            <th>{{Lang::get('生產張數')}}</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <p>{{Lang::get('開始日期')}}<input type="date" name="start" style="width:70%"></p>
                                <p>{{Lang::get('結束日期')}}<input type="date" name="end" style="width:70%"></p>
                            </td>
                            <td>
                                <p>
                                    {{Lang::get('扣')}}{{config('extra.shop.dollar_symbol')}}
                                    <input type="number" name="discount" style="width:50%">
                                </p>
                            </td>
                            <td>
                                <p>
                                    {{Lang::get('滿額')}}{{config('extra.shop.dollar_symbol')}}
                                    <input type="number" name="coupon_condition" style="width:50%">
                                </p>
                            </td>
                            <td>
                                <p><input type="radio" name="transfer" value="1"> {{Lang::get('可以')}}</p>
                                <p><input type="radio" name="transfer" value="0" checked> {{Lang::get('不可以')}}</p>
                            </td>
                            <td>
                                <p><input type="number" name="num" style="width:50%"></p>
                            </td>
                        </tr> 
                        <tr>
                            <td colspan="5">
                                {{Lang::get('適用商品設定')}} 
                                <input type="radio" class="mr-1" name="area" value="0" checked>{{Lang::get('全體')}} 
                                <input type="radio" class="mr-1 ml-1" name="area" value="1">{{Lang::get('單一')}}
                                <select id="areaSelect" name="area_id">
                                    @foreach($data['productinfo'] as $vo)
                                        <option value="{{$vo['id']}}">{{$vo['title']}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center">
                <button id="submitButton" class="btn sendbtn " 
                onclick="$('#block_block').show();">{{Lang::get('新增')}}</button>     
            </div>
        </form>
    </div>
@endsection
@section('ownJS')
<script src="{{__PUBLIC__}}/js/action.js"></script>
<script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
<script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
<script>
    ChangeImage();

    $('#areaSelect').hide();
    $('input[type=radio][name=area]').change(function () {
        if (this.value == 0) {
            $('#areaSelect').hide();
        }
        else if (this.value == 1) {
            $('#areaSelect').show();
        }
    });

    $('.realType').show();
    $('input[type=radio][name=type]').change(function() {
        if (this.value == 0) {
            $('.realType').show();
        }
        else if (this.value == 1) {
            $('.realType').hide();
        }
    });
</script>
@endsection