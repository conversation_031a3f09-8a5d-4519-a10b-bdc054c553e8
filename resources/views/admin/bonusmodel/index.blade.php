@extends('admin.Public.aside')
@section('title')回饋模組 - J參數設定@endsection

@section('css')
@endsection

@section('content')
<div id="content">
    <ul id="title" class="brand-menu">
        <li><a href="###">J參數設定</a></li>
        <li><a href="###">回饋模組</a></li>
    </ul>
    <div class="frame">
        <a href="###" class="btn clearbtn mr-3" @click="open_add_modal(-1)"><span class="bi bi-plus-lg add" ></span> 新增</a>
    </div>
    <div class="edit_form">
        <table class="table table-rwd" style="min-width:575px; width:575px;">
            <thead>
                <tr>
                    <th style="width: 75px;">
                        <!-- <input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=productinfoCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="cursor:pointer;"> -->
                        序號
                    </th>
                    <!-- <th style="width: 150px;">模組ID</th> -->
                    <th style="width: 300px;">{{Lang::get('名稱')}}</th>
                    <th style="width:200px;">{{Lang::get('操作')}}</th>
                </tr>
            </thead>
            <tbody>
                <tr></tr>
                <tr v-for="(vo, vo_idx) in items" :id="'items_' +vo.id">
                    <td>
                        <!-- <input type="checkbox" class="productinfoCheckbox" :alt="vo.id"> -->
                        <span v-text="vo_idx+1"></span>
                    </td>
                    <!-- <td><span v-text="vo.id"></span></td> -->
                    <td>
                        <a href="###" @click="open_add_modal(vo_idx)">
                            <span v-text="vo.name"></span>
                        </a>
                    </td>
                    <td>
                        <button class="btn btn-danger pt-1 pb-1 pl-2 pr-2" @click="del_item(vo_idx)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 新增/修改商品分類開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal main-modal fade" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">模組內容</h5>
                </div>
                <form name="boxForm" method="post" target="boxFormIframe" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-body">
                        <div class="row m-0">
                            <div class="col-md-6 col-12 mb-2">
                                <div class="item">
                                    模組名稱
                                    <input v-model="detail.name" type="text" class="form-control">
                                </div>
                            </div>
                            <div class="col-12 mb-2"><hr></div>
                        </div>
                        <div class="row m-0">
                            <div class="col-12 mb-2">
                                <b>一般回饋設定</b>
                                <span class="text-danger">(「一般分潤回饋」用，合計應為100%)</span>
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">推廣獎勵</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.normal_recommend" placeholder="ex:25" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">合夥平級獎勵</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.normal_partner" placeholder="ex:25" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">營運獎勵</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.normal_operation" placeholder="ex:12" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">講師獎勵</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.normal_lecturer" placeholder="ex:3" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">中心獎勵</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.normal_center" placeholder="ex:15" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">月分紅</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.normal_dividend_month" placeholder="ex:20" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">中心獎勵-發起者佔比</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.normal_center_divided_to_raiser" placeholder="ex:30" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                                <span class="text-danger">
                                    「中心獎勵」將按此比率拆分給其「發起者」。另依「中心等級設定」有「級差」變動。
                                </span>
                            </div>
                        </div>
                        <div class="row m-0">
                            <div class="col-12 mb-2"><hr></div>
                            <div class="col-12 mb-2">
                                <b>合夥批發回饋設定</b>
                                <span class="text-danger">(合計應為100%)</span><br>
                                <span class="text-danger">若消費者自身「會員級別」是任督以下，且其推廣者具「有效合夥人身分」，且模組「有用合夥批發回饋」，計算商品回饋時改採「合夥批發回饋」設定，否則一律採「一般回饋」設定。</span>
                            </div>
                            <div class="col-md-6 col-12">
                                <div class="item">
                                    是否使用合夥批發回饋
                                    <select v-model="detail.use_partner_mode" class="form-control">
                                        <option value="0">否</option>
                                        <option value="1">是</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 mb-2"></div>
                            <template v-if="detail.use_partner_mode==1">
                                <div class="col-lg-6 col-12 mb-2">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">推廣獎勵</div>
                                        </div>
                                        <input type="number" step="0.01" class="form-control text-right"
                                            v-model="detail.partner_recommend" placeholder="ex:85" min="0" max="100">
                                        <div class="input-group-apend">
                                            <div class="input-group-text">%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-12 mb-2">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">合夥平級獎勵</div>
                                        </div>
                                        <input type="number" step="0.01" class="form-control text-right"
                                            v-model="detail.partner_partner" placeholder="ex:0" min="0" max="100">
                                        <div class="input-group-apend">
                                            <div class="input-group-text">%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-12 mb-2">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">營運獎勵</div>
                                        </div>
                                        <input type="number" step="0.01" class="form-control text-right"
                                            v-model="detail.partner_operation" placeholder="ex:12" min="0" max="100">
                                        <div class="input-group-apend">
                                            <div class="input-group-text">%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-12 mb-2">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">講師獎勵</div>
                                        </div>
                                        <input type="number" step="0.01" class="form-control text-right"
                                            v-model="detail.partner_lecturer" placeholder="ex:3" min="0" max="100">
                                        <div class="input-group-apend">
                                            <div class="input-group-text">%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-12 mb-2">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">中心獎勵</div>
                                        </div>
                                        <input type="number" step="0.01" class="form-control text-right"
                                            v-model="detail.partner_center" placeholder="ex:0" min="0" max="100">
                                        <div class="input-group-apend">
                                            <div class="input-group-text">%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6 col-12 mb-2">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">月分紅</div>
                                        </div>
                                        <input type="number" step="0.01" class="form-control text-right"
                                            v-model="detail.partner_dividend_month" placeholder="ex:0" min="0" max="100">
                                        <div class="input-group-apend">
                                            <div class="input-group-text">%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 mb-2">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">中心獎勵-發起者佔比</div>
                                        </div>
                                        <input type="number" step="0.01" class="form-control text-right"
                                            v-model="detail.partner_center_divided_to_raiser" placeholder="ex:0" min="0" max="100">
                                        <div class="input-group-apend">
                                            <div class="input-group-text">%</div>
                                        </div>
                                    </div>
                                    <span class="text-danger">
                                        「中心獎勵」將按此比率拆分給其「發起者」。另依「中心等級設定」有「級差」變動。
                                    </span>
                                </div>
                            </template>
                        </div>
                        <div class="row m-0">
                            <div class="col-12 mb-2"><hr></div>
                            <div class="col-12 mb-2">
                                <b>廣告推廣獎勵設定</b>
                                <span class="text-danger">(「總部消費回饋」用，剩餘%數將回饋給系統帳號)</span>
                            </div>
                            <div class="col-lg-6 col-12 mb-2">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">廣告推廣獎勵</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.ad_bonus" placeholder="ex:25" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn sendbtn" @click="form_submit">儲存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- 新增/修改商品分類結束 -->
</div>
@endsection
@section('ownJS')
    <script>
        var empty_detail = {
            id: 0,
            name: '',
            
            normal_recommend:'',
            normal_partner:'',
            normal_operation:'',
            normal_lecturer:'',
            normal_center:'',
            normal_dividend_month:'',
            normal_center_divided_to_raiser:'',

            use_partner_mode: 0,
            partner_recommend:'',
            partner_partner:'',
            partner_operation:'',
            partner_lecturer:'',
            partner_center:'',
            partner_dividend_month:'',
            partner_center_divided_to_raiser:'',

            ad_bonus:'',
        };
        var content_data = {
            items:[],
            detail: JSON.parse(JSON.stringify(empty_detail)),
        };
        var contentVM = new Vue({
            el:'#content',
            data: content_data,
            async created(){
                $('#block_block').show()
                var resp = await this.load_data();
                this.items = resp.db_data;
                $('#block_block').hide();
            },
            methods:{
                load_data(){
                    return $.ajax({
                        type: "GET",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: "json",
                        data:{
                        },
                        url: "{{url('Bonusmodel/get_data')}}",
                    });
                },
                open_add_modal(idx=-1){
                    if(idx<-1 || idx>=this.items.length){ return; }
                    if(idx==-1){ /*新增*/
                        this.detail = JSON.parse(JSON.stringify(empty_detail));
                    }else{ /*修改*/
                        this.detail = JSON.parse(JSON.stringify(this.items[idx]));
                    }
                    $('#functionModal_btn').click();
                },
                async form_submit(){
                    // console.log(this.detail);
                    $('#block_block').show()
                    try {
                        var resp = await $.ajax({
                            type: "POST",
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            dataType: "json",
                            data:{
                                detail: JSON.parse(JSON.stringify(this.detail)),
                            },
                            url: "{{url('Bonusmodel/save_data')}}",
                        });
                        if(resp.code==1){
                            Vue.toasted.show(resp.msg.msg, vt_success_obj);
                            if(this.detail.id==0){/*新增成功*/
                                var resp = await this.load_data();
                                this.items = resp.db_data;

                                $('#functionModal').modal('hide');
                                this.detail = JSON.parse(JSON.stringify(empty_detail));
                            }else{ /*編輯成功*/
                                // for (let idx = 0; idx < this.items.length; idx++) {
                                //     const element = this.items[idx];
                                //     if(element.id==this.detail.id){
                                //         this.items[idx] = JSON.parse(JSON.stringify(this.detail));
                                //         break;
                                //     }
                                // }
                                var resp = await this.load_data();
                                this.items = resp.db_data;
                            }
                            this.$forceUpdate();
                        }else{
                            Vue.toasted.show(resp.msg, vt_error_obj);
                        }
                    } catch (error) {
                        // console.log(error);
                        Vue.toasted.show(error.statusText, vt_error_obj);
                    }
                    $('#block_block').hide();
                },
                async del_item(idx){
                    if(idx<0 || idx>=this.items.length){ return; }
                    if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }
                    var target_id = this.items[idx].id;
                    try {
                        var resp = await $.ajax({
                            type: "POST",
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            dataType: "json",
                            data:{
                            },
                            url: "{{url('Bonusmodel/delete_data')}}",
                            data:{
                                id: target_id,
                            },
                        });
                        if(resp.code==1){
                            this.items.splice(idx, 1);
                            Vue.toasted.show(resp.msg, vt_success_obj);
                        }else{
                            Vue.toasted.show(resp.msg, vt_error_obj);
                        }
                    } catch (error) {
                        // console.log(error);
                        Vue.toasted.show(error.statusText, vt_error_obj);
                    }
                    $('#block_block').hide();
                }
            },
        });
    </script>
@endsection