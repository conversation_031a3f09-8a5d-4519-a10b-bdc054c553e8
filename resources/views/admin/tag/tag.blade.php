@extends('admin.Public.aside')

@section('title')熱推分類 - 後台設定@endsection

@section('content')
<div id="content">
    <form action="{{url('tag/update_tag')}}" method="post" enctype="multipart/form-data">
        @csrf
        <input type="hidden" name="_token" value="{{csrf_token()}}" >

        <div class="block">
            <ul id="title" class="brand-menu">
                <li><a href="###">F商品管理區</a></li>
                <li><a href="###">熱推分類</a></li>
            </ul>       

            <div class="admin-content width-50">
                @foreach($data['item'] as $key => $vo)
                    
                    @if($key == 4)
                        @if(config('control.control_sepc_price')==1)
                        <div class="form-group">
                            <label>標籤{{$key}}：</label><input type="text" name="item[{{$vo['id']}}]" class="form-control" value="{{$vo['name']}}">
                            <p class="text-danger remark">*四字以內最佳(商品數無上限)</p>
                        </div>
                        @endif
                    @else
                        <div class="form-group">
                            <label>標籤{{$key}}：</label><input type="text" name="item[{{$vo['id']}}]" class="form-control" value="{{$vo['name']}}">
                            <p class="text-danger remark">*四字以內最佳(商品數限10個)</p>
                        </div>
                    @endif
                @endforeach
            </div>    
        </div>

        <div class="block" style="text-align:center">
            <button class="btn sendbtn">儲存</button>
        </div>
    </form>
</div>
@endsection

@section('ownJS')

    <script src="/public/static/admin/js/action.js"></script>

    <script>
        

        function format_float(num, pos)

        {

            var size = Math.pow(10, pos);

            return Math.round(num * size) / size;

        }

    

        function preview(input) {    

            if (input.files && input.files[0]) {

                var reader = new FileReader();               

                reader.onload = function (e) {

                    $('.preview').attr('src', e.target.result);

                }   

                reader.readAsDataURL(input.files[0]);

            }

        }
    

        $("body").on("change", ".upl", function (){

            preview(this);

        })

        $('#down').click(function(){

            $('.robots').animate({height:'710px'},1000)

            $('#down').fadeOut()

            $('#up').fadeIn()

        })

        $('#up').click(function(){

            $('.robots').animate({height:'150px'},1000)

            $('#up').fadeOut()

            $('#down').fadeIn()

        })

        function openList() {

            $('#backstageListli').click();

        }

    </script>

@endsection