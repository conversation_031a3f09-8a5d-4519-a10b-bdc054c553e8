@extends('admin.Public.aside')
@section('title')銷售列表@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>H行銷項目</li>
            <li>{{$data['kol']['kol_name']}}</li>
            <li>銷售列表</li>
        </ul>

        <div class="searchbox">
            <form action="" name="searchForm" method="get">
                @csrf
                下單時間區間
                <input type="date" name="start" placeholder="開始時間" value="{{$data['start']}}">~
                <input type="date" name="end" placeholder="結束時間" value="{{$data['end']}}">
                <input type="hidden" name="id" value="{{$data['kol']['id']}}">
                <input type="hidden" name="type" value="{{$data['type']}}">
                <a class="btn sendbtn mb-1 mr-1" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn m-1 btn-sm" onclick="location.href='/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}'">清除搜尋</a>
            </form>
        </div>

        <!--查看類型-->
        <div class="frame">
            <span class="type_btn"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type=0" :class="['{{$data['type']}}' == '0' ? 'active' : '']">未結算</a></span>
            <span class="type_btn"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type=1" :class="['{{$data['type']}}' == '1' ? 'active' : '']">結算中</a></span>
            <span class="type_btn"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type=2" :class="['{{$data['type']}}' == '2' ? 'active' : '']">己結算</a></span>
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table table-rwd" style="min-width: 1200px;">
                <thead>
                    <tr>
                        <th>序號</th>
                        <th>日期</th>
                        <th>訂單編號</th>
                        <th>客戶名</th>
                        <th>寄送地</th>
                        <th>商品名</th>
                        <th>商品圖</th>
                        <th>單價</th>
                        <th>數量</th>
                        <th>總價</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in orderform">
                        <td v-text="index+1"></td>
                        <td v-text="timestamp_to_date(item.create_time)"></td>
                        <td v-text="item.order_number"></td>
                        <td v-text="item.user_name"></td>
                        <td v-text="item.transport_location"></td>
                        <td colspan="5" style="padding: 0px">
                            <table class="productTable">
                                <tr v-for="(product) in product_decode(item.product)" v-if="product.key_type=='kol{{$data['kol']['id']}}'">
                                    <td><a target="_blank" :href="'{{request()->server('REQUEST_SCHEME')}}://'+product.url" v-text="product.name"></a></td>
                                    <td><img :src="'{{request()->server('REQUEST_SCHEME')}}://'+product.url2"></td>
                                    <td v-text="product.price"></td>
                                    <td v-text="product.num"></td>
                                    <td v-text="product.total"></td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </tbody>    
            </table>
        </div>

        <div class="text-center">
            <ul class="pagination" v-if="totalpage!=0">
                <li class="disabled" v-if="currentpage==1"><span>«</span></li>
                <li v-if="currentpage!=1"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page=1"><span>«</span></a></li>
                
                <li v-for="page in page_array()" :class="[page==currentpage?'active':'']">
                    <span v-if="page==currentpage" v-text="page"></span>
                    <a v-if="page!=currentpage" :href="'/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+page">
                        <span  v-text="page"></span>
                    </a>
                </li>
                
                <li class="disabled" v-if="currentpage==totalpage"><span>»</span></li>
                <li v-if="currentpage!=totalpage"><a :href="'/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+totalpage">»</a></li>
            </ul>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"></script>
    <script type="text/javascript">
        var content_data = {    
                                orderform: JSON.parse(`{{json_encode($data['orderform'])}}`.replace(/&quot;/g,'"').trim()),
                                totalpage:{{$data['totalpage']}},
                                currentpage:{{$data['page']}},
                            }

        var content = new Vue({
            el: '#content', 
            data: content_data,
            computed: {
            },
            methods: {
                page_array: function(){
                    var pages = [];
                    for (var i = -4; i < 4; i++) {
                        page = i+this.currentpage;
                        if( 1 <= page && page <= this.totalpage)
                            pages.push(page);

                        if(pages.length==5)
                            return pages;
                    }
                    return pages;
                },
                timestamp_to_date : function(unix_timestamp){
                    var a = new Date(unix_timestamp * 1000);
                    return moment(a).format('YYYY-MM-DD')
                },
                product_decode : function(jstring){
                    return JSON.parse(jstring);
                }
            }

        });
    </script>
@endsection