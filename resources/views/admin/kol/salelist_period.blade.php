@extends('admin.Public.aside')
@section('title')銷售列表@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu" >
            <li>H行銷項目</li>
            <li>{{$data['kol']['kol_name']}}</li>
            <li>銷售列表</li>
        </ul>

        <div class="search-All">
            <form action="" name="searchForm" method="get">
                @csrf
                開始時間區間
                <input type="date" name="start" placeholder="開始時間" value="{{$data['start']}}">~
                <input type="date" name="end" placeholder="結束時間" value="{{$data['end']}}">
                <input type="hidden" name="id" value="{{$data['kol']['id']}}">
                <input type="hidden" name="type" value="{{$data['type']}}">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn" onclick="location.href='/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}'">清除搜尋</a>
            </form>
        </div>

        <!--查看類型-->
        <div class="frame">
            <span class="type_btn"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type=0" :class="['{{$data['type']}}' == '0' ? 'active' : '']">未結算</a></span>
            <span class="type_btn"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type=1" :class="['{{$data['type']}}' == '1' ? 'active' : '']">結算中</a></span>
            <span class="type_btn"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type=2" :class="['{{$data['type']}}' == '2' ? 'active' : '']">己結算</a></span>
        </div>

        <!--表格 開始-->
        <table class="table-rwd table table-mobile">
            <thead>
                <tr>
                    <th>開始時間</th>
                    <th>結束時間</th>
                    <th>總價</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item) in period_sale">
                    <td data-th="開始時間">
                        <a :href="'/admin/kol/sale_detail?id={{$data['kol']['id']}}&period='+item.id" v-text="item.period_start">
                            
                        </a>
                    </td>
                    <td data-th="結束時間" v-text="item.period_end"></td>
                    <td data-th="總價" v-text="item.total"></td>
                </tr>
            </tbody>
            
            
            
        </table>

        <div class="text-center">
            <ul class="pagination" v-if="totalpage!=0">
                <li class="disabled" v-if="currentpage==1"><span>«</span></li>
                <li v-if="currentpage!=1"><a href="/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page=1"><span>«</span></a></li>
                
                <li v-for="page in page_array()" :class="[page==currentpage?'active':'']">
                    <span v-if="page==currentpage" v-text="page"></span>
                    <a v-if="page!=currentpage" :href="'/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+page">
                        <span  v-text="page"></span>
                    </a>
                </li>
                
                <li class="disabled" v-if="currentpage==totalpage"><span>»</span></li>
                <li v-if="currentpage!=totalpage"><a :href="'/admin/kol/salelist?id={{$data['kol']['id']}}&type={{$data['type']}}&start={{$data['start']}}&end={{$data['end']}}&page='+totalpage">»</a></li>
            </ul>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"></script>
    <script type="text/javascript">
        
        var period_sale='{{json_encode($data['period_sale'])}}';
        period_sale = period_sale.replace(/&quot;/g,'"');
        period_sale = JSON.parse(period_sale);
        
        var content_data = {    
                                period_sale: period_sale,
                                totalpage:{{$data['totalpage']}},
                                currentpage:{{$data['page']}},
                            }

        var content = new Vue({
            el: '#content', 
            data: content_data,
            computed: {
            },
            methods: {
                page_array: function(){
                    var pages = [];
                    for (var i = -4; i < 4; i++) {
                        page = i+this.currentpage;
                        if( 1 <= page && page <= this.totalpage)
                            pages.push(page);

                        if(pages.length==5)
                            return pages;
                    }
                    return pages;
                },
                product_decode : function(jstring){
                    return JSON.parse(jstring);
                }
            }

        });
    </script>
@endsection