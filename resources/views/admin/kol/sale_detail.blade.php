@extends('admin.Public.aside')
@section('title')銷售明細@endsection
@section('css')
    <style type="text/css">
        .type_btn{
            margin: 10px;
        }
        .type_btn a{
            padding: 5px 10px;
            color: #000;
            border: 1px solid #9D9D9D;
        }
        .type_btn a.active, 
        .type_btn a:hover{
            text-decoration: none;
            background-color: #9D9D9D;
            border: 1px solid gray;
            color: #fff;
        }

        /* 表格設定 */
        .kolTable th, 
        .tableListA tr:nth-child(1) td {
            background: #E0E0E0;
            border: 2px #9D9D9D solid;
            padding-right: 5px;
        }
        .tableListA td:nth-child(1){
            width:5%;
        }
        .tableListA td:nth-child(2), 
        .tableListA td:nth-child(3), 
        .tableListA td:nth-child(4),
        .tableListA td:nth-child(6),
        .tableListA td:nth-child(7),
        .tableListA td:nth-child(8),
        .tableListA td:nth-child(9),
        .tableListA td:nth-child(10){
            width:8%;
        }
        .tableListA td:nth-child(5){
            width:20%;
        }

        .tableListA table.productTable{
            border: none;
        }
        .productTable img{
            max-width: 100px;
        }
        .tableListA .productTable tr td{
            background: #fff;
            border: none;
            width: 20%;
        }
        .kolTable{
            width: 70%;
            margin-top: 20px; 
        }
        .kolTable td, .kolTable th{
            width: 100px !important;
        }

        .printBtn{
            margin: 20px 0px;
            padding: 5px 20px;
            font-size: 18px;
            background-color: #E0E0E0;
            border: 2px #9D9D9D solid;
        }
        .printBtn:hover{
            background-color: #9D9D9D;
            border: 2px gray solid;
        }

        /* 列印相關設定 */
        @page{
            margin: 0;
            size:auto;
        }
        @media print{
            a[href]:after {
                content: none !important;
            } 
            .noprint{
               display:none
            }
            .kolTable th, 
            .tableListA tr:nth-child(1) td {
                background: #E0E0E0 !important;
                border: 2px #9D9D9D solid !important;
                padding-right: 5px;
            }
            .tableListA .productTable tr td{
                background: #fff !important;
                border: none !important;
                width: 20% !important;
            }
            .type_btn a.active, 
            .type_btn a:hover{
                text-decoration: none;
                background-color: #9D9D9D !important;
                border: 1px solid gray !important;
                color: #fff !important;
            }
        }
    </style>
@endsection

@section('content')
    <div id="content">
        <p id="title">
            {{$data['kol']['kol_name']}} >銷售明細>第 {{ period_sale_one.period_start }} 期
        </p>

        <!--查看類型-->
        <div class="frame">
            <span class="type_btn"><a href="/admin/kol/salelist?id={$kol.id}&type=0">未結算</a></span>
            <span class="type_btn"><a href="/admin/kol/salelist?id={$kol.id}&type=1" :class="[!period_sale_one.confirm_date ? 'active' : '']">結算中</a></span>
            <span class="type_btn"><a href="/admin/kol/salelist?id={$kol.id}&type=2" :class="[period_sale_one.confirm_date ? 'active' : '']">己結算</a></span>
        </div>

        <div class="container-fluid">
            <div class="row">
                <h4 class="col-12 m-1">
                    {{ period_sale_one.period_start }}~{{ period_sale_one.period_end }}
                    @if($data['kol_period_term']['confirm_date']"}
                        &nbsp;&nbsp;確認時間：{{date('Y-m-d H:i', $data['kol_period_term']['confirm_date'])}}
                    @endif
                </h4>
                <div class="edit_form">
                    <table class="kolTable table table-rwd" style="min-width: 1200px;">
                        <tr>
                            <th>網紅名</th>
                            <td>{{$data['kol']['kol_name']}}</td>
                            <th>姓名</th>
                            <td>{{$data['kol']['real_name']}}</td>
                            <th>英文名</th>
                            <td>{{$data['kol']['english_name']}}</td>
                        </tr>
                        <tr>
                            <th>分類</th>
                            <td>{{$data['kol']['category']}}</td>
                            <th>電話</th>
                            <td>{{$data['kol']['phone']}}</td>
                            <th>手機</th>
                            <td>{{$data['kol']['mobile']}}</td>
                        </tr>
                        <tr>
                            <th>地址</th>
                            <td colspan="3">{{$data['kol']['address']}}</td>
                            <th>備註</th>
                            <td>{{$data['kol']['address_memo']}}</td>
                        </tr>
                        <tr>
                            <th>匯款銀行</th>
                            <td colspan="2">{{$data['kol']['bank_name']}}</td>
                            <th>匯款帳號</th>
                            <td colspan="2">{{$data['kol']['bank_account']}}</td>
                        </tr>
                        <tr>
                            <th>身份証</th>
                            <td>{{$data['kol']['id_no']}}</td>
                            <th>備註</th>
                            <td colspan="3">{{$data['kol']['memo']}}</td>
                        </tr>
                    </table>
                </div>
                

                <hr class="w-100">

                <h4 class="col-12 text-right">本單總金額：{{ period_sale_one.total }}</h4>
                <!--銷售表格 開始-->
                <div class="edit_form">
                    <table class="table table-rwd" style="min-width: 1400px;">
                        <thead>
                            <tr>
                                <th>序號</th>
                                <th>日期</th>
                                <th>訂單編號</th>
                                <th>客戶名</th>
                                <th>寄送地</th>
                                <th>商品名</th>
                                <th>商品圖</th>
                                <th>單價</th>
                                <th>數量</th>
                                <th>總價</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in orderform">
                                <td v-text="index+1"></td>
                                <td v-text="timestamp_to_date(item.create_time)"></td>
                                <td v-text="item.order_number"></td>
                                <td v-text="item.user_name"></td>
                                <td v-text="item.transport_location"></td>
                                <td colspan="5" style="padding: 0px">
                                    <table class="productTable">
                                        <tr v-for="(product) in product_decode(item.product)" v-if="product.key_type=='kol{{$data['kol']['id']}}'">
                                            <td><a target="_blank" :href="'{{request()->server('REQUEST_SCHEME')}}://'+product.url" v-text="product.name"></a></td>
                                            <td><img :src="'{{request()->server('REQUEST_SCHEME')}}://'+product.url2"></td>
                                            <td v-text="product.price"></td>
                                            <td v-text="product.num"></td>
                                            <td v-text="product.total"></td>
                                        </tr>
                                    </table>
                                </td>

                            </tr>
                        </tbody>
                        
                        
                    </table>
                </div>
                
                <h4 class="col-12 text-right">本單總金額：{{ period_sale_one.total }}</h4>
                <div class="col-12 text-center">
                    <div class="text-danger">確認後內容將不會再更改</div>
                    <input class="printBtn btn sendbtn mr-1" type="button" value="確認本單" @click="confirm_period()" v-if="!period_sale_one.confirm_date">
                    <input class="printBtn btn sendbtn" type="button" value="列印明細" @click="print_page()">
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"></script>
    <script type="text/javascript">
        var content_data = {    
            period_sale: JSON.parse(`{{json_encode($data['kol_period_term'])}}`.replace(/&quot;/g,'"').trim()),
            orderform: JSON.parse(`{{json_encode($data['orderform'])}}`.replace(/&quot;/g,'"').trim()),
        }
        var contentVM = new Vue({
            el: '#content', 
            data: content_data,
            computed: {
                period_sale_one: function(){
                    return this.period_sale;
                },
            },
            methods: {
                timestamp_to_date : function(unix_timestamp){
                    var a = new Date(unix_timestamp * 1000);
                    return moment(a).format('YYYY-MM-DD')
                },
                product_decode : function(jstring){
                    return JSON.parse(jstring);
                },
                print_page: function(){
                    //获取要打印的Dom内容
                    let newDomHtml = $('#content').html();
                    window.document.body.innerHTML = newDomHtml;
                    window.print();
                    window.location.reload();   //解决打印之后按钮失效的问题，刷新页面
                    return false;
                },
                confirm_period: function(){
                    $.ajax({
                        url: "{{url('Kol/confirm_period')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        datatype: 'json',
                        data: {
                            kol_id: "{{$data['kol']['id']}}",
                            period: "{{$data['period']}}",
                        },
                        error: function (xhr) {
                            alert('失敗');
                            console.error(xhr);
                        },
                        success: function (response) {
                            if(response.code==1){
                                alert('確認成功');
                                window.location.reload();
                            }else{
                                alert('確認失敗：'+response.msg);
                            }
                        },
                    });
                },
            }

        });
    </script>
@endsection