@extends('admin.Public.aside')
@section('title'){{Lang::get('F商品管理區')}} - {{Lang::get('產品資訊')}}@endsection
@section('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.0/jquery-confirm.min.css">
@endsection
@section('content')
        <div id="content">
            <ul id="title" class="brand-menu">
                <li>{{Lang::get('F商品管理區')}}</li>
                <li><a href="{{url('All/index')}}">{{Lang::get('產品資訊')}}</a></li>
            </ul>
            <div class="frame" style="min-height: 40px;">
                <div class="d-flex flex-wrap justify-content-between">
                    <distributors_area ref="distributors_area" :my_distributor_id="{{$data['my_distributor_id']}}" :need_all="true"></distributors_area>

                    <div>
                        <div class="d-inline-flex" v-if="can_use_add">
                            <form action="{{url('Productinfo/ISBN_create')}}" name="ISBN_create" method="post" >
                                @csrf
                                <span class="name font-weight-bold" for="ISBN">{{Lang::get('快速建立修改商品')}}：</span>
                                <input type="text" name="ISBN" value=""  placeholder="{{Lang::get('條碼')}}">
                            </form>
                        </div>
                        <button class="btn sendbtn mt-1" type="button"  data-toggle="collapse" data-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            {{Lang::get('篩選工具')}}
                        </button>
                    </div>
                </div>
                <div class="accordion mt-2 border-bottom" id="accordionExample">
                    <div id="collapseOne" class="collapse collapse" aria-labelledby="headingOne" data-parent="#accordionExample">
                        <div class="search-All">
                            <ul class="search-items">
                                <li>
                                    <form action="" name="searchForm" method="get" class="d-flex flex-wrap align-items-center w-100">
                                        @csrf
                                        <select v-model="searchform.online">
                                            <option value="-1" selected="">{{Lang::get('上下架')}}</option>
                                            <option value="0">{{Lang::get('隱藏')}}</option>
                                            <option value="1">{{Lang::get('顯示')}}</option>
                                            <option value="2">{{Lang::get('關閉')}}</option>
                                        </select>
                                        <select id="searchPrev" v-model="searchform.searchPrev" @change="searchform.searchBranch=0">
                                            <option value="0">{{Lang::get('選擇分館')}}</option>
                                            <option v-for="vo in layers" :value="vo.id" v-text="vo.title"></option>
                                        </select>
                                        <select id="searchBranch" v-model="searchform.searchBranch">
                                            <option value="0">{{Lang::get('選擇分類')}}</option>
                                            <option v-for="vo in layer_second" :value="vo.id" v-text="vo.title"></option>
                                        </select>
                                        <select v-model="searchform.product_cate">
                                            <option value="">{{Lang::get('商品類型')}}</option>
                                            <option value="1">投資</option>
                                            <option value="2">消費</option>
                                        </select>
                                        <select v-model="searchform.category_type">
                                            <option value="">{{Lang::get('商品分類')}}</option>
                                            <option value="0">天脈商城</option>
                                            <option value="1">課程專區</option>
                                        </select>
                                        <select v-model="searchform.bonus_model_id">
                                            <option value="">{{Lang::get('回饋模組')}}</option>
                                            <option value="0">無</option>
                                            @foreach($data['bonus_models'] as $model)
                                                <option value="{{$model['id']}}">{{$model['name']}}</option>
                                            @endforeach
                                        </select>
                                        <input class="enter_search" type="text" v-model="searchform.searchKey" style="text-align:center;width: 300px;" placeholder="{{Lang::get('名稱')}}" />

                                        @if(empty(config('control.close_function_current')['存放位置管理']))
                                            <select type="text" v-model="searchform.position_id">
                                                <option value="">{{Lang::get('請選擇庫存區')}}</option>
                                                <option v-for="vo in position" :value="vo.id" v-text="vo.name"></option>
                                            </select>
                                            <input class="enter_search" type="number" v-model="searchform.ck.position_number" placeholder="{{Lang::get('庫存編碼')}}" style="width: 150px;"/>
                                        @endif

                                        @if(empty(config('control.close_function_current')['標籤設定']))
                                            <!-- 人氣商品 -->
                                            <span class="d-inline-block mr-1">
                                                <input type="checkbox" id="hot_product" v-model="searchform.ck.hot_product" true-value="1" false-value="0">
                                                <label for="hot_product">{{$data['tag'][0]['name']}}</label>&nbsp;&nbsp;
                                            </span>
                                            <!-- 店長推薦 -->
                                            <span class="d-inline-block mr-1">
                                                <input type="checkbox" id="recommend_product" v-model="searchform.ck.recommend_product" true-value="1" false-value="0">
                                                <label for="recommend_product">{{$data['tag'][1]['name']}}</label>&nbsp;&nbsp;
                                            </span>
                                            <!-- 即期良品 -->
                                            <span class="d-inline-block mr-1">
                                                <input type="checkbox" id="expiring_product" v-model="searchform.ck.expiring_product" true-value="1" false-value="0">
                                                <label for="expiring_product">{{$data['tag'][2]['name']}}</label>&nbsp;&nbsp;
                                            </span>
                                            @if(config('control.control_sepc_price')==1)
                                                <!-- 特價商品 -->
                                                <span class="d-inline-block mr-1">
                                                    <input type="checkbox" id="spe_price_product" v-model="searchform.ck.spe_price_product" true-value="1" false-value="0">
                                                    <label for="spe_price_product">{{$data['tag'][3]['name']}}</label>
                                                </span>
                                            @endif
                                        @endif

                                        @if(empty(config('control.close_function_current')['網紅列表']))
                                            <select name="kol_id" v-model="searchform.kol_id">
                                                <option value="-1">{{Lang::get('請選擇網紅')}}</option>
                                                <option value="0">{{Lang::get('無網紅')}}</option>
                                                <option v-for="vo in kol" :value="vo.id" v-text="vo.kol_name"></option>
                                            </select>
                                        @endif

                                        <a class="btn sendbtn mr-1 ml-1" @click="search_productinfo">{{Lang::get('搜尋')}}</a>
                                        <a class="btn clearbtn mr-1 ml-1" @click="clear_search">{{Lang::get('清除搜尋')}}</a>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!--新增與編輯-->
            <div class="frame d-flex flex-wrap justify-content-between align-items-center">
                <div>
                    @if ($data['level']!='')
                    <form action="{{url('Product/import')}}?{{$data['level']}}" name="excelForm2" method="post"  enctype="multipart/form-data">
                        @csrf
                        <button class="btn clearbtn"><input type=file name="file" id="file_excel" style="padding:0px;"></button>
                        <a class="btn btn-success btn-sm text-white" onclick="updateProduct();">{{Lang::get('批量上傳商品')}}</a>
                        <a class="btn btn-success btn-sm text-white" href="/admin/productinfo/product_example">下載範本</a>
                    </form>
                    @endif
                    <a href="{{url('productinfo/allcreate')}}" class="btn clearbtn" target="_blank" v-if="can_use_add">
                        <i class="bi bi-plus-lg add small"></i> {{Lang::get('新增')}}
                    </a>
                    <span class="d-inline-block position-relative">
                        <div class="edit" onclick="Show('.edit-item')">
                            {{Lang::get('編輯')}} <span class="bi bi-chevron-down"></span>
                        </div>
                        <!-- 編輯開始 -->
                        <div class="edit-item none">
                            <a onclick="multiOnline();">
                                <p class="mb-0">{{Lang::get('顯示')}}&nbsp;</p>
                                <label class="switch" name="0">
                                    <input type="checkbox" disabled checked><span class="slider round"></span>
                                </label>
                            </a>
                            <a onclick="multiHide();">
                                <p class="mb-0">{{Lang::get('隱藏')}}&nbsp;</p>
                                <label class="switch" name="0">
                                    <input type="checkbox" disabled><span class="slider round"></span>
                                </label>
                            </a>
                            <a onclick="multiOffline();">
                                <p class="mb-0">{{Lang::get('關閉')}}&nbsp;</p>
                                <label class="switch" name="0">
                                    <input type="checkbox" disabled><span class="slider round"></span>
                                </label>
                            </a>
                            @if(config('control.control_copy_product')==1)
                                <a onclick="multiCopy();" class="mb-2">
                                    {{Lang::get('複製')}}
                                    <span style="margin-left: 15px;">
                                        <svg width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M5 22q-.825 0-1.413-.587Q3 20.825 3 20V6h2v14h11v2Zm4-4q-.825 0-1.412-.587Q7 16.825 7 16V4q0-.825.588-1.413Q8.175 2 9 2h9q.825 0 1.413.587Q20 3.175 20 4v12q0 .825-.587 1.413Q18.825 18 18 18Zm0-2h9V4H9v12Zm0 0V4v12Z"/></svg>
                                    </span>
                                </a>
                            @endif
                            <a onclick="multiDelete();" class="border-top pt-1">
                                {{Lang::get('刪除')}} <span style="margin-left: 15px;" class="bi bi-trash"></span>
                            </a>
                        </div>
                        <!-- 編輯結束 -->
                    </span>
                    <div class="edit" onclick="print()">
                        {{Lang::get('印出條碼')}} <span class="bi bi-printer"></span>
                    </div>
                    <span class="d-inline-block text-danger remark ml-3">
                        {{Lang::get('排序由小到大呈現')}}&nbsp;&nbsp;
                        @if($data['admin_type']!='distribution')
                            {{Lang::get('先依「置頂」、再依「一般」數值大小')}}
                        @endif<br>
                    </span>
                </div>

                <p class="d-inline-block m-0">{{Lang::get('商品數')}}:<span v-text="total"></span></p>
            </div>

            <!--表格 開始-->

            <div class="edit_form">
                <table class="table table-rwd" style="min-width:1500px;">
                    <thead>
                        <tr>
                            <th style="width: 100px;">
                                <input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=productinfoCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="cursor:pointer;">
                                {{Lang::get('商品ID')}}
                            </th>
                            <th style="width: 80px;">{{Lang::get('上下架')}}</th>
                            <th style="width: 120px;">{{Lang::get('排序')}}</th>
                            <th style="width: 120px;">{{Lang::get('圖片')}}</th>
                            <th style="width: 200px;">{{Lang::get('名稱')}}</th>
                            <th style="width: 100px;">{{Lang::get('商品分類')}}</th>
                            <th style="width: 120px;">{{Lang::get('已售出數量')}}</th>
                            <th >{{Lang::get('條碼')}}</th>
                            @if(empty(config('control.close_function_current')['網紅列表']))
                            <th >{{Lang::get('所屬網紅')}}</th>
                            @endif
                            <th>{{Lang::get('售價')}}</th>
                            @if(empty(config('control.close_function_current')['標籤設定']))
                                <th class="th-nth-child-8">{{Lang::get('首頁設定')}}</th>
                            @endif
                            <th class="text-left">{{Lang::get('階層位置')}}</th>
                            <th style="width:200px;">{{Lang::get('操作')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr></tr>
                        <tr v-for="vo in productinfoItem" :id="'productinfo_' +vo.id">
                            <td>
                                <input type="checkbox" class="productinfoCheckbox" :alt="vo.id">
                                <span v-text="vo.id"></span>
                            </td>
                            <td >
                                <select :id="vo.id" @change="getval(vo)" v-model="vo.online"
                                        :class="[ vo.online==1 ? 'color_green' : vo.online==0 ? 'color_yellow' : vo.online==2 ? 'color_red' : '']">
                                    <option class="color_green" value="1" >{{Lang::get('顯示')}}</option>
                                    <option class="color_yellow" value="0">{{Lang::get('隱藏')}}</option>
                                    <option class="color_red" value="2">{{Lang::get('關閉')}}</option>
                                </select>
                            </td>
                            <td>
                                <div v-if="searchform.searchPrev!=0 || searchform.searchBranch!=0">
                                    <!-- 分館、分類排序 -->
                                    @if($data['admin_type']!='distribution')
                                        <span class="d-flex mb-1">
                                            {{Lang::get('頂')}}：<input type="number"  class="text-right w-100"
                                                       v-model="vo.po_top_order_id"
                                                       @focus="order_focus(vo, 'po', 'top_order_id')"
                                                       @blur="order_blur(vo, 'po', 'top_order_id')">
                                        </span>
                                    @endif
                                    <input type="number"  class="text-right w-100 "
                                           v-model="vo.po_order_id"
                                           @focus="order_focus(vo, 'po', 'order_id')"
                                           @blur="order_blur(vo, 'po', 'order_id')" >
                                </div>
                                <div v-if="searchform.searchPrev==0 && searchform.searchBranch==0">
                                    <!-- productinfo排序 -->
                                    @if($data['admin_type']!='distribution')
                                        <span class="d-flex mb-1">
                                            {{Lang::get('頂')}}：<input type="number" class="text-right w-100"
                                                       v-model="vo.top_order_id"
                                                       @focus="order_focus(vo, 'main', 'top_order_id')"
                                                       @blur="order_blur(vo, 'main', 'top_order_id')">
                                        </span>
                                    @endif
                                    <input type="number" class="text-right w-100"
                                           v-model="vo.order_id"
                                           @focus="order_focus(vo, 'main', 'order_id')"
                                           @blur="order_blur(vo, 'main', 'order_id')">
                                </div>
                            </td>
                            <td>
                                <div class="position-relative d-flex align-items-center justify-content-center">
                                    <span class="bi bi-image"></span>
                                    <template v-if="vo.pic.length>0">
                                        <img class="preview" :src="'{{__UPLOAD__}}'+vo.pic[0]"/>
                                    </template>
                                </div>
                            </td>
                            <td>
                                <a :href="'{{url('Productinfo/edit')}}?id=' +vo.id" target="_blank" v-text="vo.title"></a>
                                <br>
                                <template v-if="vo.product_cate==1">(投資)</template>
                                <template v-else-if="vo.product_cate==2">(消費)</template>
                            </td>
                            <td>
                                <template v-if="vo.category_type==0">
                                    <span class="badge badge-primary">天脈商城</span>
                                </template>
                                <template v-else-if="vo.category_type==1">
                                    <span class="badge badge-success">課程專區</span>
                                </template>
                                <template v-else>
                                    <span class="badge badge-secondary">未分類</span>
                                </template>
                            </td>
                            <td>
                                <div class="sold-count-info">
                                    <div><strong v-text="formatSoldCount(vo.sold_count_display)"></strong></div>
                                    <small class="text-muted">
                                        起始: <span v-text="vo.sold_count_base"></span> |
                                        實際: <span v-text="vo.sold_count_actual"></span>
                                    </small>
                                </div>
                            </td>
                            <td><span v-text="vo.ISBN"></span></td>
                            @if(empty(config('control.close_function_current')['網紅列表']))
                                <td><span v-text="vo.kol_name"></span></td>
                            @endif
                            <td>
                                <template v-if="vo.has_price" v-for="item in JSON.parse(vo.item)">
                                    <div v-text="item.price + '-' + item.count"></div>
                                </template>
                                <div v-if="!vo.has_price">{{Lang::get('無')}}</div>
                            </td>
                            @if(empty(config('control.close_function_current')['標籤設定']))
                                <td>
                                    <!--人氣商品--->
                                    <span class="d-inline-block">
                                        <input type="checkbox" v-model="vo.hot_product" :id="'hot_product_' + vo.id" @click="change_tag('hot_product', vo)">
                                        <label class="m-0" :for="'hot_product_' + vo.id">{{$data['tag'][0]['name']}}</label>
                                    </span><br>

                                    <!--店長推薦--->
                                    <span class="d-inline-block">
                                        <input type="checkbox" v-model="vo.recommend_product" :id="'recommend_product_' + vo.id" @click="change_tag('recommend_product', vo)">
                                        <label class="m-0" :for="'recommend_product_' + vo.id">{{$data['tag'][1]['name']}}</label>
                                    </span><br>

                                    <!--即期良品--->
                                    <span class="d-inline-block">
                                        <input type="checkbox" v-model="vo.expiring_product" :id="'expiring_product_' + vo.id" @click="change_tag('expiring_product', vo)">
                                        <label class="m-0" :for="'expiring_product_' + vo.id">{{$data['tag'][2]['name']}}</label>
                                    </span><br>

                                    @if(config('control.control_sepc_price')==1)
                                        <!--特價商品--->
                                        <span class="d-inline-block">
                                            <input type="checkbox" v-model="vo.spe_price_product" :id="'spe_price_product_' + vo.id" @click="change_tag('spe_price_product', vo)">
                                            <label class="m-0" :for="'spe_price_product_' + vo.id">{{$data['tag'][3]['name']}}</label>
                                        </span><br>
                                    @endif
                                </td>
                            @endif
                            <!-- <td><span v-text="vo.updatetime"></span></td> -->
                            <td class="text-left"><span v-html="vo.show_array"></span></td>
                            <td><!-- 操作-1 -->
                                <a class="btn whitebtn"  :href="'{{url('index/Product/productinfo')}}?id=' + vo.id" target="_blank">{{Lang::get('前台查看')}}&nbsp;&nbsp;&nbsp;<i class="bi bi-box-arrow-up-right"></i></a>
                                @if(config('control.control_register')==1)
                                    <template v-if="vo.is_registrable==1">
                                        <a class="btn whitebtn" :href="'{{url('Examination/examinee_list')}}?id=' + vo.id" target="_blank">{{Lang::get('查看報名')}}</a><br>
                                        <a class="btn whitebtn" :href="'{{url('Productinfo/edit_fields')}}?id=' + vo.id" target="_blank">{{Lang::get('設定欄位')}}</a>
                                    </template>
                                @endif
                                <span class="btn sendbtn"  @click="deleteproduct(vo.id)">{{Lang::get('刪除')}} </span>
                            </td>

                        </tr>
                    </tbody>
                </table>
            </div>
            <!--表格 結束-->
            <div class="text-center">
                <ul class="pagination" v-if="pages.length>1">
                    <li class="" v-if="searchform.page>1" @click="change_page(1)">
                        <a href="###">«</a>
                    </li>
                    <li v-for="p in pages" :class="[searchform.page==p ? 'active' : '']" >
                        <a href="###" v-text="p" @click="change_page(p)"></a>
                    </li>
                    <li class="" v-if="searchform.page<last_page" @click="change_page(last_page)">
                        <a href="###">»</a>
                    </li>
                </ul>
            </div>
        </div>
    </body>
@endsection
@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.0/jquery-confirm.min.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });
    </script>
    <script>
        //批量上傳商品
        function updateProduct(){
            if($("#file_excel").val() != ''){
            excelForm2.submit();
            }else{
            alert('請選擇檔案');
            }
        }
        // 供應商切換模組串接-開始
        function init_after_get_distributors(){
            content_areaVM.init_data();
        }
        function click_distributor_emit(id){
            content_areaVM.distributors_current = id;

            content_areaVM.reset_search();
            content_areaVM.init_data();
        }
        // 供應商切換模組串接-結束

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Productinfo/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                data: blockData,
                success: function(resp) {
                    if(resp.code){
                        // location.reload();
                        content_areaVM.get_productinfos();
                    }else{
                        Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", 'bg-danger']});
                    }
                },
                error: function(xhr) {
                    Vue.toasted.show(xhr,{duration:1500, className: ["toasted-primary", 'bg-danger']});
                }
            });
        };

        /*取消商品標籤*/
        Vue.prototype.remove_to_index_adv = function (blockData) {
            var checkboxStatus = true;
            $.ajax({
                url: "{{url('Productinfo/remove_to_index_adv')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                data: blockData,
                success: function(resp) {
                    bg_success = resp.code ? 'bg-success' : 'bg-danger';
                    Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", bg_success]});
                    checkboxStatus = resp.code ? false : true;
                },
                error: function(xhr) {
                    Vue.toasted.show(xhr,{duration:1500, className: ["toasted-primary", 'bg-danger']});
                    checkboxStatus = true;
                }
            });
            return checkboxStatus;
        };
        /*勾選商品標籤*/
        Vue.prototype.add_to_index_adv = async function (blockData) {
            var checkboxStatus = false;
            await $.ajax({
                url: "{{url('Productinfo/add_to_index_adv')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                async: false,
                dataType: 'json',
                data: blockData,
                success: function(resp) {
                    bg_success = resp.code ? 'bg-success' : 'bg-danger';
                    Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", bg_success]});
                    checkboxStatus = resp.code ? true : false;
                },
                error: function(xhr) {
                    Vue.toasted.show(xhr,{duration:1500, className: ["toasted-primary", 'bg-danger']});
                    checkboxStatus = false;
                }
            });
            return checkboxStatus;
        };

        const empty_searchform = {
            page:1, /*當前頁數*/

            online: -1,
            searchPrev: 0,
            searchBranch: 0,
            product_cate: '',
            category_type: '',
            bonus_model_id: '',
            searchKey: '',
            position_id: '',
            position_number: '',
            kol_id: '-1',
            ck:{
                hot_product: 0,
                recommend_product: 0,
                expiring_product: 0,
                spe_price_product: 0,
            },
        };
        const my_distributor_id = "{{$data['my_distributor_id']}}";
        var content_area_data = {
            productinfoItem: [],
            max_page_num: 9,
            pages: [1],
            last_page: 1,
            total: 0,

            distributors_current: my_distributor_id,
            layers: [],
            kol: [],
            position: [],

            searchform: JSON.parse(JSON.stringify(empty_searchform)),

            change_order_record: {
                order: 0,
            },
        }
        if("{{request()->get('searchPrev') ?? ''}}"){ content_area_data.searchform.searchPrev = "{{request()->get('searchPrev') ?? ''}}";}
        if("{{request()->get('searchBranch') ?? ''}}"){ content_area_data.searchform.searchBranch = "{{request()->get('searchBranch') ?? ''}}";}
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            computed: {
                can_use_add: function(){
                    return this.distributors_current==my_distributor_id ? true : false;
                },
                layer_second: function(){
                    for (var i = 0; i < this.layers.length; i++) {
                        if(this.searchform.searchPrev==this.layers[i].id){
                            return this.layers[i].content;
                        }
                    }
                    return [];
                },
            },
            methods: {
                // 格式化已售出數量顯示
                formatSoldCount: function(count) {
                    if (!count || count <= 10000) {
                        return count || '0';
                    }

                    const wan = count / 10000;

                    // 如果是整數萬，直接顯示
                    if (wan === Math.floor(wan)) {
                        return Math.floor(wan) + '萬';
                    }

                    // 保留一位小數
                    return wan.toFixed(1) + '萬';
                },
                reset_search: function(){
                    self = this;
                    // self.searchform.page=1;
                    // self.searchform.searchPrev=0;
                    // self.searchform.searchBranch=0;
                    self.searchform = JSON.parse(JSON.stringify(empty_searchform))
                },
                init_data: async function(){
                    self = this;
                    await $.ajax({ /*讀取篩選下拉選資料*/
                        type: 'get',
                        dataType: 'json',
                        url: "{{url('All/init_data_ajax')}}?distributor_id="+self.distributors_current,
                        success: function(resp){
                            // console.log(resp);
                            self.kol = resp.kol;
                            self.position = resp.position;
                        },
                        error: function(e){
                        },
                    })

                    await $.ajax({ /*讀取階層資料*/
                        type: "get",
                        dataType: "json",
                        url: "{{url('Layertree/get_product_tree')}}?distributor_id="+self.distributors_current, //請求的url地址
                        success: function(req) {
                            // console.log(req)
                            self.layers = req;
                        },
                        error: function() { //請求出錯處理
                        }
                    });

                    self.searchform.kol_id = '-1';
                    self.searchform.position_id = '';
                    self.get_productinfos(); /*讀取商品資料*/

                },
                init_pages: function(){
                    self = this;
                    self.pages = [self.searchform.page];
                    var check_num = 1;
                    while (
                        self.pages.length<self.max_page_num && (
                            self.searchform.page-check_num>=1 ||
                            self.searchform.page+check_num<=self.last_page
                        )
                    ) {
                        if(self.searchform.page-check_num>=1){
                            self.pages.push(self.searchform.page-check_num);
                        }
                        if(self.searchform.page+check_num<=self.last_page){
                            self.pages.push(self.searchform.page+check_num);
                        }
                        check_num += 1;
                    }
                    self.pages = self.pages.sort(function(a, b){return a-b});
                },
                search_productinfo: function(){
                    self = this;
                    self.searchform.page = 1;
                    self.get_productinfos();
                },
                get_productinfos: function (){
                    Vue.toasted.show("{{Lang::get('載入中')}}",{duration:1500, className: ["toasted-primary", 'bg-success']});
                    self.productinfoItem = [];
                    self = this;
                    // console.log(self.searchform);
                    $.ajax({
                        type: 'get',
                        dataType: 'json',
                        url: "{{url('All/search_product_ajax')}}?distributor_id="+self.distributors_current,
                        data: self.searchform,
                        success: function(resp){
                            console.log(resp);
                            // console.log(resp.productinfoItem[0].show_array);
                            self.productinfoItem = resp.productinfoItem;
                            self.total = resp.productinfo.total;
                            self.last_page = resp.productinfo.last_page;

                            self.init_pages(); /*初始化頁數*/

                            Vue.toasted.show("{{Lang::get('載入完成')}}",{duration:1500, className: ["toasted-primary", 'bg-success']});
                        },
                        error: function(e){
                            Vue.toasted.show(e,{duration:1500, className: ["toasted-primary", 'bg-danger']});
                        },
                    })
                },
                clear_search: function(){
                    self = this;
                    self.reset_search();
                    self.get_productinfos();
                },
                change_page: function(p){
                    $('.table_scroll_area').scrollTop(0);
                    self.searchform.page = p;
                    self.get_productinfos();
                },

                /*變更上下架*/
                getval: function(item){
                    setTimeout(()=>{
                        $.ajax({
                            type: 'POST',
                            headers: {
                                'X-CSRF-Token': csrf_token
                            },
                            dataType: 'json',
                            url: "{{url('Productinfo/cellCtrl')}}",
                            data: {
                                id: item.id,
                                online: item.online,
                            },
                            success: function(resp) {
                                bg_success = resp.code ? 'bg-success' : 'bg-danger';
                                Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", bg_success]});
                            },
                            error: function(xhr) {
                                console.log(xhr);
                            }
                        });
                    }, 100)
                },
                /*變更換排序*/
                order_focus: function(item, type, col){
                    self = this;
                    self.change_order_record.order = type=='po' ? item['po_'+col] : item[col];
                },
                order_blur: function(item, type, col){
                    self = this;
                    order_num = type=='po' ? item['po_'+col] : item[col];
                    if(order_num!=self.change_order_record.order){
                        self.change_order(item, type, col);
                    }
                },
                change_order: function(item, type, col){
                    self = this;
                    blockData = { id: item.id };
                    if(type=='po'){ // 更改商品在分館、分類的排序
                        blockData.id = item.id;
                        blockData.prev_id = self.searchform.searchPrev;
                        blockData.branch_id = self.searchform.searchBranch;
                        blockData['po_'+col] = item['po_'+col];
                    }else if(type=='main'){ // 更改productinfo本身排序
                        blockData[col] = item[col];
                    }
                    setTimeout(()=>{  self.blockCtrl(blockData); }, 100);
                },
                /*變更tag*/
                change_tag: function (tableName, item) {
                    self = this;
                    setTimeout(async ()=>{
                        blockData = {
                            id: item.id,
                            tableName: tableName,
                        }
                        console.log(item[tableName]);
                        if(item[tableName]){
                            result = await self.add_to_index_adv(blockData);
                            if(!result){ item[tableName] = 0; }
                        }else{
                            result = await self.remove_to_index_adv(blockData);
                            if(!result){ item[tableName] = 1; }
                        }
                    }, 100);
                },
                /*刪除商品*/
                deleteproduct: function(pro_id){
                    if(confirm("{{Lang::get('確定刪除嗎')}}")){
                        location.href = "{{url('Productinfo/delete')}}?id="+pro_id;
                    }
                },
            },
        });


        function getMultiId() {
            var multiIdArray = [];
            $('.productinfoCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            $('.activityCheckboxAll').prop("checked", false);
            return multiIdArray;
        }

        function multiDelete() {
            if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }

            var form = document.createElement("form");
            form.action = "{{url('productinfo/multiDelete')}}";
            form.method = "post";

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            multiId.name = "id";

            form.appendChild(multiId);

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            document.body.appendChild(form);
            form.submit();
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiChange(multiIdArray,1,"{{Lang::get('顯示')}}");
        }
        function multiHide() {
            var multiIdArray = getMultiId();
            multiChange(multiIdArray,0,"{{Lang::get('隱藏')}}");
        }
        function multiOffline() {
            var multiIdArray = getMultiId();
            multiChange(multiIdArray,2,"{{Lang::get('關閉')}}");
        }
        async function multiChange(multiIdArray,value,option){
            var check = 0;
            // console.log(multiIdArray);
            for(var i=0; i<multiIdArray.length;i++){
                await $.ajax({
                    type: 'post',
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    url: "{{url('Productinfo/cellCtrl')}}",
                    data: {
                        id: multiIdArray[i],
                        online: value,
                    },
                    success: function(resp) {
                        if(!resp.code){
                            check += 1;
                        }
                    },
                    error: function(xhr) {
                        check += 1;
                    }
                });
            }
            if(check==0){
                for(var i=0; i<multiIdArray.length;i++){
                    var selector = "#" + multiIdArray[i];
                    // console.log(selector);
                    $(selector).children().each(function(){
                        if ($(this).text()==option){
                            //jQuery給法
                            $(this).attr("selected", "true");
                        }
                    });
                }
                bg_success = 'bg-success';
                msg = "{{Lang::get('操作成功')}}";
            }else{
                bg_success = 'bg-danger';
                msg = "{{Lang::get('操作失敗')}}";
            }
            Vue.toasted.show(msg,{duration:1500, className: ["toasted-primary", bg_success]});
            self.get_productinfos();
        }

        function multiCopy() {
            // console.log(JSON.stringify(getMultiId()));
            location.href = "{{url('Productinfo/copy')}}?id=" + JSON.stringify(getMultiId());
        }

        $('input[name="ISBN"]').on('input',function(){
            var ISBN =  $('input[name="ISBN"]').val();
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                url: "{{url('Productinfo/input_isbn')}}",
                data: {
                    ISBN:ISBN,
                },
                success: function(resp) {
                    if(resp > 0){
                        open_url = "{{url('Productinfo/edit')}}?id="+resp;
                    }else{
                        open_url = "{{url('Productinfo/allcreate')}}?ISBN="+ISBN;
                    }
                    window.open(open_url);
                },
                error: function(xhr) {
                    console.log(xhr);
                }
            });
        });
    </script>

    <script>
        $('.enter_search').on('keyup', function (e) {
            if (e.key === 'Enter' || e.keyCode === 13) {
                content_areaVM.searchform.page=1;
                content_areaVM.get_productinfos();
            }
        });
    </script>
    <script type="text/javascript">
        function print(){
            var query_list = [];
            keys = Object.keys(content_areaVM.searchform);
            for (var i = 0; i < keys.length; i++) {
                if(keys[i]!='ck'){
                    query_list.push(keys[i]+'='+content_areaVM.searchform[keys[i]]);
                }else{
                    keys2 = Object.keys(content_areaVM.searchform[keys[i]]);
                    for (var x = 0; x < keys2.length; x++) {
                        query_list.push(keys[i]+'['+keys2[x]+']='+content_areaVM.searchform[keys[i]][keys2[x]]);
                    }
                }
            }
            query_list = query_list.join('&');
            query = query_list ? '&'+query_list+'&page=1' : '';
            print_url = "{{url('All/print')}}?distributor_id="+content_areaVM.distributors_current + query;
            window.open(print_url);
        };
    </script>
@endsection
