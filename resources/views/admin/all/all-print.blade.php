@extends('admin.Public.aside')
@section('title'){{Lang::get('F商品管理區')}} - {{Lang::get('條碼清單')}}@endsection
@section('content')
    <div id="content">
       	<ul id="title" class="brand-menu">
            <li>{{Lang::get('F商品管理區')}}</li>
            <li><a href="{{url('All/index')}}">{{Lang::get('產品資訊')}}</a></li>
            <li>{{Lang::get('條碼清單')}}</li>
        </ul>	
		<table class="table table-rwd" style="min-width:1200px;">
			<thead>
				<tr>
					<th style="width: 150px;">{{Lang::get('圖片')}}</th>
					<th>{{Lang::get('名稱')}}</th>
					<th>{{Lang::get('階層位置')}}</th>
					<th>{{Lang::get('條碼')}}</th>
				</tr>
			</thead>
			<tbody>
				<tr></tr>
				@foreach($data['productinfoItem'] as $vo)
				<tr ng-repeat="item in contCtrl.model.actProd" on-finish-render>
					<td th-data="{{Lang::get('圖片')}}">
						<img src="/public/static/index/{{$vo['pic'][0]}}" style="max-width: 100px">
					</td>
					<td th-data="{{Lang::get('名稱')}}">{{$vo['title']}}</td>
					<td th-data="{{Lang::get('階層位置')}}" style="text-align: left; padding-left: 10px;">
						{!! $vo['show_array'] !!}
					</td>
					<td th-data="{{Lang::get('條碼')}}">
						{{$vo['ISBN']}}&nbsp;&nbsp;&nbsp;&nbsp;
						@if($vo['ISBN'])
							<img src="https://barcode.tec-it.com/barcode.ashx?data={{$vo['ISBN']}}" style="height: 55px">
						@endif
					</td>
				</tr>
				@endforeach
			</tbody>
		</table>
		<div post-render></div>
    </div>
@endsection

@section('ownJS')
<script type="text/javascript">
	$( document ).ready(function() {
	    window.print();
	});
</script>
@endsection