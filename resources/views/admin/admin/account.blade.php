@extends('admin.Public.aside')

@section('title')G功能應用項目 - 帳號管理@endsection

@section('content')
  <div id="content">
    <ul id="title" class="brand-menu" >
      <li><a href="###">G功能應用項目</a></li>
      <li><a href="###">帳號管理</a></li>
    </ul>
    <div class="edit_form" >
      <table class="table  table-rwd width-80 m-auto" style="min-width: 1300px;">
        <thead>
          <tr>
            @if($data['admin']['permission'] == 'all' )<th>當前使用者</th>@endif
            <th style="width: 80px;">名稱</th>
            <th>帳號</th>
            <th>信箱</th>
            <th style="width: 150px;">舊密碼</th>
            <th style="width: 150px;">新密碼</th>
            <th style="width: 150px;">確認新密碼</th>
            <th>操作</th>
          </tr> 
        </thead>
        <tbody>
          @foreach($data['accounts']->items() as $vo)
          <form action="{{url('admin/update')}}?id={{$vo->id}}" method="post">
            @csrf
            <input name="id" type="hidden" value="{{$vo->id}}">
            <tr>
              @if($data['admin']['permission'] == 'all' )
              <td>
                <label class="f_swi mb-0" style="display:inline-flex;margin-top: 5px;margin-left: 9px;">
                  <input type="radio" name="current" id="" class="current_{{$vo->id}}" {{App\Services\CommonService::fat_return_checked('current', $vo->permission)}} onclick="current_change({{$vo->id}})">
                  <span class="f_sli"><span class="X_reverse">X</span><span class="O_reverse">O</span></span>
                </label>
              </td>
              @endif
              <td>{{$vo->name}}</td>
              <td>{{$vo->account}}</td>
              <td><input class="email{{$vo->id}}" type="email" value="{{$vo->email}}" placeholder="請輸入信箱" autocomplete="new-password" /><a class="btn btn-sm ml-1 sendbtn" onclick="update_email({{$vo->id}})">更新</a></td>
              <td><input name="old_password" type="password" placeholder="請輸入舊密碼" autocomplete="new-password" /></td>
              <td><input name="new_password" type="password" placeholder="請輸入新密碼" autocomplete="new-password" /></td>
              <td><input name="rep_password" type="password" placeholder="請再次輸入新密碼" autocomplete="new-password" /></td>
              <td style="text-align:center">
                <button  class="btn btn-sm clearbtn">更新密碼</button>
                <button type="button" class="btn btn-sm sendbtn" data-toggle="modal" data-id="{{$vo->id}}" data-target="#exampleModal" data-whatever="權限修改">權限修改</button>
                <a class="btn btn-sm whitebtn"  href="{{url('admin/del')}}?id={{$vo->id}}">刪除</a>
              </td>
              
            </tr>    
          </form>
          @endforeach
        </tbody>
      </table>
    </div>
    <div class="text-center">
      {{$data['accounts']->links('pagination::default')}}
    </div>
    <div class="admin-content width-80 mt-4">
      <h3 class="main-title" >新增帳號</h3>
      <form action="{{url('admin/add')}}" method="post" class="account-add">
        @csrf
        <ul class="head">
          <li>
            <div class="name">名稱：</div>
            <div class="cont"><input class="form-control" name="name" type="text" placeholder="請輸入名稱" autocomplete="new-password"></div>
          </li>
          <li>
            <div class="name">帳號：</div>
            <div class="cont"><input class="form-control" name="account" type="text" placeholder="請輸入帳號" autocomplete="new-password"></div>
          </li>
          <li>
            <div class="name">密碼：</div>
            <div class="cont"><input class="form-control" name="password" type="password" placeholder="請輸入密碼" autocomplete="new-password"></div>
          </li>
          <li>
            <div class="name">信箱：</div>
            <div class="cont"><input class="form-control" name="email" type="email" placeholder="請輸入信箱" autocomplete="new-password"></div>
          </li>
          <li>
            <button class="btn sendbtn ">確認</button>
          </li>
        </ul> 
        <ul class="content">
          @foreach($data['show_list'] as $sh)
          <li>
            <div class="item">
              @if(( $sh['title'] != '' ))
              <h3 class="title">{{$sh['title']}}</h3>
              @endif
              <div class="d-flex flex-wrap ">
                <div class="main-item">
                  <label class="f_swi mb-0" style="display:inline-flex; margin-left: 9px;">
                    <input type="checkbox" id="{{$sh['name']}}" class="{{$sh['name']}}_all">
                    <span class="f_sli"><span class="X">X</span><span class="O">O</span></span>
                  </label>
                  <label class="mb-0 ml-2" for="{{$sh['name']}}">{{$sh['name']}}</label>
                </div>
                <div class="sunitems">
                  <span class="mr-1 name">次項：</span>
                  @foreach($sh['sub'] as $sub)
                  <span style="display:inline-flex; align-items: center;margin-bottom:5px">
                    <label class="f_swi mb-0" style="display:inline-flex;margin-left: 9px;margin-right:5px;">
                      <input type="checkbox" name="purview[{{$sh['id']}}][]" value="{{$sub['id']}}" id="{{$sub['show_name']}}"
                        class="{{$sh['name']}}_box">
                      <span class="f_sli"><span class="X">X</span><span class="O">O</span></span>
                    </label>
                    <label class="mb-0"  for="{{$sub['show_name']}}">{{$sub['show_name']}}</label>
                  </span>
                  @endforeach
                </div>
              </div>
            </div>
          </li>
          @endforeach
        </ul>
      </form>
    </div>
  </div>
  <div class="modal large_modal fade bd-example-modal-lg" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
    <div class="modal-header">
      <h5 class="modal-title" id="exampleModalLabel">New message</h5>
      <button type="button" class="close" data-dismiss="modal" aria-label="Close">
      <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form id="update" action="{{url('admin/update_purview')}}" method="post">
          @csrf
          <input name="up_id" type="hidden" vlaue="">
          <table style="margin:0; width: 100%;" id="update_purview">
            <tr></tr>
            <tr>
              <td colspan="5" style="text-align:left;">
              @foreach($data['show_list'] as $sh)
                @if(( $sh['title'] != '' ))
                <h5 class="title border-bottom mb-0 p-2 font-weight-bold mb-3">{{$sh['title']}}</h5>
                @endif
                <div class="d-flex flex-wrap mb-3">
                  <div style="flex:0 0 180px; display: flex;">
                    <label class="f_swi mb-0" style="display:inline-flex;margin-left: 9px;margin-right:5px;">
                      <input type="checkbox" id="update_purview{{$sh['name']}}" class="update_purview{{$sh['name']}}_all">
                      <span class="f_sli"><span class="X">X</span><span class="O">O</span></span>
                    </label>
                    <label class=mb-0 for="update_purview{{$sh['name']}}">{{$sh['name']}}</label>
                  </div>

                  <div style="flex:0 0 calc(100% - 180px);display: flex;align-items: center; flex-wrap: wrap;">
                    <span style="display:inline-flex; align-items: center;margin-bottom:5px">次項：</span>
                    @foreach($sh['sub'] as $sub)
                    <span style="display:inline-flex; align-items: center;margin-bottom:5px">
                      <label class="f_swi mb-0" style="display:inline-flex;margin-left: 9px;">
                        <input type="checkbox" name="update_purview[{{$sh['id']}}][]" value="{{$sub['id']}}"
                          id="update_purview{{$sub['id']}}" class="update_purview{{$sh['name']}}_box">
                        <span class="f_sli"><span class="X">X</span><span class="O">O</span></span>
                      </label>
                      <label class="mb-0 ml-2" for="update_purview{{$sub['id']}}">{{$sub['show_name']}}</label>
                    </span>
                    @endforeach
                  </div>
                </div> 
              @endforeach
              </td>
            
            </tr>
            
          </table>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
      <button type="button" class="btn btn-primary" onclick="update.submit();">確認</button>
    </div>
    </div>
  </div>
  </div>
@endsection

@section('ownJS')
  <script>
    function update_purview(){
      var checked = $('input[name="update_purview"]').val();  
      console.log(checked);
    }//update.submit();

    function current_change(x){
      $('input[name="current"]').prop("checked", false); 
      $('.current_'+x).prop("checked", true); 
      $.ajax({
        url: "{{url('admin/current_change')}}",
        type: 'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
          },
        data: {
          id:x
        },
        success: function(response) {
          alert(response.message);
        },
        error: function(xhr) {
          console.log(xhr);
        }
      });
    }

    function update_email(x){
      $.ajax({
        url: "{{url('admin/emailUpdate')}}",
        type: 'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
          },
        data: {
          id:x,
          email:$('.email'+x).val(),
        },
        success: function(response) {
          alert(response.message);
        },
        error: function(xhr) {
          console.log(xhr);
        }
      });
    }

    var purview = {};
    @foreach($data['accounts']->items() as $vo)
      purview["{{$vo->id}}"] = JSON.parse("{{$vo->purview}}".replace(/&quot;/g,'"').trim())
    @endforeach

    $('#exampleModal').on('show.bs.modal', function (event) {
      var button = $(event.relatedTarget)
      var recipient = button.data('whatever')
      var id = button.data('id')
      var modal = $(this)
      modal.find('.modal-title').text(recipient)
      console.log(purview[id]);

      $('input[name^="update_purview"]').prop("checked", false);    
      $('input[name="up_id"]').val(id);

      $.each( purview[id], function( key, value ) {
        $.each( purview[id][key], function( sub_key, sub_value ) {
          $("#update_purview"+sub_value).prop("checked", true);  
        });
      });

      @foreach($data['show_list'] as $sh)
        var ed = true;
        $(".update_purview{{$sh['name']}}_box").each(function(){
          if(this.checked != ed){
            ed = this.checked ;
            return false;
          }
        });

        $(".update_purview{{$sh['name']}}_all").prop("checked", ed);
      @endforeach
    })

    $(function(){
      @foreach($data['show_list'] as $sh)
        $(".{{$sh['name']}}_all").click(function(){
          if(this.checked){   
            $(".{$sh.name}_box").prop("checked", true);  
          }else{   
            $(".{$sh.name}_box").prop("checked", false);
          }   
        })

        $(".{{$sh['name']}}_box").click(function(){

          var ed = true;
          $(".{{$sh['name']}}_box").each(function(){
            if(this.checked != ed){
              ed = this.checked ;
              return false;
            }
          });   
          $(".{{$sh['name']}}_all").prop("checked", ed);
        })

        $(".update_purview{{$sh['name']}}_all").click(function(){
          if(this.checked){   
            $(".update_purview{{$sh['name']}}_box").prop("checked", true);  
          }else{   
            $(".update_purview{{$sh['name']}}_box").prop("checked", false);
          }   
        })

        $(".update_purview{{$sh['name']}}_box").click(function(){
          var ed = true;
          $(".update_purview{{$sh['name']}}_box").each(function(){
            if(this.checked != ed){
              ed = this.checked ;
              return false;
            }
          });

          $(".update_purview{{$sh['name']}}_all").prop("checked", ed);
        })
      @endforeach
    });

    function openList() {
      $('#backstageListli').click();
    }
  </script>
@endsection
