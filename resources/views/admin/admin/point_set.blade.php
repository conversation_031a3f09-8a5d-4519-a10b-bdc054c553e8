@extends('admin.Public.aside')
@section('title'){{Lang::get('H行銷項目')}} - {{Lang::get('消費點數設定')}}@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('H行銷項目')}}</li>
            <li><a href="{{url('Admin/point_set')}}">{{Lang::get('消費點數設定')}}</a></li>
        </ul>
        <div  class="width-50 admin-content">
            <h3 class="main-title">
                {{Lang::get('消費點數設定')}}
            </h3>
            <form action="{{url('Admin/point_set_update')}}" method="post">    
                @csrf
                <ul class="point_set">
                    @if($data['admin_type']=='admin')
                    <!-- <li>
                        <div class="text">
                            <span class="name">{{Lang::get('幾年到期')}}：</span>
                            <input name="limit_time" type="number" value="{{$data['limit_time']}}" min="1">  
                            <ul class="text-danger d-inline-block">
                                {!! Lang::get('幾年到期文字說明') !!}
                            </ul>
                        </div>
                    </li> -->
                    @endif
                    <!-- <li>
                        <div class="text">
                            {{Lang::get('滿多少元送一點')}}： {{config('extra.shop.dollar_symbol')}}
                            @if($data['admin_type']=='distribution')
                                {{$data['point_rate']}}
                            @else
                                <input name="point_rate" type="text" value="{{$data['point_rate']}}" placeholder="">
                            @endif
                            <span class="text-danger remark d-inline">(無條件捨去)</span>
                        </div>
                    </li> -->
                    <li id="treeMV" class="border-top mt-4 pt-2">
                        <div class="text">
                            <div class="name mb-2">
                            {{Lang::get('可『使用』點數分館')}}：
                                <span class="text-danger remark d-inline">({{Lang::get('無勾選視為允許全部分館')}})</span>
                            </div>
                            <div v-for="product in tree_list" class="item_tree">
                                <input type="checkbox" name="use_product[]" :value="product.id" :id="'product_'+product.id" v-model="use_product">
                                <label :for="'product_'+product.id" v-text="product.title"></label>
                            </div>
                        </div>
                    </li>
                    <li class="d-flex justify-content-center mt-2">
                        <button class="btn sendbtn">{{Lang::get('儲存')}}</button>
                    </li>
                </ul>
            </form>
        </div>
    </div>
@endsection
@section('ownJS')
<script>
    var tree_data = {
        tree_list: [
            /*參考資料格式*/
            // {'title':"分館", 'product_num':5, 'id':'', 'order_id':0, 'content':[/*下一層內容*/]},
        ],
        use_product: [],
    }
    var use_product ="{{$data['use_product']}}";
    
    use_product = use_product.replace(/&quot;/g,'"');

    tree_data.use_product = JSON.parse(use_product);

    function get_tree_data(){
        $.ajax({
            url: "{{url('Layertree/get_product_tree')}}", //請求的url地址
            dataType: "json", //返回格式為json
            type: "GET", //請求方式
            success: function(req) {
                console.log(req)
                tree_data['tree_list'] = req;
            },
            error: function() {
                //請求出錯處理
            }
        });
    }
    get_tree_data();

    var treeMV = new Vue({
        el: '#treeMV',
        data: tree_data,
        computed: {

        },
        methods: {
        }
    });
</script>
@endsection