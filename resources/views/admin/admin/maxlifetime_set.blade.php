@extends('admin.Public.aside')
@section('title')J參數設定區 - 自動登出設定@endsection
@section('css')
  <style type="text/css">
    table {
      margin: 30px;
      width: 450px;
    }
    table td {
      padding: 10px;
    }
    input{
      width:84.9%;
    }
    table tr:nth-child(3) td,
    table tr:nth-child(4) td,
    table tr:nth-child(5) td,
    table tr:nth-child(6) td {
      border:none;
    }            
  </style>
@endsection
@section('content')
  <div id="content">
    <p id="title">J參數設定區 > 自動登出設定</p>
    <div class="d-flex justify-content-center">
      <form action="{{url('Admin/maxlifetime_update')}}" method="post">
        @csrf
        <table>
          <tr>
            <td colspan="2">自動登出設定</td>
          </tr>
          <tr>
            <td colspan="2">秒數：<input name="value1" type="text" value="{{$data['auto_logout_time']}}" placeholder="秒數"></td>
          </tr>
          <tr>
            <td colspan="2" style="text-align:right"><button>確認</button></td>
          </tr>                
        </table>
      </form>
    </div>
  </div>
@endsection
@section('ownJS')
<script>
  function openList() {
    $('#backstageListli').click();
  }
</script>
@endsection