@extends('admin.Public.aside')
@section('title')J參數設定 - LOGO管理@endsection
@section('css')
    <style type="text/css">
        .delbutton{
            height: 20px;
            width: 100px;
            display:flex;
            margin-bottom:5px;
        }
    </style>
@endsection

@section('content')
    <form action="{{url('admin/admin_info_update')}}" name="productForm" method="post" enctype="multipart/form-data">
        @csrf
        <input name="id" value="1" type="hidden" >
        <input name="_token" value="{{csrf_token()}}" type="hidden" >
        <div id="content">
            <ul id="title" class="brand-menu" >
                <li><a href="###">J參數設定區</a></li>
                <li><a href="###">LOGO管理</a></li>
            </ul>
            <div class="frame">
                <button type="submit" class="btn sendbtn">儲存內容</button>
            </div>
            <table class="table table-rwd">
                <tr>
                    <td>
                        客戶名稱：
                        <input class="form-control" type='text' name="customer_name" value="{{$data['admin_info']['customer_name']}}">
                    </td>
                    <td>
                        <div class="d-flex flex-wrap justify-content-between">
                            <div>
                                後台logo：
                                <div class="img-box delbutton" onclick="delImg('customer_logo')">
                                    <span class="bi bi-trash"></span>
                                </div>

                                <div class="img-box" style="height: 150px; width: 100px; display:flex;">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="customer_logo" accept="image/*" onclick="ChangeImages()">
                                    @if($data['admin_info']['customer_logo'])
                                    <img class="preview" id="customer_logo" name="customer_logo" src="{{__UPLOAD__}}{{$data['admin_info']['customer_logo']}}"/>
                                    @endif
                                </div>
                            </div>
                            <div>
                                分頁縮圖：
                                <div class="img-box delbutton" onclick="delImg('favicon')">
                                    <span class="bi bi-trash"></span>
                                </div>

                                <div class="img-box" style="height: 150px; width: 100px; display:flex;">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="favicon" accept="image/*" onclick="ChangeImages()">
                                    @if($data['admin_info']['favicon'])
                                    <img class="preview" id="favicon" name="favicon" src="{{__UPLOAD__}}{{$data['admin_info']['favicon']}}"/>
                                    @endif
                                </div>
                                <p>建議大小：50*50px</p>
                            </div>
                            <div>
                                成功提示訊息圖片：
                                <div class="img-box delbutton" onclick="delImg('success_logo')">
                                    <span class="bi bi-trash"></span>
                                </div>

                                <div class="img-box" style="height: 150px; width: 100px; display:flex;">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="success_logo" accept="image/*" onclick="ChangeImages()">
                                    @if($data['admin_info']['success_logo'])
                                    <img class="preview" id="success_logo" name="success_logo" src="{{__UPLOAD__}}{{$data['admin_info']['success_logo']}}"/>
                                    @endif
                                </div>
                                <p>建議大小：490*400px</p>
                            </div>
                            <div>
                                失敗提示訊息圖片：
                                <div class="img-box delbutton" onclick="delImg('error_logo')">
                                    <span class="bi bi-trash"></span>
                                </div>
                                <div class="img-box" style="height: 150px; width: 100px; display:flex;">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="error_logo" accept="image/*" onclick="ChangeImages()">
                                    @if($data['admin_info']['error_logo'])
                                    <img class="preview" id="error_logo" name="error_logo" src="{{__UPLOAD__}}{{$data['admin_info']['error_logo']}}"/>
                                    @endif
                                </div>
                                <p>建議大小：490*400px</p>
                            </div>
                        </div>
                    </td>
                </tr>
                @if($data['admin']['account'] == 'photonic')
                    <tr>
                        <td>
                            系統開發：
                            <input class="form-control" type='text' name="system_name" value="{{$data['admin_info']['system_name']}}">
                        </td>

                        <td>
                            <div class="d-flex justify-content-between">
                                <div>
                                    系統開發logo：
                                    <div class="img-box delbutton" onclick="delImg('system_logo')">
                                        <span class="bi bi-trash"></span>
                                    </div>
                                    <div class="img-box" style="height: 150px; width: 100px; display:flex;">
                                        <span class="bi bi-pencil-square"></span>
                                        <input type='file' class="upl" name="system_logo" accept="image/*" onclick="ChangeImages()">
                                        @if($data['admin_info']['system_logo'])
                                        <img class="preview" id="system_logo" name="system_logo" src="{{__UPLOAD__}}{{$data['admin_info']['system_logo']}}"/>
                                        @endif
                                    </div>
                                    <!-- <p>建議大小：540*540</p> -->
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            網路行銷：
                            <input class="form-control" type='text' name="marketing_name" value="{{$data['admin_info']['marketing_name']}}">
                        </td>
                        <td>
                            <div class="d-flex justify-content-between">
                                <div>
                                    網路行銷logo：
                                    <div class="img-box delbutton" onclick="delImg('marketing_logo')">
                                        <span class="bi bi-trash"></span>
                                    </div>

                                    <div class="img-box" style="height: 150px; width: 100px; display:flex;">
                                        <span class="bi bi-pencil-square"></span>
                                        <input type='file' class="upl" name="marketing_logo" accept="image/*" onclick="ChangeImages()">
                                        @if($data['admin_info']['marketing_logo'])
                                        <img class="preview" id="marketing_logo" name="marketing_logo" src="{{__UPLOAD__}}{{$data['admin_info']['marketing_logo']}}"/>
                                        @endif
                                    </div>
                                    <!-- <p>建議大小：540*540</p> -->
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            網址：
                            <input class="form-control" type='text' name="url" value="{{$data['admin_info']['url']}}">
                        </td>
                        <td>
                            聯絡電話：
                            <input class="form-control" type='text' name="tel" value="{{$data['admin_info']['tel']}}">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            信箱：
                            <input class="form-control" type='text' name="email" value="{{$data['admin_info']['email']}}">
                        </td>
                        <td>
                            地址：
                            <input class="form-control" type='text' name="address" value="{{$data['admin_info']['address']}}">
                        </td>
                    </tr>
                @endif
            </table>
        </div>
        <input type='hidden' name="del_customer_logo" value="0">
        <input type='hidden' name="del_system_logo" value="0">
        <input type='hidden' name="del_marketing_logo" value="0">
        <input type='hidden' name="del_favicon" value="0">
        <input type='hidden' name="del_success_logo" value="0">
        <input type='hidden' name="del_error_logo" value="0">
    </form>
@endsection

@section('ownJS')
<script src="/public/static/admin/js/action.js"></script>
    <script>
        function delImg(id) {
            $("#" + id).attr('src', '');
            $("input[name='del_" + id + "']").val(1);
        }

        $("img").load(function () {
            if($(this).attr('src') != ''){
                $("input[name='del_" + $(this).attr('name') + "']").val(0);
            }
        });
    </script>
@endsection