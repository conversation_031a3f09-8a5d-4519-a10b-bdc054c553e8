@extends('admin.Public.aside')

@section('title')E圖文編輯項目 > {{$data['frontend_menu'][$data['table_name']]['name']}}@endsection



@section('content')
    <iframe id="boxFormIframe" name="boxFormIframe" style="display: none;"></iframe>

    <!-- 新增修改活動開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">活動內容</h5>
                    
                </div>
                <form name="boxForm" :action="action" method="post" target="boxFormIframe" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-4 ">
                                <div class="img-box" >
                                    <span class="bi bi-image"></span>
                                    <input type='file' ref="img" class="upl" name="image" accept="image/*" @change="previewImg">
                                    <img class="preview w-110" :src="src"/>
                                </div>
                                <p class="remark">建議大小：700*475</p>    
                            </div>
                            <div class="col-8">
                                <p><span class="remark">排序(越小越前面)：</span>
                                    <input type="number" name="orders" v-model="orders" class="form-control">
                                </p>
                            </div>
                            <div class="col-12 mt-4">
                                <span class="remark">標題：</span>
                                <input type="text" name="title" v-model="title" class="form-control">
                            </div>
                            <div class="col-12">
                                <span class="remark">內文：</span>
                                <textarea v-model="content" class="form-control" rows="4"></textarea>
                                <input name="content" type="hidden" v-model="contentNl2br">                 
                            </div>
                            <div class="col-12">
                                <span class="remark">URL：</span>
                                <input type="text" name="url" v-model="url" class="form-control">    
                            </div>
                        </div>
                       
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="id" v-model="id">
                        <button type="button" class="btn sendbtn" @click="formSubmit">儲存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- 新增修改活動結束 -->

    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">E圖文編輯項目</a></li>
            <li><a href="###" onclick="javascript:location.href='index'">{{$data['frontend_menu'][$data['table_name']]['name']}}</a></li>
            @if($data['searchKey']!="")
                <li>搜尋：{{$data['searchKey']}}</li>
            @endif
        </ul>    
       
        <div class="searchbox">
            <form action="" name="searchForm" method="get" name="searchForm"  class="searchKeyBox">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1" placeholder="搜尋標題">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

      

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn"  onclick="newBlock();">
                <i class="bi bi-plus-lg add small"></i>  新增        
            </a>
            <div class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a onclick="multiOnline();">
                        <p class="mb-0">上架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                  
                    <a onclick="multiOffline();">
                        <p class="mb-0">下架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                 
              
                    <a onclick="multiDelete();" class="mt-2 border-top">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                    
                </div>
            </div>
            
            <!-- 編輯結束 -->
        </div>
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width: 768px;">
                <thead>
                    <tr>
                        <th style="width: 20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=activityCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 80px">上下架</th>
                        <th style="width: 150px">排序(越小越前面)</th>
                        <th style="width: 200px">預覽圖片</th>
                        <th>標題</th>
                        <th style="width: 60px">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['activity']) == true)
                    <td colspan='6'>沒有數據</td>
                    @else
                    @foreach($data['activity'] as $vo)
                    <tr id="activity_{{$vo->id}}">
                        <td><input type="checkbox" class="activityCheckbox" alt="{{$vo->id}}"></td>
                        <td>
                            <label class="switch">
                                <input type="checkbox" v-model="online">
                                <span class="slider round"></span>
                            </label>
                        </td>
                        <td><span v-text="orders"></span></td>
                        <td>
                            <div class="img-box" @click="openBox">
                                <p style="position:absolute;">700*475</p>
                                <img class="preview" :src="src"/>
                            </div>
                        </td>
                        <td><a href="###" @click="openBox" v-text="title"></a></td>
                        <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
        </div>
        
        <div class="text-center">
            {{$data['activity']->links('pagination::default')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
            $('.edit-item').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Activity/cellCtrl')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {
            title: "", src: "", content: "", id: 0, orders:0,
            action: "", url: "",
            caller: null
        }
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
                contentNl2br: function () {
                    return this.content.replace(/\n/g, '<br>');
                }
            },
            methods: {
                formSubmit: function () {
                    $('#block_block').show();
                    setTimeout(function(){ document.boxForm.submit(); }, 50);
                },
                previewImg: function () {
                    console.log(this.$refs.img.files);
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        Box.src = e.target.result;
                    }
                    reader.readAsDataURL(this.$refs.img.files[0]);
                },
                updateCallerData: function () {
                    this.caller.src = this.src;
                    this.caller.title = this.title;
                    this.caller.orders = this.orders;
                    this.caller.url = this.url;
                    this.caller.content = this.content;
                    $('#functionModal').modal('hide');
                }
            }
        });

        $('#boxFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                // alert("上傳成功");
                // if(BoxVM.caller == 'new'){
                //     location.reload();
                // }else{
                //     BoxVM.updateCallerData();
                // }
                location.reload();
            }else{
                alert("上傳失敗");
                console.log($(this).contents().find('body').text());
            }
            $('#block_block').hide();
        });

        ///////andy/////多行文字串//////
        function heredoc(fn) {
            return fn.toString().replace(/[\\]/g,"") + '\n'
        }
        ///////////////////////////////
        @if(empty($data['activity']) == false)
        @foreach($data['activity'] as $vo)
            ///////andy/////多行文字串////////////////////
            var tmpl = heredoc(function(){
                `{!! str_replace('/','\\/',addslashes($vo->content)) !!}`
            });
            tmpl = tmpl.split('`');
            delete tmpl[0];
            var lastnum = tmpl.length -1;
            delete tmpl[lastnum];
            // console.log(tmpl);
            /////////////////////////////////////////////

            var activity_{{$vo->id}} = {
                id: "{{$vo->id}}",
                title: "{{$vo->title}}",
                orders: "{{$vo->orders}}",
                src: "{{__UPLOAD__}}{{$vo->pic}}",
                url: "{{$vo->url}}",
                content: tmpl.join(''),
                online: +"{{$vo->online}}",
                action: "{{url('Activity/update')}}"
            }
            var activity_{{$vo->id}}_VM = new Vue({
                el: '#activity_{{$vo->id}}',
                data: activity_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    }
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.title = this.title;
                        BoxVM.orders = this.orders;
                        BoxVM.src = this.src;
                        BoxVM.content = this.content;
                        BoxVM.url = this.url;
                        BoxVM.action = this.action;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    }
                }
            });
        @endforeach
        @endif

        function newBlock(){
            BoxVM.id = "";
            BoxVM.title = "";
            BoxVM.orders = 0;
            BoxVM.src = "";
            BoxVM.url = "";
            BoxVM.content = "";
            BoxVM.action = "{{url('Activity/doCreate')}}";
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.activityCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id){
            if(confirm("確定刪除?")){
                location.href = "{{url('Activity/delete')}}?id="+id;
            }
        }

        function multiDelete() {
            if(confirm("確定刪除?")){
                var form = document.createElement("form");

                form.action = "{{url('Activity/multiDelete')}}";
                form.method = "post";

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";

                form.appendChild(multiId);
                document.body.appendChild(form);
                form.submit();

                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('activity_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('activity_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }
    </script>
@endsection