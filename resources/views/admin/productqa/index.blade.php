@extends('admin.Public.aside')
@section('title')G功能應用項目 > 商品問答@endsection
@section('css')
@endsection
@section('content')
   	<div class="content">
     
		<ul id="title" class="brand-menu" onclick="javascript:location.href='index'" >
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">商品問答</a></li>
            @if($data['searchKey'] !='')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>
        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
				<input type="text" name="searchKey" class="form-control mr-1 text-center" style="width:260px;"  placeholder="請輸入會員名稱/產品網址/問題/回覆">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <div class="edit" onclick="multiDelete();">
				<a >
					刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
				</a>
			</div>
            <!-- 編輯結束 -->
        </div>

        <!--表格 開始-->
		<div class="edit_form" >
			<table class="table table-rwd" style="min-width:992px;">
				<thead>
					<tr>
						<th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" style="cursor:pointer;"
								   v-model="allSel" :true-value="true" :false-value="false" @click="selAll()"></th>
						<th>客戶名稱 </th>
						<!-- <th style="cursor:pointer">產品</th>  -->
						<th style="cursor:pointer">產品網址</th> 
						<th>問題</th>
						 <th>發問時間</th>
						<th></th>
						<th style="width: 60px;" onclick="multiDelete();">刪除</th>
					</tr>
		
				</thead>
				<tbody>
					<tr v-for="vo in model" :id="'qa_' + vo.prod_qa_id">
						<td><input 	type="checkbox" class="qaCheckbox" :alt="vo.prod_qa_id" 
									v-model="vo.checkbox" :true-value="true" :false-value="false"></td>
						<td v-text="vo.name"></td>
		
						<td><a :href="'{{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}/' + vo.prod_addr" target="_blank" v-text="vo.prod_addr"></a></td>
					   
						<td>
							<span v-html="vo.prod_q"></span>
							<span v-if="vo.new" style="color: red">new!</span>
						</td>
						<td v-text="vo.q_datetime"></td>
						<td>
							<button type="button" class="btn btn-primary" 
								@click="openModal(vo.prod_qa_id, vo.prod_q, vo.prod_a, vo.uid)">回覆
							</button>
						   </td>
						<td><span class="bi bi-trash" @click="delete_one(vo.prod_qa_id)"></span></td>
					</tr>
				</tbody>
				
			
			</table>
		</div>
       
        <div class="text-center">
            {{$data['qa']->links('pagination::default')}}
        </div>
    </div>

	<!-- Modal -->
	<div class="modal fade" id="myModal" role="dialog">
		<div class="modal-dialog">

			<!-- Modal content-->
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title" v-text="modal.prodQaId + modal.title"></h4>
					<button type="button" class="close" data-dismiss="modal">&times;</button>
				</div>
				<div class="modal-body">
					<p>問題</p>
					<p v-html="modal.q"></p>
					<p>回覆</p>
					<p><textarea v-model="modal.a" class="form-control" style="resize: vertical;"></textarea></p>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn"  @click="closeModal()">關閉</button>
					<button type="button" class="btn btn-success"  @click="qaUpdate()">更新</button>
				</div>
			</div>
		</div>
	</div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script>  
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });


        function getMultiId() {
            var multiIdArray = [];
            $('.qaCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

		function multiDelete() {
			if(confirm("確定刪除?")){
				var form = document.createElement("form");
				form.action = "{{url('Productqa/multiDelete')}}";
				form.method = "post";

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId 		= document.createElement("input");
				multiId.value 	= JSON.stringify(getMultiId());
				multiId.name 	= "id";

				form.appendChild(multiId);
				document.body.appendChild(form);
				form.submit();
			}
		}
       
	    ///////////////
	    //   Vue
	    ///////////////
		var qa_json="{{$data['qa_json']}}".replace(/&quot;/g,'"').trim();
	    qa_json = JSON.parse(qa_json);
	    for (var prop in qa_json) {
			qa_json[prop].checkbox = false;
		}

	    var content_area_data = {
	    	model: qa_json,
	    	modal: {prodQaId:"", title:"", q:"", a:""},
	    	allSel: false,
	    };
	    var content_areaVM = new Vue({
	    	el: '#content_area',
	    	data: content_area_data,
	    	methods: {
	    		openModal: function(id,q,a,uid){
					this.modal.prodQaId = id;
					// this.modal.title 	= title;
					this.modal.q 		= q;
					this.modal.a 		= a;
					this.modal.uid 		= uid;
		        	$("#myModal").modal({backdrop: "static"});
		        },
		        closeModal: function(){
		        	$("#myModal").modal("hide");
		        },
		        qaUpdate: function(){
		        	self = this;
					// $("#myModal").modal("hide");
					$.ajax({
						type: "post",
						headers: {
							'X-CSRF-Token': csrf_token 
						},
						url: "{{url('Productqa/update')}}",
						data: {
							prod_qa_id :self.modal.prodQaId,
							prod_q 	:self.modal.q,
							prod_a 	:self.modal.a,
							uid		:self.modal.uid
						},
						success: function(resp){
							console.log(resp);
							if(resp.code==0){
								Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", "bg-danger"]});
							}else{
								Vue.toasted.show('更改成功',{duration:1500, className: ["toasted-primary", "bg-success"]});
								setTimeout(()=>{window.location.reload()}, 300);
							}
						}
					});
				},
				selAll: function(){
					self = this;
					setTimeout(function(){
						for (var prop in self.model) {
							self.model[prop].checkbox = self.allSel;
						}
					}, 50);
				},
				delete_one: function(prod_qa_id){
					if(confirm("確定刪除?")){
						location.href = "{{url('Productqa/delete')}}?id=" + prod_qa_id;
					}
				},
	    	},
	    });
    </script>
@endsection