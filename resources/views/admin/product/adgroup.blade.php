@extends('admin.Public.aside')
@section('title')首頁顯示設定 - {{$data['product']['title']}} - 分類主題館@endsection

@section('content')
    <form action="{{url('product/update')}}" name="productForm" method="post" enctype="multipart/form-data">
        @csrf
        <div id="content">
            <input type="hidden" name="id" value="{{$data['product']['id']}}"/>
            <ul id="title" class="brand-menu" >
                <li><a href="###">F商品管理區</a></li>
                <li><a href="###">分類主題樹</a></li>
                <li><a href="###">{{$data['product']['title']}}</a></li>
            </ul>
            <div class="admin-content">
                <h3 class="main-title">分館修改</h3>
                <span class="d-inline-block  position-relative mb-1">
                    <div id="item-title" class="border form-control mr-2" style="background-color: #F6F6F6;" onclick="Show('.change-title')">{{$data['product']['title']}} </div>
                    <!-- 更改標題 -->
                    <div class="change-title none">
                        <p class="mb-2">分類主題館名稱</p>
                        <input id="item-name" name="title" type="text" value="{{$data['product']['title']}}" class="mb-1 form-control border" autofocus>
                        <div class="mt-2">
                            <a class="btn sendbtn mr-1" onclick="productForm.submit();" >儲存</a>
                            <a class="btn clearbtn" onclick="Show('.change-title')" >取消</a>
                        </div>
                    </div>
                </span>
   
                <div class="form-group d-inline-flex align-items-center mb-1" >
                    <label class="name mb-0 mr-2">首頁顯示</label>
                    <select class="form-control" id="radioDiv" style="width: auto;">
                      <option name="show-btn" id="open" value="1">開啟</option>
                      <option name="show-btn" id="close" value="0">隱藏</option>
                      <option name="show-btn" id="r_close" value="2">關閉</option>
                    
                    </select>
                </div>

                <div class="order">
                    <span class="name">排序</span><input class="form-control mr-1" type="number" name="order_id" value="{{$data['product']['order_id']}}">
                    <span class="remark text-danger">(排序越小越前面)</span>
                </div>
            </div>
            <!-- 更改標題 結束 -->

            <div class="{{App\Services\CommonService::compare_return($data['product']['distributor_id'],'0','d-none', true)}}">
                <!-- 首頁分館顯示設定 -->
                <h3 class="main-title mt-4">首頁分館廣告 <span class="text-danger d-inline remark">*連結請插入商品詳細內容頁網址</span></h3>
                <div class="admin-content">
                    <div class="index-probox d-flex flex-wrap">
                        <div class="col-md-4">
                            
                            <div class="img-box">
                                <span class="bi bi-pencil-square"></span>
                                <input type='file' class="upl" name="index_adv01_pic" accept="image/*" onclick="ChangeImages()">
                                <img class="preview" id="index_adv01_pic" name="index_adv01_pic" src="{{$data['product']['index_adv01_pic']}}"/>
                            </div>
                            <p class="remark text-danger mb-0">建議大小：540*540</p>
                            <div class="infobox">
                                <label class="mb-0">URL</label><input type="text" name="index_adv01_link" value="{{$data['product']['index_adv01_link']}}">
                            </div>
                            <div class="btn-sm sendbtn delbutton mt-2" onclick="delImg('index_adv01_pic')">
                                刪除
                            </div>
                        </div>
                        <div class="col-md-8 index-pro-items">
                            <div class="item">
                                
                                <div class="img-box">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="index_adv02_pic" accept="image/*" onclick="ChangeImages()">
                                    <img class="preview" id="index_adv02_pic" name="index_adv02_pic" src="{{$data['product']['index_adv02_pic']}}"/>
                                </div>
                                <div class="url-box">
                                    <p class="remark text-danger mb-0">建議大小：282*282</p>
                                    <div class="infobox">
                                        <label class="mb-0">URL</label><input type="text" name="index_adv02_link" value="{{$data['product']['index_adv02_link']}}">
                                    </div>
                                </div>
                                <div class="btn-sm sendbtn delbutton mt-2" onclick="delImg('index_adv02_pic')">
                                    刪除
                                </div>
                            </div>
                            <div class="item">
                                
                                <div class="img-box">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="index_adv04_pic" accept="image/*" onclick="ChangeImages()">
                                    <img class="preview" id="index_adv04_pic" name="index_adv04_pic" src="{{$data['product']['index_adv04_pic']}}"/>
                                </div>
                                <div class="url-box">
                                    <p class="remark text-danger mb-0">建議大小：282*282</p>
                                    <div class="infobox">
                                        <label class="mb-0 ">URL</label><input type="text" name="index_adv04_link" value="{{$data['product']['index_adv04_link']}}">
                                    </div>
                                </div>
                               
                                <div class="btn-sm sendbtn delbutton mt-2" onclick="delImg('index_adv04_pic')">
                                    刪除
                                </div>
                            </div>
                             <div class="item">
                                
                                <div class="img-box">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="index_adv06_pic" accept="image/*" onclick="ChangeImages()">
                                    <img class="preview" id="index_adv06_pic" name="index_adv06_pic" src="{{$data['product']['index_adv06_pic']}}"/>
                                </div>
                                <div class="url-box">
                                    <p class="remark text-danger mb-0">建議大小：282*282</p>
                                    <div class="infobox">
                                        <label class="mb-0 ">URL</label><input type="text" name="index_adv06_link" value="{{$data['product']['index_adv06_link']}}">
                                    </div>
                                </div>
                                <div class="btn-sm sendbtn delbutton mt-2" onclick="delImg('index_adv06_pic')">
                                    刪除
                                </div>
                            </div>
                            <div class="item">
                               
                                <div class="img-box">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="index_adv03_pic" accept="image/*" onclick="ChangeImages()">
                                    <img class="preview" id="index_adv03_pic" name="index_adv03_pic" src="{{$data['product']['index_adv03_pic']}}"/>
                                </div>
                                <div class="url-box">
                                    <p class="mb-0 text-danger remark">建議大小：282*282</p>
                                    <div class="infobox">
                                        <label class="mb-0 ">URL</label><input type="text" name="index_adv03_link" value="{{$data['product']['index_adv03_link']}}">
                                    </div>
                                </div>
                                <div class="btn-sm sendbtn delbutton mt-2" onclick="delImg('index_adv03_pic')">
                                    刪除
                                </div>
                            </div>
                            <div class="item">
                              
                                <div class="img-box">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="index_adv05_pic" accept="image/*" onclick="ChangeImages()">
                                    <img class="preview" id="index_adv05_pic" name="index_adv05_pic" src="{{$data['product']['index_adv05_pic']}}"/>
                                </div>
                                <div class="url-box">
                                    <p class="remark text-danger mb-0">建議大小：282*282</p>
                                    <div class="infobox">
                                        <label class="mb-0 ">URL</label><input type="text" name="index_adv05_link" value="{{$data['product']['index_adv05_link']}}">
                                    </div>
                                </div>
                                <div class="btn-sm sendbtn delbutton mt-2" onclick="delImg('index_adv05_pic')">
                                    刪除
                                </div>
                            </div>
                            <div class="item">
                               
                                <div class="img-box">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="index_adv07_pic" accept="image/*" onclick="ChangeImages()">
                                    <img class="preview" id="index_adv07_pic" name="index_adv07_pic" src="{{$data['product']['index_adv07_pic']}}"/>
                                </div>
                                <div class="url-box">
                                    <p class="remark text-danger mb-0">建議大小：282*282</p>
                                    <div class="infobox">
                                        <label class="mb-0 ">URL</label><input type="text" name="index_adv07_link" value="{{$data['product']['index_adv07_link']}}">
                                    </div>
                                </div>
                                <div class="btn-sm sendbtn delbutton mt-2" onclick="delImg('index_adv07_pic')">
                                    刪除
                                </div>
                            </div> 
                        </div>
                    </div>
                </div>
            </div>
            <!-- 圖片更改區 -->
            <div class="row">
                <div class="col-lg-6">
                    <h3 class="main-title mt-4">分館內頁廣告 </h3>
                    <div class="admin-content ad-box">
                        <div class="index-probox ad">
                            <div class="img-box" style="padding-bottom:50px">
                                <span class="bi bi-pencil-square"></span>
                                <input type='file' class="upl" name="inner_adv01_pic" accept="image/*" onclick="ChangeImages()">
                                <img class="preview" id="inner_adv01_pic" name="inner_adv01_pic" src="{{$data['product']['inner_adv01_pic']}}"/>
                            </div>
                            <div class="url-box mt-2">
                                <p class="mb-0 text-danger remark">建議大小：1086*244</p>
                                <div class="index-url">
                                    <div class="infobox ">
                                        <label class="mb-0 ">URL</label><input type="text" style="width: 300px;" name="inner_adv01_link" value="{{$data['product']['inner_adv01_link']}}">
                                    </div>
                                    <div class="btn sendbtn mt-1" onclick="delImg('inner_adv01_pic')">
                                        刪除
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div class="index-probox ad border-top mt-3 pt-3">
                            <div class="img-box" style="padding-bottom:100px;">
                                <span class="bi bi-pencil-square"></span>
                                <input type='file' class="upl" name="inner_adv02_pic" accept="image/*" onclick="ChangeImages()">
                                <img class="preview" id="inner_adv02_pic" name="inner_adv02_pic" src="{{$data['product']['inner_adv02_pic']}}"/>
                            </div>
                            <div class="url-box mt-2">
                                <p class="mb-0 text-danger remark">建議大小：1086*358</p>
                                <div class="index-url">
                                    <div class="infobox ">
                                        <label class="mb-0">URL</label><input type="text" style="width: 300px;" name="inner_adv02_link" value="{{$data['product']['inner_adv02_link']}}">
                                    </div>
                                    <div class="btn sendbtn mt-1" onclick="delImg('inner_adv02_pic')">
                                        刪除
                                    </div>     
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <h3 class="main-title mt-4">分館文字廣告 </h3>
                    <div class="admin-content index-probox ">
    					<div class="index-probox-ad">	
                            <div class="item mb-2">
                                <div class="img-box" style="padding-bottom: 20%;">
                                    <span class="bi bi-pencil-square"></span>
                                    <input type='file' class="upl" name="pic" accept="image/*" onclick="ChangeImages()">
                                    <img class="preview" id="pic" name="pic" src="{{$data['product']['pic']}}"/>
                                </div>
                                <p class="mb-0 text-danger remark">建議大小：516*287</p>
                            </div>
    						<div class="btn sendbtn" onclick="delImg('pic')" >
    							刪除
    						</div>
    					</div>
                        <div class="">
                            <textarea id="editor" name="content">{{$data['product']['content']}}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-lg-6">
                    <h3 class="main-title mt-4">分館隱藏資訊 </h3>
                    <div class="admin-content">
                        <div class="url-box">
                            <p class="remark mb-4">分館關鍵字(輸入範例ex:德安堂,長大人)</p>
                            <div class="infobox mb-4">
                                <label class="mb-0 w-100 font-weight-bold">關鍵字</label><input type="text" name="webtype_keywords" value="{{$data['product']['webtype_keywords']}}">
                            </div>
                            <div class="infobox">
                                <label class="mb-0 w-100 font-weight-bold">分館描述</label>
                                <textarea id="editorspace" class="w-100" name="webtype_description">{{$data['product']['webtype_description']}}</textarea>
                            </div>
                        </div>
                       
                    </div>
                </div>
                <div class="col-lg-6">
                    <h3 class="main-title mt-4">分館左側ICON圖 </h3>
                    <div class="admin-content index-probox">
                        <div class="mb-2">
                            <label class="mb-0 w-100 font-weight-bold">ICON圖</label>
                            <div class="index-probox-ad">
                                <div class="item">
                                    <div class="img-box" style="padding-bottom: 20%;">
                                        <span class="bi bi-pencil-square"></span>
                                        <input type='file'  class="upl" name="pic_icon" accept="image/*" onclick="ChangeImages()">
                                        <img class="preview" id="pic_icon" name="pic_icon" src="{{$data['product']['pic_icon']}}"/>
                                    </div>
                                    <p class="mb-0 text-danger remark">建議40px*40px</p>
                                </div>
                                <div class="btn sendbtn" onclick="delImg('pic_icon')" >
                                    刪除
                                </div>
                            </div>
    						
                            
    						
    					</div>
                    </div>

                </div>
            </div>
            <div class="d-flex justify-content-center w-100">
                <a class="btn sendbtn ml-1 mr-1" onclick="productForm.submit();">儲　存</a>
                <a class="btn clearbtn ml-1 mr-1" onclick="location.reload();">還　原</a>
            </div>
        </div>

        <input type='hidden' name="del_index_adv01_pic" value="0">
        <input type='hidden' name="del_index_adv02_pic" value="0">
        <input type='hidden' name="del_index_adv03_pic" value="0">
        <input type='hidden' name="del_index_adv04_pic" value="0">
        <input type='hidden' name="del_index_adv05_pic" value="0">
        <input type='hidden' name="del_index_adv06_pic" value="0">
        <input type='hidden' name="del_index_adv07_pic" value="0">
        <input type='hidden' name="del_inner_adv01_pic" value="0">
        <input type='hidden' name="del_inner_adv02_pic" value="0">
        <input type='hidden' name="del_pic" value="0">
    	<input type='hidden' name="del_pic_icon" value="0">
    </form>
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        var editorspace;
        KindEditor.ready(function(K) {
                editor = K.create('#editor', {
                        afterBlur: function(){this.sync();},
                        langType : 'zh_TW',
                        items:['link', 'hr'],
                        width:'100%',
                        height:'100%',
                        resizeType:0
                });
                editorspace = K.create('#editor', {
                        afterBlur: function(){this.sync();},
                        langType : 'zh_TW',
                        items:[],
                        width:'100%',
                        height:'100%',
                        resizeType:0
                });
        });
      
        function cellCtrl(data) {
            $.ajax({
                url: "{{url('product/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                    },
                dataType: 'json',
                data: data,
                success: function(response) {
                    if(response.status){
                        alert('更改成功');
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        }
        $(function() {
            $(document).click(function() {
                $('.change-title').fadeOut();
            })
            $('#item-title').click(function(event) {
                event.stopPropagation();
            })
            $('.change-title').click(function(event) {
                event.stopPropagation();
            })
        });

        switch(+"{{$data['product']['online']}}"){
            case 0 :
                $('#close').click();break;
            case 1 :
                $('#open').click();break;
            case 2 :
                $('#r_close').click();break;
        }

 
        $("#radioDiv").change(function ()
            {               
                var value = $(this).val();
                var data = {
                    id: "{{$data['product']['id']}}",
                    online: value
                };
                cellCtrl(data);         
            }
        );
        function openList() {
            $('#productListli').click();
        }
        function delImg(id) {
            $("#" + id).attr('src', '');
            $("input[name='del_" + id + "']").val(1);
        }

        $("img").load(function () {
            if($(this).attr('src') != ''){
                $("input[name='del_" + $(this).attr('name') + "']").val(0);
            }
        });
    </script>
@endsection