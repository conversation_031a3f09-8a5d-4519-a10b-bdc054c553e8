@extends('admin.Public.aside')
@section('title')G功能應用項目 > 付款方式管理@endsection

@section('content')
    <div id="content">
    
        <ul id="title" class="brand-menu" onclick="javascript:location.href='index'">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">付款方式管理</a></li>
            <!-- @if($data['searchKey'] != '')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif -->
        </ul>
        <!-- <div style="padding:10px 30px;">
            <form action="" name="searchForm" method="get">
                @csrf
                <input type="text" name="searchKey" style="text-align:center" placeholder="請輸入付款方式名稱">
                <a class="button" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div> -->
      
        <!--新增與編輯-->
        <div class="frame d-flex flex-wrap">
            <span class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a onclick="multiOnline();">
                        <p class="mb-0">上架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                    <br>
                    <a onclick="multiOffline();">
                        <p class="mb-0">下架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                </div>
                <!-- 編輯結束 -->
            </span>

            <div class="text-danger remark ml-2">
                需串接金流「線上刷卡」、「分期付款」才會顯示於前台。
                @if(empty(config('control.close_function_current')['運法標籤管理']))
                    <br>如未曾自行設定過，則依平台設定
                @endif
            </div>
        </div>

        <!--表格 開始-->
        
            <table class="table table-rwd width-50">
                <thead>
                    <tr>
                        <th style="width:4%"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=pay_feeCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        @if($data['admin_type']!='distribution')
                            <th style="width:10%">排序</th>
                        @endif
                        <th style="width:10%">上下架</th>
                        <th>付款方式名稱</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['pay_fee'])==true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['pay_fee'] as $vo)
                    <tr id="pay_fee_{{$vo->id}}">
                        <td><input type="checkbox" class="pay_feeCheckbox" alt="{{$vo->id}}"></td>
                        @if($data['admin_type']!='distribution')
                            <td><input type="number" v-model = "order_id" @blur="change_order()"></td>
                        @endif
                        <td>
                            <label class="switch">
                                <input type="checkbox" v-model="online">
                                <span class="slider round"></span>
                            </label>
                        </td>
                        <td>{{$vo->name}}</td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
                
               
            </table>
            <div class="text-center">
                {{$data['pay_fee']->links('pagination::default')}}
            </div>
        
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Payfee/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                        location.reload();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        @if(empty($data['pay_fee'])==false)
        @foreach($data['pay_fee'] as $vo)
            var pay_fee_{{$vo->id}} = {
                id: "{{$vo->id}}",
                order_id: "{{$vo->order_id}}",
                name: "{{$vo->name}}",
                online: +"{{$vo->online}}"
            }
            var pay_fee_{{$vo->id}}_VM = new Vue({
                el: '#pay_fee_{{$vo->id}}',
                data: pay_fee_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    },
                },
                methods: {
                    change_order: function () {
                        blockData = {
                            id: this.id,
                            order_id: this.order_id
                        }
                        this.blockCtrl(blockData);
                    },
                }
            });
        @endforeach
        @endif

        function getMultiId() {
            var multiIdArray = [];
            $('.pay_feeCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('pay_fee_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('pay_fee_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }
    </script>
@endsection