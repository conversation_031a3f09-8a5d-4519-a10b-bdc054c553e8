<!-- html title -->
<!-- 自定義 content -->
<!-- 自定義 javascript -->
<!DOCTYPE html>
<html ng-app="app">
    <head>
        <meta charset="utf-8" />
        <!-- <title> 使用者登入 </title> -->
        
        <title>商品管理後台</title>
        <!--  seo  start-->
        <meta name="keywords" content="">
        <meta name="description" content="">
        
        <!-- favicon -->
        <!-- <link rel="shortcut icon" href="/images/logo_sm.png"> -->
        <!--  css -->
        <!-- <link href="/css/proj.css" rel="stylesheet" type="text/css" /> -->
        <link href="/public/static/admin/css/login/bootstrap.css" rel="stylesheet" type="text/css" />

        <style>
            *{
                font-family: '微軟正黑體','Noto Sans TC', sans-serif;
            }
            body{
                background-color: #eeee;
                overflow-x: hidden;
                padding-left: 1rem;
                padding-right: 1rem;
            }
            .admin-login-box{
                width: 100%;
                height: 100%;
            }
            .admin-login-box h1.title{
                text-align: center;
                font-size: 1.5rem;
                margin-bottom: 1rem;
                font-weight: 500;
            }
            .admin-login-box .center{
                display: flex;
                align-items: center;
                width: 100%;
                height: 100%;
            }
            .profile-img-card {
                width: 100%;
                height: auto;
                margin: 0 auto;
                display: block;
                max-width: 270px;
                margin-bottom: 2rem;
            }
            .admin-login-box .card{
                padding: 4rem;
                /* background: rgba(0, 0, 0, 0.2); */
                background: #fff;
                border-radius: 0px;
            }
            .admin-login-box .container{
                max-width: 500px;
                padding-top: 3rem;
                padding-bottom: 3rem;
                padding-right: 0px;
                padding-left: 0px;
                box-sizing: border-box;
            }
            .admin-login-box p.input-title{
                font-size: 1rem;
                margin-bottom: .5rem;
            }
            .admin-login-box .text-danger{
                color: red !important;
                display: block;
                text-align: center;
                font-size: 14px;
            }
            .admin-login-box .submitBtn{
                border-radius: 0px;
                background-color: #343a40;
                border-color: #343a40;
                margin-bottom: .5rem;
            }
            .admin-login-box input:focus{
                outline:none;
                border-color: #495057;
                box-shadow:none;
            }
            .admin-login-box .submitBtn:hover{
                background-color: #4c91e2;
                border-color: #4c91e2;
            }
            .admin-login-box .form-control{
                border-radius: 0px;
            }
            .admin-login-box .ng-hide{
                display: none;
            }
            .admin-login-box .intro ul li,.admin-login-box .intro ul li a{
                font-size: 1rem;
                color: #333;
                text-decoration: none;
            }
            .admin-login-box .intro ul {
                list-style-type: none;
                margin:0px;
                padding:0px;
            }
            .admin-login-box .intro span.title{
                font-weight: 700;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid" style="padding: 0px;">
            <div>
                <div class="admin-login-box">
                    <div class="center">
                        <div class="container">
                            <h1 class="title"><span>{{$data['admin_info']['customer_name']}}</span>商品管理後台</h1>

                            <div class="card card-container">
                                @if(!empty($data['admin_info']['customer_logo']))
                                    <img id="profile-img" class="profile-img-card" src="{{__UPLOAD__}}{{$data['admin_info']['customer_logo']}}"  />
                                @endif

                                <p id="profile-name" class="profile-name-card"></p>
                                <form action="{{url('login/loginAction')}}" method="post" class="form-signin">
                                    @csrf
                                    <div class="form-group">
                                        <p class="input-title">帳號</p>
                                        <input name="account" type="text" class="form-control" required autofocus>
                                        <input name="jump_uri" type="hidden" value="{{ request()->get('jump_uri') ?? ''}}">
                                    </div>
                                    <div class="form-group">
                                        <p class="input-title">密碼</p>
                                        <input name="password" type="password" class="form-control" required>
                                    </div>
                                    @if($data['admin_type']=='admin')
                                        @if ($data['google_auth'] == 1)
                                        <div class="form-group">
                                            <p class="input-title">驗證碼</p>
                                            <input name="auth" type="password" class="form-control">
                                        </div>
                                        @endif
                                    @endif
                                    <div class="form-group">
                                        <button class="btn btn-primary btn-block btn-primary submitBtn" type="submit">登入</button>
                                    </div>

                                    @if($data['admin_type']=='admin')
                                        <div class="intro">
                                            <span class="title">製作公司資訊</span>
                                            <ul>
                                                <li>公司：<span>{{$data['admin_info']['system_name']}}</span></li>
                                                <li>電話：<span>{{$data['admin_info']['tel']}}</span></li>
                                                <li>地址：<span>{{$data['admin_info']['email']}}</span></li>
                                            </ul>
                                            <div style="display: flex; flex-direction: row; align-items: center;justify-content: center;">
                                                @if(!empty($data['admin_info']['system_logo']))
                                                <div style="display: inline-block; width:50%">
                                                    <img id="profile-img" class="profile-img-card" src="{{__UPLOAD__}}{{$data['admin_info']['system_logo']}}"  />
                                                </div>
                                                @endif
                                                @if(!empty($data['admin_info']['marketing_logo']))
                                                <div style="display: inline-block; width:50%">
                                                    <img id="profile-img" class="profile-img-card" src="{{__UPLOAD__}}{{$data['admin_info']['marketing_logo']}}"  />
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>