@extends('admin.Public.aside')
@section('title'){{Lang::get('加價購設定')}} - {{Lang::get('編輯')}}@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('H行銷項目')}}</li>
            <li><a href="{{url('Addprice/index')}}">{{Lang::get('加價購設定')}}</a></li>
            <li>{{Lang::get('編輯')}}</li>
        </ul>
        <a class="back btn sendbtn" href="{{url('Addprice/index')}}">
            <span class="bi bi-arrow-left"></span>
        </a>
        <form class="actForm" name="actForm" method="post">
            @csrf
            <div class="admin-content act">
                <div class="col-lg-12">
                    <div class="item border-bottom">
                        <div>{{Lang::get('名稱')}}：</div>
                        <input type="text" v-model="model.act.title" name="title"  placeholder="必填" class="border-0">
                    </div>
                    <div class="item border-bottom">
                        <div>{{Lang::get('加購折扣')}}：</div>
                        <input type="text" v-model="model.act.discount" name="discount" placeholder="必填" class="border-0">
                        <span class="remark text-danger">{{Lang::get('如需打85折，請輸入0.85')}}</span>
                    </div>
                    <div class="item">
                        <div>{{Lang::get('開始日期')}}：
                        <input type="date" v-model="model.act.start_time" name="start_time" id="start_time" placeholder="必填"></div>
                        <div class="ml-lg-2">{{Lang::get('結束日期')}}：
                            <input type="date"  v-model="model.act.end_time" name="end_time" id="end_time" 
                                   :style="{'display': model.act.noEndTime ? 'none' : 'inline-block' }">
                            <input type="checkbox"  id="noEndTime" v-model="model.act.noEndTime">
                            <label for="noEndTime" class="mb-0">{{Lang::get('沒有結束日期')}}</label>
                        </div>
                    </div>
                    <div class="item">
                        <div>{{Lang::get('簡介')}}</div>
                        <textarea rows="5" class="form-control" v-model="model.act.description"></textarea>
                    </div>
                </div>  
                <a class="btn sendbtn m-auto btn-sm" @click="doUpdate()">{{Lang::get('更新基本設定')}}</a>  
            </div>
        </form>
        <div class="product_layer_select m-auto">
            <div id="tabs" class="product_layer_tabs">
                <ul>
                    <li><a href="#ruleProd" @click="change_tab('RuleProd')" class="btn  border-0 btn-sm">{{Lang::get('條件商品')}}</a></li>
                    <li><a href="#actProd" @click="change_tab('ActProd')" class="btn  border-0 btn-sm">{{Lang::get('加價購商品')}}</a></li>
                </ul>
                <template v-for="prod_type in ['ruleProd', 'actProd']">
                    <div :id="prod_type">
                        <div class="container-fluid product_layer_select">
                            {{Lang::get('已套用商品')}}：
                            <span v-if="model[prod_type].length!=0" class="ml-1">
                                <a href="###" @click="selectAll(prod_type)" class="btn sendbtn btn-sm">{{Lang::get('全選商品')}}</a>
                            </span>
                            <div class="row mt-2">
                                <div v-for="(item,index) in model[prod_type]" class="col-12 col-md-4 col-lg-3 product_item">
                                    <div>
                                        <input v-model="item.select" type="checkbox">
                                        <span @click="change_prod_check(index, item, prod_type)" v-text="item.pi_title + '-' + item.title"></span>
                                    </div>
                                    <img :src="'/public/static/index' + item.pic1"
                                         @click="change_prod_check(index, item, prod_type)">
                                    <div v-if="prod_type=='actProd'" class="mt-2">
                                        {{Lang::get('加購上限')}}：<input class="w-50" v-model="item.limit_num" @blur="save_limitNum(item.adpp_id, item.limit_num)">
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-center">
                                <button @click="delActProd()" class="btn width-50 sendbtn">{{Lang::get('刪除套用商品')}}</button>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        <br>
        <div class="admin-content  product_layer_select mb-4">
            <div class="mb-3">
                <div  class="mb-1">
                    <a href="###" class="btn sendbtn btn-sm"  @click="getList()">{{Lang::get('重置階層')}}</a>
                </div>
                <div  class="mb-2 d-flex flex-wrap">
                    <div class="w-100 mb-2">
                        <span class="name">{{Lang::get('所在階層')}}：/</span> 
                        <template v-for="(currCate, index) in currCates">
                            <a href="###" class="ml-1 mr-1 layer-item"
                               @click="changeCate(currCate, index)" v-text="currCate.title"></a> /
                        </template>
                    </div>
                    <div class="w-100 mb-2">
                        <span class="name">{{Lang::get('子階層')}}：</span>
                        <span v-for="item in model.series"> 
                            <a href="###" class="item_tree" @click="getCate(item)" v-text="item.title"></a>
                        </span>
                        <span v-for="item in model.cate2"> 
                            <a href="###" class="item_tree" @click="getCate2(item)" v-text="item.title"></a>
                        </span>
                        <span v-for="item in model.cate" > 
                            <input v-model = "item.select" type="checkbox"> 
                            <span @click="getCateProd(item)" v-text="item.title"></span>
                        </span>
                    </div>
                    <div class="w-100">
                        <span class="name">{{Lang::get('所在階層商品')}}：</span>
						<span v-if="model.cateProd.length!=0" >
							<span v-text="catProd"></span>
							<a href="###" class="item_tree" @click="selectAllProd()">{{Lang::get('全選商品')}}</a>
						</span>
                    </div>
                </div>
            </div> 
            <div class="mb-3">
                <div class="container-fluid">
                    <div class="row">
                        <div v-for="(item,index) in model.cateProd" class="col-12 col-md-4 col-lg-3 product_item">
                            <div>
                                <input v-model="item.select" type="checkbox">
                                <span @click="change_cateProd_check(index, item)" v-text="item.pi_title + '-' + item.title"></span>
                            </div>
                            <img :src="'/public/static/index' + item.pic1"
                                 @click="change_cateProd_check(index, item)">
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-center">
                <button class="btn width-50 sendbtn" @click="insertActProd()">{{Lang::get('加入套用商品')}}</button>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script>
        var actId = '';
        try {
            var currUrl = window.location.pathname;
            var urlSplit= currUrl.split('/');
            var actId   = urlSplit[5].split('.');
            actId = actId[0];
        }
        catch (e) {
            var currUrl = location.href;
            var urlSplit= currUrl.split('?id=');
            if(urlSplit.length==2){
                actId = urlSplit[1].replaceAll('#', ''); 
            }
        }
        if(actId==''){
            Vue.toasted.show("{{Lang::get('資料有誤')}}", {duration:1500, className: ["toasted-primary", 'bg-danger']});
            setTimeout(()=>{ location.href="{{url('Addprice/index')}}" }, 300);
        }

        var content_area_data = {
            model: {
                act: {start_time:"", end_time:""},
                series: [],
                cate: {},
                cate2: {},
                cateProd: [],
                ruleProd: [],
                actProd: [],
            },
            catProd: "",
            currCates: [],

            actId: actId,
            currentTab: 'RuleProd',
        };
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            methods: {
                getDetail: function(){
                    self = this;
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/getDetail')}}",
                        data: {actId:self.actId},
                        success: function(resp){
                            self.model.act = resp.actInfo;
                            self.model.act['start_time'] = self.model.act.start_time.split(" ")[0];
                            self.model.act['end_time']  = self.model.act.end_time.split(" ")[0];    
                            console.log(self.model.act)

                            self.model.ruleProd = resp.ruleProd; /*條件商品*/
                            self.model.actProd = resp.actProd; /*活動商品*/
                            // console.log(self.model.actProd)
                            // console.log(self.model)
                        }
                    });
                },
                change_tab: function(tab){
                    self = this;
                    self.getList();
                    self.currentTab = tab;
                    console.log(self.currentTab);
                },
                getList: function(){
                    self = this;
                    self.model.series       = [];
                    self.model.cate2        = {};
                    self.model.cateProd     = [];
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Product/getList')}}",
                        data: {actId:self.actId},
                        success: function(resp){
                            // console.log(resp);
                            self.getCateProd({cateId:0});
                            self.model.series = resp.message;
                            self.currCates = [];
                        }
                    });
                },
                changeCate: function(item, index){
                    self = this;
                    self.currCates = self.currCates.slice(0, index);
                    if(index==0){
                        self.getCate(item);
                    }else{
                        self.getCate2(item);
                    }
                },
                getCate: function(item){
                    // console.log(item.id);
                    self = this;
                    self.model.series = [];
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Product/getCate')}}",
                        data: {seriesId:item.id},
                        success: function(resp){
                            resp['first'] = true;
                            // console.log(resp)
                            self.getCateProd(resp);

                            self.model.cate2 = resp.cate;
                            self.currCates.push(item);
                            for (var prop in self.model.series){
                                self.model.series[prop]['select'] = false;
                            }
                        }
                    });
                },
                getCate2: function(item){
                    // console.log(item.id);
                    self = this;
                    self.model.cate2 = {};
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Product/getCate2')}}",
                        data: {seriesId:item.id},
                        success: function(resp){
                            self.model.cate2 = resp.cate;
                            self.currCates.push(item);

                            if(resp.getCateProd == 1){
                                self.model.cate2 = {};
                            }

                            self.getCateProd(resp);
                        }
                    });
                },
                getCateProd: function (item){
                    // console.log(seriesId);
                    self = this;
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/getAddableProd')}}?tab="+self.currentTab,
                        data: {cateId:item.seriesId, first:item.first, actId:self.actId},
                        success: function(resp){
                            self.catProd = item.title;
                            for (var prop in resp.msg.productinfo){
                                resp.msg.productinfo[prop]['select'] = false;
                            }
                            self.model.cateProd = resp.msg.productinfo;
                        }
                    });
                },
                doUpdate: function(){
                    self = this;

                    var postData = Object.assign({}, self.model.act);
                    postData['start_time']  = $('#start_time').val();
                    postData['end_time']    = $('#end_time').val();
                    // console.log(postData);
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/doUpdate')}}",
                        data: { act: postData },
                        success: function(resp){
                            if(resp.code==1){
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                        }
                    });
                },
                insertActProd: function(){
                    $('#block_block').show();
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/insertActProd')}}?tab="+self.currentTab,
                        data: { 
                            cateProd : self.model.cateProd,
                            actId : self.actId
                        },
                        success: function(resp){
                            if(resp.code==1){
                                bg_class = 'bg-success';
                                self.getDetail();
                                self.getList();
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            $('#block_block').hide();
                        },
                        error: function(){
                            Vue.toasted.show("{{Lang::get('發生錯誤')}}",{duration:1500, className: ["toasted-primary", 'bg-danger']});
                            $('#block_block').hide();
                        },
                    });
                },
                delActProd: function(){
                    self = this;
                    $('#block_block').show();
                    if(self.currentTab == 'RuleProd'){
                        targetProduct = self.model.ruleProd;
                    }else{
                        targetProduct = self.model.actProd;
                    }

                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/delActProd')}}?tab="+self.currentTab,
                        data: {cateProd:targetProduct, actId:self.actId},
                        success: function(resp){
                            if(resp.code==1){
                                bg_class = 'bg-success';
                                self.getDetail();
                                self.getList();
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            $('#block_block').hide();
                        },
                        error: function(){
                            Vue.toasted.show("{{Lang::get('發生錯誤')}}",{duration:1500, className: ["toasted-primary", 'bg-danger']});
                            $('#block_block').hide();
                        },
                    });
                },
                selectAll: function(prod_type){
                    self = this;
                    for(var prop in self.model[prod_type]){
                        item = self.model[prod_type][prop];
                        item['select'] = true;
                        Vue.set(self.model[prod_type], prop, item);
                        // self.model.ruleProd[prop]['select'] = true;
                    }
                },
                selectAllProd: function(){
                    self = this;
                    for(var prop in self.model.cateProd){
                        item = self.model.cateProd[prop];
                        item['select'] = true;
                        Vue.set(self.model.cateProd, prop, item);
                        // self.model.cateProd[prop]['select'] = true;
                    } 
                },
                change_prod_check: function(index, item, prod_type){
                    self = this;
                    item.select = item.select ? false : true;
                    Vue.set(self.model[prod_type], index, item);
                },
                change_cateProd_check: function(index, item){
                    self = this;
                    item.select = item.select ? false : true;
                    Vue.set(self.model.cateProd, index, item);
                },

                save_limitNum: function(adpp_id, limit_num){
                    $.ajax({
                        method:"post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url:"{{url('Addprice/updatelimitNum')}}",              
                        data:{
                            adpp_id : adpp_id,
                            limitNum: limit_num,
                        },
                        success:function(resp){
                            if(resp.code==1){
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                        }
                    });
                },
            },
        });
        content_areaVM.getDetail();
        content_areaVM.getList();
    </script>

    <script>
        $( function() {
            $( "#tabs" ).tabs();
        } );
    </script>
@endsection