@extends('admin.Public.aside')
@section('title'){{Lang::get('H行銷項目')}} - {{Lang::get('加價購設定')}}@endsection
@section('css')
    <link rel="stylesheet" type="text/css" href="{{__PUBLIC__}}/css/daterangepicker.css" />
@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('H行銷項目')}}</li>
            <li><a href="{{url('Addprice/index')}}">{{Lang::get('加價購設定')}}</a></li>
            <li><a href="###" v-text="model.search"></a></li>
        </ul>
        <div class="searchbox">
            <form action="{{url('Addprice/index')}}" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1 text-center" placeholder="{{Lang::get('名稱')}}" v-model="searchKey">
                <input type="hidden" name="type"  value="keyword" >
                <a class="btn sendbtn" onclick="searchForm.submit();">{{Lang::get('搜尋')}}</a>

                <div style="visibility: hidden; display: none;" >
                    <input id="pre" type="radio" name="searchtype" value="3" >
                    <input id="now" type="radio" name="searchtype"  value="2" >
                    <input id="passed" type="radio" name="searchtype"  value="1" >
                    <input id="nouse" type="radio" name="searchtype"  value="0" >
                </div>
            </form>
            <div class="searchTimeBox flex-nowrap"> 
                <input id="searchTimeInput" class="date form-control mr-1" type="text" />
                <a class="btn sendbtn" @click="searchTime()">{{Lang::get('搜尋')}}</a>
            </div>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn" data-toggle="modal" data-target="#addModal">
                <i class="bi bi-plus-lg add small"></i>  {{Lang::get('新增')}}
            </a>
            <div class="position-relative d-inline-block">
                <div class="edit" onclick="Show('.edit-item')">
                    {{Lang::get('編輯')}} <span class="bi bi-chevron-down"></span>
                </div>
                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a @click="multiOnline();">
                        <p class="mb-0">{{Lang::get('上架')}}&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                  
                    <a @click="multiOffline();">
                        <p class="mb-0">{{Lang::get('下架')}}&nbsp;</font>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                  
                    <a @click="multiDelete();" class="border-top">
                        {{Lang::get('刪除')}} <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
                <!-- 編輯結束 -->
            </div>
        </div>
        <!--表格 開始-->
        <div class="edit_form" >
            <table class="table table-rwd" style="min-width: 992px;">
                <thead>
                    <tr>
                        <th style="width: 20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=actCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 80px">{{Lang::get('上下架')}}</th>
                        <th>{{Lang::get('名稱')}}</th>
                        <th>{{Lang::get('開始日期')}}</th>
                        <th>{{Lang::get('結束日期')}}</th>
                        <th>{{Lang::get('商品數')}}</th>
                        <th style="width: 60px">{{Lang::get('刪除')}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item,index) in model.actList" :id="'act_' + item.id">
                        <td><input type="checkbox" class="actCheckbox" :alt="index"></td>
                        <td>
                            <label class="switch">
                                <input v-model="item.online" type="checkbox" @change="onlineChange(item)">
                                <span class="slider round"></span>
                            </label>
                        </td>
                        <td><a :href="'{{url('Addprice/edit')}}?id=' + item.id" v-text="item.title"></a></td>
                        <td v-text="item.start_time"></td>
                        <td v-text="item.end_time"></td>                    
                        <td v-text="item.count"></td>                                     
                        <td><span class="bi bi-trash" @click="doDel(item)"></span></td>                    
                    </tr>
                </tbody>
            </table>
        </div>
        <!--表格 結束-->
    </div>

    <div class="modal fade in main-modal" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel" style="display: inline-block;">
                        {{Lang::get('新增加價購')}}
                    </h5>
                </div>
                <div class="modal-body row" id="boxModel">
                    <div class="form-group col-sm-12 col-12">
                        <label class="col-form-label">{{Lang::get('名稱')}}</label>
                        <input type="text" class="form-control " v-model="addModel.title">
                    </div>
                    <div class="form-group col-sm-12 col-12">
                        <label class="col-form-label">{{Lang::get('加購折扣')}}</label>
                        <input type="number" min="0" class="form-control" v-model="addModel.discount">
                        <span class="remark text-danger">{{Lang::get('如需打85折，請輸入0.85')}}</span>
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">{{Lang::get('開始日期')}}</label>
                        <input type="date" class="form-control start_time" v-model="addModel.start_time">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">{{Lang::get('結束日期')}}</label>
                        <input type="checkbox" style="margin-left:20px" id="noEndTime" v-model="addModel.noEndTime">
                        <label for="noEndTime">{{Lang::get('沒有結束日期')}}</label>
                        <input type="date" class="form-control end_time" v-model="addModel.end_time" :style="{'display': addModel.noEndTime ? 'none' : 'inline-block' }">
                    </div>
                    <div class="form-group col-sm-12 col-12">
                        <label class="col-form-label">{{Lang::get('簡介')}}</label>
                        <textarea rows="5" class="form-control" v-model="addModel.description"></textarea>
                    </div>
                </div>
                <div class="modal-footer flex-wrap justify-content-center">
                    <span class="text-danger text-center remark w-100">{{Lang::get('請在新增後套用商品')}}</span>
                    <button class="btn sendbtn" @click="do_add()">{{Lang::get('新增')}}</button>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('ownJS')
    <script type="text/javascript" src="{{__PUBLIC__}}/js/moment.min.js"></script>  
    <script type="text/javascript" src="{{__PUBLIC__}}/js/daterangepicker.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    
    <script type="text/javascript">
        // 抓取搜尋設定
        var Request = new Object();  
        request = GetRequest();
        searchKey = request.searchKey ? request.searchKey : '';
        searchKey = decodeURIComponent(searchKey).trim();
        searchtype = request.searchtype ? request.searchtype : '';
        searchtype = decodeURIComponent(searchtype).trim();
        function GetRequest() {
             var url = location.search.replaceAll('+', ''); 
             var theRequest = new Object();      
             if (url.indexOf("?") != -1) {       
                var str = url.substr(1);         
                strs = str.split("&");       
                for(var i = 0; i < strs.length; i++) {       
                   theRequest[strs[i].split("=")[0]]=decodeURI(strs[i].split("=")[1]);       
                }        
             }       
             return theRequest;
        }

        // 初始化vue
        var content_area_data = {
            timeRange: [],
            searchKey: searchKey,
            searchtype: searchtype,
            model:{
                search: "",
                actList: [],
            },
            addModel:{
                noEndTime: false,
            }
        };
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            methods: {
                getList: function(searchdata){
                    self = this;
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/getList')}}",
                        data: searchdata,
                        success: function(resp){
                            self.model.search = resp.search;
                            self.model.actList = resp.actList;
                            for(var prop in self.model.actList){
                                if (self.model.actList[prop]['online'] == 1){
                                    self.model.actList[prop]['online'] = true;
                                }else{
                                    self.model.actList[prop]['online'] = false;
                                }
                            }
                        },
                    });
                },
                searchTime: function () {
                    self = this;
                    $('a.button').removeClass('active');
                    self.timeRange = $('#searchTimeInput').val().split(" - ");
                    self.getList({'start': self.timeRange[0], 'end':self.timeRange[1],'type':'date'});
                },
                
                onlineChange: function(item){
                    $.ajax({
                        method : "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/changeOnline')}}",
                        data: item,
                    }).success(function(resp){
                        if(resp.code==1){
                            bg_class = 'bg-success';
                        }else{
                            bg_class = 'bg-danger';
                        }
                        Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                    }).error(function(){
                    })//error
                },
                multiOnline: function () {
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        multiId['online'] = true;
                        self.onlineChange(multiId);
                    }
                    setTimeout(()=>{ location.reload(); }, 300);
                },
                multiOffline: function () {
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        multiId['online'] = false;
                        self.onlineChange(multiId);
                    }
                    setTimeout(()=>{ location.reload(); }, 300);
                },
                
                doDel: async function(item){
                    self = this;
                    if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }

                    await self.doDel_one(item);
                    self.getList({'searchKey': self.searchKey, 'type':'keyword'});
                },
                multiDelete: async function (){
                    var multiIdArray = self.getMultiId();
                    if(multiIdArray.length==0){
                        Vue.toasted.show("{{Lang::get('請勾選項目')}}", {duration:1500, className: ["toasted-primary", 'bg-warning']});
                        return;
                    }
                    if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }

                    for(var i=0;i<multiIdArray.length;i++){
                        await self.doDel_one(multiIdArray[i]);
                    }
                    setTimeout(()=>{ location.reload(); }, 300);
                },
                doDel_one: function (item){
                    return $.ajax({
                        method : "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/doDel')}}",
                        data: item,
                    }).success(function(resp){
                        if(resp.code==1){
                            $('#act_'+item["id"]).css('display','none');
                            bg_class = 'bg-success';
                        }else{
                            bg_class = 'bg-danger';
                        }
                        Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                    }).error(function(){
                    })//error
                },
                getMultiId: function() {
                    self = this;
                    var multiIdArray = [];
                    $('.actCheckbox').each(function () {
                        if($(this).prop("checked")){
                            multiIdArray.push(self.model.actList[Number($(this).attr('alt'))]);
                            $(this).prop("checked", false);
                        }
                    });
                    $('.activityCheckboxAll').prop("checked", false);
                    return multiIdArray;
                },
                
                do_add: function(){
                    self = this;

                    var postData = Object.assign({}, self.addModel);
                    postData['start_time'] = $('input.start_time').val();
                    postData['end_time'] = $('input.end_time').val();
                    $.ajax({
                        method : "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Addprice/doCreate')}}",
                        data: postData,
                    }).success(function(resp){
                        if(resp.code==1){
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-success"]});
                            setTimeout(()=>{ location.href=resp.url; }, 300)
                        }else{
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    }).error(function(){
                    })//error
                }, 
            },
        });
        content_areaVM.getList({'searchKey': content_areaVM.searchKey, 'type':'keyword'})
    </script>

    <script>
       $("input#searchTimeInput").daterangepicker({locale: {format: 'YYYY-MM-DD'}});

        function timeTypeSerch(timeType){
            $('#'+timeType).click();
            searchForm.submit();
        }
        $('.searchtype{$searchtype}').addClass('active');
    </script>  
@endsection