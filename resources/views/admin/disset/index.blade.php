@extends('admin.Public.aside')
@section('title')F商品管理區 > 價格組合設定@endsection
@section('css')
    <link rel="stylesheet" type="text/css" href="/css/admin/daterangepicker.css" />

@endsection
@section('content')
    <div id="content">
  
        <ul id="title" class="brand-menu">
            <li onclick="javascript:location.href='index'"><a href="###">F商品管理區</a></li>
            <li><a href="###">價格組合設定</a></li>
          
        </ul>


        <!--新增與編輯-->
        <div class="searchbox">
            <div class="searchKeyBox">
                <input id="name" class="form-control mr-1" placeholder="文字輸入(品項)"><input id="number" class="form-control mr-1" placeholder="輸入折數"></input>
                <button type="button" onclick="action('add',0)" class="btn sendbtn">新增</button>
                <p class="text-danger remark ml-1 mb-0">註：如要打85折請輸入0.85</p>
            </div>
        </div>
      

        <!--表格 開始-->
        <table class="table width-50 ml-auto mr-auto table-rwd mt-2" >
            <thead>
                <tr>
                    <th>品項</th>
                    <th>折數</th>
                    <th style="width: 60px;">操作</td>
                </tr>
            </thead>
            <tbody>
                @if(count($data['dis'])==0)<tr><td colspan=2>沒有數據<td></tr>@endif
                @foreach($data['dis'] as $vo)
                    <tr>
                        <td>{{$vo['name']}}</td>
                        <td>{{$vo['number']}}</td>
                        <td><button type="button" onclick="action('delete',{{$vo['id']}})" class="btn sendbtn">刪除</button></td>              
                    </tr>
                @endforeach
            </tbody>
        </table>
        
        <!--表格 結束-->
       
    </div>
@endsection
@section('ownJS')
    <script type="text/javascript" src="/js/admin/moment.min.js"></script>  
    <script type="text/javascript" src="/js/admin/daterangepicker.js"></script>
    <script src="/public/static/admin/js/action.js"></script>
    
    <script>
		function action(type,id){
			
			
			var name = $('#name').val();
			var number = $('#number').val();
			
			
			if(type == 'add' && (name == '' || number == '')){
				alert('請填完整資料');
				return 0;
			}
			
			var result = (number.toString()).indexOf("0.");
			if( type== 'add' && (result == -1 || number > 1)) {
				alert("請給小於1的折");
				return 0;
			}
			
		
			$.ajax({
				type:'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
				data:{
					id:id,
					type:type,
					number:number,
					name:name
				},
				url:"{{url('Disset/edit')}}",
				success:function(res){
					if(res.trim()=='success'){
						location.reload();
					}else{
						alert(res.trim());
					}
				}
			});
		}
		
    </script>

@endsection