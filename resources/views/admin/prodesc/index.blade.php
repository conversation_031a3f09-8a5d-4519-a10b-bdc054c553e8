@extends('admin.Public.aside')

@section('title')
F商品管理區 > 屬性1
@endsection

@section('css')@endsection

@section('content')
  <div id="content">
    <ul id="title" class="brand-menu">
      <li onclick="javascript:location.href='index'"><a href="###">F商品管理區</a></li>
      <li><a href="###">屬性1</a></li>
    </ul>

    <!--新增與編輯-->
    <div class="searchbox">
      <div class="searchKeyBox">
        <input id="name" placeholder="文字輸入(商品說明)" class="form-control mr-1">
        <input id="number" type="hidden" value="0">
        <button type="button" onclick="action('add',0)" class="btn sendbtn">新增</button>
      </div>
    </div>

    <!--表格 開始-->
    <table class="table width-50 ml-auto mr-auto table-rwd mt-2" >
      <thead>
        <tr>
          <th>商品說明</th>
          <th style="width: 60px;">操作</th>
        </tr>
      </thead>
      <tbody>
        @if(count($data['dis'])==0)
        <tr><td colspan=2>沒有數據<td></tr>
        @endif
        @foreach($data['dis'] as $vo)
          <tr>
            <td>{{$vo['name']}}</td>
            <td><button type="button" onclick="action('delete',{{$vo['id']}})" class="btn sendbtn">刪除</button></td>              
          </tr>
        @endforeach
      </tbody>
    </table>
    <!--表格 結束-->
  </div>
@endsection
@section('ownJS')
  <script src="/public/static/admin/js/action.js"></script>
  <script>
    function action(type,id){
      var name = $('#name').val();
      var number = $('#number').val();
      
      if(type=='delete'){
        if(!confirm("確定刪除?")){ return 0; }
      }
      $.ajax({
        type:'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
        },
        data:{
          id:id,
          type:type,
          number:number,
          name:name
        },
        url:"{{url('Prodesc/edit')}}",
        datatype: 'json',
        success:function(res){
          if(res.trim()=='success'){
            location.reload();
          }else{
            alert(res.trim());
          }
        },
      });
    }
  </script>
@endsection