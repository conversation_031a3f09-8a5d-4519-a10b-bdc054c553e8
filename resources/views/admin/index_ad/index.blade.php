@extends('admin.Public.aside')
@section('title')首頁管理區 > 多廣告@endsection


@section('content')
    <iframe id="boxFormIframe" name="boxFormIframe" style="display: none;"></iframe>

    <!-- 新增修改廣告開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <form name="boxForm" :action="action" method="post" target="boxFormIframe" enctype="multipart/form-data">
                @csrf
                <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-header">
                        <h5 class="modal-title">廣告內容</h5>
                    </div>
                    <div class="modal-body"> 
                        <div class="row">
                            <div class="col-6">
                                <div class="img-box text-center" style="min-height: 200px;">
                                    <span class="bi bi-image"></span>
                                    <input type='file' ref="img" class="upl" name="image" accept="image/*" @change="previewImg">
                                    <img class="preview" :src="src"/>
                                </div>
                                <p class="text-danger remark">建議大小：600*600</p>
                            </div>
                            <div class="col-6">
                                <p>排序<span class="text-danger">(越小越前面)</span>：
                                    <input type="number" name="orders" v-model="orders" class="form-control">
                                </p>
                            </div>  
                        </div> 
                        <p>標題：
                            <input type="text" name="title" v-model="title" class="form-control">
                        </p>
                        <!-- <p>內文：<br>
                            <textarea v-model="content" class="form-control"></textarea>
                            <input name="content" type="hidden" v-model="contentNl2br">                    
                        </p><br> -->
                        <p>URL：
                            <input type="text" name="url" v-model="url" class="form-control">
                        </p>
                    </div>
                    <div class="modal-footer flex-wrap justify-content-center">
                        <input type="hidden" name="id" v-model="id">
                        <button type="button" class="btn sendbtn" @click="formSubmit">儲存</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 新增修改廣告結束 -->

    <div id="content">
      
        <ul id="title" class="brand-menu" >
            <li onclick="javascript:location.href='index'"><a href="###">C 首頁管理區</a></li>
            <li><a href="###">多廣告</a></li>
            @if($data['searchKey'] !='')
            <li><a href="">搜尋：{{$data['searchKey']}}</a></li>
            @endif
            
        </ul>
        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox">
                @csrf
                <input  class="form-control mr-1 mb-1 text-center" type="text" name="searchKey"  placeholder="搜尋標題">
                <a class="sendbtn btn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame flex-wrap d-flex align-items-center justify-content-between">
            <div>
                <a href="###" class="btn clearbtn" onclick="newBlock();"><i class="bi bi-plus-lg add small"></i>  新增</a>
                <span class="d-inline-block position-relative">
                    <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                    <!-- 編輯開始 -->
                    <div class="edit-item none">
                        <a onclick="multiOnline();">
                            <p class="mb-0">上架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled checked><span class="slider round"></span>
                            </label>
                        </a>
                       
                        <a onclick="multiOffline();">
                            <p class="mb-0">下架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled><span class="slider round"></span>
                            </label>
                        </a>
                       
                      
                        <a onclick="multiDelete();" class="border-top">
                            刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                        </a>
                    </div>
                    <!-- 編輯結束 -->
                </span>
                
                
            </div>
            <ul class="text-danger remark">
                <li>廣告數量建議為6的倍數，前台顯示效果較為佳</li>
            </ul>
        </div>

         <div class="edit_form" >
            <table class="table table-rwd" style="min-width:1200px;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=index_adCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 80px;">上下架</th>
                        <th style="width: 140px;">排序(越小越前面)</th>
                        <th style="width: 140px;">預覽圖片</th>
                        <th>標題</th>
                        <th>URL</th>
                        <th style="width: 60px;">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if (empty($data['index_ad']) == true)
                        <tr><td colspan="7">沒有數據</td></tr>
                    @else
                        @foreach($data['index_ad'] as $vo)
                        <tr id="index_ad_{{$vo->id}}">
                            <td><input type="checkbox" class="index_adCheckbox" alt="{{$vo->id}}"></td>
                            <td>
                                <label class="switch">
                                    <input type="checkbox" v-model="online">
                                    <span class="slider round"></span>
                                </label>
                            </td>
                            <td><span v-text="orders"></span></td>
                            <td>
                                <div class="img-box" @click="openBox">
                                    <p style="position:absolute;">600*600</p>
                                    <img class="preview" :src="src"/>
                                </div>
                            </td>
                            <td><a @click="openBox" v-text="title"></a></td>
                            <td><span v-text="url"></span></td>
                            <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                        </tr>
                        @endforeach
                    @endif
                </tbody>
            
            </table>
        </div>
        <div class="text-center">
            {!! $data['index_ad']->links('pagination::default') !!}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="/public/static/admin/js/action.js"></script>
    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
            $('.edit-item').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Indexad/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                    },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {
            title: "", src: "", content: "", id: 0, orders:0,
            action: "", url: "",
            caller: null
        }
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
                contentNl2br: function () {
                    return this.content.replace(/\n/g, '<br>');
                }
            },
            methods: {
                formSubmit: function () {
                    document.boxForm.submit();
                },
                previewImg: function () {
                    console.log(this.$refs.img.files);
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        Box.src = e.target.result;
                    }
                    reader.readAsDataURL(this.$refs.img.files[0]);
                },
                updateCallerData: function () {
                    this.caller.src = this.src;
                    this.caller.title = this.title;
                    this.caller.orders = this.orders;
                    this.caller.url = this.url;
                    this.caller.content = this.content;
                    $('#functionModal').modal('hide');
                }
            }
        });

        $('#boxFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                // alert("上傳成功");
                // if(BoxVM.caller == 'new'){
                //     location.reload();
                // }else{
                //     BoxVM.updateCallerData();
                // }
                location.reload();
            }else{
                alert("上傳失敗");
                console.log($(this).contents().find('body').text());
            }
        });

        ///////andy/////多行文字串//////
        function heredoc(fn) {
            return fn.toString().replace(/[\\]/g,"") + '\n'
        }
        ///////////////////////////////
        @foreach($data['index_ad'] as $vo)
            ///////andy/////多行文字串////////////////////
            var tmpl = heredoc(function(){
                `{!! str_replace('/','\\/',addslashes($vo->content)) !!}`
            });
            tmpl = tmpl.split('`');
            delete tmpl[0];
            var lastnum = tmpl.length -1;
            delete tmpl[lastnum];
            // console.log(tmpl);
            /////////////////////////////////////////////

            var index_ad_{{$vo->id}} = {
                id: "{{$vo->id}}",
                title: "{{$vo->title}}",
                orders: "{{$vo->orders}}",
                src: "{{$vo->pic}}" ? "{{__UPLOAD__}}{{$vo->pic}}" : '',
                url: "{{$vo->url}}",
                content: tmpl.join(''),
                online: +"{{$vo->online}}",
                action: "{{url('Indexad/update')}}"
            }
            var index_ad_{{$vo->id}}_VM = new Vue({
                el: '#index_ad_{{$vo->id}}',
                data: index_ad_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    }
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.title = this.title;
                        BoxVM.orders = this.orders;
                        BoxVM.src = this.src;
                        BoxVM.content = this.content;
                        BoxVM.url = this.url;
                        BoxVM.action = this.action;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    }
                }
            });
        @endforeach

        function newBlock(){
            BoxVM.id = "";
            BoxVM.title = "";
            BoxVM.orders = 0;
            BoxVM.src = "";
            BoxVM.url = "";
            BoxVM.content = "";
            BoxVM.action = "{{url('Indexad/doCreate')}}";
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.index_adCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id){
            if(confirm("確定刪除?")){
                location.href = '{{url('Indexad/delete')}}?id='+id;
            }
        }

        function multiDelete() {
            if(confirm("確定刪除?")){
                var form = document.createElement("form");

                form.action = "{{url('Indexad/multiDelete')}}";
                form.method = "post";

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";

                form.appendChild(multiId);
                document.body.appendChild(form);
                form.submit();

                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('index_ad_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('index_ad_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }
    </script>
@endsection