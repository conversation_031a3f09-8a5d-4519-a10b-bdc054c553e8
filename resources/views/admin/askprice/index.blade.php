@extends('admin.Public.aside')
@section('title')G功能應用項目 > 詢價詢價@endsection
@section('css')
@endsection
@section('content')
    <!-- 修改詢價表開始 -->
    <a id="contactModal_btn" data-toggle="modal" data-target="#contactModal" class="d-none">詢價內容</a>
    <div class="modal  fade main-modal" id="contactModal" tabindex="-1" role="dialog" aria-labelledby="contactModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="replyBoxView">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">詢價內容</h5>
                </div>
                <div class="modal-body ask_main">
                    <p class="text-right mb-0">留言時間：<span v-text="replyBoxModel.current.ask_time"></span></p>
                    <div class="ask_top">
                        
                        <div class="img">
                            <img v-if="replyBoxModel.main.product_pic" :src="'{{__UPLOAD__}}' + replyBoxModel.main.product_pic">
                        </div>
                        <div class="text">
                            <div class="item">
                                <div>
                                    <a :href="'{{url('Productinfo/edit')}}?id=' + replyBoxModel.main.product_id" target="_blank">
                                        <span v-text="replyBoxModel.main.product_name"></span>
                                    <template v-if="replyBoxModel.main.product_type_name">
                                        -<span v-text="replyBoxModel.main.product_type_name"></span>
                                    </template>
                                    </a>
                                    <p class="mt-1">數量：<span v-text="replyBoxModel.current.num"></span></p>

                                </div>
                                
                                <p class="price">詢價金額：<span>$</span><span v-text="replyBoxModel.current.price"></span></p>
                                
                            </div>
                        </div>
                    </div>
                    <p class="name mb-1">詢價留言：</p>
                    <p v-text="replyBoxModel.current.ask"></span></p>
                    <div class="reply-box">
                        <p class="title mb-1">回覆：</p>
                        <div class="reply">
                            <div class="col-lg-6">
                                <p><span class="name"> 回覆價格：</span><span class="text-danger remark">若同意價格，詢問者可依此價格購買</span></p>
                                <input v-if="replyBoxModel.current.status" class="form-control mb-2" type="number" min="0" v-model="replyBoxModel.current.price_final" readonly>
                                <input v-if="!replyBoxModel.current.status" class="form-control mb-2" type="number" min="0" v-model="replyBoxModel.current.price_final">
                            </div>
                            <div class="col-lg-6">
                                <p><span class="name"> 購買期限：</span><span class="text-danger remark">若同意價格，可限制何時前完成購買</span></p>
                                <input v-if="replyBoxModel.current.status" class="form-control mb-2" type="date" v-model="replyBoxModel.current.expired_date" readonly>
                                <input v-if="!replyBoxModel.current.status" class="form-control mb-2" type="date" v-model="replyBoxModel.current.expired_date">
                            </div>
                            <div class="col-lg-12">
                                <p><span class="name">回覆內容：</span><span class="text-danger remark">儲存紀錄後將不可再修改</span></p>
                                <textarea v-if="replyBoxModel.current.status" class="form-control mb-2" v-model="replyBoxModel.current.response" readonly></textarea>
                                <textarea v-if="!replyBoxModel.current.status" class="form-control mb-2" v-model="replyBoxModel.current.response"></textarea>
                            </div>
                        </div>
                        <div class="d-flex justify-content-center">
                            <template v-if="!replyBoxModel.current.status">
                                <button type="button" class="btn sendbtn  m-1" @click="ajaxSubmit">儲存回覆</button>
                                <button type="button" class="btn sendbtn  m-1" @click="ajaxSubmit_agree">儲存回覆並同意價格</button>
                            </template>
                        </div>
                        
                        
                        
                    </div>
                    
                   

                    <!-- <div class="row">
                        <div class="col-6">
                            <img v-if="replyBoxModel.main.product_pic" :src="'{{__UPLOAD__}}' + replyBoxModel.main.product_pic">
                        </div>
                        <div class="col-6">
                            建立時間：<span v-text="replyBoxModel.main.create_time"></span><br>
                            商品：<span v-text="replyBoxModel.main.product_name"></span>
                            <template v-if="replyBoxModel.main.product_type_name">
                                -<span v-text="replyBoxModel.main.product_type_name"></span>
                            </template>
                            <a class="btn sendbtn" :href="'{{url('Productinfo/edit')}}?id=' + replyBoxModel.main.product_id" target="_blank">查看</a>
                            <br>
                            詢問者：<span v-text="replyBoxModel.main.name"></span><br>
                            詢問者電話：<span v-text="replyBoxModel.main.phone"></span><br>
                            詢問者信箱：<span v-text="replyBoxModel.main.email"></span><br>
                        </div>
                        <div class="col-12">
                            <hr>
                        </div>
                        <div class="col-6">
                            目前詢問：<br>
                            要求數量：<span v-text="replyBoxModel.current.num"></span><br>
                            要求價格：<span v-text="replyBoxModel.current.price"></span><br>
                            留言內容：
                            <textarea class="form-control mb-2" v-text="replyBoxModel.current.ask" disabled></textarea>
                            留言時間：<span v-text="replyBoxModel.current.ask_time"></span><br>
                        </div>
                        <div class="col-6">
                            回覆價格：<span class="text-danger">若同意價格，詢問者可依此價格購買</span><br>
                            <input class="form-control mb-2" type="number" min="0" v-model="replyBoxModel.current.price_final">
                            購買期限：<span class="text-danger">若同意價格，可限制何時前完成購買</span><br>
                            <input class="form-control mb-2" type="date" v-model="replyBoxModel.current.expired_date">
                            回覆內容：<span class="text-danger">儲存紀錄後將不可再修改</span><br>
                            <textarea class="form-control mb-2" v-model="replyBoxModel.current.response"></textarea>
                        </div>
                        <div class="col-12 text-right mt-2">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">關閉</button>
                            <template v-if="!replyBoxModel.current.status">
                                <button type="button" class="btn btn-primary" @click="ajaxSubmit">儲存回覆內容</button>
                                <button type="button" class="btn btn-danger" @click="ajaxSubmit_agree">儲存回覆內容並同意價格</button>
                            </template>
                        </div>
                    </div> -->
                </div>
                <div class="modal-footer ask_footer w-100">
                  
                    <a data-toggle="collapse" href="#collapseAsk" role="button" aria-expanded="false" aria-controls="collapseAsk" class="btn">過去記錄 <span class="bi bi-chevron-right"></span></a>
                    <div class="collapse collapseAsk w-100" id="collapseAsk">
                        <table class="table table-rwd  table-mobile ">
                            <thead>
                                <tr>
                                    <th>數量</th>
                                    <th>價格</th>
                                    <th>留言內容</th>
                                    <th>回覆價格</th>
                                    <th>回覆內容</th>
                                 
                                </tr>
                            </thead>
                            <tbody>
                               
                                <tr v-for="history in replyBoxModel.history">
                                    <td data-th="數量"><span v-text="history.num"></span></td>
                                    <td data-th="價格"><span v-text="history.price"></span></td>
                                    <td data-th="留言內容">
                                        <span class="remark text-right" v-text="history.ask_time"></span>
                                        <textarea class="w-100 p-2" disabled v-text="history.ask"></textarea>
                                        
                                    </td>
                                   
                                    <td data-th="回覆價格"><span v-text="history.price_final"></span></td>
                                    <td data-th="回覆內容">
                                        <span  class="remark text-right" v-text="history.response_time"></span>
                                        <textarea class="w-100 p-2" disabled v-text="history.response"></textarea>
                                        
                                    </td>
                                   
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 修改詢價表結束 -->

    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###" onclick="javascript:location.href='index'">詢價詢價</a></li>
            <li><a href="###"><span v-text="model.search"></span></a></li>
        </ul>
   
        <div class="searchbox mb-4">
            <form action="{{url('Askprice/index')}}" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" placeholder="搜尋姓名/電話/手機/信箱/詢問商品" class="form-control mr-1" v-model="searchKey">
                <a class="btn sendbtn" @click="searchKeyword">搜尋</a>
            </form>
            <div class="searchTimeBox flex-nowrap">
                <input class="date form-control mr-1" type="text" id="searchTimeInput" 
                       placeholder="yyyy-mm-dd - yyyy-mm-dd" />
                <a class="btn sendbtn" @click="searchTime">搜尋</a>
            </div>
        </div>

        <!--新增與編輯-->
        <div class="frame ask_status_box">
          
            <div class="edit" onclick="multiDelete();">
                刪除 <span  class="bi bi-trash ml-2"></span>
            </div>
            
            <div class="ask_status">
                <a :class="['btn', model.status=='' ? 'active' : '']" href="###" @click="searchStatus('')">全部</a>
                <a :class="['btn', model.status=='0' ? 'active' : '']" href="###" @click="searchStatus('0')">待處理</a>
                <a :class="['btn', model.status=='1' ? 'active' : '']" href="###" @click="searchStatus('1')">處理完畢</a>
                <p class="d-inline-block m-0">共<span v-text="total"></span>個回函</p>
            </div>
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width: 1200px;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=contact_logCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 160px;">日期</th>
                        <th style="width: 100px;">狀態</th>
                        <th>姓名</th>
                        <th>聯絡電話</th>
                        <th>聯絡信箱</th>
                        <th>詢問商品</th>
                        <th style="width: 60px;">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="contact in model.contacts">
                        <td><input type="checkbox" class="contact_logCheckbox" :alt="contact.id"></td>
                        <td v-text="contact.create_time"></td>
                        <td>
                            <a @click="openBox(contact.id)" href="###">
                                <span v-text="contact.status ? '處理完畢' : '待處理'"></span>
                            </a>
                        </td>
                        <td><span v-text="contact.name"></span></td>
                        <td><span v-text="contact.phone"></span></td>
                        <td><span v-text="contact.email"></span></td>
                        <td>
                            <span v-text="contact.product_name"></span>
                            <template v-if="contact.product_type_name">
                                - <span v-text="contact.product_type_name"></span>
                            </template>
                        </td>
                        <td><span class="bi bi-trash" @click="del_contact(contact.id)"></span></td>
                    </tr>
                </tbody>
                
            </table>
        </div>
      
        <!--表格 結束-->
        <div class="text-center">
            <ul class="pagination" v-if="pages.length>1">
                <li class="" v-if="model.page>1" @click="change_page(1)">
                    <a href="###">«</a>
                </li> 
                <li v-for="p in pages" :class="[model.page==p ? 'active' : '']" >
                    <a href="###" v-text="p" @click="change_page(p)"></a>
                </li> 
                <li class="" v-if="model.page<last_page" @click="change_page(last_page)">
                    <a href="###">»</a>
                </li> 
            </ul>
        </div>
    </div>
@endsection
@section('ownJS')
    <script>
        function contact_status(post_data) {
            $('#block_block').show();
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                datatype: 'json',
                url: "{{url('Askprice/status')}}",
                data: post_data,
                error: function(xhr) {
                    alert('Ajax request 發生錯誤');
                    $('#block_block').hide();
                },
                success: function(resp) {
                    if(resp.code){
                        content_areaVM.updateCallerData();
                        Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", "bg-success"]});
                        $('#contactModal_btn').click();
                    }else{
                        // console.error(resp);
                        if(resp.msg){
                            Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    }
                    $('#block_block').hide();
                }
            });
        }

        // 初始化vue
        var content_area_data = {
            searchKey: '',
            timeRange: [],
            model:{
                search_type: 'keyword',
                search: "",
                contacts: [],
                status: '',
                page: 1,
            },
            replyBoxModel:{
                main:{
                    id: 0, 
                    name: '', 
                    phone: '', 
                    email: '', 
                    product_name: '',
                    product_type_name: '', 
                    product_pic: '',
                    create_time: '',
                },
                current: {
                    ask: '',
                    ask_time: '',
                    response: '',
                    response_time: '',
                    price: '',
                    price_final: '',
                    num: '', 
                    expired_date: '',
                    status: 0,
                    agree: 0,
                    bought: 0,
                },
                history: [],
            },

            max_page_num: 9,
            pages: [1],
            last_page: 1,
            total: 0,
        };
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            methods: {
                /*列表相關的方法*/
                searchKeyword: function () {
                    self = this;
                    self.model.search_type = 'keyword';
                    self.getList()
                },
                searchTime: function () {
                    self = this;
                    self.timeRange = $('#searchTimeInput').val().split(" - ");
                    self.model.search_type = 'date';
                    self.getList();
                },
                change_page: function(p){
                    self = this;
                    // $('.table_scroll_area').scrollTop(0);
                    self.model.page = p;
                    self.getList();
                },
                searchStatus: function(status){
                    self = this;
                    self.model.status = status;
                    self.model.page = 1;
                    self.getList();
                },
                getList: async function(){
                    self = this;
                    if(self.model.search_type=='keyword'){
                        searchdata = { 
                            'searchKey': self.searchKey,
                        };
                    }
                    if(self.model.search_type=='date'){
                        searchdata = {
                            'start': self.timeRange[0], 
                            'end':self.timeRange[1],
                        }
                    }
                    searchdata.type = self.model.search_type;
                    searchdata.status = self.model.status;
                    searchdata.page = self.model.page;
                    
                    await $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Askprice/getList')}}",
                        data: searchdata,
                        success: function(resp){
                            // console.log(resp);
                            self.model.search = resp.search;
                            self.model.contacts = resp.contacts;
                            for(var prop in self.model.contacts){
                                if (self.model.contacts[prop]['online'] == 1){
                                    self.model.contacts[prop]['online'] = true;
                                }else{
                                    self.model.contacts[prop]['online'] = false;
                                }
                            }
                            self.last_page = resp.last_page;
                            self.total = resp.total;
                        },
                    });

                    self.init_pages();
                },
                init_pages: function(){
                    self = this;
                    self.pages = [self.model.page];
                    var check_num = 1;
                    while (
                        self.pages.length<self.max_page_num && (
                            self.model.page-check_num>=1 || 
                            self.model.page+check_num<=self.last_page
                        )
                    ) {
                        if(self.model.page-check_num>=1){ 
                            self.pages.push(self.model.page-check_num);
                        }
                        if(self.model.page+check_num<=self.last_page){
                            self.pages.push(self.model.page+check_num);
                        }
                        check_num += 1;
                    }
                    self.pages = self.pages.sort();
                },           

                openBox: async function (contact_id) {
                    self = this;
                    await $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Askprice/getOne')}}",
                        data: {
                            contact_id: contact_id,
                        },
                        success: function(resp){
                            if(resp.main){
                                self.replyBoxModel.main = resp.main;
                                self.replyBoxModel.current = resp.current;
                                self.replyBoxModel.history = resp.history;
                            }
                        },
                    });
                    $('#contactModal_btn').click();
                },
                del_contact: function (contact_id) {
                    if(!confirm('確定刪除？')){ return; }
                    location.href = "{{url('Askprice/delete')}}?id="+contact_id;
                },

                /*詳細內容相關的方法*/
                ajaxSubmit: function () {
                    if(!confirm('確定儲存紀錄？\n儲存後就不可再修改')){ return; }
                    self = this;
                    var Data = {
                        id: self.replyBoxModel.current.id,
                        response: self.replyBoxModel.current.response,
                        status: 1,
                    }
                    contact_status(Data);
                },
                ajaxSubmit_agree: function () {
                    if(!confirm('確定同意價格詢問？\n確定後消費者即可以此價格進行購買')){ return; }
                    self = this;
                    var post_data = {
                        id: self.replyBoxModel.current.id,
                        response: self.replyBoxModel.current.response,
                        price_final: self.replyBoxModel.current.price_final,
                        expired_date: self.replyBoxModel.current.expired_date,
                        status: 1,
                        agree: 1,
                    }
                    contact_status(post_data);
                },
                updateCallerData: function () {
                    self = this;
                    self.replyBoxModel.current.status = 1;
                    self.getList();
                },
            }
        });
        $(document).ready(()=>{
            content_areaVM.searchKeyword();
        });

        function multiDelete() {
            if(!confirm('確定刪除？')){ return; }
            var form = document.createElement("form");
            form.action = "{{url('Askprice/multiDelete')}}";
            form.method = "post";

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            multiId.name = "id";

            form.appendChild(multiId);
            document.body.appendChild(form);
            form.submit();
            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.contact_logCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }
    </script>
@endsection
