
		<div class="mb-4 admin-content product_layer_select">
			{{Lang::get('已套用商品')}}：
			<span v-if="model.actProd.length!=0" >
				<a href="###" class="btn sendbtn btn-sm" @click="selectAll()">{{Lang::get('全選商品')}}</a>
			</span>
			<div class="row mt-2">
				<div v-for="(item,index) in model.actProd" class="col-md-4 col-lg-3 col-12 product_item">
					<div class="w-100">
                        <input v-model="item.select" type="checkbox">
                        <span @click="change_actProd_check(index, item)" v-text="item.title"></span>
					</div>
					<img :src="'/public/static/index' + item.pic1"
                         @click="change_actProd_check(index, item)" class="w-100">
				</div>
			</div>
			<div class="d-flex justify-content-center">
				<button @click="delActProd()" class="btn width-50 sendbtn">{{Lang::get('刪除套用商品')}}</button>	
			</div>
		</div>
		<div class="admin-content product_layer_select mb-4">
    		<div class="mb-3">
    			<div  class="mb-1">
    				<a href="###" class="btn sendbtn btn-sm"  @click="getList()">{{Lang::get('重置階層')}}</a>
    			</div>
    			<div  class="mb-2 d-flex flex-wrap">
					<div class="w-100 mb-2">
						<span class="name">{{Lang::get('所在階層')}}：/</span> 
						<template v-for="(currCate, index) in currCates">
							<a href="###" class="ml-1 mr-1 layer-item"
							@click="changeCate(currCate, index)" v-text="currCate.title"></a> /
						</template>
					</div>
					<div class="w-100 mb-2">
						<span class="name">{{Lang::get('子階層')}}：</span>
						<span v-for="item in model.series" > 
							<a href="###" class="item_tree" @click="getCate(item)" v-text="item.title" getCate></a>
						</span>
						<span v-for="item in model.cate2" class="ml-3"> 
							<a href="###" class="item_tree" @click="getCate2(item)" v-text="item.title" getCate2></a>
						</span>
					</div>
					<div class="w-100">
						<span class="name">{{Lang::get('所在階層商品')}}：</span>
						<span v-if="model.cateProd.length!=0" >
							<span v-text="catProd"></span>
							<a href="###" class="item_tree" @click="selectAllProd()">{{Lang::get('全選商品')}}</a>
						</span>
					</div>
	            </div>
			</div> 
			<div class="mb-3">
	    		<div class="container-fluid">
					<div class="row">
						<div v-for="(item,index) in model.cateProd" class="col-12 col-md-4 col-lg-3 product_item">
							<div>
								<input v-model="item.select" type="checkbox">
	                            <span @click="change_cateProd_check(index, item)" v-text="item.title"></span>
							</div>
							<img :src="'/public/static/index' + item.pic1"
	                             @click="change_cateProd_check(index, item)">
						</div>
					</div>
				</div>
			</div>
			<div class="d-flex justify-content-center"><button class="btn width-50 sendbtn" @click="insertAct()">{{Lang::get('加入套用商品')}}</button></div>
		</div>