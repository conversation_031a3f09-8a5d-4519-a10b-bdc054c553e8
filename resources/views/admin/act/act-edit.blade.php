@extends('admin.Public.aside')
@section('title'){{Lang::get('活動優惠')}} - {{Lang::get('編輯')}}@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('H行銷項目')}}</li>
            <li><a href="{{url('Act/index')}}">{{Lang::get('活動優惠')}}</a></li>
            <li>{{Lang::get('編輯')}}</li>
        </ul>
        <a class="back btn sendbtn" href="{{url('Act/index')}}">
            <span class="bi bi-arrow-left"></span>
        </a>
        <div class="admin-content act">
            <h3 class="main-title w-100">{{Lang::get('基本設定')}}</h3>
            <div class="col-lg-6 col-12">
                <div class="form-group col-12">
                    <label class="name">{{Lang::get('名稱')}}</label>
                    <input type="text" class="form-control" v-model="addModal.name">
                </div>
                <div class="form-group col-12">
                    <label class="name">{{Lang::get('折扣方式')}}</label>
                    <div class="d-flex flex-wrap">
                        <span class="mr-4">
                            <input type="radio" id="discount_type1" name="discount_type" value="1"  v-model="addModal.type">
                            <label  for="discount_type1">{{Lang::get('滿幾元，打幾折')}}</label>
                        </span>
                        <span class="mr-4">
                            <input type="radio" id="discount_type2" name="discount_type" value="2"  v-model="addModal.type">
                            <label  for="discount_type2">{{Lang::get('每滿幾元，扣幾元')}}</label>
                        </span>
                    </div>
                    <div class="d-flex flex-wrap">
                        <span class="mr-4">
                            <input type="radio" id="discount_type3" name="discount_type" value="3"  v-model="addModal.type">
                            <label  for="discount_type3">{{Lang::get('滿幾件，打幾折')}}</label>
                        </span>
                        <span class="mr-4">
                            <input type="radio" id="discount_type4" name="discount_type" value="4"  v-model="addModal.type">
                            <label  for="discount_type4">{{Lang::get('每滿幾件，扣幾元')}}</label>
                        </span>
                    </div>
                    <hr class="mt-0 mb-3">
                    <template v-for="num in [1,2,3]">
                        <div class="d-flex align-items-center mb-1">
                            <input type="checkbox"  v-model="addModal['online'+num]">
                            <span v-if="[2,4].indexOf(Number(addModal.type))!=-1">{{Lang::get('每')}}</span>{{Lang::get('滿')}}
                            <span v-if="[1,2].indexOf(Number(addModal.type))!=-1">{{config('extra.shop.dollar_symbol')}}</span>
                            <input type="number" v-model="addModal['condition'+num]" class="text-right form-control ml-1 mr-1" min="0" >
                            <span v-if="[3,4].indexOf(Number(addModal.type))!=-1">{{Lang::get('件')}}</span>，
                            <span v-if="[2,4].indexOf(Number(addModal.type))!=-1">{{Lang::get('扣')}}</span>
                            <span v-if="[2,4].indexOf(Number(addModal.type))!=-1">{{config('extra.shop.dollar_symbol')}}</span>
                            <span v-if="[1,3].indexOf(Number(addModal.type))!=-1">{{Lang::get('打')}}</span>
                            <input type="number" v-model="addModal['discount'+num]" class="text-right form-control ml-1 mr-1" min="0" >
                            <!-- <span v-if="[1,3].indexOf(Number(addModal.type))!=-1">{{Lang::get('折')}}</span> -->
                        </div>
                    </template>
                    <div class="text-danger" v-if="[1,3].indexOf(Number(addModal.type))!=-1">
                        {{Lang::get('如需打85折，請輸入0.85')}}
                    </div>
                </div>
                <div class="form-group col-sm-6 col-12">
                    <label class="col-form-label">{{Lang::get('開始時間')}}</label>
                    <input type="date" class="form-control start_time" v-model="addModal.start_time">
                </div>
                <div class="form-group col-sm-6 col-12">
                    <label class="col-form-label">{{Lang::get('結束時間')}}</label>
                    <input type="checkbox" style="margin-left:20px" id="noEndTime" v-model="addModal.noEndTime">
                    <label for="noEndTime">{{Lang::get('沒有結束日期')}}</label>
                    <input type="date" class="form-control end_time" v-model="addModal.end_time"
                           :style="{'display': addModal.noEndTime ? 'none' : 'inline-block' }">
                </div>
            </div>
            <div class="col-lg-6 col-12">
                <div class="col-12">
                    {{Lang::get('介紹圖片')}}：
                    <span class="remark text-danger d-inline">({{Lang::get('建議尺寸')}}:1100px*{{Lang::get('高度不限')}})</span>
                </div>
                <div class="col-lg-6">
                    <input type='file' name="img" @change="onFileSelect($event)" accept="image/png, image/jpeg">
                    <img v-if="addModal.img!=''" :src="addModal.img" class="w-100" style="max-width: 300px;"/>
                </div>
                <div class="form-group col-sm-12 col-12">
                    <label class="col-form-label">{{Lang::get('簡介')}}</label>
                    <textarea rows="5" class="form-control" v-model="addModal.content"></textarea>
                </div>
            </div>
            <a class="btn sendbtn m-auto btn-sm" @click="do_update()">{{Lang::get('更新基本設定')}}</a>
        </div>
        <span class="text-danger m-auto remark">
            (
                {{Lang::get('商品不可重複設定。')}}
                @if(!isset(config('control.close_function_current')['折扣優惠']))
                    {{Lang::get('也不可與「立馬省優惠」共用')}}
                @endif
            )
        </span>
        @include('admin.act.product_selector')
    </div>
@endsection

@section('ownJS')
    <script>
        var content_area_data = {
            model: {
                location: '0',
                act: {},
                series: [],
                // cate: {},
                cate2: {},
                cateProd: [],
                actProd: [],
            },
            catProd: "",
            currCates: [],

            actId: {{$data['actId']}},
            addModal:{
                act_type: 1,
                noEndTime: false,
                img:'',
            },
        };
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            methods: {
                getActProd: function(){
                    self = this;
                    Vue.set(self.model, 'actProd', []);
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Act/getActProd')}}",
                        data: {actId:self.actId},
                        success: function(resp){
                            // console.log(resp.actProd);
                            for (var prop in resp.actProd){
                                resp.actProd[prop]['select'] = false;
                                Vue.set(self.model.actProd, prop, resp.actProd[prop]);
                            }
                        }
                    });
                },
                getList: function(){
                    self = this;
                    self.model.act          = {};
                    self.model.series       = [];
                    self.model.cate2        = {};
                    self.model.cateProd     = [];
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Product/getList')}}",
                        data: {actId:self.actId, langId:null},
                        success: function(resp){
                            // console.log(resp);
                            self.getCateProd({cateId:0});
                            self.model.series = resp.message;
                            self.currCates = [];
                        }
                    });
                },
                changeCate: function(item, index){
                    self = this;
                    self.currCates = self.currCates.slice(0, index);
                    if(index==0){
                        self.getCate(item);
                    }else{
                        self.getCate2(item);
                    }
                },
                getCate: function(item){
                    // console.log(item.id);
                    self = this;
                    self.model.series = [];
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Product/getCate')}}",
                        data: {seriesId:item.id},
                        success: function(resp){
                            resp['first'] = true;
                            // console.log(resp)
                            self.getCateProd(resp);

                            self.model.cate2 = resp.cate;
                            self.currCates.push(item);
                            for (var prop in self.model.series){
                                self.model.series[prop]['select'] = false;
                            }
                        }
                    });
                },
                getCate2: function(item){
                    // console.log(item.id);
                    self = this;
                    self.model.cate2 = {};
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Product/getCate2')}}",
                        data: {seriesId:item.id},
                        success: function(resp){
                            self.model.cate2 = resp.cate;
                            self.currCates.push(item);

                            if(resp.getCateProd == 1){
                                self.model.cate2 = {};
                            }

                            self.getCateProd(resp);
                        }
                    });
                },
                getCateProd: function (item){
                    console.log(item);
                    self = this;
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Product/getCateProd')}}",
                        data: {cateId:item.seriesId, first:item.first},
                        success: function(resp){
                            self.catProd = item.title;
                            for (var prop in resp.productinfo){
                                resp.productinfo[prop]['select'] = false;
                            }
                            self.model.cateProd = resp.productinfo;
                        }
                    });
                },
                selDiscType: function(typeId){
                    self = this;
                    for(var prop in self.showDiscType){
                        self.showDiscType[prop] = false;
                        if (prop == typeId){
                            self.showDiscType[prop] = true;
                        }
                    }
                },
                insertAct: function(){
                    self = this;
                    $('#block_block').show();
                    //console.log(self.model.series);
                    //console.log('--檢查--');
                    for(var prop in self.model.cateProd){
                        self.model.cateProd[prop]['actId'] = self.actId;
                    }
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Act/insertAct')}}",
                        data: {actId:self.actId, actData:self.model},
                        success: function(resp){
                            if (resp.code){
                                Vue.toasted.show('',{duration:1500});
                                bg_class = 'bg-success';
                                self.getActProd();
                                self.getList();
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            $('#block_block').hide();
                        },
                        error: function(){
                            Vue.toasted.show("{{Lang::get('發生錯誤')}}", {duration:1500, className: ["toasted-primary", 'bg-danger']});
                            $('#block_block').hide();
                        },
                    });
                },
                delActProd: function(){
                    self = this;
                    $('#block_block').show();
                    
                    /* console.info(self.model.actProd);
                    return;
                    for(var prop in self.model.cateProd){
                        self.model.actProd[prop]['actId'] = self.actId;
                    } */
                     
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Act/delActProd')}}",
                        data: {
                            actId:self.actId,
                            cateProd:self.model.actProd,
                        },
                        success: function(resp){
                            if (resp.code){
                                Vue.toasted.show('',{duration:1500});
                                bg_class = 'bg-success';
                                self.getActProd();
                                self.getList();
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            $('#block_block').hide();
                        },
                        error: function(){
                            Vue.toasted.show("{{Lang::get('發生錯誤')}}", {duration:1500, className: ["toasted-primary", 'bg-danger']});
                            $('#block_block').hide();
                        },
                    });
                },
                selectAll: function(){
                    self = this;
                    for(var prop in self.model.actProd){
                        item = self.model.actProd[prop];
                        item['select'] = true;
                        Vue.set(self.model.actProd, prop, item);
                    }
                },
                selectAllProd: function(){
                    self = this;
                    for(var prop in self.model.cateProd){
                        item = self.model.cateProd[prop];
                        item['select'] = true;
                        Vue.set(self.model.cateProd, prop, item);
                        self.model.cateProd[prop]['select'] = true;
                    }
                    
                },
                change_actProd_check: function(index, item){
                    self = this;
                    item.select = item.select ? false : true;
                    Vue.set(self.model.actProd, index, item);
                },
                change_cateProd_check: function(index, item){
                    self = this;
                    item.select = item.select ? false : true;
                    Vue.set(self.model.cateProd, index, item);
                },


                getActList: function(searchdata){
                    self = this;
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Act/getActList')}}",
                        data: searchdata,
                        success: function(resp){
                            // console.log(resp);
                            self.addModal = resp.actList[0];
                            if(self.addModal.img){
                                self.addModal.img = '{{__UPLOAD__}}' + self.addModal.img;
                            }
                            console.log(self.addModal);

                            self.addModal.start_time = self.addModal.start;
                            if(self.addModal.end=="{{Lang::get('無時間')}}"){
                                $('#noEndTime').click();
                            }else{
                                self.addModal.end_time = self.addModal.end;
                            }
                            if(self.addModal.img){
                                self.addModal.img = self.addModal.img;
                            }

                        },
                    });
                },
                do_update: function(){
                    self = this;
                    var postData = Object.assign({}, self.addModal);
                    postData['start_time'] = $('input.start_time').val();
                    postData['end_time'] = $('input.end_time').val();
                    if(self.img_base64){ postData['img_base64'] = self.img_base64; }
                    $.ajax({
                        method: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url: "{{url('Act/update')}}",
                        data: postData,
                    }).success(function(resp){
                        if(resp.code==1){
                            self.img_base64 = '';
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-success"]});
                        }else{
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    }).error(function(){
                    })//error
                },
                onFileSelect: function($event){
                    var file = ($event.srcElement || $event.target).files[0];
                    content_areaVM.addModal.img = URL.createObjectURL(file);
                    const reader = new FileReader();
                    reader.addEventListener("load", () => {
                        // convert image file to base64 string
                        content_areaVM.img_base64 = reader.result;
                    }, false);
                    if (file) {
                        reader.readAsDataURL(file);
                    }
                },
            },
        });
        content_areaVM.getActList({ id: content_area_data.actId });
        content_areaVM.getActProd();
        content_areaVM.getList();
	</script>
@endsection