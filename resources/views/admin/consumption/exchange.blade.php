@extends('admin.Public.aside')
@section('title')H行銷項目 > 消費累積兌換@endsection
@section('css')
@endsection

@section('content')
    <!-- 新增/編輯開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">兌換內容</h5>
                </div>
                <div class="modal-body">
                    <p>
                        贈品圖片：
                        <input type="file" @change="select_file($event)"/>
                        <div v-if="aeModel.pic">
                            <img v-if="aeModel.pic.slice(0,4) !='data'" :src="'/public/static/index/' + aeModel.pic" style="max-width: 100%" >
                            <img v-if="aeModel.pic.slice(0,4) =='data'" :src="aeModel.pic" style="max-width: 100%" >
                        </div>
                        <span class="text-danger remark">建議尺寸：400*400px</span>
                    </p>
                    <p>
                        贈品說明：<input type="text" v-model="aeModel.name"/>
                    </p>
                    <p>
                        狀態：
                        <select v-model="aeModel.online" :class="[aeModel.online== 0 ? 'color_red' : 'color_green']">
                            <option class="color_green" value="1">啟用</option>
                            <option class="color_red" value="0">停用</option>
                        </select>
                    </p>
                    <p>
                        需求累積消費：<input type="text" v-model="aeModel.price"><br>
                    </p>
                </div>
                <div class="modal-footer justify-content-center flex-wrap">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增/編輯結束 -->

    <div id="content">
      
        <ul id="title" class="brand-menu">
            <li ><a href="###">H行銷項目</a></li>
            <li><a href="###">消費累積兌換</a></li>
        </ul>

        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox">
                @csrf
                <select class="border mr-1 mb-1 text-center" name="searchOnline" v-model="search.searchOnline">
                    <option value="-1">全部</option>
                    <option value="1">啟用</option>
                    <option value="0">停用</option>
                </select>
                <input  class="form-control mb-1 mr-1 text-center" name="searchKey" type="text" v-model="search.searchKey"
                        placeholder="請輸入贈品說明">

                <a class="btn sendbtn mb-1 mr-1" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn mb-1" onclick="location.href='{{url('Consumption/exchange')}}'">清除搜尋</a>
            </form>
        </div>


        <!--新增與編輯-->
        <div class="frame d-flex flex-wrap align-items-center justify-content-between">
            <div class="width-50">
                <a href="###" class="btn clearbtn" @click="newBlock"><i class="bi bi-plus-lg add small"></i>  新增</a>
                <span class="d-inline-block position-relative">
                    <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                    <div class="edit-item none">
                        <a @click="multiOnline();">
                            <p class="mb-0">上架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled checked><span class="slider round"></span>
                            </label>
                        </a>
                        <a @click="multiOffline();">
                            <p class="mb-0">下架&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled><span class="slider round"></span>
                            </label>
                        </a>
                        <a @click="multiDelete();" class="border-top">
                            刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                        </a>
                    </div>
                </span>
                
            </div>

            <ul class="text-danger remark col-12 col-lg-3">
                <li>領取後會員的累積消費金額不會歸零</li>
                <li>可修改狀態控制是否允許領取，但不影響已領取的會員</li>
                <li>每個贈品會員都能領取一次，編輯過後也不會重新計算領取次數</li>
            </ul>
        </div>

        <!--表格 開始-->
        <div class="edit_form" >
            <table class="table table-rwd table-mobile" style="min-width: 1200px;">
                <thead>
                    <tr>
                        <th style="width: 100px">
                            <input type="checkbox" style="cursor:pointer;" v-model="select_all" @click="selectAll()">
                            編號
                        </th>
                        <th style="width: 100px">狀態</th>
                        <th>贈品圖片</th>
                        <th style="width: 300px">贈品說明</th>
                        <th style="width: 300px">需求累積消費</th>
                        <th style="width: 200px">領取人數</th>
                        <th style="width: 100px">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(vo, index) in datas">
                        <td th-data="編號">
                            <input type="checkbox" v-model="vo.select">
                            <span v-text="index + 1"></span>
                        </td>
                        <td th-data="狀態">
                            <select v-model="vo.online" :class="[vo.online== 0 ? 'color_red' : 'color_green']" @change="save_one(index)">
                                <option class="color_green" value="1">啟用</option>
                                <option class="color_red" value="0">停用</option>
                            </select>
                        </td>
                        <td th-data="贈品圖片"><img v-if="vo.pic" :src="'/public/static/index/' + vo.pic" style="max-width:100px"></td>
                        <td th-data="贈品說明">
                            <a href="###" @click="openBox(index)" v-text="vo.name"></a>
                        </td>
                        <td th-data="需求累積消費"><span v-text="vo.price"></span>元</td>
                        <td th-data="領取人數">
                            <span v-text="vo.winner"></span>人
                            (<a :href="'{{url('Consumption/exchange_list')}}?exchange_id=' + vo.id" target="_blank">查看名單</a>)
                        </td>
                        <td th-data="操作">
                            <button type="button" @click="del(vo.id)" class="btn btn-danger p-1">刪除</button>
                        </td>              
                    </tr>
                </tbody>
                
                
            </table>
        </div>

        <!--表格 結束-->
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script>
        var datas = JSON.parse(`{{json_encode($data['datas'])}}`.replace(/&quot;/g,'"').trim());
        for (var i = 0; i < datas.length; i++) {
            datas[i]['select'] = false;
        }

        // console.log(datas);
        var content_area_data = {
            select_all: false,
            search: {searchOnline:{{$data['searchOnline']}}, searchKey: '{{$data['searchKey']}}'},
            datas: datas,
            aeModel: { id: 0, pic: "", name: "", price: 0, online: 1,
            },
        };
        var content_areaVM = new Vue({
            el: "#content_area",
            data: content_area_data,
            methods: {
                /*開啟新增畫面*/
                newBlock: function (){
                    this.aeModel.id = 0;
                    this.aeModel.pic = "";
                    this.aeModel.name = "";
                    this.aeModel.price = 0;
                    this.aeModel.online = 1;

                    $('input[type="file"]').val("");
                    $('#functionModal_btn').click();
                },
                /*開啟編輯畫面*/
                openBox: function(index){
                    self = this;
                    self.aeModel = Object.assign({}, self.datas[index]);
                    // console.log(self.aeModel);

                    $('input[type="file"]').val("");
                    $('#functionModal_btn').click();
                },
                /*新增、編輯欄位*/
                ajaxSubmit: function(){
                    self = this;
                    self.ajax_save_data(self.aeModel);
                },
                /*儲存單個欄位*/
                save_one: function(index){
                    self = this;
                    self.ajax_save_data(self.datas[index]);
                },
                /*送出儲存資料請求*/
                ajax_save_data: function(post_data, reload=true){
                    $.ajax({
                        type:'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        data: post_data,
                        url:"{{url('Consumption/exchange_save')}}",
                        success:function(res){
                            Vue.toasted.show(res.msg, {duration:1500});
                            if(res.code=='1' && reload){
                                location.reload();
                            }
                        }
                    });
                },

                /*刪除欄位*/
                del: function(id){
                    $.ajax({
                        type:'POST',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        data: {id: id},
                        url:"{{url('Consumption/exchange_delete')}}",
                        success:function(res){
                            Vue.toasted.show(res.msg, {duration:1500});
                            if(res.code=='1'){
                                location.reload();
                            }
                        }
                    });
                },
                
                /*列表操作*/
                    selectAll: function(){
                        self = this;
                        var select_type = self.select_all ? false : true;
                        for (var i = 0; i < self.datas.length; i++) {
                            self.datas[i]['select'] = select_type;
                        }
                    },
                    multiOnline: function(){
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.datas[i]['online'] = 1;
                                self.ajax_save_data(self.datas[i], false);
                            }
                        }
                        Vue.toasted.show("修改成功", {duration:1500});
                        setTimeout(function(){location.reload();}, 300);
                    },
                    multiOffline: function(){
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.datas[i]['online'] = 0;
                                self.ajax_save_data(self.datas[i], false);
                            }
                        }
                        setTimeout(function(){location.reload();}, 300);
                    },
                    multiDelete: function(){
                        if(!confirm('確認批次刪除？')){return}
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.del(self.datas[i]['id']);
                            }
                        }
                        setTimeout(function(){location.reload();}, 300);
                    },
            
                /*選擇圖檔*/
                select_file: function($event){
                    self = this;
                    var files = $($event.currentTarget)[0].files;
                    if(!files){ return; }
                    file = files[0];

                    var extension = file.name.split('.').pop().toLowerCase(),  //file extension from input file
                    isSuccess = ['png', 'jpg', 'jpeg', 'gif', 'tif'].indexOf(extension) > -1;

                    if (isSuccess) { //yes
                        var reader = new FileReader();
                        reader.onload = function (e) {
                            self.aeModel.pic = e.target.result;
                        }
                        reader.readAsDataURL(file);
                    }
                    else{
                        Vue.toasted.show('請上傳圖片',{duration:1500});
                        $($event.currentTarget).attr('value', '');

                    }
                }
            },
        });
    </script>
@endsection