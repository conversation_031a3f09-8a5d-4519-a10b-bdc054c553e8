@extends('admin.Public.aside')
@section('title')G功能應用項目 > 掃碼付費管理@endsection
@section('css')
@endsection

@section('content')
    <div id="content">
       
        <ul id="title" class="brand-menu">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">掃碼付費管理</a></li>
        </ul>

        <!-- <div style="padding:10px 30px;">
            <form action="" name="searchForm" method="get" style="display: inline-block;">
                @csrf
                <input class="enter_search" name="searchKey" type="text" v-model="search.searchKey"
                       style="text-align:center;width: 300px;" placeholder="請輸入名稱">

                <a class="button" onclick="searchForm.submit();">搜尋</a>
                <a class="button" onclick="location.href='{{url('Consumption/pay_list')}}'">清除搜尋</a>
            </form>
        </div> -->

        <!--新增與編輯-->
        <div class="frame d-flex flex-wrap align-items-center justify-content-between">
            <div class="width-50">
                <div class="btn sendbtn" @click="multiDelete()">刪除 <span class="bi bi-trash"></span></div>
            </div>

            <ul class="text-danger remark m-0">
                <li>審核通過後將於系統中建立訂單，並處理後續消費回饋(不含紅利點數贈送)</li>
                <li>消費者付費網址：
                    {{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}/consumption/create_pay?distributor_id={{$data['my_distributor_id'] ?? ""}}
                    ，可自行將此網址製作成QR code供消費者掃描
                </li>
            </ul>
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table table-rwd" style="min-width: 1400px;">
                <thead>
                    <tr>
                        <th style="width:80px">
                            <input type="checkbox" style="cursor:pointer;" v-model="select_all" @click="selectAll()">
                            編號
                        </th>
                        <th style="width:60px">狀態</th>
                        <th style="width:120px">會員姓名</th>
                        <th style="width:160px">會員編號</th>
                        <th>會員信箱</th>
                        <th style="width:160px">會員手機</th>
                        <th style="width:160px">付款金額</th>
                        <th>付款時間</th>
                        <th style="width:80px">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(vo, index) in datas">
                        <td th-data="編號">
                            <input type="checkbox" v-model="vo.select">
                            <span v-text="index + 1"></span>
                        </td>
                        <td th-data="狀態">
                            <select v-model="vo.audit" :class="[vo.audit== 0 ? 'color_red' : 'color_green']" @change="save_one(index)">
                                <option class="color_green" value="1">通過</option>
                                <option class="color_red" value="0">未通過</option>
                            </select>
                        </td>
                        <td th-data="會員姓名"><span v-if="vo.user" v-text="vo.user.name"></span></td>
                        <td th-data="會員編號"><span v-if="vo.user" v-text="vo.user.number"></span></td>
                        <td th-data="會員信箱"><span v-if="vo.user" v-text="vo.user.email"></span></td>
                        <td th-data="會員手機"><span v-if="vo.user" v-text="vo.user.phone"></span></td>
                        <td th-data="付款金額" v-text="vo.price"></td>
                        <td th-data="付款時間" v-text="vo.f_datetime"></td>
                        <td th-data="操作">
                            <button type="button" @click="del(vo.id)" 
                                    class="btn clearbtn">刪除</button>
                        </td>              
                    </tr>
                </tbody>
                
                
            </table>
        </div>
        
        <!--表格 結束-->
    </div>
@endsection
@section('ownJS')
    <!-- <script type="text/javascript" src="{{__PUBLIC__}}/js/moment.min.js"></script>   -->
    <!-- <script type="text/javascript" src="{{__PUBLIC__}}/js/daterangepicker.js"></script> -->
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script>
        var datas = `{{json_encode($data['datas'], JSON_UNESCAPED_UNICODE)}}` ? JSON.parse(`{{json_encode($data['datas'], JSON_UNESCAPED_UNICODE)}}`.replace(/&quot;/g, '"').trim()) : [];
        for (var i = 0; i < datas.length; i++) {
            datas[i]['select'] = false;
        }

        // console.log(datas);
        var content_area_data = {
            select_all: false,
            search: {searchKey: '{{$data["searchKey"]}}'},
            datas: datas,
        };
        var content_areaVM = new Vue({
            el: "#content_area",
            data: content_area_data,
            methods: {
                /*儲存單個欄位*/
                save_one: function(index){
                    self = this;
                    self.ajax_save_data(self.datas[index]);
                },
                /*送出儲存資料請求*/
                ajax_save_data: function(post_data, reload=true){
                    $.ajax({
                        type:'POST',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        data: post_data,
                        url:"{{url('Consumption/pay_list_save')}}",
                        success:function(res){
                            Vue.toasted.show(res.msg, {duration:1500});
                            if(res.code=='1' && reload){
                                location.reload();
                            }
                        }
                    });
                },

                /*刪除欄位*/
                del: function(id){
                    $.ajax({
                        type:'POST',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        data: {id: id},
                        url:"{{url('Consumption/pay_list_delete')}}",
                        success:function(res){
                            Vue.toasted.show(res.msg, {duration:1500});
                            if(res.code=='1'){
                                location.reload();
                            }
                        }
                    });
                },
                
                /*列表操作*/
                    selectAll: function(){
                        self = this;
                        var select_type = self.select_all ? false : true;
                        for (var i = 0; i < self.datas.length; i++) {
                            self.datas[i]['select'] = select_type;
                        }
                    },
                    multiDelete: function(){
                        if(!confirm('確認批次刪除？')){return}
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.del(self.datas[i]['id']);
                            }
                        }
                        setTimeout(function(){location.reload();}, 300);
                    },
            
            },
        });
    </script>
@endsection