@extends('admin.Public.aside')
@section('title')H行銷項目 > 消費刮刮樂中獎名單@endsection
@section('css')
@endsection

@section('content')
    <div id="content">
       
        <ul id="title" class="brand-menu">
            <li><a href="###">H行銷項目</a></li>
            <li><a href="###">消費刮刮樂</a></li>
        </ul>


        <div class="searchbox mb-4">
            <form action="" name="searchForm" method="get" class="searchForm">
                @csrf
                <div class="item">
                    <select name="draw_id" v-model="search.draw_id">
                        <option value="">全部</option>
                        @foreach($data['consumption_draws'] as $vo)
                            <option value="{{$vo['id']}}">{{$vo['name']}}</option>
                        @endforeach
                    </select>
                    <input class="enter_search" name="searchKey" type="text" v-model="search.searchKey"
                    clsss="form-control w-100  text-center"  placeholder="請輸入獎品說明,中獎人姓名,會員編號">
               
                </div>
                <div class="item">
                    中獎日期區間：
                    <input type="date" name="s_date" v-model="search.s_date">~
                    <input type="date" name="e_date" v-model="search.e_date">
                </div>
                <div class="item">
                    領獎日期區間：<input type="date" name="s_ex_date" v-model="search.s_ex_date">~
                    <input type="date" name="e_ex_date" v-model="search.e_ex_date">
                </div>
               

                <a class="btn sendbtn m-1 btn-sm" onclick="searchForm.submit();">搜尋</a>
                <a class="btn clearbtn m-1 btn-sm" onclick="location.href='{{url('Consumption/draw_list')}}'">清除搜尋</a>
            </form>
        </div>


        <!--新增與編輯-->
    

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table table-rwd" style="min-width: 992px;">
                <thead>
                    <tr>
                        <th style="width:5%">編號</th>
                        <th style="width:5%">獎品圖片</th>
                        <th style="width:15%">獎品說明</th>
                        <th style="width:15%">中獎人姓名</th>
                        <th style="width:15%">會員編號</th>
                        <th style="width:15%">中獎日期</th>
                        <th style="width:15%">領獎日期</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['datas'] as $vo)
                    <tr>
                        <td th-data="編號">{{$vo['index']}}</td>
                        <td th-data="獎品圖片">@if($vo['gift_pic']!='')<img src="{{$vo['gift_pic']}}">@endif</td>
                        <td th-data="獎品說明">{{$vo['gift_name']}}</td>
                        <td th-data="中獎人姓名"><a href="/order/index/edit?id={{$vo['user']['id']}}" target="_blank">{{$vo['user']['name']}}</a></td>
                        <td th-data="會員編號">{{$vo['user']['number']}}</td>
                        <td th-data="中獎日期">{{$vo['createdate']}}</td>
                        <td th-data="領獎日期">{{$vo['ex_date']}}</td>
                    </tr>
                    @endforeach
                </tbody>
                
            
        </table>
        <!--表格 結束-->

        <div class="text-center">
            
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="/public/static/admin/js/action.js"></script>

    <script>
        // console.log(datas);
        var content_area_data = {
            search: {
                draw_id:'{$draw_id}', searchKey:'{{$data['searchKey']}}',
                s_date: '{$s_date}', e_date: '{$e_date}', s_ex_date: '{$s_ex_date}', e_ex_date: '{$e_ex_date}',
            }
        };
        var content_areaVM = new Vue({
            el: "#content_area",
            data: content_area_data,
            methods: {
            },
        });
    </script>
@endsection