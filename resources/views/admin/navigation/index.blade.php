@extends('admin.Public.aside')

@section('htmlTitle', '導航選單管理')

@section('content')
<div>
    <div class="container-fluid">
        <div class="row mb-2 mt-2">
            <div class="col-12">
                <a href="{{ route('admin.navigation.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新增選單
                </a>
            </div>
        </div>

        @if(session('success'))
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                {{ session('error') }}
            </div>
        @endif

        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="menuTable">
                <thead>
                    <tr>
                        <th>排序</th>
                        <th>選單識別碼</th>
                        <th>選單名稱</th>
                        <th>連結</th>
                        <th>開啟方式</th>
                        <th>狀態</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="sortable">
                    @foreach($data['menus'] as $menu)
                        <tr data-id="{{ $menu->id }}">
                            <td class="text-center">
                                <span class="handle" style="cursor: move;">
                                    <i class="fas fa-grip-vertical"></i>
                                </span>
                                {{ $menu->sort_order }}
                            </td>
                            <td>{{ $menu->menu_key }}</td>
                            <td>{{ $menu->menu_name }}</td>
                            <td>{{ $menu->menu_url }}</td>
                            <td>
                                @if($menu->target === '_blank')
                                    <span class="badge badge-info">新視窗</span>
                                @else
                                    <span class="badge badge-secondary">當前視窗</span>
                                @endif
                            </td>
                            <td>
                                @if($menu->status)
                                    <span class="badge badge-success">啟用</span>
                                @else
                                    <span class="badge badge-danger">停用</span>
                                @endif
                            </td>
                            <td>
                                <a href="{{ route('admin.navigation.edit', $menu->id) }}"
                                   class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i> 編輯
                                </a>
                                <form method="POST" action="{{ route('admin.navigation.destroy', $menu->id) }}"
                                      style="display: inline-block;"
                                      onsubmit="return confirm('確定要刪除此選單嗎？')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i> 刪除
                                    </button>
                                </form>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@section('css_header')
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css">
<style>
#sortable tr {
    cursor: move;
}
#sortable tr:hover {
    background-color: #f5f5f5;
}
</style>
@endsection

@section('ownJS')
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
$(document).ready(function() {
    // 拖拽排序
    $("#sortable").sortable({
        handle: ".handle",
        axis: "y",
        helper: function(e, tr) {
            var $originals = tr.children();
            var $helper = tr.clone();
            $helper.children().each(function(index) {
                // Set helper cell sizes to match the original sizes
                $(this).width($originals.eq(index).width());
            });
            return $helper;
        },
        forcePlaceholderSize: true,
        update: function(event, ui) {
            var orders = [];
            $('#sortable tr').each(function(index) {
                orders.push({
                    id: $(this).data('id'),
                    sort_order: index + 1
                });
            });

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $.post('/admin/api/navigation/order', {
                orders: orders
            }).done(function(response) {
                if(response.success) {
                    // 更新顯示的排序數字
                    $('#sortable tr').each(function(index) {
                        $(this).find('td:first').html(
                            '<span class="handle" style="cursor: move;"><i class="fas fa-grip-vertical"></i></span> ' + (index + 1)
                        );
                    });

                    // 顯示成功訊息
                    $('<div class="alert alert-success alert-dismissible">' +
                      '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
                      response.message + '</div>').prependTo('.container-fluid').delay(3000).fadeOut();
                }
            }).fail(function(e) {
                console.log(e);
                // alert('排序更新失敗');
                // location.reload();
            });
        }
    });
});
</script>
@endsection
