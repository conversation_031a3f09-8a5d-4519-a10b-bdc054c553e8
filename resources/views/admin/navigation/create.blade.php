@extends('admin.Public.aside')

@section('htmlTitle', '新增導航選單')

@section('content')
<div>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.navigation.index') }}">導航選單管理</a></li>
            <li class="breadcrumb-item active">新增選單</li>
        </ol>
    </nav>

    <div class="container-fluid">
        <form method="POST" action="{{ route('admin.navigation.store') }}">
            @csrf

            @if(isset($errors) && $errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="menu_key">選單識別碼 <span class="text-danger">*</span></label>
                        <input type="text"
                               class="form-control"
                               id="menu_key"
                               name="menu_key"
                               value="{{ old('menu_key') }}"
                               placeholder="例如: about, news"
                               pattern="[a-zA-Z_][a-zA-Z0-9_-]*"
                               required>
                        <small class="form-text text-muted">只能包含字母、數字、底線和連字符</small>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label for="menu_name">選單名稱 <span class="text-danger">*</span></label>
                        <input type="text"
                               class="form-control"
                               id="menu_name"
                               name="menu_name"
                               value="{{ old('menu_name') }}"
                               placeholder="例如: 關於我們"
                               required>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label for="menu_url">選單連結 <span class="text-danger">*</span></label>
                        <input type="text"
                               class="form-control"
                               id="menu_url"
                               name="menu_url"
                               value="{{ old('menu_url') }}"
                               placeholder="例如: /about 或 https://example.com"
                               required>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label for="sort_order">排序 <span class="text-danger">*</span></label>
                        <input type="number"
                               class="form-control"
                               id="sort_order"
                               name="sort_order"
                               value="{{ old('sort_order', 99) }}"
                               min="0"
                               required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="target">開啟方式 <span class="text-danger">*</span></label>
                        <select class="form-control"
                                id="target"
                                name="target"
                                required>
                            <option value="_self" {{ old('target') === '_self' ? 'selected' : '' }}>當前視窗</option>
                            <option value="_blank" {{ old('target') === '_blank' ? 'selected' : '' }}>新視窗</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label for="status">狀態 <span class="text-danger">*</span></label>
                        <select class="form-control"
                                id="status"
                                name="status"
                                required>
                            <option value="1" {{ old('status', '1') === '1' ? 'selected' : '' }}>啟用</option>
                            <option value="0" {{ old('status') === '0' ? 'selected' : '' }}>停用</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> 儲存
                </button>
                <a href="{{ route('admin.navigation.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>
        </form>
    </div>
</div>
@endsection

@section('javascript')
<script>
$(document).ready(function() {
    // 自動生成選單識別碼（從選單名稱）
    $('#menu_name').on('input', function() {
        var menuName = $(this).val();
        var menuKey = menuName.toLowerCase()
                              .replace(/[^\w\s-]/g, '') // 移除特殊字符
                              .replace(/\s+/g, '_')     // 空格替換為底線
                              .replace(/-+/g, '_');     // 連字符替換為底線

        if($('#menu_key').val() === '') {
            $('#menu_key').val(menuKey);
        }
    });
});
</script>
@endsection
