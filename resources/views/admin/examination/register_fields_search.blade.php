<ul class="search-items">
    <li class=" w-100">
        <input type="hidden" name="prod_id" value="{{$data['info_id']}}">
        <input type="text" name="order_number" style="text-align:center" value="{{request()->get('order_number')}}" placeholder="訂單編號">
        <select name="type_id">
            <option value="">品項</option>
            @foreach($data['productinfo_type'] as $a)             
                <option value="{{$a['id']}}" {{App\Services\CommonService::compare_return(request()->get('type_id'), $a['id'], 'selected')}}>{{$a['title']}}</option>
            @endforeach
        </select>
        <select name="receipts_state">
            <option value="" {{App\Services\CommonService::compare_return(request()->get('receipts_state'), '', 'selected')}}>繳費狀態</option>
            <option value="1" {{App\Services\CommonService::compare_return(request()->get('receipts_state'), '1', 'selected')}}>已繳費</option>
            <option value="0" {{App\Services\CommonService::compare_return(request()->get('receipts_state'), '0', 'selected')}}>未繳費</option>
        </select>
    
        <select name="cancel_status">
            <option value="" {{App\Services\CommonService::compare_return(request()->get('cancel_status'), '', 'selected')}}>報名狀態</option>
            <option value="0" {{App\Services\CommonService::compare_return(request()->get('cancel_status'), '0', 'selected')}}>完成</option>
            <option value="1" {{App\Services\CommonService::compare_return(request()->get('cancel_status'), '1', 'selected')}}>已取消</option>
        </select>
    
        @if(config('control.control_pre_buy')==1 && empty(config('control.close_function_current')['庫存警示']))
        @if($data['productinfo']['pre_buy'])
            <select name="reg_status">
                <option value="" {{App\Services\CommonService::compare_return(request()->get('reg_status'), '', 'selected')}}>候補狀態</option>
                <option value="-1" {{App\Services\CommonService::compare_return(request()->get('reg_status'), '-1', 'selected')}}>正取+已補上</option>
                <option value="1" {{App\Services\CommonService::compare_return(request()->get('reg_status'), '1', 'selected')}}>正取</option>
                <option value="0" {{App\Services\CommonService::compare_return(request()->get('reg_status'), '0', 'selected')}}>候補</option>
                <option value="2" {{App\Services\CommonService::compare_return(request()->get('reg_status'), '2', 'selected')}}>已補上</option>
                <option value="3" {{App\Services\CommonService::compare_return(request()->get('reg_status'), '3', 'selected')}}>無法錄取</option>
            </select>
        @endif
        @endif
    </li>
    <li class="fields_for_search">
        <input type="hidden" id="register_data" name="register_data" value="">
        <!-- 考生資料搜尋 -->
        @foreach($data['fields_for_search'] as $vo)
            <span class="d-inline-flex align-items-start mr-2 mb-1">
                <span>{{$vo['title']}}</span>：
                @switch($vo['type'])
                    @case('text')
                    @case('number')
                    @case('date')
                        <input type="{{$vo['type']}}" name="search_field_id_{{$vo['id']}}" value="{{$vo['ans']}}">
                        @break
                    @case('radio')
                    @case('checkbox')
                    @case('checkbox_time')
                            <div class="">
                                <div class="d-inline-block mr-2">
                                    <input type="{{ App\Services\CommonService::delimitate_string_back_index($vo['type'], '_') }}" id="search_field_id_{{$vo['id']}}_00" name="search_field_id_{{$vo['id']}}" value=""
                                        @if(in_array('', $vo['ans'])) checked @endif
                                    >
                                    <label class="m-0" for="search_field_id_{{$vo['id']}}_00">無</label>
                                </div>
                                @foreach($vo['options'] as $o_k => $option)
                                    <div class="d-inline-block mr-2">
                                        <input type="{{ App\Services\CommonService::delimitate_string_back_index($vo['type'], '_') }}" id="search_field_id_{{$vo['id']}}_{{$o_k}}" name="search_field_id_{{$vo['id']}}" value="{{$option}}"
                                            @if(in_array($option, $vo['ans'])) checked @endif
                                        >
                                        <label class="m-0" for="search_field_id_{{$vo['id']}}_{{$o_k}}">{{$option}}</label>
                                    </div>
                                @endforeach
                            </div>
                        @break
                    @case('checkbox_time')
                        @break
                    @case('select')
                        <select class="" name="search_field_id_{{$vo['id']}}">
                            <option value="">請選擇</option>
                            @foreach($vo['options'] as $o_k => $option)
                                <option value="{{$option}}" @if($option==$vo['ans']) selected @endif>{{$option}}</option>
                            @endforeach
                        </select>
                        @break
                    @case('file')
                        <input type="text" name="search_field_id_{{$vo['id']}}" value="{{$vo['ans']->file_name}}">
                        @break
                    @default
                        <input type="text" name="search_field_id_{{$vo['id']}}" value="">
                        @break
                @endswitch
            </span>
        @endforeach
    </li>
</ul>


