@extends('admin.Public.aside')

@section('title')G功能應用項目 > 商品價格搜尋@endsection

@section('content')
  <iframe id="boxFormIframe" name="boxFormIframe" style="display: none;"></iframe>

  <!-- 新增修改活動開始 -->
  <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
  <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document" id="Box">
      <div class="modal-content">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-header">
          <h5 class="modal-title">新增價格區間</h5>
        </div>
        <form name="boxForm" :action="action" method="post" target="boxFormIframe" enctype="multipart/form-data">
          @csrf
          <div class="modal-body">
            <p>排序(越小越前面)：
              <input type="number" name="orders" v-model="orders" class="form-control">
            </p>
            <p>選項名稱：
              <input type="text" name="title" v-model="title" class="form-control">
            </p>
            <p>比對區間：
              <input type="text" name="content" v-model="content" class="form-control">
              <sapn class="text-danger remark">(格式：上界~下界，可不輸入上界或下界，ex：「1000~」表示搜尋1000以上)</sapn>
            </p>
          </div>
          <div class="modal-footer">
            <input type="hidden" name="id" v-model="id">
            <button type="button" class="btn sendbtn" @click="formSubmit">儲存</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <!-- 新增修改活動結束 -->

  <div id="content">
    <ul id="title" class="brand-menu">
      <li onclick="javascript:location.href='index'" ><a href="###">G功能應用項目</a></li>
      <li><a href="{{url('Pricesearch/index')}}">商品價格搜尋</a></li>
    </ul>
    <div class="searchbox">
      <form action="" name="searchForm" method="get" class="searchKeyBox flex-nowrap"> 
        @csrf
        <input type="text" name="searchKey" class="form-control mr-1" value="{{$_GET['searchKey']??''}}" placeholder="搜尋標題">
        <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
      </form>
    </div>

    <!--新增與編輯-->
    <div class="frame">
      <a href="###" class="btn clearbtn" onclick="newBlock();"><i class="bi bi-plus-lg add small"></i> 新增</a>
        <span class="d-inline-block position-relative">
        <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
        <!-- 編輯開始 -->
        <div class="edit-item none">
          <a onclick="multiOnline();">
            <p class="mb-0">上架&nbsp;</p>
            <label class="switch" name="0">
              <input type="checkbox" disabled checked><span class="slider round"></span>
            </label>
          </a>
          
          <a onclick="multiOffline();">
            <p class="mb-0">下架&nbsp;</p>
            <label class="switch" name="0">
              <input type="checkbox" disabled><span class="slider round"></span>
            </label>
          </a>
           
          <a onclick="multiDelete();" class="border-top">
            刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
          </a>
        </div>
        <!-- 編輯結束 -->
      </span>
      
    </div>
    <div class="edit_form" >
      <table class="table table-rwd" style="min-width: 992px;">
        <thead>
          <tr>
            <th style="width: 20px;"><input type="checkbox" class="pricesearchCheckboxAll" onclick="$('.table input[class=pricesearchCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
            <th style="width: 80px;">上下架</th>
            <th>排序(越小越前面)</th>
            <th>選項名稱</th>
            <th>比對區間</th>
            <th style="width: 60px;">刪除</th>
          </tr>
        </thead>
        <tbody>
          @if(empty($data['pricesearch'])==true)
          <td colspan='6'>沒有數據</td>
          @else
          @foreach($data['pricesearch'] as $vo)
          <tr id="pricesearch_{{$vo['id']}}">
            <td><input type="checkbox" class="pricesearchCheckbox" alt="{{$vo['id']}}"></td>
            <td>
              <label class="switch">
                <input type="checkbox" v-model="online">
                <span class="slider round"></span>
              </label>
            </td>
            <td><span v-text="orders"></span></td>
            <td><a href="###" @click="openBox" v-text="title"></a></td>
            <td><a href="###" @click="openBox" v-text="content"><a></td>
            <td><span class="bi bi-trash" onclick="delete_one('{{$vo['id']}}')"></span></td>
          </tr>
          @endforeach
          @endif
        </tbody>
        
        
      </table>
    </div>
     
  </div>
@endsection
@section('ownJS')
  <script src="{{__PUBLIC__}}/js/action.js"></script>
  <script>
    $(function() {
      $(document).click(function() {
        $('.edit-item').fadeOut();
      })
      $('.edit').click(function(event) {
        event.stopPropagation();
      })
      $('.edit-item').click(function(event) {
        event.stopPropagation();
      })
    });

    Vue.prototype.blockCtrl = function (blockData) {
      $.ajax({
        url: "{{url('Pricesearch/cellCtrl')}}",
        type: 'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
        },
        dataType: 'json',
        data: blockData,
        success: function(response) {
          if(response.status){
            //alert('留言成功');
          }else{
            alert('更改失敗');
            console.log(response.message);
          }
        },
        error: function(xhr) {
          alert('更改失敗');
          console.log(xhr);
        }
      });
    };

    var Box = {
      title: "", content: "", id: 0, orders:0,
      action: "",
      caller: null
    }
    var BoxVM = new Vue({
      el: '#Box', 
      data: Box,
      methods: {
        formSubmit: function () {
          $('#block_block').show();
          setTimeout(function(){ document.boxForm.submit(); }, 50);
        },
        previewImg: function () {
          console.log(this.$refs.img.files);
          var reader = new FileReader();
          reader.onload = function (e) {
            Box.src = e.target.result;
          }
          reader.readAsDataURL(this.$refs.img.files[0]);
        },
        updateCallerData: function () {
          this.caller.title = this.title;
          this.caller.orders = this.orders;
          this.caller.content = this.content;
          $('#functionModal').modal('hide');
        }
      }
    });

    $('#boxFormIframe').load(function () {
      var uploadStatus = $(this).contents().find('h1').text();
      if(uploadStatus == "上傳成功"){
        // alert("上傳成功");
        // if(BoxVM.caller == 'new'){
        //     location.reload();
        // }else{
        //     BoxVM.updateCallerData();
        // }
        location.reload();
      }else{
        alert("上傳失敗");
        console.log($(this).contents().find('body').text());
      }
      $('#block_block').hide();
    });

    ///////andy/////多行文字串//////
    function heredoc(fn) {
      return fn.toString().replace(/[\\]/g,"") + '\n'
    }
    ///////////////////////////////
    @if(empty($data['pricesearch'])==false)
    @foreach($data['pricesearch'] as $vo)
      ///////andy/////多行文字串////////////////////
      var tmpl = heredoc(function(){
        `{!! str_replace('/','\\/',addslashes($vo['content'])) !!}`
      });
      tmpl = tmpl.split('`');
      delete tmpl[0];
      var lastnum = tmpl.length -1;
      delete tmpl[lastnum];
      // console.log(tmpl);
      /////////////////////////////////////////////

      var pricesearch_{{$vo['id']}} = {
        id: "{{$vo['id']}}",
        title: "{{$vo['title']}}",
        orders: "{{$vo['orders']}}",
        content: tmpl.join(''),
        online: +"{{$vo['online']}}",
        action: "{{url('Pricesearch/update')}}"
      }
      var pricesearch_{{$vo['id']}}_VM = new Vue({
        el: '#pricesearch_{{$vo['id']}}',
        data: pricesearch_{{$vo['id']}},
        watch: {
          online: function () {
            blockData = {
              id: this.id,
              online: this.online ? 1 : 0
            }
            this.blockCtrl(blockData);
          }
        },
        methods: {
          openBox: function () {
            BoxVM.id = this.id;
            BoxVM.title = this.title;
            BoxVM.orders = this.orders;
            BoxVM.content = this.content;
            BoxVM.action = this.action;
            BoxVM.caller = this;
            $('#functionModal_btn').click();
          }
        }
      });
    @endforeach
    @endif

    function newBlock(){
      BoxVM.id = "";
      BoxVM.title = "";
      BoxVM.orders = 0;
      BoxVM.content = "";
      BoxVM.action = "{{url('Pricesearch/doCreate')}}";
      BoxVM.caller = "new";
      $('#functionModal_btn').click();
    }

    function getMultiId() {
      var multiIdArray = [];
      $('.pricesearchCheckbox').each(function () {
        if($(this).prop("checked")){
          multiIdArray.push($(this).attr('alt'));
          $(this).prop("checked", false);
        }
      });
      return multiIdArray;
    }

    function delete_one(id){
      if(confirm("確定刪除?")){
        location.href = "{{url('Pricesearch/delete')}}?id="+id;
      }
    }

    function multiDelete() {
      if(confirm("確定刪除?")){
        var form = document.createElement("form");

        form.action = "{{url('Pricesearch/multiDelete')}}";
        form.method = "post";
        multiId = document.createElement("input");
        multiId.value = JSON.stringify(getMultiId());
        multiId.name = "id";
        form.appendChild(multiId);

        csrf = document.createElement("input");
        csrf.type = "hidden";
        csrf.name = "_token";
        csrf.value = csrf_token;
        form.appendChild(csrf);             
        
        document.body.appendChild(form);
        form.submit();

        $('.pricesearchCheckboxAll').each(function () {
          if($(this).prop("checked")){
            $(this).prop("checked", false);
          }
        });
      }
    }

    function multiOnline() {
      var multiIdArray = getMultiId();
      multiIdArray.forEach(function(element) {
        eval('pricesearch_' + element + '.online = true;');
      });
      $('.pricesearchCheckboxAll').each(function () {
        if($(this).prop("checked")){
          $(this).prop("checked", false);
        }
      });
    }

    function multiOffline() {
      var multiIdArray = getMultiId();
      multiIdArray.forEach(function(element) {
        eval('pricesearch_' + element + '.online = false;');
      });
      $('.pricesearchCheckboxAll').each(function () {
        if($(this).prop("checked")){
          $(this).prop("checked", false);
        }
      });
    }
  </script>
@endsection