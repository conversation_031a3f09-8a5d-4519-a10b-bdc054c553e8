@extends('admin.Public.aside')

@section('title')
G功能應用項目 > 庫存警示
@endsection

@section('content')
    <ul id="title" class="brand-menu" >
        <li><a href="###">G功能應用項目</a></li>
        <li><a href="###">庫存警示</a></li>
        @if($data['searchKey'] !='')
            <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
        @endif
    </ul>
    <div class="searchbox">
        <form class="mb-3 search-position width-70" action="" name="searchForm" method="get">
            @csrf
            查看方式：
            <select name="view_way" class="p-2 border mb-2 rounded mr-1">
                <option value="limit" @if($data['view_way']=='limit') selected @endif>低於警示</option>
                <option value="all" @if($data['view_way']=='all') selected @endif>查看全部</option>
            </select>
            <input type="text" name="searchKey" class="form-control mr-1 text-center"  value="{{$data['searchKey']}}" placeholder="請輸入商品條碼/商品名稱/品項名稱">

            @if(!isset(config('control.close_function_current')['網紅列表']))
                @if($data['admin_type']!='distribution')
                    <select name="kol_id" class="p-2 border mb-2 rounded">
                        @if(-1 == $data['kol_id'])
                            <option value="-1" selected>請選擇網紅</option>
                        @else
                            <option value="-1">請選擇網紅</option>
                        @endif

                        @if(0 == $data['kol_id'])
                            <option value="0" selected>無指派網紅</option>
                        @else
                            <option value="0">無指派網紅</option>
                        @endif

                        @foreach($data['kol'] as $vo)
                            @if($vo['id'] == $data['kol_id'])
                                <option value="{{$vo['id']}}" selected>{{$vo['kol_name']}}</option>
                            @else
                                <option value="{{$vo['id']}}">{{$vo['kol_name']}}</option>
                            @endif
                        @endforeach
                    </select>
                @endif
            @endif
            <a class="btn sendbtn mr-1 ml-1 mb-2" onclick="searchForm.submit();">搜尋</a>
            <a class="btn clearbtn mb-2" href="/admin/limit/index?searchKey=&kol_id=-1">清除搜尋</a>
        </form>
    </div> 
    <div class="edit_form" >
        <table class="table table-rwd" style="min-width:1400px;">
            <!-- 標題 -->
            <thead>
                <tr>
                    <th>商品條碼</th>
                    <th>商品名稱</th>
                    <th>商品品項</th>

                    @if(!isset(config('control.close_function_current')['網紅列表']))
                        <th>所屬網紅</th>
                    @endif

                    <th><a href="{{url('limit/index')}}?orderGet=create_time">上架日期<span class="bi bi-arrow-down"></span></a></th>
                    <th><a href="{{url('limit/index')}}?orderGet=limit_num">警示數量<span class="bi bi-arrow-down"></span></a></th>
                    <th><a href="{{url('limit/index')}}?orderGet=num">線上數量<span class="bi bi-arrow-down"></span></a></th>

                    @if(!isset(config('control.close_function_current')['存放位置管理']))
                        <th style="width: 60px;">實際庫存</th>
                    @endif
                </tr>
            </thead>
            <!-- 內容 -->
            <tbody>
                <tr></tr>
                @foreach($data['limit_num_items'] as $voproductinfo)            
                <tr>
                    <td data-th="商品條碼">{{$voproductinfo['productinfo_ISBN']}}</td>
                    <td data-th="商品名稱">{{$voproductinfo['productinfo_title']}}</td>
                    <td data-th="商品品項"><a href="{{url('productinfo/edit', ['id' => $voproductinfo['productinfo_id']])}}">{{$voproductinfo['productinfo_type_title']}}</a></td>

                    @if(!isset(config('control.close_function_current')['網紅列表']))
                        <td data-th="所屬網紅">{{$voproductinfo['kol_name']}}</a></td>
                    @endif

                    <td data-th="上架日期">
                        {{$voproductinfo['productinfo_create_time']}}
                        
                    </td>
                    <td data-th="警示數量">{{$voproductinfo['limit_num']}}</td>
                    <td data-th="線上數量">
                        @if(!isset(config('control.close_function_current')['存放位置管理']))
                            {{$voproductinfo['productinfo_type_num']}}
                        @else
                            <input class="input_type_num" type="number" style="width: 100px;" type_id="{{$voproductinfo['type_id']}}"
                                value="{{$voproductinfo['productinfo_type_num']}}">
                        @endif
                    </td>

                    @if(!isset(config('control.close_function_current')['存放位置管理']))
                        <td data-th="實際庫存"><input class="input_type_num" type="number" style="width: 100px;" type_id="{{$voproductinfo['type_id']}}"
                                value="{{$voproductinfo['productinfo_type_total']}}"></td>
                    @endif
                </tr>
                @endforeach        
            </tbody>
        </table>
    </div>   

    <div class="text-center">
        {{$data['limit_num']->links('pagination::default')}}
    </div>
@endsection

@section('ownJS')
    <script>
        $(function(){
            $(document).ready(function(){
                $(window).resize(function() {
                    var i ;
                    for(i=1 ; i<=8; i++){
                        $('table').find('td:nth-child('+i+')').outerWidth($('table').find('th:nth-child('+i+')').outerWidth());
                    }
                    if($('table').find('thead').find('tr').outerWidth() != $('table').find('tbody').find('tr').outerWidth()){
                        for(i=4 ; i<=8; i++){
                            $('table').find('td:nth-child('+i+')').css('padding-right','calc(19px - .5em)')
                        }
                    }
                });
            });

            var i ;
            for(i=1 ; i<=8; i++){
                $('table').find('td:nth-child('+i+')').outerWidth($('table').find('th:nth-child('+i+')').outerWidth());
            }
            if($('table').find('thead').find('tr').outerWidth() != $('table').find('tbody').find('tr').outerWidth()){
                for(i=4 ; i<=8; i++){
                    $('table').find('td:nth-child('+i+')').css('padding-right','calc(19px - .5em)')
                }
            }
        });

        // 修改數量
        $('.input_type_num').on('focus', function(e){
            ori_value = $(this).val();
            $(this).on('blur', function(e){
                var type_id = $(this).attr('type_id');
                var new_type_num = $(this).val();
                if(ori_value != new_type_num){
                    console.log(type_id);
                    console.log(new_type_num);

                    $.ajax({
                        url: "{{url('Limit/change_type_num')}}",
                        type: 'POST',
                        headers: {
                        'X-CSRF-Token': csrf_token 
                        },
                        data: {
                            type_id:type_id,
                            new_type_num:new_type_num,
                        },
                        success: function(response) {
                            Vue.toasted.show(response.msg, vt_success_obj);
                        },
                        error: function(xhr) {
                            console.log(xhr);
                        }
                    });
                }
                $(this).off('blur');
            });
        });
    </script>
@endsection