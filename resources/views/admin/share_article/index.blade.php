@extends('admin.Public.aside')
@section('title')會員文章分享 - E圖文編輯項目@endsection

@section('css')
@endsection

@section('content')
<div id="content">
  <ul id="title" class="brand-menu">
    <li><a href="###">E圖文編輯項目</a></li>
    <li><a href="###">會員文章分享</a></li>
  </ul>
  <div class="frame">
    <div class="d-flex flex-wrap justify-content-between">
      <div>
        <form @submit="search_record($event)">
          <div class="d-inline-block mr-2">
            <span class="name">會員關鍵字：</span>
            <input type="text" v-model="searchform.member_key" placeholder="會員編號/姓名/手機">
          </div>
          <div class="d-inline-block mr-2">
            <span class="name">文章名稱：</span>
            <input type="text" v-model="searchform.name" placeholder="">
          </div>
          <div class="d-inline-block mr-2">
            <span class="name">文章狀態：</span>
            <select v-model="searchform.front_show">
              <option value="">請選擇</option>
              <option value="1">顯示</option>
              <option value="2">隱藏</option>
            </select>
          </div>
          <input class="btn sendbtn" type="submit" value="搜尋">
          <input class="btn" type="submit" value="清除搜尋" @click="clear_search($event)">
        </form>
      </div>
    </div>
  </div>
  <div class="edit_form">
    <table class="table table-rwd" style="min-width:750px">
      <thead>
        <tr>
          <th style="width:60px;">
            {{Lang::get('序號')}}
            <!-- <button class="btn btn-secondary ml-1" @click="open_detail(-1)">
              <i class="bi bi-plus-lg add"></i>
            </button> -->
          </th>
          <th class="text-left" style="width:100px;">{{Lang::get('會員')}}</th>
          <th class="text-left" style="width:80px;">{{Lang::get('系統編號')}}</th>
          <th class="text-left" style="width:300px;">{{Lang::get('文章')}}</th>
          <th class="text-left" style="width:150px;">{{Lang::get('建立日期')}}</th>
          <th class="text-right" style="width:60px;">{{Lang::get('排序')}}</th>
          <th style="width:100px;">{{Lang::get('狀態')}}</th>
          <th style="width:100px;">{{Lang::get('操作')}}</th>
        </tr>
      </thead>
      <tbody>
        <tr></tr>
        <template v-for="(record, idx) in records">
          <tr>
            <td>
              <span v-text="idx+1+(searchform.page-1)*searchform.count_of_items"></span>
            </td>
            <td class="text-left">
              <template v-if="users[record.user_id]?.number">
                <span v-text="users[record.user_id].name"></span>
                (<span v-text="users[record.user_id].number"></span>)
              </template>
            </td>
            <td class="text-left"><span v-text="record.id"></span></td>
            <td class="text-left">
              <div class="d-flex flex-wrap align-items-center">
                <img :src="record.img" @click="open_detail(idx)" class="mr-3" style="max-height: 100px; max-weight: 100px;">
                <a href="###" @click="open_detail(idx)"><span v-text="record.name"></span></a>
              </div>
            </td>
            <td class="text-left">
              <span v-text="record.create_time_f.slice(0,16)"></span>
            </td>
            <td class="text-right">
              <span v-text="record.orders"></span>
            </td>
            <td><span v-text="record.show==0 && '管理員隱藏' || record.show_status==0 && '隱藏' || '顯示'"></span></td>
            <td>
              <button class="btn btn-danger" @click="delete_record(record.id)">
                <i class="bi bi-trash"></i>
              </button>
            </td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
  <div class="text-center">
    <crm_index_pages 
      :change_page="change_page"
      :current_page="searchform.page" 
      :count_of_items="records_total" 
      :count_of_page="searchform.count_of_items"
    ></crm_index_pages>
  </div>

  <a id="share_article_detail_btn" data-toggle="modal" data-target="#share_article_detailModel" class="d-none"></a>
  <!-- 跳出視窗：註冊商品 -->
  <div class="modal fade shoppingCart smallMOdel share_article_detailModel" id="share_article_detailModel" role="dialog" aria-labelledby="share_article_detailModelTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="share_article_detailModelTitle">{{Lang::get('文章內容')}}</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row fluid-container">
            <div class="col-md-6 col-12">
              <div class="form-group">
                {{Lang::get('文章名稱')}}<span class="text-danger">*</span>
                <input type="text" v-model="detail.name" class="form-control">
              </div>
            </div>
            <div class="col-md-6 col-12">
              <div class="form-group">
                {{Lang::get('狀態')}}<span class="text-danger">*</span>
                <select v-model="detail.show" class="form-control">
                  <option value="0">{{Lang::get('管理員隱藏')}}</option>
                  <option value="1">{{Lang::get('顯示')}}</option>
                </select>
                <span class="text-danger">(會員設定：<span v-text="detail.show_status==1 ? '顯示' : '隱藏'"></span>)</span>
              </div>
            </div>
            <div class="col-md-6 col-12">
              <div class="form-group">
                <img :src="detail.img" class="mw-100" style="max-height: 200px;">
              </div>
            </div>
            <div class="col-md-6 col-12">
              <div class="form-group">
                {{Lang::get('文章圖片')}}<span class="text-danger">*</span>
                <input type="file" @change="preview_img" ref="input_img" class="form-control" accept="image/png, image/jpeg">
                <span class="text-danger">(建議尺寸：480*270px)</span>
              </div>
            </div>
            <div class="col-md-6 col-12">
              <div class="form-group">
                {{Lang::get('排序')}}<span class="text-danger">*</span>
                <input type="text" v-model="detail.orders" class="form-control">
                <span class="text-danger">(數字越小越前)</span>
              </div>
            </div>
            <div class="col-md-12 col-12">
              <div class="form-group">
                {{Lang::get('內容')}}<span class="text-danger">*</span>
                <textarea class="form-control" id="editor"></textarea>
              </div>
            </div>
            <div class="col-md-12 col-12 text-center">
              <a class="btn sendbtn" @click="save">{{Lang::get('儲存')}}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
@endsection
@section('ownJS')
  <script charset="utf-8" src="/public/static/admin/js/kindeditor/kindeditor.js"></script>
  <script charset="utf-8" src="/public/static/admin/js/kindeditor/lang/zh_TW.js"></script>
  <script type="text/javascript">
    var editor;
    KindEditor.ready(function(K) { //插入影片功能 items裡加入 'code'
      editor = K.create('#editor', {
        afterBlur: function(){this.sync();},
        langType : 'zh_TW',
        items: [
          'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
          'image', 'link', 'unlink','|',
          'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
        ],
        width:'100%',
        height:'300px',
        resizeType:0,
      });
    });
  </script>
  <script>
    Vue.component('crm_index_pages', {
      template:`
        <ul class="pagination">
          <li class="" v-if="current_page-1 > 0">
            <a href="###" @click="trigger_change_page(current_page-1)">«</a>
          </li> 
          <template v-for="page in pages">
            <li :class="[current_page==page ? 'active' : '']" >
              <a v-if="current_page!=page" href="###" v-text="page" @click="change_page(page)"></a>
              <span class="text-dark" v-else v-text="page"></span>
            </li>
          </template>
          <li class="" v-if="current_page+1 <= computed_page_num">
            <a href="###" @click="trigger_change_page(current_page+1)">»</a>
          </li> 
        </ul>
      `,
      data: function() {
        return {
          pages: [1],
        };
      },
      props: {
        change_page: Function,  /*換頁*/
        current_page: Number,   /*當前頁數*/
        
        count_of_items: Number, /*項目總數(計算總頁數用)*/
        count_of_page: Number,  /*一頁數量(計算總頁數用)*/
        
        total_pages: Number,    /*總頁數*/
      },
      computed: {
        computed_page_num: function(){
          page_num = 1;
          if(this.total_pages){ /*有傳入總頁數*/
            page_num = this.total_pages;
          }else if(this.count_of_items && this.count_of_page){ /*有傳入一頁數量&項目總數*/
            page_num = Math.ceil( this.count_of_items / this.count_of_page);
          }
          return page_num;
        },
      },
      watch: {
        current_page: {
          immediate: true, // 立即执行一次监听器
          handler: function() { this.updatePages(); },
        },
        count_of_items: {
          handler: function() { this.updatePages(); },
        },
        count_of_page: {
          handler: function() { this.updatePages(); },
        },
        total_pages: {
          handler: function() { this.updatePages(); },
        },
      },
      methods: {
        updatePages() { /*根據傳入最大頁數生成新的頁數列表*/
          var pages = [];
          for (var i=-5; i<5; i++) {
            if(i+this.current_page > 0 && i+this.current_page <= this.computed_page_num){
              pages.push(i+this.current_page);
            }
          }
          this.pages = pages;
        },
        trigger_change_page(page){
          if (typeof this.change_page === 'function') {
            if(page > 0 && page <= this.computed_page_num){
              this.change_page(page);
            }
          }
        }
      },
    });
  </script>
  <script>
    const empty_searchform = {
      page: 1, /*當前頁數*/
      count_of_items: 20,

      member_key: '',
      name: '',
      front_show: '',
    };
    const empty_detail = {
      id: 0,
      name: '',
      img: '',
      content: '',
      show_status: 1,
    }
    var content_data = {      
      searchform: JSON.parse(JSON.stringify(empty_searchform)),
      records: [],
      records_total: 0,
      users: {},

      detail: JSON.parse(JSON.stringify(empty_detail)),
    };
    var contentVM = new Vue({
      el: '#content',
      data: content_data,
      created(){
        this.get_records();
      },
      methods: {
        clear_search:async function(){
          this.searchform = JSON.parse(JSON.stringify(empty_searchform));
          await this.get_records();
        },
        change_page:async function(p){
          if(this.searchform.page!=p){
            this.searchform.page = p;
            await this.get_records();
          }
        },
        search_record:async function($event){
          $event.preventDefault();
          this.searchform.page = 1;
          await this.get_records();
        },
        get_records: async function (){
          Vue.toasted.show("{{Lang::get('載入中')}}",{duration:1500, className: ["toasted-primary", 'bg-success']});
          this.point_records = [];
          try {
            resp = await $.ajax({
              type: 'post',
              dataType: 'json',
              url: "{{url('ShareArticle/get_share_article')}}",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              data: this.searchform,
            });
            this.records = resp.records_show;
            this.records_total = resp.records_total;
            this.users = resp.users;

            Vue.toasted.show("{{Lang::get('載入完成')}}",{duration:1500, className: ["toasted-primary", 'bg-success']}); 
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
        },

        open_detail(idx){
          if(idx<0 || idx>=this.records.length){
            this.detail = JSON.parse(JSON.stringify(empty_detail));
          }else{
            this.detail = JSON.parse(JSON.stringify(this.records[idx]));
          }
          editor.html(this.detail.content);
          $('#share_article_detail_btn').click();
        },
        async preview_img(){
          // 將圖片壓縮的函式
          var compressImage = async function(file, targetSizeKB = 560) {
            return new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = (event) => {
                const img = new Image();
                img.onload = async () => {
                  const canvas = document.createElement('canvas');
                  const ctx = canvas.getContext('2d');

                  canvas.width = img.width;
                  canvas.height = img.height;
                  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                  let quality = 0.9; // 初始壓縮品質
                  let blob;

                  // 自動調整壓縮品質直到符合目標大小
                  const compressToTargetSize = async () => {
                    return new Promise((resolveInner) => {
                      const tryCompress = () => {
                        canvas.toBlob((b) => {
                          if (b.size / 1024 <= targetSizeKB || quality <= 0.1) {
                            // 如果檔案大小符合要求或品質太低，停止壓縮
                            resolveInner(b);
                          } else {
                            // 減少品質再試一次
                            quality -= 0.1;
                            tryCompress();
                          }
                        }, file.type, quality);
                      };
                      tryCompress();
                    });
                  };

                  blob = await compressToTargetSize();
                  resolve(blob);
                };

                img.onerror = reject;
                img.src = event.target.result;
              };
              reader.onerror = reject;
              reader.readAsDataURL(file);
            });
          };
          // 將檔案轉換為 Base64
          var readAsDataURL = function(file) {
            return new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result);
              reader.onerror = (error) => reject(error);
              reader.readAsDataURL(file);
            });
          };

          var files = this.$refs.input_img.files;
          if(files && files[0]){
            // 壓縮圖片
            const compressedBlob = await compressImage(files[0], 300); // 壓縮品質設定為 70%
            // 將壓縮後的 Blob 轉為 Base64
            const result = await readAsDataURL(compressedBlob);
            // 將結果推入 new_img.imgs
            this.detail.img = result;
          }
          $(this.$refs.input_img).val('');
        },
        async save(){
          this.detail.content = editor.html();
          // console.log(this.detail);
          $('#body_block').show();
          try {
            resp = await $.ajax({
              type: 'post',
              dataType: 'json',
              url: "{{url('ShareArticle/save_share_article')}}",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              data: this.detail,
            });
            if(resp.code==1){
              await this.get_records();
              $('#share_article_detailModel [data-dismiss="modal"]').click();
            }else{
              Vue.toasted.show(resp.msg, vt_error_obj); 
            }
          } catch (error) {
            Vue.toasted.show(error, vt_error_obj);
          } 
          $('#body_block').hide();
        },
        async delete_record(id){
          if(confirm("{{Lang::get('確定刪除嗎')}}")){
            $('#body_block').show();
            try {
              resp = await $.ajax({
                type: 'post',
                dataType: 'json',
                url: "{{url('ShareArticle/delete_share_article')}}",
                headers: {
                  'X-CSRF-Token': csrf_token 
                },
                data: {id: id,},
              });
              if(resp.code==1){
                await this.get_records();
              }else{
                Vue.toasted.show(resp.msg, vt_error_obj); 
              }
            } catch (error) {
              Vue.toasted.show(error, vt_error_obj);
            } 
            $('#body_block').hide();
          }
        },
      },
    });
  </script>
@endsection