@extends('admin.Public.aside')
@section('title')商品管理區 > 實體銷存@endsection

@section('content')
  <div id="content">
    <ul id="title" class="brand-menu">
      <li onclick="javascript:location.href='index'" ><a href="###">G功能應用項目</a></li>
      <li><a href="###">實體銷存</a></li>
    </ul>
    <div class="searchbox justify-content-start border-bottom pb-2">
      <div class="item col-lg-4 mb-2">
        <form name="search_userForm" method="get" class="d-inline-flex ">
          @csrf
          <span style="width: 80px;" class="pt-2 d-inline-block">會員：</span><input type="text" name="user_keyword" class="form-control mr-1 text-center" placeholder="請輸入會員帳號或會員編號">
          <a class="btn sendbtn" onclick="set_user_id();">設定</a>
        </form>

      </div>
      <div class="item col-lg-4 mb-2">
        <form name="searchForm" method="get" class="d-inline-flex ">
          @csrf
          <span style="width: 80px;" class="pt-2 d-inline-block">條碼：</span><input type="text" name="searchKey" class="form-control mr-1 text-center" placeholder="搜尋" onpaste="search_bar_mul();">
          <a class="btn sendbtn"  onclick="search_bar_mul();">搜尋</a>
        </form>
      </div>
    </div>		
    <h3 class="main-title mt-4">銷售列表</h3>
    <table class="table table-rwd" style="min-width: 992px;">
      <h5>購買者：<span id="user_name">遊客</span></h5>
      <!-- 標題 -->
      <thead>
        <tr>
          <th>商品名稱</th>
          <th>市價</th>
          <th>品項</th>
          <th>售價</th>
          <th>位置編碼</th>
          <th style="width: 80px;">數量</th>
          <th style="width: 60px;">刪除</th>
        </tr>
      </thead>
      <!-- 內容 -->
      <tbody id="sell"></tbody>
    </table>
    <form name="Form" action="{{url('Sell/update')}}" method="post" >
      @csrf
      <input type="hidden" name="sell_array" value='{}'/>
      <input type="hidden" name="searchKey" value="{{$data['searchKey']}}"/>
      <input type="hidden" name="user_id" id="user_id" value="0">
    </form>
    <button type="button" class="btn sendbtn" onclick="ck_submit();">確認</button>
  </div>

  <!-- Modal -->
  <div class="modal fade main-modal" id="select_type" tabindex="-1" role="dialog" aria-labelledby="select_typeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
      <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-header">
          <h5 class="modal-title" id="select_typeModalLabel">選擇商品</h5>
        </div>
        <div class="modal-body">
          ...
        </div>
        <div class="modal-footer">
        </div>
      </div>
    </div>
  </div>
@endsection
@section('ownJS')
  <script>
    /*送出表單又發送條碼查詢(搭配掃描機)*/
    $('[name="searchForm"]').on('submit', function(e){
      e.preventDefault();
      search_bar_mul();
    });		
    
    function ck_submit(){
      if(confirm("是否確認執行實體銷存?")){
        var sell_array = $('input[name="sell_array"]').val();
        var error = 0;

        if(sell_array == '{}'){
          alert('請先選擇商品');
          error = 1;
          return 0;
        }
        if(error == 0){
          Form.submit();
        }
      }
    }
    
    function select_position(){
      var select_position = $('#select_position').val();
      var select_position_number = $('#select_position_number').val();
      //console.log(select_position);
      //console.log(select_position_number);
      $.ajax({
        url: "{{url('sell/search')}}",
        type: 'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
        },
        data: {
          product_id:'{{$data["id"]}}',
          position_id:select_position,
          position_number:select_position_number
        },
        success: function(r) {
          del_re(r[0]['productinfo_type'],r[0]['position_portion']);
        },
        error: function(xhr) {
          console.log(xhr);
        }
      });
    }

    function del_array(id,por){
      var sell_array = $('input[name="sell_array"]').val();
      sell_array = JSON.parse(sell_array);
      //console.log(sell_array);
      var result = sell_array.filter(function(item,ikey){
        //console.log(item);
        //console.log(ikey);
        return item.position_portion != por;
      });
      /***/
        var va = $('#num'+id).html();
        va--;
        $('#num'+id).html(va);
      /****/
      $('#por_'+por).hide();
      $('input[name="sell_array"]').val(JSON.stringify(result));
    }
  
    function del_re(id,por){
      var show_error = $('#por_'+por).css('display');
      if(show_error != 'none'){
        alert('已顯示或是查詢無此位置');
        return 0;	
      }
        
      $('#por_'+por).show();
      var va = $('#num'+id).html();
      va++;
      $('#num'+id).html(va);
      
      var sell_array = $('input[name="sell_array"]').val();
      if(sell_array == '{}'){
        sell_array = [
          {
            "productinfo_type":id,
            "position_portion": por,
            "num":1
          }
        ];
        //sell_array.push({'num': '0'});
      }else{
        sell_array = JSON.parse(sell_array);
        sell_array.push({
            "productinfo_type":id,
            "position_portion": por,
            "num":1
          });
      }
      //JSON.parse()
      $('input[name="sell_array"]').val(JSON.stringify(sell_array));
    }

    function del_no(id,por){
      var va = $('#num'+id).html();
      var num = $('#por_num'+por).val();
      va++;
      $('#num'+id).html(va);
      
      var sell_array = $('input[name="sell_array"]').val();
      if(sell_array == '{}'){
        sell_array = [
          {
            "productinfo_type":id,
            "position_portion": por,
            "num":num
          }
        ];
        //sell_array.push({'num': '0'});
      }else{
        sell_array = JSON.parse(sell_array);
        sell_array.push({
            "productinfo_type":id,
            "position_portion": por,
            "num":num
          });
      }
      //JSON.parse()
      $('input[name="sell_array"]').val(JSON.stringify(sell_array));
    }

    $(function(){
      $(document).ready(function(){
        $(window).resize(function() {
          var i ;
          for(i=1 ; i<=8; i++){
            $('table').find('td:nth-child('+i+')').outerWidth($('table').find('th:nth-child('+i+')').outerWidth());
          }
          if($('table').find('thead').find('tr').outerWidth() != $('table').find('tbody').find('tr').outerWidth()){
            for(i=4 ; i<=8; i++){
              $('table').find('td:nth-child('+i+')').css('padding-right','calc(19px - .5em)')
            }
          }
        });
      });

      var i ;
      for(i=1 ; i<=8; i++){
        $('table').find('td:nth-child('+i+')').outerWidth($('table').find('th:nth-child('+i+')').outerWidth());
      }
      if($('table').find('thead').find('tr').outerWidth() != $('table').find('tbody').find('tr').outerWidth()){
        for(i=4 ; i<=8; i++){
          $('table').find('td:nth-child('+i+')').css('padding-right','calc(19px - .5em)')
        }
      }
    })
  </script>

  <script>
    $('input[name="searchKey"]').focus();

    function search_bar_mul(){
      setTimeout(function(){
        var bar = $('input[name="searchKey"]').val();
        var sell_array = $('input[name="sell_array"]').val();
      
        if(bar.length >= 0 ){
          $('input[name="searchKey"]').val('');
          $.ajax({
            url: "{{url('sell/search_bar_mul')}}",
            type: 'POST',
            headers: {
              'X-CSRF-Token': csrf_token 
            },
            data: {
              bar:bar,
              sell_array:sell_array
            },
            success: function(re) {
              if(re == 'no'){
                $.alert('無商品');
                return 0;
              }
              $('#select_type').modal('show');
              $('#select_type .modal-body').html(re);
            }
          });
        }
      }, 100)
    }

    function search_bar(pp_id,product_id){
      $('#select_type').modal('hide');
      var sell_array = $('input[name="sell_array"]').val();
      
      $.ajax({
        url: "{{url('sell/search_bar')}}",
        type: 'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
        },
        data: {
          pp_id:pp_id,
          sell_array:sell_array,
          product_id:product_id
        },
        success: function(re) {
          if(re == 'no'){
            $.alert('無商品');
            return 0;
          }

          $('input[name="sell_array"]').val(re);

          de_array = JSON.parse(re);
          console.log(de_array);
          show_goods_td(de_array);

          $('input[name="searchKey"]').focus();			
        }
      });
    }

    function show_goods_td(de_array){
      var total = 0;
      $('#sell').empty();	
      $('#sell').append("<tr></tr>");
      $.each(de_array, function(i, item) {
        $('#sell').append('\
            <tr>\
              <td>'+item.title+'</td>\
              <td>'+item.price+'</td>\
              <td>'+item.dis+'</td>\
              <td>'+item.count+'</td>\
              <td>'+item.p_code+'</td>\
              <td>'+item.num+'</td>\
              <td><a href="###" onclick="sell_array_de('+ item.pp_id +')">刪除</a></td>\
            </tr>\
        ');
        total += (item.count*item.num);
      });
      $('#sell').append( '\
        <tr>\
          <td colspan="5"></td>\
          <td>總計：</td>\
          <td>'+total+'元</td>\
        </tr>\
      ');
    }

    function sell_array_de(pp_id){
      var sell_array = $('input[name="sell_array"]').val();
      sell_array = JSON.parse(sell_array);
            
      var result = sell_array.filter(function(item,i){
         return item.pp_id != pp_id;
      });
      $('#sell').empty();	
      
      $('input[name="sell_array"]').val(JSON.stringify(result));
      
      show_goods_td(result);
    }

    function set_user_id(){
      var user_keyword = $('[name="user_keyword"]').val();

      $.ajax({
        url: "{{url('sell/search_user')}}",
        type: 'POST',
        headers: {
          'X-CSRF-Token': csrf_token 
        },
        data: {
          user_keyword:user_keyword,
        },
        success: function(re) {
          if(re){
            $('#user_name').html(re['name']);
            $('#user_id').val(re['id']);
          }
          Vue.toasted.show('已更新購買者設定', vt_success_obj);

          $('input[name="searchKey"]').focus();
        }
      });
    }
  </script>
@endsection