@extends('admin.Public.aside')

@section('title')
後台
@endsection

@section('css')
    <style>
        .tableListA{
            padding:10px 20px;
            margin: 0px auto;
            margin-top: 10px;
            width: 99%
        }
    </style>
@endsection
@section('content')
    <div id="content">
        <p id="title">
           J參數設定 > <span style="margin: 0;">版本說明</span>
        </p>

        <form action="{{url('System/update')}}" method="post">
			@csrf
			<input type="hidden" name="_token" value="{{csrf_token()}}" >
	        <div class="tableListA">
	        	<div class="row">
					<div class="col-md-6">
						<div class="form-group">
							<label for="sys_title">系統名稱</label>
							<input type="text" class="form-control ng-pristine ng-untouched ng-valid" id="sys_title" placeholder="" value="{{$data['system_intro']['system_title']}}" name="system_title">
						</div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
							<label for="sys_version">系統版本</label>
							<input type="text" class="form-control ng-pristine ng-untouched ng-valid" id="sys_version" placeholder="" value="{{$data['system_intro']['system_version']}}" name="system_version">
						</div>
					</div>

					<div class="col-md-12">
						<div class="form-group">
							<label for="sys_note">系統備註</label>
							<textarea class="form-control ng-pristine ng-untouched ng-valid" id="sys_note" rows="3" placeholder="" name="system_note">{{$data['system_intro']['system_note']}}</textarea>
						</div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
							<label for="sys_front_end">前台開發程式</label>
							<textarea class="form-control ng-pristine ng-untouched ng-valid" id="sys_front_end" rows="3" placeholder="" name="front_end">{{$data['system_intro']['front_end']}}</textarea>
						</div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
							<label for="sys_back_end">後台開發程式</label>
							<textarea class="form-control ng-pristine ng-untouched ng-valid" id="sys_back_end" rows="3" placeholder="" name="back_end">{{$data['system_intro']['back_end']}}</textarea>
						</div>
					</div>

					<div class="col-md-6">
						<div class="form-group">
							<label for="php_v">php版本</label>
							<input type="text" class="form-control ng-pristine ng-untouched ng-valid" id="php_v" placeholder="" value="{{$data['system_intro']['php_version']}}" name="php_version">
						</div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
							<label for="sql_v">sql版本</label>
							<input type="text" class="form-control ng-pristine ng-untouched ng-valid" id="sql_v" placeholder="" value="{{$data['system_intro']['sql_version']}}" name="sql_version">
						</div>
					</div>
					<div class="col-md-12">
						<div class="form-group">
							<label for="time">結案時間</label>
							<input type="text" class="form-control ng-pristine ng-untouched ng-valid" id="time" placeholder="" value="{{$data['system_intro']['closing_date']}}" name="closing_date">
						</div>
					</div>
					<div class="col-md-12">
						<div class="form-group">
							<label for="s_note">備註</label>
							<textarea class="form-control ng-pristine ng-untouched ng-valid" id="s_note" rows="20" placeholder="" name="note">{{$data['system_intro']['note']}}</textarea>
						</div>
					</div>
					
					<div class="col-12 mt-3">
						<input type="submit" class="btn btn-success btn-block" value="確認送出">
					</div>
				</div>
	        </div>
	    </form>
    </div>
@endsection
@section('ownJS')
@endsection