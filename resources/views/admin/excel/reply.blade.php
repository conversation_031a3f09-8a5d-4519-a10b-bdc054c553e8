@extends('admin.Public.aside')
@section('title')G功能應用項目 > 註冊商品回函@endsection

@section('content')
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" tabindex="-1" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">發票照片</h5>
                    
                </div>
                <div class="modal-body">
                    <div class="pt-4 pb-4 mb-4 w-100 h-100">
                        <a :href="q" target="_blank">
                            <img class="qaimg" v-bind:src="q" />
                        </a>
                    </div>
                    <br><br>
                    <div class="d-flex justify-content-center position-absolute w-100" style="left:0; bottom: 1rem;">
                        <a href="javascript:rotateimg()"><button class="btn">旋轉</button></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="content">
  
        <ul id="title" class="brand-menu" onclick="javascript:location.href='reply'">
            <li><a href="###">G功能應用項目</a></li>
            <li><a href="###">註冊商品回函</a></li>
            @if($data['searchKey'] != '')
                <li><a href="###">搜尋：{{$data['searchKey']}}</a></li>
            @endif
        </ul>

        <div class="searchbox">
            <form action="" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1 text-center"  placeholder="搜詢回函編號或機身碼">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame ask_status_box">
            <a class="edit" onclick="multiDelete();">
                刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
            </a>
            <div class="ask_status">
                <a class="btn" href="{{url('excel/reply')}}">全部</a>
                <a class="btn" href="{{url('excel/reply')}}?do=1&searchKey={{$data['searchKey']}}">已處理</a>
                <a class="btn" href="{{url('excel/reply')}}?do=0&searchKey={{$data['searchKey']}}">未處理</a>
            </div>

           
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table-rwd table" style="min-width: 1400px;">
                <thead>
                    <tr>
                        <th style="width: 20px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=qaCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th>回函編號</th>
                        <th>姓名</th>
                        <th>購買日期</th>
                        <th>機身碼</th>
                        <th>商品名稱</th>
                        <th>發票號碼</th>
                        <th>發票照片</th>
                        <th>狀態</th>
                        <th style="width: 60px;">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['qa']) == false)
                    @foreach($data['qa'] as $vo)
                        <tr id="qa_{{$vo->id}}">
                            <td><input type="checkbox" class="qaCheckbox" alt="{{$vo->id}}"></td>
                            <td>{{$vo->pro_id}}</td>
                            <td>{{$vo->account_name}}</td>
                            <td>{{$vo->buytime}}</td>
                            <td>{{$vo->product_code}}</td>
                            <td>{{$vo->product_name}}</td>
                            <td>{{$vo->tax_ID_number}}</td>
                            <td><a href="###" @click="openBox">(點選看圖)</a></td>
                            @if($vo->status == 1) 
                                <td>成功</td>
                            @elseif($vo->status==2)
                                <td>失敗</td>
                            @else 
                            <td id="select_{{$vo->id}}">
                                <select id="{{$vo->id}}">
                                    <option value ="">待確認</option>
                                    <option value ="1">成功</option>
                                    <option value="2">失敗</option>
                                </select>
                            </td>
                            @endif
                            
                            <td><span class="bi bi-trash" onclick="location.href = '{{url('Excel/delete')}}?id={{$vo->id}}'"></span></td>
                        </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
        </div>
        
        <div class="text-center">
            {{$data['qa']->links('pagination::default')}}
        </div>
    </div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script>
    
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Excel/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.createQa = function (Data) {
            $.ajax({
                url: "{{url('Excel/doCreate')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        location.reload();
                    }else{
                        alert('新增失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('新增失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.updateQa = function (Data) {
            $.ajax({
                url: "{{url('Excel/update')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: Data,
                success: function(response) {
                    if(response.status){
                        alert('更改成功');
                        BoxVM.updateCallerData();
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {q: "", a: "",  id: 0, caller: null}
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            computed: {
                qNl2br: function () {
                    return this.q.replace(/\n/g, '<br>');
                },
                aNl2br: function () {
                    return this.a.replace(/\n/g, '<br>');
                }
            },
            methods: {
                ajaxSubmit: function () {
                    var Data = {
                        q: this.qNl2br,
                        a: this.aNl2br
                    }
                    if(this.caller == 'new'){
                        this.createQa(Data);
                    }else{
                        Data.id = this.id;
                        this.updateQa(Data);
                    }
                },
                updateCallerData: function () {
                    this.caller.q = this.q;
                    this.caller.a = this.a;;
                    $('#functionModal_btn').modal('hide');
                }
            }
        });

        @if(empty($data['qa']) == false)
        @foreach($data['qa'] as $vo)
            var qa_{{$vo->id}} = {
                id: "{{$vo->id}}",
                q: "{{__UPLOAD__}}/{{$vo->pic}}"
            }
            var qa_{{$vo->id}}_VM = new Vue({
                el: '#qa_{{$vo->id}}',
                data: qa_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    },
                    order_id: function () {
                        blockData = {
                            id: this.id,
                            order_id: this.order_id
                        }
                        this.blockCtrl(blockData);
                    }
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.q = this.q;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    }
                }
            });

            $( "#{{$vo->id}}" ).change(function() {
                var v = $( "#{{$vo->id}}" ).val();
                if(v == 1){
                    var te = '成功';
                }else{
                    var te = '失敗';
                }
                $.ajax({
                url: "{{url('Excel/update')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data: {
                    id:'{{$vo->id}}',
                    user_id:'{{$vo->account_number}}',
                    user_name:'{{$vo->account_name}}',
                    pro_id:'{{$vo->pro_id}}',
                    name:"{{$vo->product_name}}",
                    product_code:'{{$vo->product_code}}',
                    pic:'{{$vo->pic}}',
                    regtime:'{{$vo->regtime}}',
                    tax_ID_number:'{{$vo->tax_ID_number}}',
                    buytime:'{{$vo->buytime}}',
                    val:v
                },
                success: function(response) {
                    //alert(response);
                    if(response == 1){
                        alert('更改成功');
                        $("#select_{{$vo->id}}").empty();

                        $("#select_{{$vo->id}}").html(te);
                    }else if(response == 2){
                        alert('已更改資料但寄信失敗');
                        $("#select_{{$vo->id}}").empty();

                        $("#select_{{$vo->id}}").html(te);
                    }else if(response == 4){
                        alert('此機身碼已註冊登記');
                    }else{
                        alert('更改失敗');
                    }    
                },   
                error: function(response) {
                    alert('更改失敗');
                    console.log(response);
                }
            });




            });
        @endforeach
        @endif
		var count = 0
		function rotateimg(){
			count += 0.25
			$('.qaimg').css('transform', 'rotate(' + count + 'turn)');
		}

        function newBlock(){
            BoxVM.id = 0;
            BoxVM.q = "";
            BoxVM.a = "";
            BoxVM.caller = "new";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.qaCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function multiDelete() {
            var form = document.createElement("form");
            form.action = "{{url('Excel/multiDelete')}}";
            form.method = "post";

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            multiId = document.createElement("input");
            multiId.value = JSON.stringify(getMultiId());
            multiId.name = "id";

            form.appendChild(multiId);
            document.body.appendChild(form);
            form.submit();
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('qa_' + element + '.online = true;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('qa_' + element + '.online = false;');
            });
            $('.activityCheckboxAll').each(function () {
            if($(this).prop("checked")){
                $(this).prop("checked", false);
            }
        });
        }
    </script>
@endsection