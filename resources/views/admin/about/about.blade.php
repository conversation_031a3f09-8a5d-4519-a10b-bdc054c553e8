@extends('admin.Public.aside')

@section('title')D關於我們 > {{$data['frontend_menu']['about']['name']}}@endsection



@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">D關於我們</a></li>
            <li><a href="###">{{$data['frontend_menu']['about']['name']}}</a></li>
        </ul>
        <!-- <p >D關於我們 > {{$data['frontend_menu']['about']['name']}}</p> -->
        <?php //dd($data); ?>
        <div id="about" class="about width-70 admin-content">
            <h3 class="main-title">
                <!-- 歷史沿革 -->
                {{$data['frontend_menu']['about']['second_menu']['about_story']['name']}}<span class="bi bi-chevron-right"></span>
            </h3>
            <form action="{{url('about/update')}}" name="aboutForm" method="post" enctype="multipart/form-data">
                @csrf
                <div class="mb-4" style="width:100%; height:75%">
                  <textarea id="editor" name="content" autofocus>{!!$data['about_story']['content']!!}</textarea>
                    <!-- <div style="float:left; width:49%">
                        <div class="img-box">
                            <span class="bi bi-pencil-square"></span>
                            <input type='file' class="upl" name="image1" accept="image/*" onclick="ChangeImages()">
                            <img class="preview" name="image1" src="{{__UPLOAD__}}{$data['about_story']['image_left_top']}"/>
                        </div><br><br>
                    </div>
                    <div style="float:right; width:49%">
                        <div class="img-box">
                            <span class="bi bi-pencil-square"></span>
                            <input type='file' class="upl" name="image2" accept="image/*" onclick="ChangeImages()">
                            <img class="preview" name="image2" src="{{__UPLOAD__}}{$data['about_story']['image_right_top']}"/>
                        </div><br><br>
                        <div class="img-box">
                            <span class="bi bi-pencil-square"></span>
                            <input type='file' class="upl" name="image3" accept="image/*" onclick="ChangeImages()">
                            <img class="preview" name="image3" src="{{__UPLOAD__}}{$data['about_story']['image_right_bottom']}"/>
                        </div>
                    </div> -->
                </div>
                <h3 class="main-title">
                    <!-- 地圖資訊 -->
                    {{$data['frontend_menu']['about']['second_menu']['about_map']['name']}}<span class="bi bi-chevron-right"></span>
                </h3>
                <div class="infobox">
                    <label>code：</label><br/>
                    <textarea name="mapurl" class="w-100" rows="6">{{$data['about_story']['mapurl']}}</textarea>
                </div>
                <span class="remark">請開啟google地圖，搜尋要嵌入的地址，點擊分享>嵌入地圖>複製HTML，然後貼入上方輸入區</span>

                <a class="btn sendbtn aboutButton mt-2 ml-auto mr-auto w-110" onclick="aboutForm.submit();">儲存</a>
            </form>
        </div>
    </div>
@endsection
@section('ownJS')
    <script>
        $(function () {
            $(".upl").on("change", function (){
                var name = $(this).attr('name');
                preview(this, name);
            })

            function preview(input, name) {
                if (input.files && input.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        $('.preview[name='+name+']').attr('src', e.target.result);
                    }
                    reader.readAsDataURL(input.files[0]);
                }
            }
        });
    </script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        KindEditor.ready(function(K) {
                editor = K.create('#editor', {
                    afterBlur: function(){this.sync();},
                    langType : 'zh_TW',
                    items:['source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink','|',
                    'justifyleft', 'justifycenter', 'justifyright'],
                    width:'100%',
                    height:'500px',
                    resizeType:0
                });
        });
    </script>
@endsection

