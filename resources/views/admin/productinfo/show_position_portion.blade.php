@extends('admin.Public.aside')
@section('title'){{Lang::get('產品資訊')}} - {{Lang::get('商品存放位置')}}@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('F商品管理區')}}</li>
            <li><a href="{{url('All/index')}}">{{Lang::get('產品資訊')}}</a></li>
            <li>{{Lang::get('商品存放位置')}}</li>
        </ul>
		<a href="{{url('Productinfo/edit') . '?id=' . $data['searchKey']}}" class="btn whitebtn">{{Lang::get('查看商品內容')}}</a>
        <div class="width-50 m-auto">
            <table class="table table-rwd mt-2">
                <!-- 標題 -->
                <thead>
                    <tr>
						<th>{{Lang::get('品項')}}</th>
                        <th>{{Lang::get('庫存編碼')}}</th>
						<th style="width: 60px;">{{Lang::get('數量')}}</th>
                    </tr>
                </thead>
                <!-- 內容 -->
                <tbody>
                    <tr></tr>
                    @if (empty($data['show_list_app']) == false)
				    @foreach($data['show_list_app'] as $vo)
    					<tr>
    						<td>{{$vo['p_type']}}</td>
    						<td>{{$vo['position_code']}}</td>
    						<td>{{$vo['num']}}</td>
    					</tr>
				    @endforeach
                    @endif
                </tbody>
            </table>
        </div>
    </div>
@endsection

@section('ownJS')
@endsection