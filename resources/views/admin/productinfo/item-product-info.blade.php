@extends('admin.Public.aside')
@section('title'){{Lang::get('產品資訊')}} - {{Lang::get('詳細內容')}}@endsection

@section('css')
    <style>
        .upl{ z-index: 2; }
        .info-div .name{
            width: 120px;
            flex: 0 0 120px;
        }
    </style>
@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li>{{Lang::get('F商品管理區')}}</li>
            <li><a href="{{url('All/index')}}">{{Lang::get('產品資訊')}}</a></li>
            @if($data['productinfo']['id'] !=0)
                <li>{{Lang::get('編輯')}}</li>
            @else
                <li>{{Lang::get('新增')}}</li>
            @endif
        </ul>
        <div class="d-flex flex-wrap justify-content-between w-100">
            @if($data['productinfo']['id'] !=0)
                <div>
                    @if(empty(config('control.close_function_current')['存放位置管理']))
                        <a href="{{url('Productinfo/show_position_portion')}}?searchKey={{$data['productinfo']['id']}}" class="whitebtn btn"> {{Lang::get('查看商品存放位置')}}</a>
                    @endif
                    <a href="{{url('index/Product/productinfo')}}?id={{$data['productinfo']['id']}}" class="whitebtn btn" target="_blank"> {{Lang::get('前台查看')}}</a>
                    <a href="{{url('index/Product/productinfo_one')}}?id={{$data['productinfo']['id']}}" class="whitebtn btn" target="_blank"> {{Lang::get('一頁式網址')}}</a>
                    @if(config('control.control_register')==1 && $data['productinfo']['is_registrable']==1)
                        <span class="border ml-3 mr-2"></span>
                        <a href="{{url('Productinfo/edit_fields') . '?id=' . $data['productinfo']['id']}}" class="whitebtn btn" target="_blank">{{Lang::get('設定欄位')}}</a>
                        <a href="{{url('Examination/examinee_list') . '?id=' . $data['productinfo']['id']}}" class="whitebtn btn" target="_blank">{{Lang::get('查看報名')}}</a>
                    @endif
                </div>
                <a onclick="deleteproduct()" class="btn sendbtn">{{Lang::get('刪除')}}</a>
            @endif
        </div>
        <form name="productinfoForm" action="{{url('productinfo/update')}}" method="post" enctype="multipart/form-data">
    		@csrf
            <input name="id" type="hidden" value="{{$data['productinfo']['id']}}">
    		<input name="form_error" type="hidden" value="0">
            <input name="_token" type="hidden" value="{{csrf_token()}}">
    		<input type="hidden" name="final_array" value="{{$data['productinfo']['final_array']}}"/>
            <div class="admin-content product_select">
                <div class="col-lg-6">
                    <div class=""  id="product_select_area">
                        <input id="parent_id" type="hidden" value="0">
                        <input id="branch_id" type="hidden" value="0">
                        <input id="prev_id" type="hidden" value="0">
                        {{Lang::get('選擇顯示位置')}}：
                        <select class="product_select- " onchange="product_position('product_select','')">
                            <option value="0">{{Lang::get('請選擇階層')}}</option>
                            @foreach($data['product'] as $vo)
                                <option value="{{$vo['id']}}">{{$vo['title']}}</option>
                            @endforeach
                        </select>
                        <span id="branch_select"></span>
                        <input type="button" class="btn-sm sendbtn" value="{{Lang::get('添加')}}" onclick="add_array()">
                    </div>
                    @if($data['admin_type']=='distribution')
                        <div class="" id="product_select_area2">
                            <input id="parent_id2" type="hidden" value="0">
                            <input id="branch_id2" type="hidden" value="0">
                            <input id="prev_id2" type="hidden" value="0">
                            {{Lang::get('選擇顯示位置')}}({{Lang::get('平台')}})：
                            <select class="product_select2-" onchange="product_position('product_select','', '2')">
                                <option value="0">{{Lang::get('請選擇階層')}}</option>
                                @foreach($data['product_share'] as $vo)
                                    <option value="{{$vo['id']}}">{{$vo['title']}}</option>
                                @endforeach
                            </select>
                            <span id="branch_select2"></span>
                            <input type="button" class="btn-sm sendbtn" value="{{Lang::get('添加')}}" onclick="add_array('2')">
                        </div>
                    @endif
                    <div class="product_select-show" id="show_array"></div>
                </div>
                <div class="col-lg-6">
                    @if(!isset(config('control.close_function_current')['存放位置管理']))
                        <span class="name">{{Lang::get('庫存編碼方式')}}：</span>
                        <div class="d-inline-flex align-items-center">
                            @if($data['productinfo']['r_repeat'] == '1')
                                <input type="radio" name="r_repeat" value="1" checked id="repeat">
                                <label for="repeat" class="mr-1 mb-1">{{Lang::get('依品項編碼')}}</label>
                            @endif
                            @if($data['productinfo']['r_repeat'] == '0')
                                <input type="radio" name="r_repeat" value="0" checked id="no_repeat">
                                <label for="no_repeat" class="mb-0">{{Lang::get('依實體編碼')}}</label>
                            @endif
                            @if($data['productinfo']['r_repeat'] === '')
                                <input type="radio" name="r_repeat" value="1" id="repeat" onclick="repeat_vue()" checked>
                                <label for="repeat" class="mr-1 mb-1">{{Lang::get('依品項編碼')}}</label>
                                <input type="radio" name="r_repeat" value="0" id="no_repeat" onclick="repeat_vue()">
                                <label for="no_repeat" class="mb-0">{{Lang::get('依實體編碼')}}</label>
                            @endif
                        </div>
                    @else
                        <input type="radio" name="r_repeat" value="1" checked id="repeat" style="visibility: hidden;">
                    @endif
                    @if(config('control.control_prod_edm')==1)
                        @if($data['admin_type'] != 'distribution')
                        <div class="info-div mt-1">
                            {{Lang::get('外崁EDM ID')}}：<input name="out_ID" type="text"  value="{{$data['productinfo']['out_ID']}}">
                        </div>
                        @endif
                    @endif
                </div>
            </div>
            <div class="pro-main-content">
                <div id="changeGroupBox" class="col-lg-6 pro-main-img">
                    <div class="pro-main-img-box">
                        <div class="img-box img-box-b position-relative">
                            <div class="img-box position-absolute w-100 h-100 border-0">
                                <h4>{{Lang::get('點擊此處選擇檔案')}}</h4>
                            </div>
                            <img v-if="check_type(src) == 'img'" :src="src.length>6 ? src : ''" class="preview"/>
                            <video v-if="check_type(src) == 'video' && {{config('control.control_upload_film')}}==1" :src="src" class="preview"></video>
                            @for($i="0";$i < config('control.control_img_quantity');$i++)
                                @if(config('control.control_upload_film') == 1)
                                   <input type='file' ref="img{{$i}}" class="upl" name="image{{$i}}" v-show="show_index == {{$i}}" accept="image/*,video/*" @change="previewImg({{$i}})">
                                @else
                                   <input type='file' ref="img{{$i}}" class="upl" name="image{{$i}}" v-show="show_index == {{$i}}" accept="image/*" @change="previewImg({{$i}})">
                                @endif

                                <input type='hidden' name="image_base64_{{$i}}">
                            @endfor
                        </div>
                        <!-- 多圖上傳 -->
                        <div class="img-more">
                            <input style="display: inline-block;height: 100%; width: 100%; opacity: 0; position: absolute; top: 0px; left: 0px;"
                                   id="multiple_imgs"
                                   type="file" multiple="multiple" accept="image/*"
                                   onchange="upload_imgs()">
                            <p>
                                {{Lang::get('多圖上傳，請拖拉檔案至此區塊')}}<br>
                                ({{Lang::get('多圖上傳順序不固定')}})
                            </p>
                        </div>
                    </div>
                    <p class="text-danger remark mb-1">
                        @if(config('control.control_upload_film') == 1){{Lang::get('第一個檔案請上傳圖片')}}@endif
                        {{Lang::get('建議尺寸')}}：700px*700px
                    </p>
                    <div class="d-flex align-items-center flex-wrap">
                        @for($i=0;$i < config('control.control_img_quantity');$i++)
                            <div class="img-box hi-{{$i}} mb-1" style="@if($i > count($data['productinfo']['pic'])) display: none !important; @endif" >
                                <div class="sm-img-box d-flex align-items-center justify-content-center"
                                     @click="switchSrc({{$i}})">
                                    <img v-if="check_type(src{{$i}}) == 'img'" :src="src{{$i}}.length>6 ? src{{$i}} : ''" class="preview"/>
                                    <video v-if="check_type(src{{$i}}) == 'video' && {{config('control.control_upload_film')}} == 1" :src="src{{$i}}" class="preview" style='height: 71px;'></video>
                                </div>
                                <div class="bi bi-trash preview w-100" style="border-top: 1px black solid;" @click="delSrc({{$i}})"></div>
                            </div>
                        @endfor
                        @for($i="0";$i < config('control.control_img_quantity');$i++)
                            <input type="hidden" v-model="delimg{{$i}}" name="delimg{{$i}}">
                        @endfor
                    </div>
                    <span class="text-danger remark mt-1">{{Lang::get('切換圖片')}}</span>
                </div>
                <div class="col-lg-6">
                    <div id="productinfo_main">
<div class="info-div d-flex align-items-center mb-2">
                            <span class="name">分類型態：</span>
                            <select name="category_type" class="mr-3">
                                <option value="0" @if(isset($data['productinfo']['category_type']) && $data['productinfo']['category_type']==0) selected @endif>商城分類</option>
                                <option value="1" @if(isset($data['productinfo']['category_type']) && $data['productinfo']['category_type']==1) selected @endif>課程分類</option>
                            </select>
                            <span class="text-danger">請選擇商品屬於商城或課程</span>
                        </div>
                        <div class="info-div d-flex align-items-center">
                            <span class="name"> {{Lang::get('名稱')}}：</span>
                            <input class="w-c100" name="title" value="{{$data['productinfo']['title']}}" type="text" style="border:none" >
                        </div>
                        <div class="info-div d-flex align-items-center">
                            <span class="name"> {{Lang::get('條碼')}}：</span>
                            <input class="w-c100" name="ISBN" value="{{$data['productinfo']['ISBN']}}" type="text">
                        </div>
                        @if($data['admin_type']=='admin')
                            <div class="info-div d-flex align-items-center">
                                <span class="name"> {{Lang::get('供應商ID')}}：</span>
                                <input class="w-c100 mb-0" name="distributor_id" v-model="distributor_id" type="text">
                            </div>
                        @endif
                        <div class="mb-2"><span class="text-danger">請輸入會員ID，若設為「0」表示無供應商</span></div>
                        <div class="info-div d-flex align-items-center mb-2">
                            <span class="name"> 商品類型：</span>
                            <select name="product_cate" v-model="product_cate" class="mr-3">
                                <option value="1">投資</value>
                                <option value="2">消費</value>
                            </select>
                            <span class="text-danger">若設為「消費」，可進一步設定「回饋模組」、「廣告」與「提升會員級別」</span>
                        </div>
                        <template v-if="product_cate==2">
                            <div class="info-div d-flex align-items-center mb-2">
                                <span class="name"> 套用回饋模組：</span>
                                <select name="bonus_model_id" v-model="bonus_model_id" class="mr-3">
                                    <option value="0">無</option>
                                    @foreach($data['bonus_models'] as $vo)
                                        <option value="{{$vo['id']}}">{{$vo['name']}}</option>
                                    @endforeach
                                </select>
                                <span class="text-danger">若設為「無」，全部回饋給「系統帳號」</span>
                            </div>
                            <div class="info-div d-flex align-items-center mb-2">
                                <span class="name"> 套用廣告：</span>
                                <select name="use_ad" v-model="use_ad" class="mr-3">
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                                <span class="text-danger">若設為「是」，將套用所選「回饋模組」的「廣告推廣獎勵」，並依權重分給「有效合夥人」</span>
                            </div>
                            <div class="info-div d-flex align-items-center mb-2">
                                <span class="name"> 提升會員級別：</span>
                                <select name="vip_type_reward" v-model="vip_type_reward" class="mr-3">
                                    <option value="0">無</option>
                                    @foreach($data['vip_types'] as $vo)
                                        <option value="{{$vo['id']}}">{{$vo['vip_name']}}</option>
                                    @endforeach
                                </select>
                                <span class="text-danger">若設為「無」，表示為「一般」商品</span>
                            </div>
                        </template>
                        <template v-else>
                            <input name="bonus_model_id" type="hidden" value="0">
                            <input name="use_ad" type="hidden" value="0">
                            <input name="vip_type_reward" type="hidden" value="0">
                        </template>
                        <div class="info-div d-flex align-items-center mb-2">
                            <span class="name"> 需求課程進度：</span>
                            <select name="vip_type_require" v-model="vip_type_require" class="mr-3">
                                <option value="0">無</option>
                                @foreach($data['vip_types'] as $vo)
                                    <option value="{{$vo['id']}}">{{$vo['vip_name']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    @if(!isset(config('control.close_function_current')['網紅列表']))
                        <div class="info-div d-flex align-items-center mb-1">
                            <span class="name">{{Lang::get('所屬網紅')}}：</span>
                            <select name="kol_id" value="{{$data['productinfo']['kol_id']}}">
                                <option value="0">{{Lang::get('無')}}</option>
                                @foreach($data['kol'] as $vo)
                                    @if($vo['id'] == $data['productinfo']['kol_id'])
                                        <option value="{{$vo['id']}}" selected>{{$vo['kol_name']}}</option>
                                    @else
                                        <option value="{{$vo['id']}}">{{$vo['kol_name']}}</option>
                                    @endif
                                @endforeach
                             </select>
                            @if($data['productinfo']['kol_id'] != 0)
                                <span class="position-relative">
                                    <input id="copy_input" type="button" class="btn-sm sendbtn" value="{{Lang::get('複製網紅推廣網址')}}">
                                    <input id="copy_url" type="text"
                                           value='http://{{$_SERVER["HTTP_HOST"]}}/index/product/productinfo?id={{$data['productinfo']['id']}}&kol={{$data['productinfo']['kol_id']}}'
                                           class="position-absolute"
                                           style="width: 50px; height:10px; left: 0px; z-index: -99;">
                                </span>
                            @endif
                        </div>
                    @endif
                    @if(config('extra.productinfo.property1')!='-1')
                    <div class="info-div d-flex align-items-center">
                        <span class="name">{{config('extra.productinfo.property1')}}</span>
                        <input class="w-c100" name="Author" value="{{$data['productinfo']['Author']}}" type="text" style="border:none" >
                    </div>
                    @endif
                    @if(config('extra.productinfo.property2')!='-1')
                    <div class="info-div d-flex align-items-center">
                        <span class="name">{{config('extra.productinfo.property2')}}</span>
                        <input class="w-c100" name="house" value="{{$data['productinfo']['house']}}" type="text" style="border:none" >
                    </div>
                    @endif
                    @if(config('extra.productinfo.property3')!='-1')
                    <div class="info-div d-flex align-items-center">
                        <span class="name">{{config('extra.productinfo.property3')}}</span>
                        <input class="w-c100" name="house_date" value="{{$data['productinfo']['house_date']}}" type="text" style="border:none" >
                    </div>
                    @endif
                    @if(!isset(config('control.close_function_current')['商品描述設定']))
                    <div class="info-div d-flex align-items-center">
                        <span class="name">{{Lang::get('商品描述')}}：</span>
                        <select name="prodesc_select">
                            <option>{{Lang::get('請選擇')}}</option>
                            @foreach($data['prodesc'] as $vo)
                                <option>{{$vo['name']}}</option>
                            @endforeach
                        </select>
                        <input name="prodesc" value="{{$data['productinfo']['prodesc']}}" type="text" style="border:none">
                    </div>
                    @endif

                    <!-- 已售出數量設定 -->
                    <div class="info-div d-flex align-items-center mb-2">
                        <span class="name">已售出起始數值：</span>
                        <input class="w-c100 mr-2" name="sold_count_base" value="{{$data['productinfo']['sold_count_base'] ?? 0}}" type="number" min="0" style="border:1px solid #ccc; padding: 5px;">
                        <span class="text-muted">實際：{{$data['productinfo']['sold_count_actual'] ?? 0}}</span>
                        <span class="text-muted ml-2">顯示：{{$data['productinfo']['sold_count_display'] ?? 0}}</span>
                    </div>
                    <div class="text-danger remark mb-2">
                        <small>
                            • 起始數值：未超過前顯示此數值，超過後顯示實際數值<br>
                            • 實際數值：從訂單自動計算，只要下單即+1，不管取消或退貨<br>
                            • 顯示數值：取起始數值和實際數值的較大值
                        </small>
                    </div>

                    <div class="">
                        {{Lang::get('簡介')}}：<br><textarea name="content" id="editor">{{$data['productinfo']['content']}}</textarea>
                    </div>

                    <div id="indexADV_tag">
                        <input name="index_ADV_Json" type="hidden" v-model="index_ADV_Json">
                        @if(!isset(config('control.close_function_current')['標籤設定']))
                            <!-- 人氣商品 -->
                            <input type="checkbox" id="fomus" v-model="hot_product">
                            <label for="fomus">{{$data['tag'][0]['name']}}</label>

                            <!-- 店長推薦 -->
                            <input type="checkbox" style="margin-left:15px" id="recommend" v-model="recommend_product">
                            <label for="recommend">{{$data['tag'][1]['name']}}</label>

                            <!-- 即期良品 -->
                            <input type="checkbox" style="margin-left:15px" id="expiring" v-model="expiring_product">
                            <label for="expiring">{{$data['tag'][2]['name']}}</label>

                            @if(config('control.control_sepc_price')==1)
                                <!-- 特價商品 -->
                                <input type="checkbox" id="spe_price_product" v-model="spe_price_product">
                                <label for="spe_price_product">{{$data['tag'][3]['name']}}</label>
                            @endif
                            <p class="text-danger remark">
                                {{Lang::get('若廣告區已滿，此商品將不會加入該廣告區，但是一樣會儲存其他資料')}}
                            </p>
                        @endif
                    </div>
                </div>
            </div>

            <div class="pro-other mb-4">
            @if(config('control.control_product_paying')==1 || config('control.control_product_shipping')==1 || !isset(config('control.close_function_current')['運費標籤管理']))
                <div class="col-lg-6">
                    @if(config('control.control_product_paying')==1 || config('control.control_product_shipping')==1 || !isset(config('control.close_function_current')['運費標籤管理']))
                        <h3 class="main-title"><i class="bi bi-currency-dollar"></i> {{Lang::get('運費付款管理')}}</h3>
                    @endif
                    <div class="admin-content">
                        <div class="mb-2" id="pay_div">
                            @if(config('control.control_product_paying')==1)
                                {{Lang::get('請選擇商品限用付款方法')}}：<br>
                                <input type="hidden" name="pay_type" v-model="pay_type">
                                <span v-for="item in pay_decode" style="margin-right: 10px;">
                                    <input type="checkbox" :id="'pay'+item.id" :value="item" v-model="pay_selected">
                                    <label :for="'pay'+item.id" v-text="item.name"></label>
                                </span>
                                <p class="text-danger">{{Lang::get('若都無勾選，則視為允許全部方法')}}</p>
                            @endif
                        </div>

                        <div class="mb-2" id="shipping_div">
                            @if(config('control.control_product_shipping')==1)
                                {{Lang::get('請選擇商品限用運法')}}：<br>
                                <input type="hidden" name="shipping_type" v-model="shipping_type">
                                <span v-for="item in shpping_decode" style="margin-right: 10px;">
                                    <input type="checkbox" :id="'shipping'+item.id" :value="item" v-model="shipping_selected">
                                    <label :for="'shipping'+item.id" v-text="item.name"></label>
                                </span>
                                <p class="text-danger">{{Lang::get('若都無勾選，則視為允許全部方法')}}</p>
                            @endif

                            @if(!isset(config('control.close_function_current')['運費標籤管理']))
                                {{Lang::get('運費標籤')}}：
                                <input type="radio" id="shipping_fee_tag_0" name="shipping_fee_tag" value="0" v-model="shipping_fee_tag">
                                <label for="shipping_fee_tag_0">不使用{{Lang::get('運費標籤')}}</label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                @foreach($data['shipping_fee_tag'] as $vo)
                                    <input type="radio" id="shipping_fee_tag_{{$vo['id']}}" name="shipping_fee_tag" value="{{$vo['id']}}" v-model="shipping_fee_tag">
                                    <label for="shipping_fee_tag_{{$vo['id']}}">{{$vo['name']}}({{$vo['price']}}元)</label>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                @endforeach
                                <p class="text-danger">
                                    {{Lang::get('未勾選一律視為0元')}}
                                </p>
                            @else
                                <input type="hidden" name="shipping_fee_tag" v-model="shipping_fee_tag">
                            @endif
                        </div>
                    </div>
                </div>
                @endif
                <div class="col-lg-6">
                    <h3 class="main-title"><i class="bi bi-currency-dollar"></i> {{Lang::get('付款管理')}}</h3>
                    <div class="mb-2 admin-content" id="indexADV">
                        @if(config('control.control_pre_buy')==1 && !isset(config('control.close_function_current')['庫存警示']))
                            <div class="mb-2">
                                {{Lang::get('是否可超額購買')}}：
                                <input type="radio" name="pre_buy" id="pre_buy_yes" value="1" v-model="pre_buy">
                                <label for="pre_buy_yes">{{Lang::get('可以')}}</label>
                                &nbsp;&nbsp;
                                &nbsp;&nbsp;
                                <input type="radio" name="pre_buy" id="pre_buy_no"  value="0" v-model="pre_buy">
                                <label for="pre_buy_no">{{Lang::get('不可以')}}</label>
                                <div v-if="pre_buy==1">
                                    {{Lang::get('超額購買上限')}}：<input type="number" name="pre_buy_limit" v-model="pre_buy_limit" min="0">
                                    <span class="text-danger">({{Lang::get('若設為0則表示無上限')}})</span>
                                </div>
                            </div>
                        @else
                            <input type="hidden" name="pre_buy" id="pre_buy_yes" value="1">
                            <input type="hidden" name="pre_buy_limit" value="0">
                        @endif
                        <div class="mb-2">
                            @if(config('control.control_card_pay') == 1 )
                                {{Lang::get('是否可刷卡')}}：
                                <input type="radio" name="card_pay" id="card_pay_yes" value="1" v-model="card_pay">
                                <label for="card_pay_yes">{{Lang::get('可以')}}</label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="radio" name="card_pay" id="card_pay_no"  value="0" v-model="card_pay">
                                <label for="card_pay_no">{{Lang::get('不可以')}}</label>
                            @else
                                <input type="hidden" name="card_pay" id="card_pay_yes" value="1">
                            @endif
                        </div>
                        @if(config('control.control_register'))
                            <div class="mb-2">
                                {{Lang::get('是否需填寫報名資料')}}：
                                <input type="radio" name="is_registrable" id="is_registrable_yes" value="1" v-model="is_registrable">
                                <label for="is_registrable_yes">{{Lang::get('需要')}}</label>
                                &nbsp;&nbsp;&nbsp;&nbsp;
                                <input type="radio" name="is_registrable" id="is_registrable_no"  value="0" v-model="is_registrable">
                                <label for="is_registrable_no">{{Lang::get('不需要')}}</label>
                            </div>
                            <div v-if="is_registrable==1">
                                <div class="mb-2">
                                    {{Lang::get('是否需點名')}}：
                                    <input type="radio" name="is_roll_call" id="is_roll_call_yes" value="1" v-model="is_roll_call">
                                    <label for="is_roll_call_yes">{{Lang::get('需要')}}</label>

                                    <input type="radio" name="is_roll_call" id="is_roll_call_no"  value="0" v-model="is_roll_call">
                                    <label for="is_roll_call_no">{{Lang::get('不需要')}}</label>
                                    <div class="mt-2" v-if="is_registrable==1 && is_roll_call==1 && '{{$data['productinfo']['id']}}'!=0">
                                        {{Lang::get('點名網址')}}：<input style="width: 100%" value="http://{{request()->server('HTTP_HOST')}}/admin/examination/start_roll_call?code={{$data['productinfo']['product_id']}}">
                                    </div>
                                </div>
                                <div>
                                    {{Lang::get('報名資料修改截止日')}}：
                                    <input type="date" name="register_data_change_limit" v-model="register_data_change_limit">
                                </div>
                            </div>
                        @else
                            <input type="hidden" name="is_registrable" id="is_registrable_yes" value="0">
                            <input type="hidden" name="is_roll_call" id="is_roll_call_yes" value="0">
                        @endif
                    </div>
                    <h3 class="main-title">{{Lang::get('其他')}}</h3>
                    <div class="admin-content">
                        <div class="info-div">
                            <span>
                                {{Lang::get('隱藏關鍵字')}}：
                                <p class="text-danger remark mb-0">{{Lang::get('請用英文逗號(,)區隔')}}</p>
                            </span>
                            <input class="w-c100" name="keywords" value="{{$data['productinfo']['keywords']}}" type="text">
                        </div>
                        <div class="info-div">
                            <span>{{Lang::get('隱藏描述詞')}}：</span>
                            <textarea  name="display">{{$data['productinfo']['display']}}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="edit_block mb-3" id="subProject">
                <div>
                    @if(empty(config('control.close_function_current')['詢價回函']))
                        {{Lang::get('消費者可否詢價')}}：
                        <input type="radio" name="ask_price" id="ask_price_yes" value="1" v-model="ask_price">
                        <label for="ask_price_yes">{{Lang::get('可以')}}</label>
                        <input type="radio" name="ask_price" id="ask_price_no"  value="0" v-model="ask_price">
                        <label for="ask_price_no">{{Lang::get('不可以')}}</label>
                        <span class="ml-4 text-danger">
                            {{Lang::get('若可以，消費者可向您提出詢價請求，獲得同意後即可用此價格購買商品。')}}
                        </span>
                        <span class="text-danger">
                            {{Lang::get('至少需搭配一個品項。')}}
                        </span>
                    @else
                        <input type="hidden" name="ask_price" id="ask_price_yes" value="1">
                    @endif
                </div>
                <div>
                    <input name="has_price" type="hidden" v-model="has_price_Num">
                    <label class="switch mb-0"><input type="checkbox" id="inquiry-btn" v-model="has_price"> <span class="slider round"></span></label>
                    <label for="inquiry-btn" class="font-weight-bold mb-0">{{Lang::get('售價功能開關')}}</label>
                    <span class="ml-4 text-danger">{{Lang::get('取消勾選「啟用」，前台將不顯示商品金額，消費者亦不可購買此商品。')}}</span>
                </div>
                <div :class="[has_price?'':'d-none', 'mb-3']">
                    <div class="notice mt-0">
                        <button class="btn noticeBtn" type="button"
                                data-toggle="collapse" data-target="#collapseNotice" aria-expanded="false" aria-controls="collapseNotice">
                            {{Lang::get('注意事項')}} <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="collapse show" id="collapseNotice">
                            <div class="d-flex ">
                                <span>1.{{Lang::get('定價、售價若輸入相同，則前台就只會顯示一個金額，若不同則會有劃線改價格的樣式。售價若設為0，則消費者選擇此品項時將顯示回函表按鈕')}}</span>
                            </div>
                            @if(config('control.control_prod_type_layer')==1)
                                <div class="d-flex ">
                                    <span>2.{{Lang::get('品項如有輸入底線(_)，前台會自動拆分品項成多組供消費者選擇，但有幾點須特別注意')}}：</span>
                                    <ul class="text-danger mb-0">
                                        <li>{{Lang::get('消費者組合後的品項如未被新增則無法加入購物車')}}</li>
                                        <li>{{Lang::get('請確保所有品項所輸入的底線數量一致')}}</li>
                                        <li>{{Lang::get('如有組合名稱重複的情況將導致消費者無法選擇到正確的品項')}}</li>
                                        <li>{{Lang::get('品項可不輸入文字，但若商品有多品項，前台顯示將出現問題')}}</li>
                                    </ul>
                                </div>
                                <div class="d-flex">
                                    <span>3.{{Lang::get('可使用excel複製貼上方式快速建立品項，但有幾點須特別注意')}}：</span>
                                    <ul class="text-danger mb-0">
                                        <li>
                                            {{Lang::get('請下載並修改品項範例檔的內容')}}:
                                            <a href="{{url('Productinfo/price_example')}}">{{Lang::get('下載')}}</a>
                                        </li>
                                        <li>{{Lang::get('輸入時請注意儲存格格式(建議使用文字)及系統需求格式')}}</li>
                                        <li>{{Lang::get('複製內容時選取適當範圍(須包含最上方欄位名稱)')}}</li>
                                        <li>{{Lang::get('貼入下方輸入區即可快速生成品項，資料格式若有誤，品項該欄位資料將無法正常顯示')}}</li>
                                    </ul>
                                </div>
                                <textarea id="textarea_price" class="form-control mb-2" rows="1"
                                          placeholder="{{Lang::get('請貼入品項')}}..." @paste="paste_data($event)"></textarea>
                            @endif
                        </div>
                    </div>
                    <div id="inquiry" class="edit_form">
                        <table class="table-rwd table" style="min-width: 1600px;">
                            <thead>
                                <tr>
                                    <th type-data="price" style="width:200px;">{{Lang::get('定價')}}({{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}})</th>
                                    <th type-data="title_name" style="width: 140px;">{{Lang::get('品項')}}<span v-if="is_registrable==1">(或含時間之場次)</span></th>
                                    <th  type-data="count" style="width: 140px;">{{Lang::get('售價')}}({{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}})</th>
                                    <th  type-data="price_cv" style="width: 140px;">CV值(美金)<br>(小數2位)</th>
                                    <th  type-data="price_supplier" style="width: 140px;">供應商分潤(美金)<br>(小數2位)</th>
                                    @if(!isset(config('control.close_function_current')['庫存警示']))
                                        <th type-data="online_num" style="width: 150px;">{{Lang::get('庫存數量')}}</th>
                                        <th type-data="limit_num" style="width: 70px;">{{Lang::get('警示數量')}}</th>
                                    @endif
                                    @if(!isset(config('control.close_function_current')['存放位置管理']))
                                        <th type-data="position" style="width: 70px;">{{Lang::get('存放位置')}}</th>
                                    @endif
                                    <th type-data="pic_index" style="width:70px;">{{Lang::get('對應圖片')}}</th>
                                    <th type-data="start_time~end_time" style="width: 250px;">{{Lang::get('顯示期限')}}</th>
                                    @if(config('control.control_register'))
                                        <th type-data="act_time~act_time_end" style="width: 350px;" v-if="is_registrable==1">{{Lang::get('活動時間')}}</th>
                                        <th type-data="act_remind_time" style="width: 350px;" v-if="is_registrable==1">
                                            {{Lang::get('活動提醒時間')}}<br>({{Lang::get('有設定則會於整點寄送提醒信')}})
                                        </th>
                                        @if($data['productinfo']['id'] !=0)
                                            <th style="width: 80px;" v-if="is_registrable==1">{{Lang::get('取消')}}</th>
                                        @endif
                                    @endif
                                    <th type-data="order_id" style="width: 60px;">{{Lang::get('排序')}}</th>
                                    <th style="width: 60px;">{{Lang::get('刪除')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in items">
                                    <td>{{config('extra.shop.dollar_symbol')}}
                                        <input type="number" v-model="item.price" min="0" @blur="check_zero(item)" style="width: calc(100% - 30px);">
                                    </td>
                                    <td>
                                        @if(!isset(config('control.close_function_current')['價格組合設定']))
                                        {{Lang::get('預設模組')}}：
                                        <select class='' v-model="item.title" @change="ch_pos(index)">
                                            @foreach($data['discount'] as $vo)
                                                <option value="{{$vo['id']}}" >{{$vo['name']}}</option>
                                            @endforeach
                                        </select>
                                        @endif
                                        <input type="text" v-model="item.title_name">
                                    </td>
                                    <td>
                                        {{config('extra.shop.dollar_symbol')}}
                                        <input type="number" v-model="item.count" min="0" @blur="check_zero(item)" style="width: calc(100% - 30px);">
                                    </td>
                                    <td>
                                        {{config('extra.shop.dollar_symbol')}}
                                        <input type="number" v-model="item.price_cv" min="0" @blur="check_zero(item)" style="width: calc(100% - 30px);">
                                    </td>
                                    <td>
                                        {{config('extra.shop.dollar_symbol')}}
                                        <input type="number" v-model="item.price_supplier" min="0" @blur="check_zero(item)" style="width: calc(100% - 30px);">
                                    </td>
                                    @if(!isset(config('control.close_function_current')['庫存警示']))
                                        <td>
                                            @if(!isset(config('control.close_function_current')['存放位置管理']))
                                                <div>
                                                    {{Lang::get('實際庫存')}}:
                                                    <input type="number" style="width:100px;"
                                                           v-model="item.num" @blur="position_check(item)">
                                                </div>
                                                <div class="mt-2"> {{Lang::get('線上可購買數量')}}:<span v-text="item.online_num"></span></div>
                                            @else
                                                <div>
                                                    <input type="number" style="width: 100px;"
                                                           v-model="item.online_num" @blur="check_zero(item)">
                                                </div>
                                            @endif
                                        </td>
                                        <td>
                                            <input type="number" style="width: 100px;"
                                                   v-model="item.limit_num" @blur="check_zero(item)">
                                        </td>
                                    @endif
                                    @if(!isset(config('control.close_function_current')['存放位置管理']))
                                        <td>
                                            <select class="position_select" v-model="item.position" @change="position_check(item)" >
                                                @foreach($data['position'] as $vo)
                                                <option value="{{$vo['id']}}" >{{$vo['name']}}</option>
                                                @endforeach
                                            </select>
                                        </td>
                                    @endif
                                    <td><input type="number" style="width: 100px;" v-model="item.pic_index" min="1"></span></td>
                                    <td>
                                        {{Lang::get('開始')}}:<input type="date" style="width: 75%;" v-model="item.start_time"><br>
                                        {{Lang::get('結束')}}:<input type="date" style="width: 75%;" v-model="item.end_time">
                                    </td>
                                    @if(config('control.control_register'))
                                        <td v-if="is_registrable==1">
                                            {{Lang::get('開始')}}:<input type="datetime-local" style="width: 75%;" v-model="item.act_time"><br>
                                            {{Lang::get('結束')}}:<input type="datetime-local" style="width: 75%;" v-model="item.act_time_end">
                                        </td>
                                        <td v-if="is_registrable==1">
                                            <input type="datetime-local" style="width: 75%;" v-model="item.act_remind_time">
                                            <span class="text-danger"><br>{{Lang::get('上午12:00為24小時制的00:00')}}<br></span>
                                        </td>

                                        @if($data['productinfo']['id'] !=0)
                                            <td  v-if="is_registrable==1">
                                                <span v-if="typeof(item.id)!='undefined'">
                                                    <span v-if="item.closed==0" class="btn btn-danger" @click="close_type(item.id)">{{Lang::get('取消')}}</span>
                                                    <span v-if="item.closed==1" class="text-danger">{{Lang::get('已取消')}}</span>
                                                </span>
                                            </td>
                                        @endif
                                    @endif

                                    <td><input type="number" style="width: 100px;" v-model="item.order_id"></span></td>
                                    <td><span class="bi bi-trash" @click="del(item)"></span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-center">
                        <span class="btn clearbtn" @click="add"><i class="bi bi-plus-lg"></i> {{Lang::get('添加')}}</span>
                    </div>
                </div>

                @if(config('control.control_register'))
                    <div v-show="is_registrable==1" class="mt-3">
                        {{Lang::get('活動提醒訊息')}}：
                        <span class="text-danger">{{Lang::get('此內容會帶入活動提醒信中')}}</span>
                        <textarea id="act_msg" name="remind_msg" placeholder="{{Lang::get('請輸入活動提醒訊息')}}">{{$data['productinfo']['remind_msg']}}</textarea>
                    </div>
                @endif

                <input name="subProjectJson" type="hidden" v-model="subProjectJson">
                <input name="delProjectJson" type="hidden" v-model="delProjectJson">
            </div>

            <div class="edit_block">
                <table id="exposure" class="table-rwd table">
                    <thead>
                        <tr>
                            <th class="text-center">{{Lang::get('推薦商品')}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <p class="text-danger text-left mb-0">{{Lang::get('請輸入「商品ID」並用英文逗號(,)區隔')}}</p>
                                <textarea class="chooseitemsid" name="pushitem">{{$data['productinfo']['pushitem']}}</textarea>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="edit_block pro_txt_content">
                <ul class="nav nav-tabs">
                    <li><a data-toggle="tab" href="#text1" class="active">{{Lang::get('商品介紹')}}</a></li>
                    <li><a data-toggle="tab" href="#text2">{{Lang::get('商品屬性')}}</a></li>
                    <li><a data-toggle="tab" href="#text3">{{Lang::get('訂購須知')}}</a></li>
                    <li><a data-toggle="tab" href="#text4">{{Lang::get('付款方式')}}</a></li>
                    <li><a data-toggle="tab" href="#text5">{{Lang::get('其他說明')}}</a></li>
                </ul>
                <div class="tab-content" id="textCentent">
                    <div id="text1" class="prod_description tab-pane fade in active show">
                        <input type="radio" value="1" name="text1_online"
                        @if($data['productinfo']['text1_online'] =="1" )
                            checked
                        @endif
                        id="show1"> <label for="show1">{{Lang::get('顯示')}}</label>
                        <input type="radio" value="0" name="text1_online"
                        @if($data['productinfo']['text1_online'] !="1" )
                            checked
                        @endif
                        style="margin-left:5px" id="close1"> <label for="close1">{{Lang::get('隱藏')}}</label>
                        <br>
                        <a class="btn sendbtn mr-1 mb-1" onclick="cellGetFromDefault(1)">{{Lang::get('載入預設訊息')}}</a>
                        <a class="btn clearbtn mb-1" onclick="cellCtrlFromDefault(1)">{{Lang::get('儲存預設訊息')}}</a>
                        <br>
                        <textarea name="text1" id="editor1" style="width:100%; height:500px;">{{$data['productinfo']['text1']}}</textarea>
                    </div>
                    <div id="text2" class="prod_description tab-pane fade">
                        <input type="radio" value="1" name="text2_online"
                        @if($data['productinfo']['text2_online'] =="1" )
                            checked
                        @endif
                        id="show2"> <label for="show2">{{Lang::get('顯示')}}</label>
                        <input type="radio" value="0" name="text2_online"
                        @if($data['productinfo']['text2_online'] != "1" )
                            checked
                        @endif
                        style="margin-left:5px" id="close2"> <label for="close2">{{Lang::get('隱藏')}}</label>
                        <br>
                        <a class="btn sendbtn mr-1 mb-1" onclick="cellGetFromDefault(2)">{{Lang::get('載入預設訊息')}}</a>
                        <a class="btn clearbtn mb-1" onclick="cellCtrlFromDefault(2)">{{Lang::get('儲存預設訊息')}}</a>
                        <br>
                        <textarea name="text2" id="editor2" style="width:100%; height:500px;">{!! $data['productinfo']['text2'] !!}</textarea>
                    </div>
                    <div id="text3" class="prod_description tab-pane fade">
                        <input type="radio" value="1" name="text3_online"
                        @if($data['productinfo']['text3_online'] == "1" )
                            checked
                        @endif
                        id="show3"> <label for="show3">{{Lang::get('顯示')}}</label>
                        <input type="radio" value="0" name="text3_online"
                        @if($data['productinfo']['text3_online'] !="1" )
                            checked
                        @endif
                        style="margin-left:5px" id="close3"> <label for="close3">{{Lang::get('隱藏')}}</label>
                        <br>
                        <a class="btn sendbtn mr-1 mb-1" onclick="cellGetFromDefault(3)">{{Lang::get('載入預設訊息')}}</a>
                        <a class="btn clearbtn mb-1" onclick="cellCtrlFromDefault(3)">{{Lang::get('儲存預設訊息')}}</a>
                        <br>
                        <textarea name="text3" id="editor3" style="width:100%; height:500px;">{!! $data['productinfo']['text3'] !!}</textarea>
                    </div>
                    <div id="text4" class="prod_description tab-pane fade">
                        <input type="radio" value="1" name="text4_online"
                        @if($data['productinfo']['text4_online'] =="1" )
                            checked
                        @endif
                        id="show4"> <label for="show4">{{Lang::get('顯示')}}</label>
                        <input type="radio" value="0" name="text4_online"
                        @if($data['productinfo']['text4_online'] !="1" )
                            checked
                        @endif
                        style="margin-left:5px" id="close4"> <label for="close4">{{Lang::get('隱藏')}}</label>
                        <br>
                        <a class="btn sendbtn mr-1 mb-1" onclick="cellGetFromDefault(4)">{{Lang::get('載入預設訊息')}}</a>
                        <a class="btn clearbtn mb-1" onclick="cellCtrlFromDefault(4)">{{Lang::get('儲存預設訊息')}}</a>
                        <br>
                        <textarea name="text4" id="editor4" style="width:100%; height:500px;">{!! $data['productinfo']['text4'] !!}</textarea>
                    </div>
                    <div id="text5" class="prod_description tab-pane fade">
                        <input type="radio" value="1" name="text5_online"
                        @if($data['productinfo']['text5_online'] =="1" )
                            checked
                        @endif
                        id="show5"> <label for="show5">{{Lang::get('顯示')}}</label>
                        <input type="radio" value="0" name="text5_online"
                        @if($data['productinfo']['text5_online'] !="1" )
                            checked
                        @endif
                        style="margin-left:5px" id="close5"> <label for="close5">{{Lang::get('隱藏')}}</label>
                        <br>
                        <a class="btn sendbtn mr-1 mb-1" onclick="cellGetFromDefault(5)">{{Lang::get('載入預設訊息')}}</a>
                        <a class="btn clearbtn mb-1" onclick="cellCtrlFromDefault(5)">{{Lang::get('儲存預設訊息')}}</a>
                        <br>
                        <textarea name="text5" id="editor5" style="width:100%; height:500px;">{!! $data['productinfo']['text5'] !!}</textarea>
                    </div>
                </div>
            </div>
            <br><br>
            <div class="confirm_area">

                <a id="confirm" class="btn-lg sendbtn mr-1 mb-1" onclick="productinfoForm_ck()">{{Lang::get('儲存')}}</a>
                <p class="text-center text-light remark w-100">
                    @if(!isset(config('control.close_function_current')['會員瀏覽商品設定']))
                        {{Lang::get('新增後需至「會員瀏覽商品設定」勾選商品才能於前台正常顯示')}}
                        @if($data['admin_type']=='distribution')
                            ({{Lang::get('需由平台處理')}})
                        @endif
                    @endif
                </p>
            </div>
        </form>
    </div>
@endsection

@section('ownJS')
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var img_Quantity = Number("{{config('control.control_img_quantity')}}"); /*限制上傳圖片數量*/

    	function productinfoForm_ck(){
    		var final_array =  $('input[name="final_array"]').val();
    		var r_repeat =  $('input[name="r_repeat"]:checked').val();
            var title =  $('input[name="title"]').val();
    		var form_error = $("input[name='form_error']").val();

    		if(final_array == '[]' || final_array == '{}' || final_array == '' ){
                Vue.toasted.show("{{Lang::get('至少一個階層')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
    			return;
    		}
    		if(isNaN(r_repeat)){
                Vue.toasted.show("{{Lang::get('分配重複或是不重複序號位置')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
    			return;
    		}

            var has_no_img = true;
            for (var i = 0; i < img_Quantity; i++) {
                if(changeGroupBox['src' + i].length > 5){
                    has_no_img = false;
                    break;
                }
            }
    		if(has_no_img){
                Vue.toasted.show("{{Lang::get('請上傳至少一個照片')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                return;
            }

            if(title == ''){
                Vue.toasted.show("{{Lang::get('請輸入標題')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                return;
            }

            for (let i = 0; i < subProjectVM.items.length; i++) {
                const element = subProjectVM.items[i];
                if(Number(element.price_supplier)){
                    if(productinfo_mainVM.product_cate=='1'){
                        Vue.toasted.show("{{Lang::get('投資商品不可設定供應商結算金額')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                        return;
                    }
                    if(!Number(productinfo_mainVM.distributor_id)){
                        Vue.toasted.show("{{Lang::get('有設定供應商結算金額卻未設定供應商')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                        return;
                    }
                }
            }

            $.when(subProjectVM.position_check()).done(function(f){
                var form_error = $("input[name='form_error']").val();
                if(form_error==0){
                    $('#block_block').show();
                    productinfoForm.submit();
                }
            });
    	}
        window.addEventListener('pageshow', (event) => {
            $('#block_block').hide();
            $('.upl').get().forEach(element => {
                $(element).val('');
            });
        });

    	function add_array(target_num=''){
    		var parent_id =  $('#parent_id'+target_num).val();
    		var branch_id =  $('#branch_id'+target_num).val();
    		var prev_id   =  $('#prev_id'+target_num).val();
            branch_id = branch_id=="0" ? parent_id : branch_id;

    		if(parent_id==0 && prev_id==0){
                Vue.toasted.show("{{Lang::get('請選擇階層')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
    			return;
    		}
            var final_array = $('input[name="final_array"]').val() ? $('input[name="final_array"]').val() : '[]';
            if(final_array == '{}'){
                final_array = [{
                    "parent_id":parent_id,
                    "branch_id":branch_id,
                    "prev_id": prev_id,
                }];
            }else{
                final_array = JSON.parse(final_array);
                final_array.push({
                    "parent_id":parent_id,
                    "branch_id":branch_id,
                    "prev_id": prev_id,
                });
            }

            var final_array_string = [];
            for(var i=0;i<final_array.length;i++){
                final_array_string.push(
                    '{"prev_id":"'+final_array[i]['prev_id']+'","branch_id":"'+final_array[i]['branch_id']+'","parent_id":"'+final_array[i]['parent_id']+'"}'
                );
            }
            final_array_string = '['+final_array_string.join(',')+']';
            // console.log(final_array_string);
            $('input[name="final_array"]').val(final_array_string);

    		$('#parent_id'+target_num).val(0);
    		$('#branch_id'+target_num).val(0);
    		$('#prev_id'+target_num).val(0);
    		$('.product_select'+target_num+'-').val(0).trigger('change');
    		show_array();
    	}
    	function show_array(){
    		var final_array = $('input[name="final_array"]').val();
    		$('#show_array').html("");
            $.ajax({
                url: "{{url('productinfo/show_array')}}",
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                type: 'POST',
                data: {
    				array:final_array,
    				type:'text'
    			},
                success: function(response) {
    				var array = response;
                    console.log(array);
    				array.forEach(function(item,key){
    				    $('#show_array').append(
                            '<p>'+item+'<a href="###" onclick="del_array('+key+')" style="color: rgb(0, 123, 255);"> '+"{{Lang::get('取消')}}"+'</a></p>'
                        );

    				    if(key == 0){
    					   $('#show_list').html(item+">");
                        }
    				});
                },
                error: function(xhr) {
                    console.log(xhr);
                }
            });
    	}
    	function del_array(key){
    		var final_array = $('input[name="final_array"]').val();
    		final_array = JSON.parse(final_array);
    		// console.log(final_array);

    		var result = final_array.filter(function(item,ikey){
    			return ikey != key;
    		});

    		$('input[name="final_array"]').val(JSON.stringify(result));

    		show_array();
    	}
    	function product_position(type,next,target_num=''){
            // console.log('#product_select_area'+target_num+' select.'+type+target_num+'-'+next);
    		var value = $('#product_select_area'+target_num+' select.'+type+target_num+'-'+next).val();
            $.ajax({
                url: "{{url('productinfo/position_select')}}",
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                type: 'POST',
                data: {
    				type:type,
    				value:value,
                    next:next,
    				target_num:target_num,
    			},
                success: function(response) {
    				switch (type) {
    				  case "product_select":
    					//$('#'+type).html(response.trim());
    					$('#branch_select'+target_num).html(response.trim());

    					$("#branch_id"+target_num).val(0);
    					$("#parent_id"+target_num).val(0);
    					$("#prev_id"+target_num).val(value);
    					break;
    				  default:
    					if(next ==1){
    						$("#branch_id"+target_num).val(0);
    						$("#parent_id"+target_num).val(value);
    					}else{
    						$("#branch_id"+target_num).val(value);
    					}
    					next = parseInt(next)+1;
    					// console.log('span.'+type+target_num+'-'+next);
    					$('span.'+type+target_num+'-'+next).html(response.trim());

    				}
                },
                error: function(xhr) {
                    console.log(xhr);
                }
            });
    	}

        var editor;
        KindEditor.ready(function(K) {
            editor = K.create('#editor', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items:['source', '|',  'hr','|','emoticons','|','forecolor','bold', 'italic', 'underline','link', 'unlink',],
                width:'100%',
                height:'180px',
                resizeType:0
            });
        });
        var editor1;
        KindEditor.ready(function(K) { //插入影片功能 items裡加入 'code'
            editor1 = K.create('#editor1', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'code', 'link', 'unlink','|',
                    'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
                ],
                width:'100%',
                height:'500px',
                resizeType:0
            });
        });
        var editor2;
        KindEditor.ready(function(K) {
            editor2 = K.create('#editor2', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
                ],
                width:'100%',
                height:'500px',
                resizeType:0
            });
        });
        var editor3;
        KindEditor.ready(function(K) {
            editor3 = K.create('#editor3', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
                ],
                width:'100%',
                height:'500px',
                resizeType:0
            });
        });
        var editor4;
        KindEditor.ready(function(K) {
            editor4 = K.create('#editor4', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
                ],
                width:'100%',
                height:'500px',
                resizeType:0
            });
        });
        var editor5;
        KindEditor.ready(function(K) {
            editor5 = K.create('#editor5', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
                ],
                width:'100%',
                height:'500px',
                resizeType:0
            });
        });

        var act_msg;
        KindEditor.ready(function(K) {
            act_msg = K.create('#act_msg', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
                ],
                width:'100%',
                resizeType:0
            });
        });
    </script>
    <script>
        // 複製網紅推廣網址
        var clipBoardContent = "{{request()->server('HTTP_HOST')}}/index/product/productinfo.html?id={{$data['productinfo']['id']}}&kol={{$data['productinfo']['kol_id']}}";
        $('#copy_input').on('click', function(e){
            var copyText = $("#copy_url");
            copyText.select();
            document.execCommand("Copy");
            Vue.toasted.show("{{Lang::get('操作成功')}}",{duration:1500, className: ["toasted-primary", "bg-success"]});
        });

        pic_num = Array(img_Quantity);

        /* 商品圖片Vue --------------------------------------*/
            var changeGroupBox = {};
            var pic_data = "{{json_encode($data['productinfo']['pic'])}}".replace(/&quot;/g,'"').trim();
            pic_data = pic_data ? JSON.parse(pic_data) : [];
            // console.log(pic_data);
            for (var i = 0; i < pic_num.length; i++) {
                changeGroupBox['delimg' + i] = 0;

                if(typeof(pic_data[i])!="undefined"){
                    if(pic_data[i]!=""){
                        changeGroupBox['src' + i] = '{{__UPLOAD__}}' + pic_data[i];
                        continue;
                    }
                }
                changeGroupBox['src' + i] = '';
                // changeGroupBox['src' + i] = 'src' + i;
            }
            changeGroupBox['src'] = changeGroupBox['src0'];
            changeGroupBox['show_index'] = 0;
            var changeGroupBoxVM = new Vue({
                el: '#changeGroupBox',
                data: changeGroupBox,
                methods: {
                    check_type: function(url){
                        // console.log(url);
                        if(url.split('.').length >=2){
                            type = url.split('.')[1].substr(0,3).toLowerCase();
                        }else if(url.split(';').length >=2){
                            var list = url.split(';')[0].split('/')
                            type = list[list.length-1].toLowerCase();
                        }else{
                            return 'img'
                        }

                        if(['png', 'jpg', 'jpeg', 'gif', 'tif'].indexOf(type) != -1){
                            return 'img';
                        }else if(['mp4', 'wmv'].indexOf(type) != -1){
                            return 'video';
                        }else{
                            return 'img';
                        }
                    },
                    previewImg: function (number) {
                        var reader = new FileReader();
                        reader.onload = function (e) {
                            changeGroupBox.src = e.target.result;
                            changeGroupBox['src' + number] = e.target.result;
                            $('input[name="image_base64_'+ number +'"]').val("");
                        }
        				this['delimg' + number] = 0;
                        reader.readAsDataURL(this.$refs['img' + number].files[0]);
                        if(number < img_Quantity){
                            $(".hi-" + (number+1)).css("display","");
                        }
                    },
                    switchSrc: function (number) {
                        this['src'] = this['src' + number];
                        this.show_index = number;
                        // $('input[name="image'+number+'"]').click();
                    },
                    delSrc: function (number) {
                        if(this.show_index==number){
                            this['src'] = '';
                        }
                        this['src' + number] = 'src' + number;
                        this['delimg' + number] = 1;
                    },
                }
            });

            function upload_imgs(){
                var files = $('#multiple_imgs')[0].files
                for (var i = 0; i < files.length; i++) {
                    var extension = files[i].name.split('.').pop().toLowerCase(),  //file extension from input file
                    isSuccess = ['png', 'jpg', 'jpeg', 'gif', 'tif'].indexOf(extension) > -1;

                    if (isSuccess) { //yes
                        var reader = new FileReader();
                        reader.onload = function (e) {
                            var out_of_limit = true;
                            for (var x = 0; x < img_Quantity; x++) {
                                if(changeGroupBox['src' + x].length <= 5){
                                    changeGroupBox.src = e.target.result;
                                    changeGroupBox['src' + x] = e.target.result;
                                    changeGroupBox['delimg' + x] = 0;
                                    if(x < img_Quantity){
                                        $(".hi-" + (x+1)).css("display","");
                                    }
                                    out_of_limit = false;
                                    $('input[name="image_base64_'+ x +'"]').val(e.target.result);
                                    break;
                                }
                            }
                            if(out_of_limit){
                                Vue.toasted.show("{{Lang::get('超出上限')}}",{duration:1500, className: ["toasted-primary", "bg-warning"]});
                            }
                        }
                        reader.readAsDataURL(files[i]);
                    }
                    else{
                        Vue.toasted.show("{{Lang::get('請上傳圖片')}}",{duration:1500, className: ["toasted-primary", "bg-warning"]});
                    }
                }
                $('#multiple_imgs').val('');
            }

        /* 特價商品等選項勾選Vue --------------------------------------*/
        var indexADV_tag = {
            recommend_product: +"{{$data['productinfo']['recommend_product']}}",
            expiring_product: +"{{$data['productinfo']['expiring_product']}}",
            hot_product: +"{{$data['productinfo']['hot_product']}}",
            spe_price_product: +"{{$data['productinfo']['spe_price_product']}}",
        }
        var indexADV_tagVM = new Vue({
            el: '#indexADV_tag',
            data: indexADV_tag,
            computed: {
                index_ADV_Json: function () {
                    var index_ADV_Array = [
                        {
                            tableName: 'spe_price_product',
                            value: Number(this.spe_price_product)
                        },
                        {
                            tableName: 'recommend_product',
                            value: Number(this.recommend_product)
                        },
                        {
                            tableName: 'expiring_product',
                            value: Number(this.expiring_product)
                        },
                        {
                            tableName: 'hot_product',
                            value: Number(this.hot_product)
                        }
                    ];
                    return JSON.stringify(index_ADV_Array);
                }
            },
        })

        /* 報名付款等的Vue --------------------------------------*/
            var indexADV = {
                pre_buy: "{{$data['productinfo']['pre_buy']}}",
                pre_buy_limit: "{{$data['productinfo']['pre_buy_limit']}}",
                card_pay: "{{$data['productinfo']['card_pay']}}",
                is_registrable: "{{$data['productinfo']['is_registrable']}}",
                is_roll_call: "{{$data['productinfo']['is_roll_call']}}",
                register_data_change_limit: "{{$data['productinfo']['register_data_change_limit']}}",
            }
            var indexADVVM = new Vue({
                el: '#indexADV',
                data: indexADV,
                methods: {
                    set_null_time: function(item){
                        this[item] = "1970-01-01T00:00";
                    }
                }
            });

        /* 商品品項Vue --------------------------------------*/
            var default_position = @if( empty(config('control.close_function_current')['存放位置管理'] )) 0 @else 1 @endif;
            var prod_id = "{{$data['productinfo']['id']}}";
            var default_price_date = {
                product_id: prod_id,
                discount_id:0,
                num: 0,
                limit_num: 10,
                old_num:0,
                total: 0,
                price: 0,
                count: 0,
                price_cv: 0,
                price_supplier: 0,
                title: 0,
                title_name:"",
                pic_index: 1,
                position:default_position,
                order_id:0,
            };
            var position_options = {};
            options = $($('.position_select')[0]).find('option');
            for (var i = 0; i < options.length; i++) {
                var option = $(options[i]);
                position_options[ option.html().trim() ] = option.attr('value');
            }

            var discount_data = "{{json_encode($data['discount'], JSON_UNESCAPED_UNICODE)}}".replace(/&quot;/g,'"').trim();            discount_data = discount_data;
            discount_data = discount_data ? JSON.parse(discount_data) : [];
            var items_data = "{{$data['productinfo']['items']}}".replace(/&quot;/g,'"').trim();
            items_data = items_data ? JSON.parse(items_data) : [];
            var subProject = {
                ask_price: parseInt("{{$data['productinfo']['ask_price']}}"),
                items: items_data,
                delitems: [],
                has_price: parseInt("{{$data['productinfo']['has_price']}}"),
                discount_value : {},
                discount_name : {},
            }
            for (var i = 0; i < discount_data.length; i++) {
                x = discount_data[i];
                subProject['discount_value'][x['id']] = x['number'];
                subProject['discount_name'][x['id']] = x['name'];
            }
            var subProjectVM = new Vue({
                el: '#subProject',
                data: subProject,
                computed: {
                    is_registrable: function(){
                        return indexADVVM.is_registrable;
                    },
                    subProjectJson: function () {
        				this.items.map(element => {
        					// element.count = Math.ceil(element.price * this.discount_value[element.title]);
        					element.discount_id = element.title;
        					/*
        					if(element.num <= parseInt(element.old_num)){
        						element.num  = element.old_num;
        					}
        					*/

                            if(element.title_name == "" || !element.title_name){
        						element.title_name = this.discount_name[element.title];
                            }

                            if(this.is_registrable!="1"){
                                element.act_time = "";
                                element.act_time_end = "";
                                element.act_remind_time = "";
                            }
        				});
                        return JSON.stringify(this.items);
                    },
                    delProjectJson: function () {
                        return JSON.stringify(this.delitems);
                    },
                    has_price_Num: function () {
                        return Number(this.has_price);
                    }
                },
                methods: {
                    check_zero: function(item){
                        setTimeout(function() {
                            if(item.price<0){
                                Vue.toasted.show("{{Lang::get('請輸入大於零的數')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                                item.price = 0;
                                return;
                            }
                            if(item.count<0){
                                Vue.toasted.show("{{Lang::get('請輸入大於零的數')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                                item.count = 0;
                                return;
                            }
                            if(item.limit_num<0){
                                Vue.toasted.show("{{Lang::get('請輸入大於零的數')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                                item.limit_num = 0;
                                return;
                            }
                            if(item.online_num<0){
                                Vue.toasted.show("{{Lang::get('請輸入大於零的數')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                                item.online_num = 0;
                                return;
                            }
                            if(item.price_cv<0){
                                Vue.toasted.show("{{Lang::get('請輸入大於零的數')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                                item.price_cv = 0;
                                return;
                            }
                            if(item.price_supplier<0){
                                Vue.toasted.show("{{Lang::get('請輸入大於零的數')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                                item.price_supplier = 0;
                                return;
                            }
                        }, 100);
                    },
        			position_check:function(item=null){
        				this.items.map(element => {
        					if(element.num <= parseInt(element.old_num)){
        						element.num  = element.old_num;
        					}
        				});

        				var tmp = $("input[name='r_repeat']:checked").val();

                        var data = item!==null ? JSON.stringify([item]) : JSON.stringify(this.items);
        				if (typeof(tmp) == "undefined"){
                            Vue.toasted.show("{{Lang::get('請選擇位置存放方式')}}",{duration:1500, className: ["toasted-primary", "bg-warning"]});
                            $("input[name='form_error']").val(1);
        				}else{
        					return $.ajax({
        						url: "{{url('Productinfo/position_portion')}}",
        						type: 'POST',
                                headers: {
                                    'X-CSRF-Token': csrf_token
                                },
        						data:{
        							data:data,
        							repeat:tmp,
        						},
        						success: function(response) {
                                    // console.log(response);
        							// console.log(item);
        							if(response['alert'] != 'ok'){
                                        Vue.toasted.show(response['alert'],{duration:1500, className: ["toasted-primary", "bg-danger"]});
        								$("input[name='form_error']").val(response['form_error']);
        							}else{
        								$("input[name='form_error']").val(0);
        							}
        							//console.log(response);
        						},

        					});
        				}
        			},
        			ch_pos: function (index) {
        				var tmp = $("input[name='r_repeat']:checked").val();
                        this.items[index]['count'] = Math.round(this.items[index]['price'] * this.discount_value[this.items[index]['title']]);
                        this.items[index]['title_name'] = this.discount_name[this.items[index]['title']];
                        return JSON.stringify(this.items);
        			},
                    del: function (del_item) {
                        this.items = this.items.filter(function (item) {
                            return item != del_item;
                        });
                        if(del_item.id){
                            this.delitems.push(del_item.id);
                        }
                        //console.log(this.delitems);
                        //console.log(this.delProjectJson);
                    },
                    close_type: function(type_id){
                        if(confirm("{{Lang::get('確定取消嗎')}}")){
                            location.href= "/admin/examination/close_type?type_id=" + type_id;
                        }
                    },
                    add: function () {
                        this.items.push( Object.assign({}, default_price_date) );
                    },

                    paste_data: function($event){ /*excel貼上品項功能*/
                        count = 0;
                        paste_data = [];
                        let paste = (event.clipboardData || window.clipboardData).getData('text');
                        paste = paste.split("\n");
                        // console.log(paste);

                        var price_table_columns_to_vue = {};
                        var price_table_columns_to_vue_keys = [];
                        if(paste.length>1){
                            var price_table_columns = $('#inquiry [type-data]');
                            for (var i = 0; i < price_table_columns.length; i++) {
                                column_target = $(price_table_columns[i]);
                                price_table_columns_to_vue[ column_target.html().trim().split('<')[0] ] = $(column_target).attr('type-data')
                            }
                            price_table_columns_to_vue_keys = Object.keys(price_table_columns_to_vue);
                            // console.log(price_table_columns_to_vue);
                            // console.log(price_table_columns_to_vue_keys);

                            var columns = paste[0].replace('\r', '').split("\t").map(item => { return item.split('(')[0]; });
                            // console.log(columns);
                            for (var i = 1; i < paste.length; i++) {
                                if(!paste[i]) continue;

                                datas = paste[i].replace('\r', '').split("\t");
                                new_price = {};
                                for (var x = 0; x < datas.length; x++) {
                                    new_price[columns[x]] = datas[x];
                                }
                                // console.log(new_price);
                                paste_data.push(new_price);

                                var add_data = Object.assign({}, default_price_date);
                                data_keys = Object.keys(new_price);
                                // console.log(data_keys);

                                var date_pattern = /^\d{4}-\d{2}-\d{2}$/gi;
                                var date_time_pattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/gi;
                                for (var x = 0; x < data_keys.length; x++) {
                                    if(price_table_columns_to_vue_keys.indexOf(data_keys[x])!=-1){ /*貼入的欄位有存在於當前使用的欄位中*/
                                        // console.log(price_table_columns_to_vue[data_keys[x]]);
                                        if(price_table_columns_to_vue[data_keys[x]]=='online_num'){
                                            if("{{ empty(config('control.close_function_current')['存放位置管理']) }}"=="1"){
                                                add_data[ 'num' ] = new_price[data_keys[x]];
                                            }else{
                                                add_data[ price_table_columns_to_vue[data_keys[x]] ] = new_price[data_keys[x]];
                                            }
                                        }
                                        else if(price_table_columns_to_vue[data_keys[x]]=='position'){
                                            if(position_options[ new_price[data_keys[x]] ]){
                                                add_data[ price_table_columns_to_vue[data_keys[x]] ] = position_options[ new_price[data_keys[x]] ];
                                            }
                                        }
                                        else if(price_table_columns_to_vue[data_keys[x]]=='start_time~end_time'){ /*顯示期限*/
                                            var varible_names = price_table_columns_to_vue[data_keys[x]].split('~');
                                            var data_values = new_price[data_keys[x]].split('~');
                                            add_data[varible_names[0]] = data_values[0].match(date_pattern) ? data_values[0] : "";
                                            if(data_values.length>1){
                                                add_data[varible_names[1]] = data_values[1].match(date_pattern) ? data_values[1] : "";
                                            }
                                        }
                                        else if(price_table_columns_to_vue[data_keys[x]]=='act_time~act_time_end'){ /*活動時間*/
                                            var varible_names = price_table_columns_to_vue[data_keys[x]].split('~');
                                            var data_values = new_price[data_keys[x]].split('~');
                                            add_data[varible_names[0]] = data_values[0].match(date_time_pattern) ? data_values[0].replace(' ', 'T') : "";
                                            if(data_values.length>1){
                                                add_data[varible_names[1]] = data_values[1].match(date_time_pattern) ? data_values[1].replace(' ', 'T') : "";
                                            }
                                        }
                                        else if(price_table_columns_to_vue[data_keys[x]]=='act_remind_time'){ /*活動提醒時間*/
                                            add_data[ price_table_columns_to_vue[data_keys[x]] ] = new_price[data_keys[x]].match(date_time_pattern) ? new_price[data_keys[x]].replace(' ', 'T') : "";
                                        }
                                        else{
                                            add_data[ price_table_columns_to_vue[data_keys[x]] ] = new_price[data_keys[x]];
                                        }
                                    }
                                }
                                // console.log(add_data);
                                this.items.push(add_data);
                            }
                        }

                        // dump(paste_data);
                        if(paste_data.length==0){
                            Vue.toasted.show("{{Lang::get('貼入的內容有誤')}}",{duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                        setTimeout(function(){ $('#textarea_price').val(""); }, 50);
                    },
                }
            });
            function repeat_vue(){
                subProjectVM.position_check();
            }

        /* 商品說明頁籤功能 --------------------------------------*/
            var cellCtrlFromDefault = function (textNumber) {
                var Data = { default_type: 'productinfo' };
                editor = eval('editor' + textNumber);
                Data['text' + textNumber] = editor.html();
                $.ajax({
                    url: "{{url('Productinfo/cellCtrlFromDefault')}}",
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    type: 'POST',
                    dataType: 'json',
                    data: Data,
                    success: function(response) {
                        if(response.status){
                            Vue.toasted.show("{{Lang::get('操作成功')}}",{duration:1500, className: ["toasted-primary", "bg-success"]});
                        }else{
                            Vue.toasted.show(response.message,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    },
                    error: function(xhr) {
                        Vue.toasted.show(xhr,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                    }
                });
            };
            var cellGetFromDefault = function (textNumber) {
                $.ajax({
                    url: "{{url('Productinfo/cellGetFromDefault')}}",
                    type: 'POST',
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    dataType: 'json',
                    data: {
                        textNumber: 'text' + textNumber,
                        default_type: 'productinfo',
                    },
                    success: function(response) {
                        if(response.status){
                            editor = eval('editor' + textNumber);
                            editor.html(response.message);
                            editor.sync();
                            Vue.toasted.show("{{Lang::get('操作成功')}}",{duration:1500, className: ["toasted-primary", "bg-success"]});
                        }else{
                            Vue.toasted.show(response.message,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                            console.log();
                        }
                    },
                    error: function(xhr) {
                        Vue.toasted.show(xhr,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                    }
                });
            };
    </script>

    <script src='//ajax.googleapis.com/ajax/libs/jquery/2.0.0/jquery.min.js'></script>
    <script>
        $( document ).ready(function() {
            show_array();

            var prodesc_select = $('select[name="prodesc_select"]');
            prodesc_select.on('change', function(){ $('input[name="prodesc"').val(prodesc_select.val()) });
        });

        function deleteproduct(){
            if(confirm("{{Lang::get('確定刪除嗎')}}")){
                location.href="{{url('productinfo/delete')}}?id={{$data['productinfo']['id']}}";
            }
        }
    </script>

    <script type="text/javascript">
        /* 商品關聯付款方法 */
        var pay_data = {
            pay : "{{json_encode($data['pay_fee'], JSON_UNESCAPED_UNICODE)}}".replace(/&quot;/g,'"').trim(),
            pay_selected : [],
        }
        var payVM = new Vue({
            el: '#pay_div',
            data: pay_data,
            computed: {
                pay_decode: function(){
                    return JSON.parse(this.pay);
                },
                pay_type: function(){
                    var pay_type = [];
                    var pay_selected = this.pay_selected
                    for (var i = 0; i < pay_selected.length; i++) {
                        pay_type.push(pay_selected[i].id);
                    }
                    return pay_type.join(',');
                }
            },
        });
        /* 初始化勾選 */
        var pay_selected = "{{json_encode($data['pay_selected'])}}".replace(/&quot;/g,'"').trim();
        payVM.pay_selected = JSON.parse(pay_selected);
    </script>
    <script type="text/javascript">
        /* 商品關聯運費 */
        var shipping_data = {
            shipping : "{{json_encode($data['shipping_fee'])}}".replace(/&quot;/g,'"').trim(),
            shipping_selected : [],
            shipping_fee_tag: "{{$data['productinfo']['shipping_fee_tag']}}",
        }
        var shippingVM = new Vue({
            el: '#shipping_div',
            data: shipping_data,
            computed: {
                shpping_decode: function(){
                    return JSON.parse(this.shipping);
                },
                shipping_type: function(){
                    var shipping_type = [];
                    var shipping_selected = this.shipping_selected
                    for (var i = 0; i < shipping_selected.length; i++) {
                        shipping_type.push(shipping_selected[i].id);
                    }
                    return shipping_type.join(',');
                }
            },
        });
        /* 初始化勾選 */
        var shipping_selected = "{{json_encode($data['shipping_selected'], JSON_UNESCAPED_UNICODE)}}".replace(/&quot;/g,'"').trim();
        shippingVM.shipping_selected = JSON.parse(shipping_selected);
    </script>
    <script>
        var productinfo_mainVM = new Vue({
            'el': '#productinfo_main',
            data: {
                distributor_id: "{{$data['productinfo']['distributor_id']}}",
                product_cate: "{{$data['productinfo']['product_cate']}}",
                bonus_model_id: "{{$data['productinfo']['bonus_model_id']}}",
                use_ad: "{{$data['productinfo']['use_ad']}}",
                vip_type_reward: "{{$data['productinfo']['vip_type_reward']}}",
                vip_type_require: "{{$data['productinfo']['vip_type_require']}}",
            },
        });
    </script>
@endsection
