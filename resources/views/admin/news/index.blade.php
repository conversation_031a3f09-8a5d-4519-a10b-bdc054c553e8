@extends('admin.Public.aside')
@section('title')E圖文編輯項目 > {{$data['frontend_menu'][$data['table_name']]['name']}}@endsection


@section('content')
    <iframe id="boxFormIframe" name="boxFormIframe" style="display: none;"></iframe>

    <div id="content">
     
        <ul id="title" class="brand-menu">
            <li><a href="###">E圖文編輯項目</a></li>
            <li><a href="###" onclick="javascript:location.href='index'">{{$data['frontend_menu'][$data['table_name']]['name']}}</a></li>
            @if($data['searchKey'] !="")
                <li>搜尋：{{$data['searchKey']}}</li>
            @endif
        </ul> 
        <div class="searchbox">
            <form action="" name="searchForm" method="get" name="searchForm"  class="searchKeyBox">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1" placeholder="搜尋">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div> 

        <!-- 新增修改消息開始 -->
        <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
        <div class="modal fade main-modal" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document" id="Box">
                <div class="modal-content">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <div class="modal-header">
                        <h5 class="modal-title">消息內容</h5>
                        
                    </div>
                    <form name="boxForm" :action="action" method="post" target="boxFormIframe" enctype="multipart/form-data">
                        @csrf
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-4 ">
                                    <div class="img-box">
                                        <span class="bi bi-image"></span>
                                        <input type='file' ref="img" class="upl" name="image" accept="image/*" @change="previewImg">
                                        <img class="preview" :src="src"/>
                                    </div>
                                    <p class="remark">建議大小：386*190</p>
                                </div>
                                <div class="col-8">
                                    <p><span class="remark">排序(越小越前面)：</span>
                                        <input type="number" name="orders" v-model="orders" class="form-control">
                                    </p>
                                </div>
                                <div class="col-12 mt-4">
                                    <span class="remark">標題：</span>
                                    <input type="text" name="title" v-model="title" class="form-control">
                                </div>
                                <div class="col-12">
                                    <span class="remark">小說明：</span>
                                    <input type="text" name="description" v-model="description" class="form-control">
                                </div>
                                <div class="col-12">
                                    <span class="remark">內容：</span>
                                    <input type="hidden" name="content" v-model="content" class="form-control">
                                    <input type="hidden" id="editor" class="form-control">
                                </div>
                            </div>
                            
                        </div>
                        <div class="modal-footer">
                            <input type="hidden" name="id" v-model="id">
                            <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- 新增修改消息結束 -->

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn"  onclick="newBlock();"><i class="bi bi-plus-lg add small"></i>  新增</a>
            <div class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>

                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a onclick="multiOnline();">
                        <p class="mb-0">上架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                    <a onclick="multiOffline();">
                        <p class="mb-0">下架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                    <a onclick="multiDelete();" class="mt-2 border-top">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
                <!-- 編輯結束 -->
            </div>
            
        </div>

        <!--表格 開始-->
        <div class="edit_form">
            <table class="table-rwd table table-striped" style="min-width:1200px;">
                <thead>
                    <tr>
                        <th style="width: 20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=newsCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 80px">上下架</th>
                        <th style="width: 150px">排序(越小越前面)</th>
                        <th style="width: 200px">上架日期</th>
                        <th style="width: 200px">預覽圖片</th>
                        <th>標題</th>
                        <th>小說明</th>
                        <th style="width: 60px">刪除</th>
                    </tr>
                </thead>
                <tbody>
                    @if(empty($data['news'])==true)
                    <tr><td colspan='20'>沒有數據</td></tr>
                    @else
                    @foreach($data['news'] as $vo)
                        <tr id="news_{{$vo->id}}">
                            <td><input type="checkbox" class="newsCheckbox" alt="{{$vo->id}}"></td>
                            <td>
                                <label class="switch" style="display:inline-flex; margin-top: 5px;">
                                    <input type="checkbox" v-model="online">
                                    <span class="slider round"></span>
                                </label>
                            </td>
                            <td><span v-text="orders"></span></td>
                            <td v-text="time"></td>
                            <td>
                                <div class="img-box" @click="openBox">
                                    <p style="position:absolute;">386*190</p>
                                    <img class="preview" :src="src"/>
                                </div>
                            </td>
                            <td><a href="###" @click="openBox" v-text="title"></a></td>
                            <td v-text="description"></td>
                            <td><span class="bi bi-trash" onclick="delete_one('{{$vo->id}}')"></span></td>
                        </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
        </div>
        <!--表格 結束-->

        <div class="text-center">
            {{$data['news']->links('pagination::default')}}
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        KindEditor.ready(function (K) {
            editor = K.create('#editor', {
                afterBlur: function () { 
                    this.sync();
                    BoxVM.content = this.html();
                },
                langType: 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'emoticons', 'image', 'link', 'unlink', '|',
                    'justifyleft', 'justifycenter', 'justifyright'
                ],
                width: '100%',
                height: '300px',
                resizeType: 0
            });
        });
    </script>
    <script>  
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });

        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('News/cellCtrl')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        //alert('留言成功');
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        Vue.prototype.createNews = function (Data) {
            $.ajax({
                url: "{{url('News/doCreate')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                dataType: 'json',
                data: Data,
                success: function(response) {
                    location.reload();
                },
                error: function(xhr) {
                    location.reload();
                }
            });

        };

        Vue.prototype.updateNews = function (Data) {
            $.ajax({
                url: "{{url('News/update')}}",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                type: 'POST',
                data: Data,
                success: function(response) {
                    // console.log(response);

                    var uploadStatus = $(response).text();
                    if(uploadStatus == "上傳成功"){
                        // alert('更改成功');
                        // BoxVM.updateCallerData();
                        location.reload();                       
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var Box = {id:0, content: "", title: "", src: "", description: "", caller: null, orders:0, action: "",}
        var BoxVM = new Vue({
            el: '#Box', 
            data: Box,
            watch: {
                content: function (val) {
                    editor.html(val);
                }
            },
            methods: {
                ajaxSubmit: function () {
                    editor.sync();
                    this.content = editor.html();
                    this.content = this.content.replace(/\n/g, '');
                    $('#block_block').show();
                    setTimeout(function(){
                        document.boxForm.submit();
                    }, 50);
                },
                previewImg: function () {
                    console.log(this.$refs.img.files);
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        Box.src = e.target.result;
                    }
                    reader.readAsDataURL(this.$refs.img.files[0]);
                },
                updateCallerData: function () {
                    this.caller.content = this.content;
                    this.caller.title = this.title;
                    this.caller.orders = this.orders;
                    this.caller.src = this.src;
                    this.caller.description = this.description;
                    $('#functionModal').modal('hide');
                }
            }
        });

        $('#boxFormIframe').load(function () {
            var uploadStatus = $(this).contents().find('h1').text();
            if(uploadStatus == "上傳成功"){
                // alert("上傳成功");
                // if(BoxVM.caller == 'new'){
                //     location.reload();
                // }else{
                //     BoxVM.updateCallerData();
                // }
                location.reload();
            }else{
                alert("上傳失敗");
                console.log($(this).contents().find('body').text());
            }
            $('#block_block').hide();
        });

        ///////andy/////多行文字串//////
        function heredoc(fn) {
            return fn.toString().replace(/[\\]/g,"") + '\n'
        }
        ///////////////////////////////
        @if(empty($data['news'])==false)
        @foreach($data['news'] as $vo)
            ///////andy/////多行文字串////////////////////
            var tmpl = heredoc(function(){
                `{!! str_replace('/','\\/',addslashes($vo->content)) !!}`
            });
            tmpl = tmpl.split('`');
            delete tmpl[0];
            var lastnum = tmpl.length -1;
            delete tmpl[lastnum];
            // console.log(tmpl);
            /////////////////////////////////////////////

            var news_{{$vo->id}} = {
                id: "{{$vo->id}}",
                title: "{{$vo->title}}",
                orders: "{{$vo->orders}}",
                src: "{{__UPLOAD__}}{{$vo->pic}}",
                description: "{{$vo->description}}",
                content: tmpl.join(''),
                time: "{{$vo->time}}",
                online: +"{{$vo->online}}",
                action: "{{url('News/update')}}",
            }
            var news_{{$vo->id}}_VM = new Vue({
                el: '#news_{{$vo->id}}',
                data: news_{{$vo->id}},
                watch: {
                    online: function () {
                        blockData = {
                            id: this.id,
                            online: this.online ? 1 : 0
                        }
                        this.blockCtrl(blockData);
                    }
                },
                methods: {
                    openBox: function () {
                        BoxVM.id = this.id;
                        BoxVM.content = this.content;
                        BoxVM.title = this.title;
                        BoxVM.src = this.src;
                        BoxVM.description = this.description;
                        BoxVM.orders = this.orders;
                        BoxVM.action = this.action;
                        BoxVM.caller = this;
                        $('#functionModal_btn').click();
                    }
                }
            });
        @endforeach
        @endif

        function newBlock(){
            BoxVM.id = 0;
            BoxVM.content = "";
            BoxVM.title = "";
            BoxVM.src = "";
            BoxVM.description = "";
            BoxVM.orders = 0;
            BoxVM.caller = "new";
            BoxVM.action = "{{url('News/doCreate')}}";
            $('#functionModal_btn').click();
        }

        function getMultiId() {
            var multiIdArray = [];
            $('.newsCheckbox').each(function () {
                if($(this).prop("checked")){
                    multiIdArray.push($(this).attr('alt'));
                    $(this).prop("checked", false);
                }
            });
            return multiIdArray;
        }

        function delete_one(id){
            if(confirm("確定刪除?")){
                location.href = "{{url('News/delete')}}?id="+id;
            }
        }

        function multiDelete() {
            if(confirm("確定刪除?")){
                var form = document.createElement("form");
                form.action = "{{url('News/multiDelete')}}";
                form.method = "post";

                csrf = document.createElement("input");
                csrf.type = "hidden";
                csrf.name = "_token";
                csrf.value = csrf_token;
                form.appendChild(csrf);

                multiId = document.createElement("input");
                multiId.value = JSON.stringify(getMultiId());
                multiId.name = "id";
                form.appendChild(multiId);
                document.body.appendChild(form);
                form.submit();

                $('.activityCheckboxAll').each(function () {
                    if($(this).prop("checked")){
                        $(this).prop("checked", false);
                    }
                });
            }
        }

        function multiOnline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('news_' + element + '.online = true;');
            });

            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }

        function multiOffline() {
            var multiIdArray = getMultiId();
            multiIdArray.forEach(function(element) {
                eval('news_' + element + '.online = false;');
            });

            $('.activityCheckboxAll').each(function () {
                if($(this).prop("checked")){
                    $(this).prop("checked", false);
                }
            });
        }
    </script>
@endsection