<!DOCTYPE html>
<html>
    <title>@yield('title')</title>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2, user-scalable=0">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <!-- Font Awesome 5.15.4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
        <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

        <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
        <script src="//stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.js"></script>
        <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js"></script>
        <link rel="stylesheet" href="/public/static/index/css/bootstrap-icons.css">
        <link rel="stylesheet" href="/public/static/index/css/iconstyle.css">

        @yield('cssChange')
        <link rel="stylesheet" href="/public/static/admin/css/style.css?20241120-1">
        <link rel="stylesheet" href="/public/static/admin/css/style2023.css?20241120-1">
        <link rel="stylesheet" href="/public/static/admin/css/order2023.css?20241120-1">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.js"></script>

        <link rel="stylesheet" href="/public/static/index/css/body_block.css?20241120-1">
        @yield('css')

        <style>
            .ke-dialog-default.ke-dialog{
                top: 25% !important;
            }
        </style>
    </head>
    <body>
        <div id="block_block" class="body_block position-fixed w-100 h-100 bg-dark">
            <div class="loader">
                <span></span>
            </div>
        </div>

        <a href="###" class="asideMenu" ><svg width="25" height="25" viewBox="0 0 24 24"><path fill="currentColor" d="M6.4 18L5 16.6L9.575 12L5 7.4L6.4 6l6 6Zm6.6 0l-1.4-1.4l4.575-4.6L11.6 7.4L13 6l6 6Z"/></svg></a>
        <header>
            <a href="https://edm.skychakra.com/admin/login" target="_blank" class="btn"><b>LINE名片活動推廣後台</b></a>
            <div class="head-text">
                <p class="mr-1">歡迎　{{$data[$data['admin_type']]['account'] ?? $data[$data['admin_type']]['name'] ?? ''}}您好</p>
                <a href="{{url('admin/login/logout')}}" >
                    登出　<span class="bi bi-box-arrow-right"></span>
                </a>
            </div>

        </header>
        <div class="container-block">
            <div class="aside">
                <a class="border-top" href="{{url('/')}}" target="_blank">查看前台 ↗</a>
                @if($data['admin_type'] == 'distribution')
                    <a class="border-top" href="{{url('index/Product/distributor')}}?id={{$data[$data['admin_type']]['id']}}" target="_blank">我的賣場 ↗</a>
                @endif
                <ul>
                    @foreach($data['show_list_group'] as $group)
                        <li class="border-0"><h4 class="title">{{$group['title']}}</h4></li>
                        @foreach($group['show_list'] as $vo)
                            <li id="target{{$vo['id']}}">
                                <a class="first_list">
                                    {{$vo['name']}}<span class="bi bi-chevron-right"></span>
                                </a>
                                <ul class="none">
                                    @foreach($vo['sub'] as $sub)
                                        <li class="{{$sub['class']}}">
                                            @if($sub['important']=='1')
                                            <a href="{{$sub['url_show']}}" target="{{$sub['target']}}" style="color:red">{{$sub['show_name']}}
                                                @if(empty($sub['count_id']) == false)<span class="warning" id="{{$sub['count_id']}}"></span>@endif
                                            </a>
                                            @else
                                            <a href="{{$sub['url_show']}}" target="{{$sub['target']}}">{{$sub['show_name']}}
                                                @if(empty($sub['count_id']) == false)<span class="warning" id="{{$sub['count_id']}}"></span>@endif
                                            </a>
                                            @endif
                                        </li>
                                    @endforeach
                                </ul>
                            </li>
                        @endforeach
                    @endforeach
                </ul>
            </div>
            <div id="content_area" class="content_area">
                @yield('content')
            </div>
        </div>

    </body>

    <script src="//cdnjs.cloudflare.com/ajax/libs/Sortable/1.6.0/Sortable.min.js"></script>

    <!-- Vue -->
    <script src="//cdnjs.cloudflare.com/ajax/libs/Vue.Draggable/2.14.1/vuedraggable.min.js"></script>
    <script type="text/javascript" src="/public/static/admin/js/vue.min.js"></script>
    <script src="https://unpkg.com/vue-toasted@1.1.28/dist/vue-toasted.min.js"></script>
    <script>
        Vue.use(Toasted);
        const vt_error_obj = {duration:1500, className: ["toasted-primary", "bg-danger"]};
        const vt_success_obj = {duration:1500, className: ["toasted-primary", "bg-success"]};
        const vt_warning_obj = {duration:1500, className: ["toasted-primary", "bg-warning"]};
    </script>
    <script src="/public/static/admin/js/distributors_area.js?20241120-1"></script> <!-- vue元件(查看不同供應商) -->

    <script>
        var csrf_token = '{{csrf_token()}}';
        $(function(){
            $('.first_list').click(function(){

                var $ul = $(this).next('ul');
                var isHidden = $ul.is(':hidden');
                var isActive = $(this).hasClass('active');


                if($ul.length != 0){
                    if(isHidden==true){
                        $(this).contents('.bi-chevron-right').removeClass('bi-chevron-right').addClass('bi-chevron-down');
                        $ul.slideDown(500);
                    }
                    else{
                        $(this).contents('.bi-chevron-down').removeClass('bi-chevron-down').addClass('bi-chevron-right');
                        $ul.slideUp(500);
                    }
                }
            })

            // 庫存警示數字框變色
            $('#warning').mouseover(function(){
                $('.warning').css('border-color','#fff')
            })
            $('#warning').mouseout(function(){
                $('.warning').css('border-color','#000000')
            })

            // 左側選單
            var content_area  = $('.content_area');
            var aside  = $('.aside');
            $('.asideMenu').click(function(){
                aside.toggleClass('close');

                if(aside.hasClass('close')){
                    content_area.css('flex','1 0 calc(100% - 150px)');
                    $(this).addClass('close');

                }else{
                    content_area.css('flex','0 0 calc(100% - 150px)');
                    $(this).removeClass('close');
                }
            })


        });

        // 點擊+主題館、經銷專增加區塊
        $('.add').click(function(event){
            var add_item  = $(event.currentTarget).prev().children();
            if(add_item.is(':visible')==true){
                add_item.slideUp(500);
                add_item.val('')
            }
            else{
                add_item.slideDown(500);
            }
            return false;
        })

        // 隱藏主題館、經銷專增加區塊
        function ShowList(item){
            var add_item = $(item).parent().parent();
            var input = $(item).parent().prev();
            if(add_item.is(':visible')==true){
                add_item.slideUp(500);
                input.val('')
            }
            else{
                add_item.slideDown(500);
            }
        }

        function limit_getCount(){
            //AJAX get limit productinfo count
            return $.ajax({
                url: "/{{$data['admin_type']}}/limit/get_count",
                type: 'GET',
                datatype: 'json',
                error: function (xhr) {
                    //alert('Ajax request 發生錯誤');
                    console.error(xhr);
                },
                success: function (response) {
                    if (response.status) {
                        $('#limitCount').html(response.message);
                    } else {
                        console.error(response);
                    }
                }
            });
        }
        function contact_getCount(){
            return $.ajax({
                url: "/{{$data['admin_type']}}/contact/get_count",
                type: 'GET',
                datatype: 'json',
                error: function (xhr) {
                    //alert('Ajax request 發生錯誤');
                    console.error(xhr);
                },
                success: function (response) {
                    if (response.status) {
                        $('#ConCount').html(response.message[0]+"/"+response.message[1]);
                    } else {
                        console.error(response);
                    }
                }
            });
        }
        function askprice_getCount(){
            return $.ajax({
                url: "/{{$data['admin_type']}}/askprice/get_count",
                type: 'GET',
                datatype: 'json',
                error: function (xhr) {
                    //alert('Ajax request 發生錯誤');
                    console.error(xhr);
                },
                success: function (response) {
                    if (response.status) {
                        $('#askpriceCount').html(response.message[0]+"/"+response.message[1]);
                    } else {
                        console.error(response);
                    }
                }
            });
        }
        async function auto_open_menu(){
            //預設展開選單
            var top_active = "{{$data['top_active']}}";
            var selector ="#target" +top_active+" .first_list";
            $(selector).click();
            var sec_active = "{{$data['sec_active']}}";
            $('.'+sec_active+' a').addClass('active');

            await limit_getCount();
            await contact_getCount();
            await askprice_getCount();
        }
        $(document).ready(()=>{
            auto_open_menu();
        });
    </script>
    @yield('ownJS')

    <script>
        window.addEventListener('pageshow', (event) => {
            $('#block_block').hide();
        });
    </script>
</html>
