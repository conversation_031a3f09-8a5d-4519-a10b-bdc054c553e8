@extends('admin.Public.aside')
@section('title')
後台
@endsection


@section('content')
    <div id="content">
      
        <ul id="title" class="brand-menu" >
            <li onclick="goback()"><a href="###">C首頁展示</a></li>
            <li><a href="###">首頁管理</a></li>
            <li><a href="###">{{$data['tag'][3]['name']}}</a></li>
        </ul>

        <div class="frame d-flex flex-wrap align-items-start">
            <div class="btn sendbtn mr-2" onclick="save()">儲存設定</div>
            <ul class="text-danger remark">
                <li>1.請先用拖拉方式修改下方商品排序，再點擊「儲存設定」按鈕紀錄設定</li>
                <li>2.取消商品後還需點擊「儲存設定」按鈕才會記錄設定</li>
            </ul>
        </div>
        <div class="admin-content">
            <div class="d-flex flex-wrap">
                    @if(count($data['productInfo'])==0)沒有數據@endif
                    @foreach($data['productInfo'] as $vo)
                    <div class="col-md-2 col-sm-4 col-6 mb-2">
                        <div class="p_item d-flex flex-wrap justify-content-center" id="stronghold_{{$vo['sp_id']}}" sp_id="{{$vo['sp_id']}}" pname="{{$vo['title']}}" del='0'>
                            <div class="img-box">
                                <!-- <p style="position:absolute;">700*475</p> -->
                                <img class="preview" src="{{$vo['pic']}}"/>
                            </div>
                            <div class="text-center mt-1 col-12">{{$vo['title']}}</div>
                            
                            <span class="sendbtn btn  mt-2" onclick="del_spe_product('{{$vo['sp_id']}}')"><i class="bi bi-trash"></i> 刪除</span>
                        </div>
                    </div>
                    @endforeach
            </div>
        </div>
        
    </div>
@endsection
@section('ownJS')
    <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script>
        $(function () {
            $( ".row" ).sortable({
                revert: true
            });
            $( ".row>div" ).draggable({
              connectToSortable: ".row",
              // helper: "clone",
              revert: "invalid"
            });
            $( ".row>div" ).disableSelection();
        });

        function save() {
            items = $('div.p_item');

            var sort_item = [];
            for(var i=0; i<items.length;i++){
                name = $(items[i]).attr('pname');
                sp_id = $(items[i]).attr('sp_id');
                del = $(items[i]).attr('del');
                sort_item.push({'name':name, 'sp_id':sp_id ,'orders':i, 'del':del});
            }
            // console.log(sort_item);

            $.ajax({
                url: "{{url('index/updateSpePrice')}}", //請求的url地址
                dataType: "json", //返回格式為json
                data: {data:sort_item}, //引數值
                type: "POST", //請求方式
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                success: function(req) {
                    alert("儲存成功");
                },
                error: function() {
                    //請求出錯處理
                }
            });
        }

        function del_spe_product(sp_id) {
             if(confirm("確定取消商品？")){
                console.log('#stronghold_'+sp_id);
                $('#stronghold_'+sp_id).attr('del', 1);
                $('#stronghold_'+sp_id).hide();
             }
        }
  </script>
@endsection