@extends('admin.Public.aside')
@section('title')任務牆 - J參數設定@endsection

@section('css')
@endsection

@section('content')
<div id="content">
  <ul id="title" class="brand-menu">
    <li><a href="###">J參數設定</a></li>
    <li><a href="###">任務牆</a></li>
  </ul>
  <div class="frame">
    <div class="d-flex flex-wrap justify-content-between">
      <div>
        <form @submit="search_record($event)">
          <div class="d-inline-block mr-2">
            <span class="name">活動類型：</span>
            <select v-model="searchform.type">
              <option value="">請選擇</option>
              <template v-for="(type_name, type_key) in type_options">
                <option :value="type_key" v-text="type_name">請選擇</option>
              </template>
            </select>
          </div>
          <div class="d-inline-block mr-2">
            <span class="name">名稱：</span>
            <input type="text" v-model="searchform.name" placeholder="">
          </div>
          <div class="d-inline-block mr-2">
            <span class="name">套用狀態：</span>
            <select v-model="searchform.using">
              <option value="">請選擇</option>
              <option value="1">進行中</option>
              <option value="2">尚未開始</option>
              <option value="3">結束</option>
            </select>
          </div>
          <input class="btn sendbtn" type="submit" value="搜尋">
          <input class="btn" type="submit" value="清除搜尋" @click="clear_search($event)">
        </form>
      </div>
    </div>
  </div>
  <div class="frame">
    <a href="###" class="btn clearbtn mr-3" @click="open_add_modal(-1)"><span class="bi bi-plus-lg add" ></span> 新增</a>
    <span class="text-danger">(越早開始排越前面)</span>
  </div>
  <div class="edit_form">
    <table class="table table-rwd" style="min-width:850px;">
      <thead>
        <tr>
          <th style="width: 50px;">
            <!-- <input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=productinfoCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="cursor:pointer;"> -->
            序號
          </th>
          <th style="width:100px;" class="text-left">{{Lang::get('活動類型')}}</th>
          <th style="width:200px;" class="text-left">{{Lang::get('名稱')}}</th>
          <th style="width:125px;" class="text-left">{{Lang::get('開始時間')}}</th>
          <th style="width:125px;" class="text-left">{{Lang::get('結束時間')}}</th>
          <th style="width:300px;" class="text-left">{{Lang::get('贈送記錄文字')}}</th>
          <th style="width:50px;">{{Lang::get('操作')}}</th>
        </tr>
      </thead>
      <tbody>
        <tr></tr>
        <tr v-for="(vo, vo_idx) in items" :id="'items_' +vo.id">
          <td>
            <!-- <input type="checkbox" class="productinfoCheckbox" :alt="vo.id"> -->
            <span v-text="vo_idx+1"></span>
          </td>
          <!-- <td><span v-text="vo.id"></span></td> -->
          <td class="text-left">
            <span v-text="type_options[vo.type]"></span>
          </td>
          <td class="text-left">
            <a href="###" @click="open_add_modal(vo_idx)">
              <span v-text="vo.name"></span>
            </a>
          </td>
          <td class="text-left">
            <span v-text="vo.time_s ? vo.time_s : '無'"></span>
          </td>
          <td class="text-left">
            <span v-text="vo.time_e ? vo.time_e : '無'"></span>
          </td>
          <td class="text-left">
            <span v-text="vo.msg"></span>
          </td>
          <td>
            <button class="btn btn-danger pt-1 pb-1 pl-2 pr-2" @click="del_item(vo_idx)">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="text-center">
    <crm_index_pages 
      :change_page="change_page"
      :current_page="searchform.page" 
      :count_of_items="total_num" 
      :count_of_page="searchform.count_of_items"
    ></crm_index_pages>
  </div>

  <!-- 新增/修改商品分類開始 -->
  <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
  <div class="modal main-modal fade" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document" id="Box">
      <div class="modal-content">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-header">
          <h5 class="modal-title">任務內容</h5>
        </div>
        <form name="boxForm" method="post" target="boxFormIframe" enctype="multipart/form-data">
          @csrf
          <div class="modal-body">
            <div class="row m-0">
              <div class="col-lg-6 col-12 mb-2">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text">活動類型</div>
                  </div>
                  <select class="form-control" v-model="detail.type">
                    <option value="">請選擇</option>
                    <template v-for="(type_name, type_key) in type_options">
                      <option :value="type_key" v-text="type_name">請選擇</option>
                    </template>
                  </select>
                  <!-- <div class="input-group-apend">
                    <div class="input-group-text">%</div>
                  </div> -->
                </div>
              </div>
              <div class="col-lg-6 col-12 mb-2">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text">任務名稱</div>
                  </div>
                  <input type="text" class="form-control" v-model="detail.name">
                  <!-- <div class="input-group-apend">
                    <div class="input-group-text">%</div>
                  </div> -->
                </div>
              </div>
              <div class="col-lg-6 col-12 mb-2">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text">開始時間</div>
                  </div>
                  <input type="date" class="form-control" v-model="detail.time_s">
                  <!-- <div class="input-group-apend">
                    <div class="input-group-text">%</div>
                  </div> -->
                </div>
              </div>
              <div class="col-lg-6 col-12 mb-2">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text">結束時間</div>
                  </div>
                  <input type="date" class="form-control" v-model="detail.time_e">
                  <!-- <div class="input-group-apend">
                    <div class="input-group-text">%</div>
                  </div> -->
                </div>
              </div>
              <div class="col-lg-12 col-12 mb-2">
                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text">贈送記錄文字</div>
                  </div>
                  <input type="text" class="form-control" v-model="detail.msg">
                  <!-- <div class="input-group-apend">
                    <div class="input-group-text">%</div>
                  </div> -->
                </div>
                <span class="text-danger">(消費者查看)</span>
              </div>
            </div>
            <div class="row m-0">
              <div class="col-12 mb-2"><hr></div>
              <div class="col-12 mb-2">
                <b>獎勵設定</b>
                <span class="text-danger">(依據「活動類型」將有不同意義，請留意相關說明)</span>
              </div>
              <template v-if="detail.type=='1'">
                <div class="col-lg-6 col-12 mb-2">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">消費額度</div>
                    </div>
                    <input type="number" class="form-control text-right" v-model="detail.bonus_column1" min="1">
                    <div class="input-group-apend">
                      <div class="input-group-text">點數</div>
                    </div>
                  </div>
                </div>
                <div class="col-12 mb-2">
                  <span class="text-danger">時間區間內註冊，該會員就可獲得所設定量的「消費圓滿點數」</span>
                </div>
              </template>
              <template v-else-if="detail.type=='2'">
                <div class="col-lg-6 col-12 mb-2">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">消費額度</div>
                    </div>
                    <input type="number" class="form-control text-right" v-model="detail.bonus_column1" min="1">
                    <div class="input-group-apend">
                      <div class="input-group-text">點數</div>
                    </div>
                  </div>
                </div>
                <div class="col-12 mb-2">
                  <span class="text-danger">時間區間內註冊，該會員的推薦者就可獲得所設定量的「消費圓滿點數」</span>
                </div>
              </template>
              <template v-else-if="detail.type=='3'">
                <div class="col-lg-6 col-12 mb-2">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">消費額度</div>
                    </div>
                    <input type="number" class="form-control text-right" v-model="detail.bonus_column1" min="1">
                    <div class="input-group-apend">
                      <div class="input-group-text">點數</div>
                    </div>
                  </div>
                </div>
                <div class="col-12 mb-2">
                  <span class="text-danger">時間區間內登入，該會員就可獲得所設定量的「消費圓滿點數」(每日一次)</span>
                </div>
              </template>
              <template v-else-if="detail.type=='4'">
                <div class="col-lg-6 col-12 mb-2">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">基數</div>
                    </div>
                    <input type="number" class="form-control text-right" v-model="detail.bonus_column1" step="1" placeholder="100">
                    <!-- <div class="input-group-apend">
                      <div class="input-group-text">基數</div>
                    </div> -->
                  </div>
                </div>
                <div class="col-12 mb-2">
                  <span class="text-danger">會員可獲得上傳文章在時間區間累積瀏覽次數 / 基數量的「消費圓滿點數」(結束時間後一日自動派發)</span>
                </div>
              </template>
              <template v-else-if="detail.type=='5'">
                <div class="col-lg-6 col-12 mb-2">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">基數</div>
                    </div>
                    <input type="number" class="form-control text-right" v-model="detail.bonus_column1" step="1" placeholder="100">
                    <!-- <div class="input-group-apend">
                      <div class="input-group-text">基數</div>
                    </div> -->
                  </div>
                </div>
                <div class="col-12 mb-2">
                  <span class="text-danger">會員可獲得上傳文章在時間區間累積互動次數 / 基數量的「消費圓滿點數」(結束時間後一日自動派發)</span>
                </div>
              </template>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn sendbtn" @click="form_submit">儲存</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <!-- 新增/修改商品分類結束 -->
</div>
@endsection
@section('ownJS')
  <script>
    Vue.component('crm_index_pages', {
      template:`
        <ul class="pagination">
          <li class="" v-if="current_page-1 > 0">
            <a href="###" @click="trigger_change_page(current_page-1)">«</a>
          </li> 
          <template v-for="page in pages">
            <li :class="[current_page==page ? 'active' : '']" >
              <a v-if="current_page!=page" href="###" v-text="page" @click="change_page(page)"></a>
              <span class="text-dark" v-else v-text="page"></span>
            </li>
          </template>
          <li class="" v-if="current_page+1 <= computed_page_num">
            <a href="###" @click="trigger_change_page(current_page+1)">»</a>
          </li> 
        </ul>
      `,
      data: function() {
        return {
          pages: [1],
        };
      },
      props: {
        change_page: Function,  /*換頁*/
        current_page: Number,   /*當前頁數*/
        
        count_of_items: Number, /*項目總數(計算總頁數用)*/
        count_of_page: Number,  /*一頁數量(計算總頁數用)*/
        
        total_pages: Number,    /*總頁數*/
      },
      computed: {
        computed_page_num: function(){
          page_num = 1;
          if(this.total_pages){ /*有傳入總頁數*/
            page_num = this.total_pages;
          }else if(this.count_of_items && this.count_of_page){ /*有傳入一頁數量&項目總數*/
            page_num = Math.ceil( this.count_of_items / this.count_of_page);
          }
          return page_num;
        },
      },
      watch: {
        current_page: {
          immediate: true, // 立即执行一次监听器
          handler: function() { this.updatePages(); },
        },
        count_of_items: {
          handler: function() { this.updatePages(); },
        },
        count_of_page: {
          handler: function() { this.updatePages(); },
        },
        total_pages: {
          handler: function() { this.updatePages(); },
        },
      },
      methods: {
        updatePages() { /*根據傳入最大頁數生成新的頁數列表*/
          var pages = [];
          for (var i=-5; i<5; i++) {
            if(i+this.current_page > 0 && i+this.current_page <= this.computed_page_num){
              pages.push(i+this.current_page);
            }
          }
          this.pages = pages;
        },
        trigger_change_page(page){
          if (typeof this.change_page === 'function') {
            if(page > 0 && page <= this.computed_page_num){
              this.change_page(page);
            }
          }
        }
      },
    });
  </script>
  <script>
    const empty_searchform = {
      page: 1, /*當前頁數*/
      count_of_items: 20,

      type: '',
      name: '',
      using: '',
    };
    var empty_detail = {
      id: 0,
      name: '',
      type:'',
      time_s:'',
      time_e:'',
      msg:'',
      bonus_column1:'',
    };
    var content_data = {
      searchform: JSON.parse(JSON.stringify(empty_searchform)),
      total_num: 0,
      items:[],
      detail: JSON.parse(JSON.stringify(empty_detail)),

      type_options: [],
    };
    var contentVM = new Vue({
      el:'#content',
      data: content_data,
      async created(){
        $('#block_block').show()
        await this.load_data();
        $('#block_block').hide();
      },
      methods:{
        reset_search: function(){
          this.searchform = JSON.parse(JSON.stringify(empty_searchform))
        },
        clear_search:async function($event){
          $event.preventDefault();
          this.reset_search();
          await this.load_data();
        },
        change_page:async function(p){
          if(this.searchform.page!=p){
            this.searchform.page = p;
            await this.load_data();
          }
        },
        search_record:async function($event){
          $event.preventDefault();
          this.searchform.page = 1;
          await this.load_data();
        },
        async load_data(){
          var resp = await $.ajax({
            type: "GET",
            headers: {
              'X-CSRF-Token': csrf_token 
            },
            dataType: "json",
            data: this.searchform,
            url: "{{url('Task/get_data')}}",
          });
          this.items = resp.db_data;
          this.total_num = resp.total_num;
          this.type_options = resp.type_options;
        },
        open_add_modal(idx=-1){
          if(idx<-1 || idx>=this.items.length){ return; }
          if(idx==-1){ /*新增*/
            this.detail = JSON.parse(JSON.stringify(empty_detail));
          }else{ /*修改*/
            this.detail = JSON.parse(JSON.stringify(this.items[idx]));
          }
          $('#functionModal_btn').click();
        },
        async form_submit(){
          // console.log(this.detail);
          $('#block_block').show()
          try {
            var resp = await $.ajax({
              type: "POST",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              dataType: "json",
              data:{
                detail: JSON.parse(JSON.stringify(this.detail)),
              },
              url: "{{url('Task/save_data')}}",
            });
            if(resp.code==1){
              Vue.toasted.show(resp.msg.msg, vt_success_obj);
              if(this.detail.id==0){/*新增成功*/
                var resp = await this.load_data();
                this.items = resp.db_data;

                $('#functionModal').modal('hide');
                this.detail = JSON.parse(JSON.stringify(empty_detail));
              }else{ /*編輯成功*/
                // for (let idx = 0; idx < this.items.length; idx++) {
                //     const element = this.items[idx];
                //     if(element.id==this.detail.id){
                //         this.items[idx] = JSON.parse(JSON.stringify(this.detail));
                //         break;
                //     }
                // }
                var resp = await this.load_data();
                this.items = resp.db_data;
              }
              this.$forceUpdate();
            }else{
              Vue.toasted.show(resp.msg, vt_error_obj);
            }
          } catch (error) {
            // console.log(error);
            Vue.toasted.show(error.statusText, vt_error_obj);
          }
          $('#block_block').hide();
        },
        async del_item(idx){
          if(idx<0 || idx>=this.items.length){ return; }
          if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }
          var target_id = this.items[idx].id;
          try {
            var resp = await $.ajax({
              type: "POST",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              dataType: "json",
              data:{
              },
              url: "{{url('Task/delete_data')}}",
              data:{
                id: target_id,
              },
            });
            if(resp.code==1){
              this.items.splice(idx, 1);
              Vue.toasted.show(resp.msg, vt_success_obj);
            }else{
              Vue.toasted.show(resp.msg, vt_error_obj);
            }
          } catch (error) {
            // console.log(error);
            Vue.toasted.show(error.statusText, vt_error_obj);
          }
          $('#block_block').hide();
        }
      },
    });
  </script>
@endsection