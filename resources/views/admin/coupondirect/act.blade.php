@extends('admin.Public.aside')
@section('title'){{Lang::get('H行銷項目')}} - {{Lang::get('活動優惠券')}}@endsection
@section('css')
    <link rel="stylesheet" type="text/css" href="{{__PUBLIC__}}/css/daterangepicker.css" />
@endsection
@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li onclick="javascript:location.href='index'" ><a href="###">{{Lang::get('H行銷項目')}}</a></li>
            <li><a href="{{url('Coupondirect/index')}}">{{Lang::get('活動優惠券')}}</a></li>
            <li><a href="###" v-text="model.search"></a></li>
        </ul>
        <div class="searchbox">
            <form action="{{url('Coupondirect/index')}}" name="searchForm" method="get" class="searchKeyBox flex-nowrap">
                @csrf
                <input type="text" class="form-control mr-1 text-center" name="searchKey"  placeholder="{{Lang::get('名稱')}}" >
                <input type="hidden" name="type" class="text-center" value="keyword">
                <a class="btn sendbtn" onclick="searchForm.submit();">{{Lang::get('搜尋')}}</a>
            </form>
            <div class="searchTimeBox flex-nowrap">                
                <input class="date form-control mr-1" type="text" id="searchTimeInput" style="font-size:14px;"/>
                <a class="btn sendbtn" @click="searchTime()">{{Lang::get('搜尋')}}</a>
            </div>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn" data-toggle="modal" data-target="#addModal">
                <i class="bi bi-plus-lg add small"></i>  {{Lang::get('新增')}}
            </a>
            <span class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">
                    {{Lang::get('編輯')}}<span class="bi bi-chevron-down"></span>
                </div>
                <div class="edit-item none">
                    <a @click="multiOnline();">
                        <p class="mb-0">{{Lang::get('上架')}}&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                    <a @click="multiOffline();">
                        <p class="mb-0">{{Lang::get('下架')}}&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                    <a @click="multiDelete();" class="border-top">
                        {{Lang::get('刪除')}} <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
            </span>
        </div>

        <!--表格 開始-->
        <div class="edit_form" >
            <table class="table table-rwd" style="min-width: 1200px;">
                <thead>
                    <tr>
                        <th style="width: 20px"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=actCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="width:100%; cursor:pointer;"></th>
                        <th style="width: 80px">{{Lang::get('上下架')}}</th>
                        <th>{{Lang::get('編號')}}</th>
                        <th>{{Lang::get('名稱')}}</th>
                        <th>{{Lang::get('輸入代碼')}}</th>
                        <th>{{Lang::get('開始日期')}}</th>
                        <th>{{Lang::get('結束日期')}}</th>
                        <th style="width: 60px">{{Lang::get('刪除')}}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item,index) in model.actList" :id="'act_' + item.id">
                        <td><input type="checkbox" class="actCheckbox" :alt="index"></td>
                        <td>
                            <label class="switch">
                                <input v-model="item.online" type="checkbox" @change="onlineChange(item)">
                                <span class="slider round"></span>
                            </label>
                        </td>
                        <td v-text="item.number"></td>
                        <td><a :href="'{{url('Coupondirect/edit')}}?id=' + item.id" v-text="item.name"></a></td>
                        <td v-text="item.user_code"></td>
                        <td v-text="item.start"></td>
                        <td v-text="item.end"></td>                    
                        <td><span class="bi bi-trash" @click="delAct(item)"></span></td>                    
                    </tr>
                </tbody>
            </table>
        </div>
        <!--表格 結束-->
        <div class="text-center">
        </div>
    </div>

    <div class="modal fade in main-modal" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel" style="display: inline-block;">{{Lang::get('新增活動優惠券')}}</h5>
                </div>
                <div class="modal-body row" id="boxModel">
                    <div class="form-group col-sm-12 col-12">
                        <label class="col-form-label">{{Lang::get('名稱')}}</label>
                        <input type="text" class="form-control" v-model="addModal.name">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">{{Lang::get('輸入代碼')}}<span class="text-danger">({{Lang::get('不可重複')}})</span></label>
                        <input type="text" class="form-control" v-model="addModal.user_code">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">{{Lang::get('限用次數')}}<span class="text-danger">({{Lang::get('不輸入則不限制')}})</span></label>
                        <input type="number" min="0" class="form-control" v-model="addModal.limit_num">
                    </div>
                    <div class="form-group col-12">
                        <label class="name">{{Lang::get('折扣方式')}}</label>
                        <div class="d-flex flex-wrap">
                            <span class="mr-4">
                                <input type="radio" id="discount_type1" name="discount_type" value="1"  v-model="addModal.type">
                                <label  for="discount_type1">{{Lang::get('滿幾元，打幾折')}}</label>
                            </span>
                            <span class="mr-4">
                                <input type="radio" id="discount_type2" name="discount_type" value="2"  v-model="addModal.type">
                                <label  for="discount_type2">{{Lang::get('每滿幾元，扣幾元')}}</label>
                            </span>
                        </div>
                        <div class="d-flex flex-wrap">
                            <span class="mr-4">
                                <input type="radio" id="discount_type3" name="discount_type" value="3"  v-model="addModal.type">
                                <label  for="discount_type3">{{Lang::get('滿幾件，打幾折')}}</label>
                            </span>
                            <span class="mr-4">
                                <input type="radio" id="discount_type4" name="discount_type" value="4"  v-model="addModal.type">
                                <label  for="discount_type4">{{Lang::get('每滿幾件，扣幾元')}}</label>
                            </span>
                        </div>
                        <hr class="mt-0 mb-3">
                        <template v-for="num in [1,2,3]">
                            <div class="d-flex align-items-center mb-1">
                                <input type="checkbox"  v-model="addModal['online'+num]">
                                <span v-if="[2,4].indexOf(Number(addModal.type))!=-1">{{Lang::get('每')}}</span>{{Lang::get('滿')}}
                                <span v-if="[1,2].indexOf(Number(addModal.type))!=-1">{{config('extra.shop.dollar_symbol')}}</span>
                                <input type="number" v-model="addModal['condition'+num]" class="text-right form-control ml-1 mr-1" min="0" >
                                <span v-if="[3,4].indexOf(Number(addModal.type))!=-1">{{Lang::get('件')}}</span>，
                                <span v-if="[2,4].indexOf(Number(addModal.type))!=-1">{{Lang::get('扣')}}</span>
                                <span v-if="[2,4].indexOf(Number(addModal.type))!=-1">{{config('extra.shop.dollar_symbol')}}</span>
                                <span v-if="[1,3].indexOf(Number(addModal.type))!=-1">{{Lang::get('打')}}</span>
                                <input type="number" v-model="addModal['discount'+num]" class="text-right form-control ml-1 mr-1" min="0" >
                                <!-- <span v-if="[1,3].indexOf(Number(addModal.type))!=-1">{{Lang::get('折')}}</span> -->
                            </div>
                        </template>
                        <div class="text-danger" v-if="[1,3].indexOf(Number(addModal.type))!=-1">
                            {{Lang::get('如需打85折，請輸入0.85')}}
                        </div>
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">{{Lang::get('開始時間')}}</label>
                        <input type="date" class="form-control start_time" v-model="addModal.start_time">
                    </div>
                    <div class="form-group col-sm-6 col-12">
                        <label class="col-form-label">{{Lang::get('結束時間')}}</label>
                        <input type="checkbox" style="margin-left:20px" id="noEndTime" v-model="addModal.noEndTime">
                        <label for="noEndTime">{{Lang::get('沒有結束日期')}}</label>
                        <input type="date" class="form-control end_time" v-model="addModal.end_time"
                               :style="{'display': addModal.noEndTime ? 'none' : 'inline-block' }">
                    </div>
                    <div class="form-group col-sm-12 col-12">
                        <label class="col-form-label">{{Lang::get('簡介')}}</label>
                        <textarea rows="5" class="form-control" v-model="addModal.content"></textarea>
                    </div>
                </div>
                <div class="modal-footer flex-wrap justify-content-center">
                    <span class="text-danger text-center remark w-100 mb-2">{{Lang::get('請在新增後套用商品')}}</span>
                    <button class="btn sendbtn" @click="do_add()">{{Lang::get('新增')}}</button>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('ownJS')
    <script type="text/javascript" src="{{__PUBLIC__}}/js/moment.min.js"></script>  
    <script type="text/javascript" src="{{__PUBLIC__}}/js/daterangepicker.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    
    <script type="text/javascript">
        // 抓取搜尋關鍵字
        var Request = new Object();  
        searchKey = GetRequest() ? GetRequest().trim() : '';
        searchKey = decodeURIComponent(searchKey).trim();;
        function GetRequest() {      
             var url = location.search.replaceAll('+', ''); 
             var theRequest = new Object();      
             if (url.indexOf("?") != -1) {       
                var str = url.substr(1);         
                strs = str.split("&");       
                for(var i = 0; i < strs.length; i++) {       
                   theRequest[strs[i].split("=")[0]]=decodeURI(strs[i].split("=")[1]);       
                }        
             }       
             return theRequest.searchKey;      
        }

        // 初始化vue
        var content_area_data = {
            timeRange: [],
            model:{
                search: "",
                actList: [],
            },
            addModal:{
                type: 1,
                noEndTime: false,
            },
        };
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            methods: {
                getList: function(searchdata){
                    self = this;
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Coupondirect/getActList')}}",
                        data: searchdata,
                        success: function(resp){
                            // console.log(resp);
                            self.model.search = resp.search;
                            self.model.actList = resp.actList;
                            for(var prop in self.model.actList){
                                if (self.model.actList[prop]['online'] == 1){
                                    self.model.actList[prop]['online'] = true;
                                }else{
                                    self.model.actList[prop]['online'] = false;
                                }
                            }
                        },
                    });
                },                
                searchTime: function () {
                    self = this;
                    self.timeRange = $('#searchTimeInput').val().split(" - ");
                    self.getList({
                        'start': self.timeRange[0], 
                        'end':self.timeRange[1],
                        'type':'date'
                    });
                },
                onlineChange: function(item){
                    return $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Coupondirect/changeOnline')}}",
                        data: item,
                        success: function(resp){
                            if(resp.code==1){
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            if(resp.code==0){ location.reload(); }
                        },
                    });
                },
                multiOnline: async function() {
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        multiId['online'] = true;
                        await self.onlineChange(multiId);
                    }
                },
                multiOffline: async function() {
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        multiId['online'] = false;
                        await self.onlineChange(multiId);
                    }
                },
                delAct: function(item){
                    if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }
                    return $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('Coupondirect/delAct')}}",
                        data: item,
                        success: function(resp){
                            if(resp.code==1){
                                $('#act_'+item["id"]).css('display','none');
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                        },
                    });
                },
                multiDelete: async function() {
                    if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        await self.delAct(multiId);
                    }
                    location.reload();
                },
                getMultiId: function() {
                    self = this;
                    var multiIdArray = [];
                    $('.actCheckbox').each(function () {
                        if($(this).prop("checked")){
                            multiIdArray.push(self.model.actList[Number($(this).attr('alt'))]);
                            $(this).prop("checked", false);
                        }
                    });
                    $('.activityCheckboxAll').prop("checked", false);
                    return multiIdArray;
                },

                do_add: function(){
                    self = this;

                    var postData = Object.assign({}, self.addModal);
                    postData['start_time'] = $('input.start_time').val();
                    postData['end_time'] = $('input.end_time').val();
                    $.ajax({
                        method: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url: "{{url('Coupondirect/doCreate')}}",
                        data: postData,
                    }).success(function(resp){
                        if(resp.code==1){
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-success"]});
                            setTimeout(()=>{ location.href=resp.url; }, 300)
                        }else{
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    }).error(function(){
                    })//error
                }, 
            },
        });
        content_areaVM.getList({ searchKey: searchKey, type:'keyword' })
    </script>

    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });
        $("input.date").daterangepicker({locale: {format: 'YYYY-MM-DD'}});
    </script>
@endsection