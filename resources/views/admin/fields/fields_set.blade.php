@extends('admin.Public.aside')
@section('title')參數設定 > 常用欄位管理@endsection
@section('css')
@endsection

@section('content')
    <!-- 新增/編輯開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal main-modal fade" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">欄位內容</h5>
                </div>
                <div class="modal-body">
                    <p>資料類型：
                        <select v-model="aeModel.type" @change="change_type">
                            <option v-for="key in Object.keys(data_types)" :value="key" v-text="data_types[key]"></option>
                        </select>
                    </p>
                    <div class="mb-3">
                        標題：<input type="text" v-model="aeModel.title"/>
                        &nbsp;&nbsp;
                        必填：<input type="checkbox" v-model="aeModel.required" true-value="1" false-value="0"/>&nbsp;&nbsp;&nbsp;&nbsp;
                        <div class="mt-1">
                            特殊欄位：<input type="checkbox" v-model="aeModel.special" true-value="1" false-value="0"/>
                            <span class="text-danger remark">(勾選後，若使用者有填寫此欄位之內容，將通知管理者)</span>
                        </div>
                    </div>
                    <p>
                        排序：<input type="number" v-model="aeModel.order_id"/>
                        &nbsp;&nbsp;
                        狀態：
                        <select v-model="aeModel.online" :class="[aeModel.online== 0 ? 'color_red' : 'color_green']">
                            <option class="color_green" value="1">啟用</option>
                            <option class="color_red" value="0">停用</option>
                        </select>
                    </p>
                    <div v-if="types_need_option.indexOf(aeModel.type) != -1">
                        <table style="width: 100%">
                            <tr>
                                <td>選項內容</td>
                                <td>操作
                                    <span class="bi bi-plus-lg" @click="add_option()" style="float:right"></span>
                                </td>
                            </tr>
                            <tr v-for="(item, index) in aeModel.options">
                                <td>
                                    <input type="text" v-model="aeModel.options[index]">
                                </td>
                                <td>
                                    <span @click="del_option(index)" class="bi bi-trash"></span>
                                </td>
                            </tr>
                        </table>
                        <br>
                    </div>
                    <div v-if="types_need_limit.indexOf(aeModel.type) != -1" class="format">
                        限定格式：<input type="text" v-model="aeModel.limit" style="width: 100%;"><br>
                        <ul class="mt-1 mb-2 ml-1 mr-1 p-1">
                            <li>文字類型資料請輸入「正規表達式」，可參考<a href="https://ihateregex.io" target="_blank">此網站</a>，用於檢查輸入內容是否符合格式</li>
                            <li>檔案資料則請參考<a href="https://blog.gtwang.org/web-development/html-input-accept-attribute-tutorial/" target="_blank">此網頁的「指定副檔名」格式</a>來輸入，用於設定允許上傳的檔案類型</li>
                        </ul>
                    </div>
                    <p>
                        欄位說明：
                        <select @change="set_field_discription" v-model="select_discription">
                            <option value="-1">請選擇</option>
                            <option v-for="(comment, index) in comments_set" :value="index" v-text="comment.title"></option>
                        </select>
                        <br>
                        <textarea id="editor"></textarea>
                        <input type="hidden" v-model="aeModel.discription"/>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" 
                            v-if="aeModel.id!=1 && aeModel.id!=2"
                            @click="ajaxSubmit"
                    >儲存</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 新增/編輯結束 -->

    <div id="content">
        <ul id="title" class="brand-menu" >
            <li><a href="###">K其它功能</a></li>
            <li><a href="###">常用欄位管理</a></li>
        </ul>

        <div class="searchbox">
            <div class="searchKeyBox">
                <select name="searchOnline" v-model="search.searchOnline" class="form-control mr-1 mb-1">
                    <option value="-1">全部</option>
                    <option value="1">啟用</option>
                    <option value="0">停用</option>
                </select>
                <input class="form-control mr-1 mb-1 text-center" name="searchKey" type="text" v-model="search.searchKey"
                        placeholder="請輸入標題/內容">

                <a class="btn sendbtn mr-1 mb-1" @click="go_search">搜尋</a>
                <a class="btn clearbtn" onclick="location.href='{{url('fields/fields_set')}}'">清除搜尋</a>
            </div>
        </div>

        <!--新增與編輯-->
        <div class="frame d-flex flex-wrap align-items-center justify-content-between">
            <div>
                
                <a href="###" class="btn clearbtn" @click="newBlock"><i class="bi bi-plus-lg add small"></i>  新增</a>
                <span class="d-inline-block position-relative">
                    <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                    <div class="edit-item none">
                        <a @click="multiOnline()">
                            <p class="mb-0">開啟&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled="" checked=""><span class="slider round"></span>
                            </label>
                        </a>
                        <a @click="multiOffline()">
                            <p class="mb-0">關閉&nbsp;</p>
                            <label class="switch" name="0">
                                <input type="checkbox" disabled=""><span class="slider round"></span>
                            </label>
                        </a>
                        
                        <a @click="multiDelete()" class="border-top">
                            刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                        </a>
                    </div>
                </span>



                
            </div>

            <ul class="text-danger remark">
                <li>1.姓名、手機為各項報名通用的欄位，不可刪除，也不可編輯</li>
                <li>2.姓名、手機此二欄位不會在報名欄位設定時的常用欄位選項中顯示，因為會自動套用</li>
                <li>3.常用欄位的修改不會溯及既往，即修改常用欄位不會影響過去已套用此常用欄位的商品</li>
            </ul>
        </div>

        <!--表格 開始-->
        <div class="edit_form" >
            <table class="table table-rwd" style="min-width:1200px;">
                <thead>
                    <tr>
                        <th style="width:80px">
                            <input type="checkbox" style="cursor:pointer;" v-model="select_all" @click="selectAll()">
                            編號
                        </th>
                        <th style="width:60px">狀態</th>
                        <th style="width:120px">標題</th>
                        <th style="width:100px">資料類型</th>
                        <th>說明</th>
                        <th style="width:80px">排序</th>
                        <th style="width:80px">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(vo, index) in datas">
                        <td th-data="編號">
                            <input type="checkbox" v-model="vo.select" v-if="vo.id!=1 && vo.id!=2">
                            <span v-text="index + 1 + num_pre_count"></span>
                        </td>
                        <td th-data="狀態">
                            <select v-if="vo.id!=1 && vo.id!=2"
                                    v-model="vo.online" :class="[vo.online== 0 ? 'color_red' : 'color_green']" @change="save_one(index, false)">
                                <option class="color_green" value="1">啟用</option>
                                <option class="color_red" value="0">停用</option>
                            </select>
        
                            <span v-if="vo.id==1 || vo.id==2" class="color_green">啟用</span>
                        </td>
                        <td th-data="標題">
                            <a href="###" @click="openBox(index)" v-text="vo.title"></a>
                            <span style="color:red;" v-if="vo.required==1">*</span>
                        </td>
                        <td th-data="資料類型" v-html="data_types[vo.type]"></td>
                        <td th-data="說明" v-html="vo.discription"></td>
                        <td th-data="排序"><input value="number" v-model="vo.order_id" @change="save_one(index)"></td>
                        <td th-data="操作">
                            <button v-if="vo.id!=1 && vo.id!=2" type="button" @click="del(vo.id)" 
                                    class="btn sendbtn">
                                刪除
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!--表格 結束-->
        
        
    </div>
    <div v-html="datas_links"></div>
@endsection
@section('ownJS')
    <script src="{{__PUBLIC__}}/js/action.js"></script>

    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="{{__PUBLIC__}}/js/kindeditor/lang/zh_TW.js"></script>
    <script type="text/javascript">
        /*初始化編輯器*/
        var editor = KindEditor.ready(function(K) {
            editor = K.create('#editor', {
                langType : 'zh_TW',
                items:['source', '|', 'hr','|','emoticons','|','forecolor','bold', 'italic', 'underline','link', 'unlink',],
                width:'100%',
                height:'200px',
                resizeType:0
            });
        });
    </script>

    <script>
        // console.log(datas);
        var content_area_data = {
            num_pre_count: 0,
            types_need_option: [],
            types_need_limit: [],

            select_all: false,
            search: {
                searchOnline: {{$_GET['searchOnline']??-1}}, 
                searchKey: "{{$_GET['searchKey'] ?? ''}}",
                page: "{{$_GET['page'] ?? 1}}",
            },
            datas: [],
            datas_links: '',
            aeModel: { 
                id: 0, title: "", type: "text", 
                required: 0, special: 0, limit: "", discription: "", 
                options:[], order_id: 0, online: 1,
            },
            data_types:{
                text:"單行文字",
                textarea: "多行文字",
                radio: "單選題",
                // radio_box: "單選題_開視窗",
                checkbox: "多選題",
                // checkbox_box: "多選題_開視窗",
                select: "下拉選單",
                number: "數字題",
                file: "檔案上傳",
                date: "日期",
            },
            select_discription: -1,
            comments_set: [],
        };
        var content_areaVM = new Vue({
            el: "#content_area",
            data: content_area_data,
            created(){
                this.get_data(true);
            },
            methods: {
                async go_search(){
                    this.search.page = 1;
                    await this.get_data();
                },
                get_data: async function(need_setting_data=0){
                    this.search.need_setting_data = need_setting_data;
                    res = await $.ajax({
                        type:'get',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: this.search,
                        url:"{{url('Fields/get_fields')}}",
                    });
                    for (var i = 0; i < res.datas_items.length; i++) {
                        res.datas_items[i]['discription'] = res.datas_items[i]['discription'].replaceAll('\"', "'");
                        res.datas_items[i]['select'] = false;
                    }
                    this.datas = res.datas_items;
                    res.datas_links = res.datas_links.replaceAll('fields/get_fields', 'fields/fields_set');
                    this.datas_links = res.datas_links;
                    this.num_pre_count = res.num_pre_count;
                    
                    if(this.search.need_setting_data){
                        for (var i = 0; i < res.comments_set.length; i++) {
                            res.comments_set[i]['content'] = res.comments_set[i]['content'].replaceAll('\"', "'");
                        }
                        this.comments_set = res.comments_set ? res.comments_set : [];
    
                        this.types_need_option = res.types_need_option ? res.types_need_option : [];
                        this.types_need_limit = res.types_need_limit ? res.types_need_limit : [];
                    }
                },

                /*切換資料類型*/
                change_type: function(){
                    if(['text', 'textarea', 'number', 'file'].indexOf(this.aeModel.type) == -1){ /*切換至不須限定格式的類型*/
                        this.aeModel.limit = "";
                    }
                },
                /*開啟新增畫面*/
                newBlock: function (){
                    this.aeModel.id = 0;
                    this.aeModel.title = "";
                    this.aeModel.type = "text";
                    this.aeModel.required = 0;
                    this.aeModel.special = 0;
                    this.aeModel.limit = "";
                    this.aeModel.discription = "";
                    this.aeModel.options = [];
                    this.aeModel.order_id = 0;
                    this.aeModel.online = 1;
                    editor.html("");
                    $('#functionModal_btn').click();
                },
                /*開啟編輯畫面*/
                openBox: function(index){
                    self = this;
                    self.aeModel = Object.assign({}, self.datas[index]);
                    console.log(self.aeModel)
                    editor.html(self.aeModel.discription)
                    $('#functionModal_btn').click();
                },
                /*新增、編輯欄位*/
                ajaxSubmit: function(){
                    self = this;
                    self.aeModel.discription = editor.html();
                    self.ajax_save_data(self.aeModel);
                },
                /*儲存單個欄位*/
                save_one: function(index, reload=true){
                    self = this;
                    self.ajax_save_data(self.datas[index], reload);
                },
                /*送出儲存資料請求*/
                ajax_save_data: function(post_data, reload=true){
                    clear_option = [];
                    for (var i = 0; i < post_data.options.length; i++) {
                        if( $.trim(post_data.options[i])!="" ){
                            clear_option.push(post_data.options[i]);
                        }
                    }
                    post_data.options = clear_option;

                    return $.ajax({
                        type:'post',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: post_data,
                        url:"{{url('Fields/fields_set_save')}}",
                        success:function(res){
                            if(res.code=='1'){
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(res.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            if(reload){ setTimeout(()=>{ location.reload(); }, 300); }
                        }
                    });
                },

                /*刪除欄位*/
                del: function(id){
                    $.ajax({
                        type:'post',
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        data: {id: id},
                        url:"{{url('Fields/fields_set_delete')}}",
                        success:function(res){
                            if(res.code=='1'){
                                bg_class = 'bg-success';
                                setTimeout(()=>{ location.reload(); }, 300);
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(res.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                        }
                    });
                },
                
                /*列表操作*/
                    selectAll: function(){
                        self = this;
                        var select_type = self.select_all ? false : true;
                        for (var i = 0; i < self.datas.length; i++) {
                            if( [1,2].indexOf(self.datas[i]['id']) == -1 ){
                                self.datas[i]['select'] = select_type;
                            }
                        }
                    },
                    multiOnline: async function(){
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.datas[i]['online'] = 1;
                                await self.ajax_save_data(self.datas[i], false);
                            }
                        }
                        setTimeout(function(){location.reload();}, 300);
                    },
                    multiOffline: async function(){
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.datas[i]['online'] = 0;
                                await self.ajax_save_data(self.datas[i], false);
                            }
                        }
                        setTimeout(function(){location.reload();}, 300);
                    },
                    multiDelete: function(){
                        if(!confirm('確認批次刪除？')){return}
                        self = this;
                        for (var i = 0; i < self.datas.length; i++) {
                            if(self.datas[i]['select']){
                                self.del(self.datas[i]['id']);
                            }
                        }
                        setTimeout(function(){location.reload();}, 300);
                    },

                /*添加選項*/
                add_option: function(){
                    self = this;
                    self.aeModel.options.push("");
                },
                /*刪除選項*/
                del_option: function(index){
                    self = this;
                    self.aeModel.options.splice(index, 1);
                },
                /*套用常用註記詞*/
                set_field_discription: function(){
                    self = this;
                    if(self.select_discription==-1){ return; }
                    
                    html = self.comments_set[self.select_discription].content;
                    self.aeModel.discription = html;
                    editor.html(html);
                    self.select_discription = -1;
                },
            },
        });
    </script>
@endsection