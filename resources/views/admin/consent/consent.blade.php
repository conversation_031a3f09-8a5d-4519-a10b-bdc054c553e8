@extends('admin.Public.aside')

@section('title')
J參數設定區 > 同意書
@endsection

@section('css')
@endsection

@section('content')
    <!-- 文字編輯器 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal fade main-modal" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="changeTextBox">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" v-text="title + '內容'">內容</h5>
                </div>
                <div class="modal-body">
                    <input type="hidden" v-model="content">
                    <input type="hidden" id="editor">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn sendbtn" @click="ajaxSubmit">儲存</button>
                </div>
            </div>
        </div>
    </div>
    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">J參數設定區</a></li>
            <li><a href="###">同意書</a></li>
        </ul>
        <div id="consentView" class="width-70 admin-content d-flex flex-wrap">
            <a class="consentLink" href="###" @click="openBox(0, '加入會員同意書')">加入會員同意書</a>
            <!--<a class="consentLink">非會員購物同意書</a>-->
            <a class="consentLink" href="###" @click="openBox(1, '點數折扣同意書')">點數折扣同意書</a>
            <a class="consentLink" href="###" @click="openBox(2, '優惠券使用同意書')">優惠券使用同意書</a>
            <a class="consentLink" href="###" @click="openBox(3, '購物條款')">購物條款</a>
            <a class="consentLink" href="###" @click="openBox(4, '匯款資訊')">匯款資訊</a>
            <a class="consentLink" href="###" @click="openBox(5, '隱私政策')">隱私政策</a>
            @if(config('control.control_register')==1)
                <a class="consentLink" href="###" @click="openBox(6, '報名流程說明')">報名流程說明</a>
                <a class="consentLink" href="###" @click="openBox(7, '分數查詢說明')">分數查詢說明</a>
                <a class="consentLink" href="###" @click="openBox(8, '報名注意事項')">報名注意事項</a>
            @endif
        </div>
    </div>
@endsection

@section('ownJS')
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/lang/zh_TW.js"></script>
    <script>
        var editor;
        KindEditor.ready(function(K) {
            editor = K.create('#editor', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items:['source', '|',  'hr','|','emoticons','|','forecolor','bold', 'italic', 'underline','link', 'unlink',],
                width:'100%',
                height:'500px',
                resizeType:0
            });
        });
    </script>

    <script>
        var index_to_db_column = "{{$data['index_to_db_column_json']}}";
        index_to_db_column = index_to_db_column.replace(/&quot;/g,'"');
       
        var index_to_db_column = JSON.parse(index_to_db_column);
        
        Vue.prototype.blockCtrl = function (blockData) {
            $.ajax({
                url: "{{url('Consent/cellCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                    },
                dataType: 'json',
                data: blockData,
                success: function(response) {
                    if(response.status){
                        alert('更改成功');
                        changeTextBoxVM.updateCallerData();
                        $('#functionModal').modal('hide');
                    }else{
                        alert('更改失敗');
                        console.log(response.message);
                    }
                },
                error: function(xhr) {
                    alert('更改失敗');
                    console.log(xhr);
                }
            });
        };

        var changeTextBox = {
            number: 0,
            content: '',
            title: '',
            visibility:　false,
            caller: null
        }
        var changeTextBoxVM = new Vue({
            el: '#changeTextBox', 
            data: changeTextBox,
            watch: {
                content: function (val) {
                    editor.html(val);
                }
            },
            methods: {
                ajaxSubmit: function () {
                    editor.sync();
                    this.content = editor.html();
                    var Data = {id: 1}
                    Data[index_to_db_column[this.number]] = this.content.replace(/\n/g, '');
                    this.blockCtrl(Data);
                },
                updateCallerData: function () {
                    varible_content= 'content'+this.number
                    this['caller'][varible_content] = this.content;
                }
            }
        });

		///////andy/////多行文字串//////
		function heredoc(fn) {
			return fn.toString().replace(/[\\]/g,"") + '\n'
		}

		var tmpl = [];
        @foreach($data['index_to_db_column'] as $key => $column)
            tmpl[{{$key}}] = heredoc(function(){                
                /*{!! $data['consent'][$column] !!}*/
            });
        @endforeach//

        var consentModel = {};
		for(var i=0; i<tmpl.length; i++){
			tmpl[i] = tmpl[i].split('*');
			delete tmpl[i][0];
			var lastnum = tmpl[i].length -1;
			delete tmpl[i][lastnum];

            consentModel['content'+i] = tmpl[i].join('');
		}
        var consentVM = new Vue({
            el: "#consentView",
            data: consentModel,
            methods:{
                openBox: function (number, title) {
                    varible_content= 'content'+number;
                    changeTextBox.content = this[varible_content];
                    changeTextBox.title = title;

                    changeTextBox.number = number;
                    changeTextBox.visibility = true;
                    changeTextBox.caller = this;
                    $('#functionModal_btn').click();
                }
            }
        });
    </script>
@endsection