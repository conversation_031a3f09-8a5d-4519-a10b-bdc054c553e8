<!-- 引入 vue2-datepicker 的 JavaScript 文件 -->
<script src="https://cdn.jsdelivr.net/npm/vue2-datepicker@3.11.1/index.min.js"></script>
<script>
    // 供應商切換模組串接-開始
    function init_after_get_distributors() {}

    function click_distributor_emit(id) {
        content_areaVM.distributors_current = String(id);
        content_areaVM.get_orderforms();
    }
    // 供應商切換模組串接-結束

    const my_distributor_id = '{{$data["my_distributor_id"]}}';
    const clear_search = {
        searchKey: '',
        searchKey2: '',
        searchKey2_exclude: "",
        payment: '',
        receipts_state: '',
        transport: '',
        transport_location_name: '',
        transport_location_phone: '',
        stock_status: '',
        do_award_time: '',
    };
    var content_area_data = {
        /*搜尋相關資料*/
        distributors_current: '{{$data["distributor_id_search"]}}',
        date: "{{request()->get('date')??''}}", /*預設搜尋購買日期區間用*/
        time: null, /* vue date-picker 綁定用 */
        buy_date_st2: '', /*由 time 拆解*/
        buy_date_en2: '', /*由 time 拆解*/
        searchKey2_exclude_list: [],
        currentPage: 1,
        page_count: Number(document.querySelector('select[name="page_count"]').children[0].value),
        lastPage: 1,
        search_data: {
            state: "{{request()->get('state')??'New'}}",
            order_ship_status: "{{request()->get('order_ship_status')??2}}",
            searchKey: "{{request()->get('searchKey')??''}}",
            searchKey2: "{{request()->get('searchKey2')??''}}",
            searchKey2_exclude: "",
            payment: "{{request()->get('payment')??''}}",
            receipts_state: "{{request()->get('receipts_state')??''}}",
            transport: "{{request()->get('transport')??''}}",
            transport_location_name: "{{request()->get('transport_location_name')??''}}",
            transport_location_phone: "{{request()->get('transport_location_phone')??''}}",
            stock_status: "{{request()->get('stock_status')??''}}",
            do_award_time: "{{request()->get('do_award_time')??''}}",
        },

        /*列表顯示相關資料*/
        show_edit: false,
        search_resault_text: '',
        total_orderform_num: 0,
        total_orderform_unpay_num: 0,
        orderforms: [],

        /*跳出視窗-訂單商品詳細內容*/
        modal_data: {
            product: [],
        },
        /*跳出視窗-揀貨列表提示&結果*/
        pick_hint: {
            selected_num: 0,
            success_num: 0,
            out_of_stock: [],
            instance_code: [],
        },

        /*搜尋付款方式的選項*/
        payments: [
            @foreach($data['payments'] as $vo)
                @if($vo['sys_status']==1)
                    {
                        id: "{{$vo['id']}}",
                        name: "{{$vo['name']}}"
                    },
                @endif
            @endforeach
        ],
        receipts_states: [
            {id: "1", name: "已付款"},
            {id: "0", name: "未付款"},
        ],
        transports: [
            @foreach($data['transports'] as $vo)
                "{{$vo->transport}}",
            @endforeach
        ],

        /*批次回饋訂單*/
        do_award_order_id : '',
        batch_do_award_order: [],
        allow_batch_do_award: false,
    }

    /*依據篩選的date還原出綁定時間區間選擇器的time*/
    if (content_area_data.date) {
        date = content_area_data.date
        dates = date.split(' ~ ');
        if (dates.length == 2) {
            var time_0 = new Date(dates[0]);
            var time_1 = new Date(dates[1]);
            if (time_0 != 'Invalid Date' && time_1 != 'Invalid Date') {
                content_area_data.time = [time_0, time_1];
                content_area_data.buy_date_st2 = dates[0];
                content_area_data.buy_date_en2 = dates[1];
            }
        }
    }

    var content_areaVM = new Vue({
        el: '#content_area',
        data: content_area_data,
        computed: {},
        updated() {
            //備註字數限制
            let message = document.querySelectorAll("table .ps");
            message.forEach((title) => {
                let trimmedText = title.innerHTML.trim();
                if (trimmedText.length >= 20) {
                    let slicedText = trimmedText.slice(0, 20);
                    title.innerHTML = slicedText + "...";
                }
            });
        },
        methods: {
            renew_search_resault_text() {
                /*依據篩選的資料組織出search_resault_text*/
                search_resault_text = [];

                date = $('[name="date"]').val();
                date_range = date.split(' ~ ');
                if (date_range.length == 2) {
                    search_resault_text.push(date);
                }

                for (let index = 0; index < Object.keys(this.search_data).length; index++) {
                    var key = Object.keys(this.search_data)[index];
                    var value = this.search_data[key];
                    if (value == clear_search[key]) {
                        continue;
                    }
                    if (['state', 'order_ship_status', 'searchKey2_exclude', 'stock_status', 'do_award_time'].indexOf(key) != -1) {
                        continue;
                    }
                    if (key == 'payment') {
                        target = this.payments.filter((item) => item.id == value);
                        if (target) {
                            search_resault_text.push(target[0].name);
                        }
                    } else if (key == 'receipts_state') {
                        target = this.receipts_states.filter((item) => item.id == value);
                        if (target) {
                            search_resault_text.push(target[0].name);
                        }
                    } else if (key == 'searchKey2') {
                        if (this.searchKey2_exclude_list.indexOf('排除') != -1) {
                            value += "(排除)";
                        }
                        search_resault_text.push(value);
                    } else if (key == 'transport_location_name') {
                        search_resault_text.push('收件人姓名：' + value);
                    } else if (key == 'transport_location_phone') {
                        search_resault_text.push('收件人電話：' + value);
                    } else {
                        search_resault_text.push(value);
                    }
                }

                search_resault_text = search_resault_text.join('、');

                if (search_resault_text) {
                    search_resault_text = '搜尋項目 | ' + search_resault_text;
                }

                this.search_resault_text = search_resault_text;
            },
            clear_date() {
                this.time = null;
                $('[name="date"]').val('');
            },
            clear_page_count() {
                this.page_count = document.querySelector('select[name="page_count"]').children[0].value;
            },
            change_order_ship_status(order_ship_status) {
                this.search_data.order_ship_status = order_ship_status;
                if (order_ship_status == 5) {
                    this.search_data.state = 'Complete';
                } else {
                    this.search_data.state = 'New';
                }
                this.currentPage = 1;
                this.get_orderforms();
            },
            cancel_search($event) {
                /*清除搜尋*/
                if ($event) {
                    $event.preventDefault()
                }
                this.search_data = {
                    ...this.search_data,
                    ...clear_search
                };
                this.clear_date();
                this.clear_page_count();
                this.get_orderforms();
                this.renew_search_resault_text();
            },
            go_search($event) {
                /*搜尋*/
                if ($event) {
                    $event.preventDefault()
                }
                this.currentPage = 1;
                this.get_orderforms();
                this.renew_search_resault_text();
                $('#select').modal('hide');
            },
            change_page(page) {
                if (page > 0 && page <= this.lastPage) {
                    this.currentPage = page;
                    this.get_orderforms();
                }
            },
            async get_orderforms() {
                $('.loading').parent().show();
                this.reset_select_all();
                this.show_edit = false;
                this.orderforms = [];
                post_data = this.get_post_data(true);
                data = await this.ajax_get_orderforms(post_data);
                content_areaVM.total_orderform_num = data.total_orderform.length;
                total_orderform_unpay_num = 0;
                data.total_orderform.forEach(o => {
                    if (o.receipts_state == 0) {
                        total_orderform_unpay_num += 1;
                    }
                });
                content_areaVM.total_orderform_unpay_num = total_orderform_unpay_num;

                content_areaVM.orderforms = data.rowDataItem;
                content_areaVM.currentPage = data.CurrentPage;
                content_areaVM.page_count = data.listRows;
                content_areaVM.lastPage = data.lastPage;

                // content_areaVM.search_data = clear_search;

                $('.loading').parent().hide();
            },
            async ajax_get_orderforms(post_data){
                console.log(post_data);
                data = await $.ajax({
                    type: "GET",
                    dataType: "json",
                    data: post_data,
                    url: "{{url('order/OrderCtrl/get_orderforms')}}",
                });
                return data;
            },
            get_post_data(need_page = false) {
                var post_data = JSON.parse(JSON.stringify(this.search_data));
                post_data.distributor_id = this.distributors_current;
                date_range = $('[name="date"]').val();
                date_range = date_range.split(' ~ ');
                if (date_range.length == 2) {
                    post_data.buy_date_st2 = date_range[0];
                    post_data.buy_date_en2 = date_range[1];
                }
                post_data.searchKey2_exclude = this.searchKey2_exclude_list.length > 0 ? content_areaVM.searchKey2_exclude_list[0] : '';
                if (need_page) {
                    post_data.page = this.currentPage;
                    post_data.page_count = this.page_count;
                }
                if(post_data.order_ship_status!=2){ /*不是「可揀貨」頁籤*/
                    post_data.stock_status = ''; /*略過庫存狀態篩選*/
                }
                if(post_data.order_ship_status!=5){ /*不是「已出貨」頁籤*/
                    post_data.do_award_time = ''; /*略過庫存狀態篩選*/
                }
                return post_data;
            },
            async check_stock(){
                await $.ajax({
                    type: "GET",
                    dataType: "json",
                    data: post_data,
                    url: "{{url('order/OrderCtrl/check_stock')}}",
                });
                this.get_orderforms();
            },
            excel() {
                get_data = this.get_post_data();
                params = new URLSearchParams(get_data);
                location.href = "{{url('order/OrderCtrl/excel')}}?" + params;
            },
            group_excel() {
                get_data = this.get_post_data();
                params = new URLSearchParams(get_data);
                location.href = "{{url('order/OrderCtrl/group_excel')}}?" + params;
            },

            reset_select_all() {
                $('.activityCheckboxAll').each(function() {
                    if ($(this).prop("checked")) {
                        $(this).prop("checked", false);
                    }
                });
            },
            toggle_edit() {
                this.show_edit = this.show_edit ? false : true;
            },
            printOrders() {
                /*TODO*/
                /*列印訂單*/
                alert('列印訂單們');
            },
            operateCancel(orderform_id) {
                /*單一取消訂單(利用批次處理)*/
                if (confirm('確定取消訂單嗎？')) {
                    $('.orderCheckbox[alt="' + orderform_id + '"]').prop('checked', true);
                    multiOperate('Cancel');
                }
            },
            operateCompleted(orderform_id) {
                /*單一完成訂單(利用批次處理)*/
                if (confirm('確定拋轉到已出貨嗎？')) {
                    $('.orderCheckbox[alt="' + orderform_id + '"]').prop('checked', true);
                    multiOperate('Next');
                }
            },
            async operateRestore(orderform_id) {
                /*單一恢復訂單*/
                if (confirm('確定恢復訂單嗎？')) {
                    $('#block_block').show();
                    try {
                        data = await $.ajax({
                            type: "POST",
                            dataType: "json",
                            headers: {
                                'X-CSRF-TOKEN': csrf_token
                            },
                            url: "{{url('order/OrderCtrl/changeStatus2Restore')}}",
                            data: {
                                id: orderform_id,
                            },
                        });
                        if (data.code) {
                            await this.get_orderforms();
                        } else {
                            alert(data.msg);
                        }
                        $('#block_block').hide();
                    } catch (error) {
                        $('#block_block').hide();
                    }
                }
            },
            operateDelete(orderform_id) {
                /*單一刪除訂單(利用批次處理)*/
                if (confirm('確定刪除訂單嗎？')) {
                    $('.orderCheckbox[alt="' + orderform_id + '"]').prop('checked', true);
                    multiOperate('Delete');
                }
            },

            move_to_pick_hint() {
                ids = getMultiId(false);
                if (ids.length) {
                    this.pick_hint.selected_num = ids.length;
                    $('#hint1').modal('show');
                } else {
                    alert('請選擇訂單');
                }
            },
            async move_to_pick() {
                $('#hint1').modal('hide');
                $('#block_block').show();
                this.show_edit = false;
                try {
                    res = await $.ajax({
                        // type: "POST",
                        type: "GET",
                        dataType: "json",
                        data: {
                            idList: JSON.stringify(getMultiId())
                        },
                        url: "{{url('order/OrderCtrl/move_to_pick')}}", //拋轉到揀貨列表
                    });
                    this.pick_hint.success_num = res.success.length;
                    this.pick_hint.out_of_stock = res.out_of_stock;
                    this.pick_hint.instance_code = res.instance_code;
                    $('#hint2').modal('show');
                    if (this.pick_hint.success_num > 0) {
                        await this.get_orderforms();
                    }
                    $('#block_block').hide();
                } catch (error) {
                    $('#block_block').hide();
                }
            },
            move_to_shipped() {
                if (confirm('確定拋轉到已出貨嗎？')) {
                    multiOperate('Next');
                }
            },
            cnacel_orderforms() {
                if (confirm('確定取消訂單嗎？')) {
                    multiOperate('Cancel');
                }
            },
            delete_orderforms() {
                if (confirm('確定刪除訂單嗎？')) {
                    multiOperate('Delete');
                }
            },

            open_product_modal(index) {
                orderform = this.orderforms[index];
                this.modal_data = JSON.parse(JSON.stringify(orderform));
                $('#productDetail_btn').click();
            },
            show_real_products(products) {
                return products.filter((one) => typeof(one.type_id)!='undefined');
            },
            count_product_num(products) {
                products = this.show_real_products(products);
                var num = 0;
                products.forEach(product => {
                    num += Number(product.num);
                });
                return num;
            },

            /*天脈*/
            async batch_do_award(){
                if(!confirm('確定進行批次回饋?')){ return; }
                $('#block_block').show();
                post_data = this.get_post_data(true);
                post_data.do_award_time = "0"; /*尚未回饋的訂單*/
                data = await this.ajax_get_orderforms(post_data);
                data.total_orderform.reverse();
                this.batch_do_award_order = data.total_orderform;
                if(this.batch_do_award_order.length){
                    $('#modal_reward_btn').click();
                    $('#block_block').hide();
                    this.allow_batch_do_award = true;
                    for (let idx = 0; idx < this.batch_do_award_order.length; idx++) {
                        if(!this.allow_batch_do_award){ 
                            this.do_award_order_id = '';
                            this.allow_batch_do_award = false;
                            return; 
                        }
                        const order = this.batch_do_award_order[idx];
                        this.do_award_order_id = order.id;

                        let result= null;
                        try {
                            result = await $.ajax({
                                type: "GET",
                                dataType: "json",
                                url: "{{url('order/OrderCtrl/do_pointback')}}?id=" + this.do_award_order_id,
                            });
                        } catch (error) {
                            result = { code: 0, msg: error.message, };
                        }
                        this.batch_do_award_order[idx].do_result = {
                            code: result.code,
                            msg: result.msg,
                        };
                        if(result.code==0){
                            alert('批次處理發生錯誤，已中斷執行。\n請按說明修改資料後，關閉此跳出視窗，重新觸發批次處理回饋。');
                            break;
                        }
                    }
                    this.do_award_order_id = '';
                    this.allow_batch_do_award = false;
                }
                alert('已完成批次處理回饋');
                $('#block_block').hide();
            },
            show_format_time(linux_timestamp){
                let d_boj = new Date(linux_timestamp*1000);
                let format_time = '';
                format_time += d_boj.getFullYear() + '-';
                format_time += (d_boj.getMonth()+1).toString().padStart(2, '0') + '-';
                format_time += (d_boj.getDate()).toString().padStart(2, '0');
                return format_time;
            },
        },
        components: {
            DatePicker
        },
    });
    content_areaVM.get_orderforms();
    content_areaVM.renew_search_resault_text();


    $("#modal_reward").on('hide.bs.modal', function (e) {
        if(content_areaVM.allow_batch_do_award){
            if(!confirm("確定批次處理回饋視窗？\n(剩餘尚未處理訂單將略過執行)")){
                e.preventDefault();
            }else{
                content_areaVM.allow_batch_do_award = false;
            }
        }
    });
</script>
<script>
    async function multiOperate(Operate) { /*批次拋轉完成or取消or刪除訂單*/
        if(Operate=='Next'){ /*完成*/
            var action = "{{url('OrderCtrl/multiNext')}}";
        }else if(Operate=='Cancel'){ /*取消*/
            var action = "{{url('OrderCtrl/multiCancel')}}";
        }else if(Operate=='Delete'){ /*刪除*/
            var action = "{{url('OrderCtrl/multiDelete')}}";
        }else{
            alert('無此操作方式');
            return;
        }
        content_areaVM.reset_select_all()
        $('#block_block').show();
        try {
            data = await $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                dataType: "json",
                url: action,
                data: {
                    idList: JSON.stringify(getMultiId()),
                },
            });
            if(data.code){
                await content_areaVM.get_orderforms();
            }else{
                alert(data.msg);
            }
            $('#block_block').hide();
        } catch (error) {
            $('#block_block').hide();
        }
    }

    function getMultiId(renew=true) {
        var multiIdArray = [];
        $('.orderCheckbox').each(function () {
            if($(this).prop("checked")){
                multiIdArray.push($(this).attr('alt'));
                if(renew){
                    $(this).prop("checked", false);
                }
            }
        });
        if(renew){
            $('.activityCheckboxAll').prop('checked', false);
        }
        return multiIdArray;
    }
</script>