@extends('admin.Public.aside')

@section('title')
    訂單管理 - 垃圾桶訂單
@endsection

@section('css')
@endsection

@section('content')
    <div class="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">I訂單管理</a></li>
            <li><a href="###" onclick="javascript:location.href='{{url('OrderCtrl/index')}}">訂單管理</a></li>
            <li><a href="###">垃圾桶訂單</a></li>
        </ul>

        @include('order.order_ctrl.order_search')

        <div class="frame d-flex flex-wrap justify-content-between align-items-center"> 
            <!-- 多項編輯開始 -->
            <div class="edit">
                <a onclick="multiOperate('Delete')">
                    刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                </a>
            </div>

            <div class="d-flex align-items-center tool_item_right">
                <!-- <button class="btn print-btn font-weight-bold mr-2" @click="printOrders()"><i class="bi bi-printer d-inline-block mr-2"></i>列印此頁訂單</button> -->
                <span class="d-inline-block mr-2" style="color:#757575">共 <span v-text="total_orderform_num"></span> 項</span>
                <span class="d-inline-block mr-2" style="color:#757575">(共 <span v-text="lastPage"></span> 頁)</span>
                <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)-1)"><i class="bi bi-chevron-left"></i></button>
                <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)+1)"><i class="bi bi-chevron-right"></i></button>
            </div>
        </div>
        <div class="edit_form" style="min-width: 1200px;">
            <table class="table table-rwd">
                <thead>
                    <tr>
                        <th style="width:5%">
                            <input type="checkbox" class="activityCheckboxAll" onclick="$('input[class=orderCheckbox]').prop('checked', ($(this).is(':checked')?true:false))" style="vertical-align:middle"><span>次項</span>
                        </th>
                        <th style="width:10%">訂單日期/編號<a href="###"><i class="bi bi-caret-down-fill d-inline-block ml-2"></i></a></th>
                        <th style="width:10%">
                            付款方式/狀態
                            <template v-if="total_orderform_unpay_num">
                                <br><span class="not-paid-num" v-text="'未付款：'+total_orderform_unpay_num"></span>
                            </template>
                        </th>
                        <th style="width:10%">運送方式</th>
                        <th style="width:15%">發票</th>
                        <th style="width:10%">姓名</th>
                        <th style="width:15%">備註</th>
                        <th style="width:10%">商品</th>     
                        <th style="width:10%">購買數量/總金額</th> 
                        <th style="width:5%">操作</th>                 
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="loading"><div class="spin"><i class="bi bi-arrow-clockwise" style="font-size: 25px;"></i></div></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr v-for="(vo, vo_idx) in orderforms">
                        <td><input type="checkbox" class="orderCheckbox" :alt="vo.id">
                            <span v-text="((currentPage-1)*page_count) + vo_idx+1"></span>
                        </td>
                        <td>
                            <div v-text="vo.create_date"></div>
                            <a :href="'{{url('order/OrderCtrl/show')}}?id='+vo.id" v-text="vo.order_number"></a>
                        </td>
                        <td>
                            <div v-text="vo.payment_name"></div>
                            <div>
                                <template v-if="vo.receipts_state==1"><span style="color: #757575;font-size:14px;">已付款</span></template>
                                <template v-else><span style="color: #D05050;font-size:14px;">未付款</span></template>
                            </div>
                        </td>
                        <td>
                            <div v-text="vo.transport"></div>
                            <!-- 這部分要等報價確定才做
                                <div>
                                <input type="button" value="產生託運單" class="transport-btn">
                                <input type="button" value="已印製" class="transport-btn already-print">
                            </div> -->
                        </td>
                        <td>
                            <!-- 資料庫可能要新增？
                                發票形式有五種：公司、電子發票、載具、紙本、捐贈
                                發票狀態有兩種：已開立(灰)、未開立(紅)  (請參考付款方式樣式)-->

                            <div v-text="vo.InvoiceStyleText"></div>
                            <div>
                                <span v-if="vo.InvoiceNo" style="color: #757575;font-size:14px;">已開立</span>
                                <span v-else style="color: #D05050;font-size:14px;">未開立</span>
                            </div>
                        </td>
                        <td>
                            <!-- <span v-if="vo.number" v-text="vo.number"></span>
                            <span v-else>-</span>
                            <br> -->
                            <span v-if="vo.name" v-text="vo.name"></span>
                            <span v-else v-text="vo.transport_location_name"></span>
                        </td>
                        <td class="text-left">
                            <a :href="'{{url('order/OrderCtrl/show')}}?id='+vo.id" 
                                class="text-left d-inline-block ps" 
                                v-text="vo.ps">
                            </a>
                        </td>
                        <td>
                            <!-- 只顯示第一的商品名，若有一個商品以上，則後面加上 more -->
                            <template v-if="show_real_products(vo.product).length>0">
                                <a href="###" class="product" @click="open_product_modal(vo_idx)" 
                                   v-text="vo.product[0].name"></a>
                                <a href="###" class="more-btn" @click="open_product_modal(vo_idx)"
                                   v-if="show_real_products(vo.product).length>1">more</a>
                            </template>
                        </td>
                        <td>
                            <div style="color:#757575;" v-text="count_product_num(vo.product)"></div>
                            {{config('extra.shop.dollar_symbol')}}<span v-text="vo.total"></span>
                        </td>
                        <td>
                            <a href="###" class="btn" @click="operateRestore(vo.id)">恢復訂單</a>
                            <button class="btn btn-danger" @click="operateDelete(vo.id)">完全刪除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
@endsection
    
@section('ownJS')
    @include('order.order_ctrl.order_search_js')
@endsection
