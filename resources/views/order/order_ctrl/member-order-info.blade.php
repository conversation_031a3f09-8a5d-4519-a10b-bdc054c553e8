@extends('admin.Public.aside')
@section("title"){{$data['singleData']['order_number']}} - {{$data['singleData']['statusName']}} - 訂單管理@endsection

@section("content")
    <a id="positionModal_btn" data-toggle="modal" data-target="#positionModal"></a>
    <div class="modal large_modal main-modal fade in" id="positionModal" tabindex="-1" role="dialog" aria-labelledby="positionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="positionModalLabel" style="display: inline-block;">選擇品項</h5>
                </div>
                <div class="modal-body row" id="positionVM">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-md-6 col-12">
                                <h4>請選擇項目:</h4>
                                <table class="table">
                                    <tr>
                                        <th>庫存編碼</th>
                                        <th>數量</th>
                                        <th>選擇</th>
                                    </tr>
                                    <tr v-for="item in positions">
                                        <td v-text="item.p_code"></td>
                                        <td v-text="item.num"></td>
                                        <td><a href="###" @click="change_item(item.pp_id,item.p_code, item.num,'plus')">選擇</a></td>
                                    </tr>
                                </table>
                            </div>
                            <hr>
                            <div class="col-md-6 col-12">
                                <h4>已選擇項目:</h4>
                                <table class="table">
                                    <tr>
                                        <th>庫存編碼</th>
                                        <th>數量</th>
                                        <th>刪除</th>
                                    </tr>
                                    <tr v-for="item in positions_selected">
                                        <td v-text="item.p_code"></td>
                                        <td v-text="item.num"></td>
                                        <td><a href="###" @click="change_item(item.pp_id,item.p_code, item.num,'minus')">刪除</a></td>
                                    </tr>
                                </table>
                                <a class="w-100 btn btn-primary btn-sm text-white" style="float:right;" @click="check_num()">確認撿貨</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">I訂單管理</a></li>
            <li><a href="###">{{$data['singleData']['statusName']}}</a></li>
            <li><a href="###">{{$data['singleData']['order_number']}}</a></li>
        </ul>
        <a class="btn sendbtn" href="
            @if($data['singleData']['status'] == 'Return') OR ($data['singleData']['status'] == 'Cancel'))
                {{url('order/order_ctrl/trash')}}
            @else
                {{url('order/order_ctrl/index?state=').$data['singleData']['status']}}

            @endif
            ">
            <span class="bi bi-arrow-left"></span>
        </a>
        <table class="table table-order-ctrl mb-5">
            <tbody>
                <tr>
                    <td data-th="訂單編號">{{$data['singleData']['order_number']}}</td>
                    <td data-th="下單日期">{{date('Y-m-d H:i', $data['singleData']['create_time'])}}</td>
                    <td data-th="付款方式">
                        @if(isset($data['pay_fee_dict']['k_'.$data['singleData']['payment']]))
                            {{$data['pay_fee_dict']['k_'.$data['singleData']['payment']]['name']}}
                        @else
                            {{$data['singleData']['payment']}}
                        @endif
                    </td>
                </tr>
                <tr>
                    <td data-th="運送方式">
                        <div class="d-inline-flex flex-wrap">
                            <span class="mr-3">{{$data['singleData']['transport']}}</span>
                            <span id="transportPaperBtn_area">
                                {!! $data['transportPaperBtn'] !!}
                            </span>
                        </div>
                    </td>
                    <td data-th="購買金額">{{$data['singleData']['total']}}</td>
                    <td data-th="匯款回報">
                        {{$data['singleData']['report'] ?? "尚未回報"}}
                        @if(($data['singleData']['status'] != 'Return') && ($data['singleData']['status'] != 'Cancel'))
                            @if($data['singleData']['report_check_time'] == "")
                                @if($data['singleData']['report'] != "")
                                    <button class="NoPrint" id="checkReportButton" onclick="checkReport();">確認</button>
                                @endif
                            @endif
                        @endif
                    </td>
                </tr>
                <tr>
                    <td data-th="匯款確認">
                        @if($data['singleData']['report_check_time'] != "")
                            {{$data['singleData']['report_check_time']}}
                        @endif
                    </td>
                    <td data-th="收款狀況" id="receiptsState">{{$data['singleData']['receipts_state']}}
                        @if(($data['singleData']['status'] != 'Return') && ($data['singleData']['status'] != 'Cancel'))
                            @if($data['singleData']['receipts_state'] == "未收款")
                                <button onclick="setReceiptsState();" class="NoPrint btn clearbtn btn-sm">收款</button>
                            @endif
                        @endif
                    </td>
                    <td data-th="出貨狀況" id="transportState">{{$data['singleData']['transport_state']}}
                        @if(($data['singleData']['status'] != 'Return') && ($data['singleData']['status'] != 'Cancel') )
                            @if($data['singleData']['transport_state'] == "未出貨")
                                <button onclick="setTransportState();" class="NoPrint btn clearbtn btn-sm">出貨</button>
                            @endif
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>

        <h3 class="main-title">回饋對象設定</h3>
        <table class="table table-order-ctrl mb-0 pb-0">
            <tbody>
                <tr>
                    <td data-th="處理回饋時間" colspan="3">
                        @if($data['singleData']['do_award_time'])
                        {{ date('Y-m-d H:i', $data['singleData']['do_award_time']) }}
                        <!-- 喵啊 這裡用get真的對嗎?? 算了..無所謂了..一起get把我沒時間重構了..-->
                            <a class="btn btn-danger btn-sm"
                               onclick="$('#block_block').show();"
                               href="{{url('order/OrderCtrl/rollback_pointback')}}?id={{$data['singleData']['id']}}">
                                取消回饋
                            </a>
                        @else
                            <a class="NoPrint btn clearbtn btn-sm"
                               onclick="$('#block_block').show();"
                               href="{{url('order/OrderCtrl/do_pointback')}}?id={{$data['singleData']['id']}}">
                                處理回饋
                            </a>
                            <span class="text-danger">訂單需先拋轉到完成才可「處理回饋」</span>
                        @endif
                    </td>
                </tr>
            </tobdy>
        <table>
        <form action="{{url('order/OrderCtrl/update')}}" method="post" enctype="multipart/form-data" class="d-inline">
            @csrf
            <input type="hidden" name="id" value="{{$data['singleData']['id']}}">
            <table class="table table-order-ctrl mb-5 mt-0">
                <tbody>
                    <tr>
                        <td data-th="營運者會員ID">
                            @if($data['singleData']['do_award_time'])
                                {{$data['singleData']['user_id_operation']}}
                            @else
                                <input type="text" class="p-1 mr-1" name="user_id_operation" value="{{$data['singleData']['user_id_operation']}}">
                            @endif
                        </td>
                        <td data-th="講師會員ID">
                            @if($data['singleData']['do_award_time'])
                                {{$data['singleData']['user_id_lecturer']}}
                            @else
                                <input type="text" class="p-1 mr-1" name="user_id_lecturer" value="{{$data['singleData']['user_id_lecturer']}}">
                            @endif
                        </td>
                        <td data-th="中心會員ID">
                            @if($data['singleData']['do_award_time'])
                                {{$data['singleData']['user_id_center']}}
                            @else
                                <input type="text" class="p-1 mr-1" name="user_id_center" value="{{$data['singleData']['user_id_center']}}">
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" class="text-center p-2">
                            <button class="btn sendbtn btn-sm mr-4">修改</button><br>
                            <span class="text-danger">若回饋積分不想贈與會員，可指定回饋會員ID為「{{config('extra.skychakra.member_system')}}」(系統會員)</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        <h3 class="main-title position-relative">商品細項
            @if($data['singleData']['AllPayLogisticsID'] != '')
                <a style="position: absolute; right: 0px; top:0px" data-toggle="modal" data-target="#logisticModel"><span>查看物流狀態</span></a>
            @endif
        </h3>
        <table class="table table-mobile table-rwd mb-5">
            <thead>
                <tr>
                    <th class="theadGray">品項</th>
                    <th class="theadGray">商品類型</th>
                    <th class="theadGray">單價</th>
                    <th class="theadGray">數量</th>
                    <th class="theadGray">總價</th>
                    <th class="theadGray">圓滿點折抵</th>
                    <th class="theadGray">折後CV</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data['singleData']['product'] as $product)
                    <tr>
                        <td>
                            <div class="row">
                                <div class="col-sm-2">
                                    @if(!empty($product['url2']))
                                        <img src="{{request()->server('REQUEST_SCHEME')}}://{{$product['url2']}}" style="width:100px;"/>
                                    @endif

                                    @if(isset($product['key_type']))
                                    @if(substr($product['key_type'],0,3) == 'kol')
                                        <span class="prod_tag kol_tag">網紅推薦</span>
                                    @endif
                                    @endif

                                    @if(isset($product['key_type']))
                                    @if($product['key_type'] == 'add')
                                        <span class="prod_tag add_tag">加價購</span>
                                    @endif
                                    @endif
                                </div>

                                <div class="col-sm-9">
                                    {{$product['name']}}
                                    @if(!empty($product['ISBN']))
                                        ：{{$product['ISBN']}}
                                    @endif
                                    @if(!empty($product['Author']))
                                        <br>
                                        {{$product['position']}}&emsp;
                                        {{$product['Author']}}&emsp;
                                        {{$product['house']}}
                                    @endif
                                    @if(empty(config('control.close_function_current')['庫存警示']))
                                        @if(!empty($product['pre_buy']))
                                            【超額購買】
                                        @endif
                                    @endif
                                    <br>
                                    @if(!empty($product['url']))
                                        <a target="_blank" href="{{request()->server('REQUEST_SCHEME')}}://{{$product['url']}}">{{$product['url']}}</a>
                                    @endif
                                    @if(config('control.control_register')==1)
                                        @if($product['is_registrable'] == '1')
                                            <a target="_blank" href="{{request()->server('REQUEST_SCHEME')}}://{{($product['url']) ?? $product['change_url']}}?order_number={{$data['singleData']['order_number']}}&type_id={{$product['type_id']}}" class="btn btn-success btn-sm text-white">查看報名資料</a>
                                        @endif
                                    @endif
                                    @if(!isset(config('control.close_function_current')['存放位置管理']))
                                        @if(isset($product['key_type']))
                                            @if($data['singleData']['status']=='New' && $data['has_instance_product'])
                                                @if( $product['deal_position']=='0' && !in_array($product['key_type'], $data['NOT_PRODUCTINFO_PRODTYPE']) )
                                                    <a href="javascript:deal_position('{{$product['info_id']}}','{{$product['type_id']}}_{{$product['key_type']}}','{{$product['num']}}')"
                                                        class="btn sendbtn  btn-sm text-white"
                                                        style="text-decoration: none;">撿貨
                                                    </a>
                                                @endif
                                            @endif
                                        @endif
                                    @endif
                                    @if(!isset(config('control.close_function_current')['存放位置管理']) && isset($product['position_code']))
                                        <br><span>撿貨編碼 {{$product['position_code']}}</span>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td data-th="商品類型">
                            @if($product['product_cate']==1)
                                投資
                            @elseif($product['product_cate']==2)
                                消費 (
                                    廣告：{{$data['arr_use_ad'][$product['use_ad']]['name'] ?? ''}}。
                                    供應商編號：@if($product['distributor_id'])
                                        <a href="{{url('order/Index/edit')}}?id={{$product['distributor_id']}}"
                                           target="_blank"
                                        >{{$data['distributor_users'][$product['distributor_id']]}}</a>，
                                    @else
                                        '無'，
                                    @endif
                                    供應商分潤：{{$product['price_supplier']}}，
                                    回饋方式：{{$product['distributor_id'] ? ($product['supplier_bonus']==1 ? '增值積分' : '現金'): '無'}}
                                )<br>
                                回饋：{{$data['arr_bonus_models'][$product['bonus_model_id']]['name'] ?? ''}}
                            @endif
                        </td>
                        <td data-th="單價">{{$product['price']}}</td>
                        <td data-th="數量">{{$product['num']}}</td>
                        <td data-th="總價">{{$product['total']}}</td>
                        <td data-th="圓滿點折抵">{{$product['deduct_invest']+$product['deduct_consumption']}}</td>
                        <td data-th="折後CV">{{$product['price_cv']-($product['deduct_invest']+$product['deduct_consumption'])}}</td>
                    </tr>
                @endforeach

                @foreach($data['singleData']['discount'] as $discount)
                    <tr>
                        <td colspan="5">【{{$discount['type']}}】{{$discount['name']}}</td>
                        <td>{{$discount['count']}}</td>
                        <td></td>
                    </tr>
                @endforeach

                @if($data['singleData']['freediscount'] != 0)
                    <tr>
                        <td colspan="5">【折扣】購物現折</td>
                        <td>折抵{{$data['singleData']['freediscount']}}元</td>
                        <td></td>
                    </tr>
                @endif
                <tr>
                    <td colspan="5">
                        @if(!isset(config('control.close_function_current')['點數設定']))
                            <!-- 本訂單增加紅利{{$data['singleData']['add_point']}}點 -->
                        @endif
                    </td>
                    <td><h4 class="total font-weight-bold">總金額 :{{$data['singleData']['total']}}</h4></td>
                    <td></td>
                </tr>
            </tbody>
        </table>
        @if(empty(config('control.close_function_current')['會員管理']))
        <h3 class="main-title">購買人資料</h3>
        <table class="table table-order-ctrl mb-5">
            <tbody>
                <tr>
                    <td data-th="會員編號">
                        <a href="{{url('order/Index/edit')}}?id={{$data['singleData']['user']['id'] ?? ''}}" target="_blank">
                            {{$data['singleData']['user']['number'] ?? ''}}
                        </a>
                    </td>
                    <td data-th="信箱"><a href="mailto:{{$data['singleData']['user']['email'] ?? ''}}">{{$data['singleData']['user']['email'] ?? ''}}</a></td>
                    <td data-th="姓名">{{$data['singleData']['user']['name'] ?? ''}}</td>
                </tr>
                <tr>
                    <td data-th="手機">{{$data['singleData']['user']['phone'] ?? ''}}</td>
                    <td data-th="聯絡電話">{{$data['singleData']['user']['tele'] ?? ''}}</td>
                    <td data-th="信用卡末4碼">{{$data['singleData']['card4no']}}</td>
                </tr>
            </tbody>
            <tr>
                <td colspan="3" data-th="聯絡地址">{{$data['singleData']['user']['home'] ?? ''}}</td>
            </tr>
        </tbody>
        </table>
        @endif
        <h3 class="main-title"><span class="text-danger">*</span>收件人資料</h3>
        <table class="table table-order-ctrl mb-5">
            <tbody>
                <tr>
                    <td data-th="姓名">
                        <form action="{{url('order/OrderCtrl/update')}}" method="post" enctype="multipart/form-data" class="d-inline">
                            @csrf
                            <input type="hidden" name="id" value="{{$data['singleData']['id']}}">
                            <input type="text" class="p-1 mr-1" name="transport_location_name" value="{{$data['singleData']['transport_location_name']}}"><button class="btn sendbtn btn-sm">修改</button>
                        </form>
                    </td>
                    <td data-th="手機">{{$data['singleData']['transport_location_phone']}}</td>
                    <td data-th="聯絡電話">{{$data['singleData']['transport_location_tele']}}</td>
                </tr>
                <tr>
                    <td data-th="聯絡信箱" >
                        {{$data['singleData']['transport_email']}}
                    </td>
                    <td colspan="2" data-th="出貨地址">
                        @if(config('control.thirdpart_logistic') && in_array($data['singleData']['transport'], config('extra.ecpay.shippable_supermarket')))
                            {{$data['singleData']['CVSStoreID']}} {{$data['singleData']['CVSStoreName']}} {{$data['singleData']['CVSTelephone']}}
                        @else
                            <form action="{{url('order/OrderCtrl/update')}}" method="post" enctype="multipart/form-data" class="d-inline">
                                @csrf
                                <input type="hidden" name="id" value="{{$data['singleData']['id']}}">
                                <input type="text" class="p-1 mr-1 form-control d-inline" style="width: 60%;" name="transport_location" value="{{$data['singleData']['transport_location']}}" ><button class="btn sendbtn btn-sm">修改</button>
                            </form>
                        @endif
                    </td>
                </tr>
                <tr>
                    <td colspan="3" data-th="訂單重量">
                        <form action="{{url('order/OrderCtrl/update')}}" method="post" enctype="multipart/form-data" class="d-inline">
                            @csrf
                            <input type="hidden" name="id" value="{{$data['singleData']['id']}}">
                            <input type="text" class="p-1 mr-1 form-control d-inline" style="width: 15%;" name="GoodsWeight" value="{{$data['singleData']['GoodsWeight']}}" >
                            Kg(小數3位，郵局宅配用)
                            <button class="btn sendbtn btn-sm">修改</button>
                        </form>
                    </td>
                </tr>
                <tr>
                    <td data-th="統一編號" >
                        {{$data['singleData']['uniform_numbers'] == '' ? '無':$data['singleData']['uniform_numbers']}}
                    </td>
                    <td colspan="2" data-th="公司抬頭">
                        {{$data['singleData']['company_title'] == '' ? '無':$data['singleData']['company_title']}}
                    </td>
                </tr>

                <tr>
                    <td colspan="3" data-th="發票類型">{{$data['singleData']['InvoiceStyleText']}}</td>
                </tr>
                <tr>
                    <td data-th="發票號碼" >
                        @if(config('control.thirdpart_invoice')==1)
                            @if($data['singleData']['InvoiceNo'] == '')
                                無
                            @else
                                {{$data['singleData']['InvoiceNo']}}&emsp;
                                @if(in_array($data['singleData']['InvoiceStyle'], ['1', '4']) == true)
                                <form action="{{url('ajax/InvoiceCreate/print')}}" method="post" enctype="multipart/form-data" class="d-inline">
                                    @csrf
                                    <input type="hidden" name="id" value="{{$data['singleData']['id']}}">
                                    <input type="hidden" name="captcha" value="{{$data['singleData']['captcha']}}">
                                    @if($data['singleData']['InvoiceNo'] == '')
                                    <span>(尚未開立發票)</span>
                                    @else
                                    @if($data['singleData']['Print'] == '1')
                                    <button class="btn sendbtn btn-sm">列印</button>
                                    @else
                                    <span>發票免列印</span>
                                    @endif
                                    @endif
                                </form>
                                @endif
                            @endif
                        @else
                            <form action="{{url('order/OrderCtrl/update')}}" method="post" enctype="multipart/form-data" class="d-inline">
                                @csrf
                                <input type="hidden" name="id" value="{{$data['singleData']['id']}}">
                                <input type="text" class="p-1 mr-1 form-control d-inline" style="width: 30%;" name="InvoiceNo" value="{{$data['singleData']['InvoiceNo']}}" >
                                <button class="btn sendbtn btn-sm">修改</button>
                            </form>
                        @endif
                    </td>
                    <td data-th="發票日期" >
                        @if(config('control.thirdpart_invoice')=='')
                            @if($data['singleData']['InvoiceDate'] == '')
                                無
                            @else
                                {{$data['singleData']['InvoiceDate']}}
                            @endif
                        @else
                            <form action="{{url('order/OrderCtrl/update')}}" method="post" enctype="multipart/form-data" class="d-inline">
                                @csrf
                                <input type="hidden" name="id" value="{{$data['singleData']['id']}}">
                                <input type="date" class="p-1 mr-1 form-control d-inline" style="width: 30%;" name="InvoiceDate" value="{{$data['singleData']['InvoiceDate']}}" >
                                <button class="btn sendbtn btn-sm">修改</button>
                            </form>
                        @endif
                    </td>
                    <td data-th="發票隨機碼">
                        @if(config('control.thirdpart_invoice')=='')
                            @if($data['singleData']['InvoiceRandom'] == '')
                                無
                            @else
                                {{$data['singleData']['InvoiceRandom']}}
                            @endif
                        @else
                            無
                        @endif
                    </td>
                </tr>
                    @switch($data['singleData']['InvoiceStyle'])
                        @case('2')
                            <!-- 發票類型為個人電子郵件寄送發票(2) -->
                            <tr>
                                <td colspan="3" data-th="電子信箱">{{$data['singleData']['transport_email']}}</td>
                            </tr>
                        @break

                        @case('3')
                            <!-- 發票類型為個人共通性載具(3) -->
                            <tr>
                                <td data-th="載具類型">
                                    @if($data['singleData']['CarrierType'] == '2')
                                        自然人憑證號碼
                                    @else
                                        手機條碼載具
                                    @endif
                                </td>
                                <td colspan="2" data-th="載具編號">{{$data['singleData']['CarrierNum']}}</td>
                            </tr>
                        @break
                        @case('4')
                            <!-- 發票類型為公司戶發票(4) -->
                            <tr>
                                <td data-th="統一編號" >{{$data['singleData']['uniform_numbers']}}</td>
                                <td colspan="2" data-th="公司抬頭">{{$data['singleData']['company_title']}}</td>
                            </tr>
                        @break
                        @case('5')
                            <!-- 發票類型為捐贈(5) -->
                            <tr>
                                <td data-th="捐贈碼">{{$data['singleData']['LoveCode']}}</td>
                                <td colspan="2" data-th="捐贈單位名稱">{{$data['singleData']['LoveCodeText']}}</td>
                            </tr>
                        @break
                        @default
                        @break
                    @endswitch
            </tbody>
        </table>
        <ul class="order-remark">
            <li>
                <h3 class="main-title">訂單備註</h3>
                <textarea id="ps" class="border w-100 p-2" style=" height:125px;  resize:none;">{{$data['singleData']['ps']}}</textarea>
                <div class="d-flex justify-content-end">
                    <button onclick="setPS('ps');"  class="btn-sm clearbtn border-0">更新備註</button>
                </div>
            </li>
            <li>
                <h3 class="main-title">訂單備註(消費者查看)</h3>
                <textarea id="ps2" class="border w-100 p-2" style="height:125px; resize:none;">{{$data['singleData']['ps2']}}</textarea>
                <div class="d-flex justify-content-end">
                    <button onclick="setPS('ps2');" class="btn-sm clearbtn border-0">更新備註</button>
                </div>
            </li>
            <li>
                <h3 class="main-title">訂單取消備註</h3>
                @switch($data['singleData']['status'])
                    @case('Cancel')
                        <div class="border w-100 p-2">
                            {{$data['singleData']['cancel_ps']}}
                        </div>
                        @break
                    @default
                        <textarea id="cancel_ps" class="border w-100 p-2" style="height:125px;  resize:none;"></textarea>
                        <div class="d-flex justify-content-end">
                            <button onclick="changeStatus2Cancel();" class="btn-sm clearbtn border-0">確認取消</button>
                        </div>
                        @break
                @endswitch
            </li>
        </ul>
        <div class="d-flex justify-content-center">
            <button class="btn sendbtn m-1" onclick="print_order()">列印訂單</button>
            <button class="btn clearbtn m-1">
                <a  href="
                    @if(($data['singleData']['status'] == 'Return') OR ($data['singleData']['status'] == 'Cancel'))
                        {{url('order/OrderCtrl/trash')}}
                    @else
                        {{url('order/OrderCtrl/index') . '?state=' . $data['singleData']['status']}}
                    @endif
                ">
                    返回列表
                </a>
            </button>
        </div>
		<!-- <table class="table ">
            <tbody>
            <tr class="ps" style="display:none; border-bottom:none;">
                <td colspan="10" style="padding:2% 10px; border-top:none; border-bottom:none;">
                    <font style="margin-left:10px">訂單備註</font>
                    <div id="print_ps" style="border:2px solid #9D9D9D; margin:10px; position:relative; padding:30px">
                        {{nl2br($data['singleData']['ps'])}}
                    </div>
                </td>
            </tr>
        </table> -->
    </div>

    <!-- logisticModel start-->
    <div class="modal fade main-modal" id="logisticModel" tabindex="-1" role="dialog" aria-labelledby="processModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content ">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div class="modal-header">
                    <span class="modal-title" id="processModelTitle">物流狀態</span>

                </div>
                <div class="modal-body">
                    <p>取貨方式：<span id="logist_type"></span></p>
                    <table class="table table-striped table-bordered table-rwd">
                        <thead>
                            <tr>
                                <th>狀態說明</th>
                                <th>時間</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- logisticModel end-->
@endsection

@section("ownJS")
    <script type="text/javascript" src="/public/static/admin/js/vue.min.js"></script>
    <script>
        function checkReport(){
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                url: "{{url('order/OrderCtrl/checkReport')}}",
                data:{
                    id: "{{$data['singleData']['id']}}",
                    status: "{{$data['singleData']['report']}}"
                },
                success: function(resp) {
                    if(resp.code){
                        $.alert('確認成功');
                        $('#checkReportButton').hide();
                        var NowDate = new Date();
                        var year = NowDate.getFullYear();
                        var month = NowDate.getMonth() + 1;
                        var date = NowDate.getDate();
                        $('#checkDay').text(year+'-'+month+'-'+date);
                    }else{
                        $.alert('確認失敗');
                        console.log(resp.msg);
                    }
                },
                error: function(xhr) {
                    $.alert('確認失敗');
                    console.log(xhr);
                }
           });
        }

        function setReceiptsState(){
            $('#block_block').show();
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                url: "{{url('order/OrderCtrl/setReceiptsState')}}",
                data:{
                    id: "{{$data['singleData']['id']}}",
                    state: '1'
                },
                success: function(resp) {
                    if(resp.code){
                        $.alert('更動成功');
                        $('#receiptsState').html('已收款');
                    }else{
                        $.alert('更動失敗');
                        console.log(resp.msg);
                    }
                    $('#block_block').hide();
                },
                error: function(xhr) {
                    $.alert('更動失敗');
                    console.log(xhr);
                    $('#block_block').hide();
                }
            });
        }

        function setTransportState(){
            $('#block_block').show();
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                url: "{{url('order/OrderCtrl/setTransportState')}}",
                data:{
                    id: "{{$data['singleData']['id']}}",
                    state: '1'
                },
                success: function(resp) {
                    if(resp.code){
                        $.alert('更動成功');
                        $('#transportState').html('已出貨');

                    }else{
                        // $.alert('更動失敗');
                        $.alert(resp.msg);
                    }
                    $('#block_block').hide();
                },
                error: function(xhr) {
                    $.alert('更動失敗');
                    console.log(xhr);
                    $('#block_block').hide();
                }
            });
        }

        function setPS(column){
            var data = {};
            data['id'] = "{{$data['singleData']['id']}}";
            data[column] = column == 'ps' ? $('#ps').val() : $('#ps2').val();
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                url: "{{url('order/OrderCtrl/setPS')}}",
                data: data,
                success: function(resp) {
                    if(resp.code){
                        $.alert('更動成功');
                        $('#print_ps').html($('#ps').val().replace(/\n/g, '<br>'));
                    }else{
                        $.alert('更動失敗');
                        console.log(resp.msg);
                    }
                },
                error: function(xhr) {
                    $.alert('更動失敗');
                    console.log(xhr);
                }
            });
        }

        function changeStatus2Cancel(){
            var form = document.createElement("form");
            form.action = "{{url('order/OrderCtrl/changeStatus2Cancel')}}";
            form.method = "post";

            id = document.createElement("input");
            id.value = "{{$data['singleData']['id']}}";
            id.name = 'id';
            form.appendChild(id);

            csrf = document.createElement("input");
            csrf.type = "hidden";
            csrf.name = "_token";
            csrf.value = csrf_token;
            form.appendChild(csrf);

            reason = document.createElement("input");
            reason.value = $('#cancel_ps').val().replace(/\n/g, '<br>');
            reason.name = 'reason';
            form.appendChild(reason);

            document.body.appendChild(form);
            form.submit();
        }
    </script>

    <script type="text/javascript">
        function getTransportPaper() {
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                url: "{{url('ajax/EcpayLogistic/getTransportPaper')}}",
                data:{
                    id: "{{$data['singleData']['id']}}",
                },
                beforeSend:function() {
                    $('.view_block').css('display','block');
                },
                success: function(response) {
                },
                error: function(xhr) {
                    $.alert('網路錯誤');
                },
                complete: function() {
                    $('.view_block').css('display','none');
                },
            });
        }

        function get_logistic_record(){
            $.ajax({
                type: 'get',
                dataType: 'json',
                url: "{{url('/order/OrderCtrl/ajax_logistic_status') . '?id=' . $data['singleData']['id']}}",
                success: function(response) {
                    console.log(response);
                    $("#logist_type").html("{{$data['singleData']['transport']}}");
                    record_html = "";
                    for(var i =0; i<response.length; i++){
                        record_html = record_html + '<tr><td>'+response[i]['message']+'</td><td>'+response[i]['time']+'</td></tr>';
                    }
                    $('#logisticModel tbody').html(record_html);
                },
                error: function(xhr) {
                    alert('失敗');
                    console.log(xhr);
                }
            });
        }
        get_logistic_record();
    </script>

    <script type="text/javascript">
        var position_table = {
            deal_num: 0,
            type_id:'',
            positions:[],
            positions_selected:[],
        }

        var  positionVM = new Vue({
            el: '#positionVM',
            data: position_table,
            methods: {
                change_item: function(pp_id, p_code, top_num, methods){
                    change_num = methods == 'plus' ? 1 : -1;

                    not_exsit = true;
                    for (var i = 0; i < this.positions_selected.length; i++) {
                        if(this.positions_selected[i]['pp_id']==pp_id){
                            final_num = parseInt(this.positions_selected[i]['num']) + (change_num);
                            if(final_num > parseInt(top_num) ){
                                alert('超出數量上限');
                                return;
                            }else{
                                this.positions_selected[i]['num'] = final_num;
                            }
                            not_exsit = false;
                            if( this.positions_selected[i]['num'] <= 0){
                                this.positions_selected.splice(i, 1);
                            }
                            break;
                        }
                    }
                    if(not_exsit && methods == 'plus'){
                        this.positions_selected.push({
                            'pp_id' : pp_id,
                            'p_code': p_code,
                            'num'   : 1
                        });
                    }
                },
                check_num: function(){
                    total_num = 0;
                    for (var i = 0; i < this.positions_selected.length; i++) {
                       total_num += parseInt(this.positions_selected[i]['num']);
                    }

                    if(this.deal_num!=total_num){
                        alert("本次需撿貨 "+ this.deal_num +"個，請確認數量正確");
                        return;
                    }

                    this.release_position();
                },
                release_position: function(){
                    $.ajax({
                        type: 'post',
                        headers: {
                            'X-CSRF-Token': csrf_token
                        },
                        dataType: 'json',
                        url: "{{url('order/OrderCtrl/release_position')}}",
                        data: {
                            order_number: "{{$data['singleData']['order_number']}}",
                            type_id     : this.type_id,
                            positions   : this.positions_selected,
                        },
                        success: function(response) {
                            if(response.code==1){
                                alert("撿貨成功");
                                location.reload();
                            }else{
                                alert(response.msg);
                            }
                        },
                        error: function(xhr) {
                            alert('失敗');
                            console.log(xhr);
                        }
                    });
                }
            }
        });

        /* 核銷商品(撿貨) */
        function deal_position(prod_id, type_id, num){
            // console.log(prod_id+' '+type_id+' '+num);
            $.ajax({
                type: 'post',
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                dataType: 'json',
                url: "{{url('order/OrderCtrl/get_position')}}",
                data: {
                    order_number: "{{$data['singleData']['order_number']}}",
                    prod_id     : prod_id,
                    type_id     : type_id,
                    num         : num,
                },
                success: function(resp) {
                    if(resp.code==1){
                        positionVM.deal_num = num;
                        positionVM.type_id = type_id;
                        positionVM.positions = resp.position_portion;
                        positionVM.positions_selected = [];
                        $('#positionModal_btn').click();
                    }else if(resp.code==2){
                        alert("撿貨成功");
                        location.reload();
                    }else{
                        alert(resp.msg);
                    }
                },
                error: function(xhr) {
                    alert('失敗');
                    console.log(xhr);
                }
            });
        }

        $('.block').on('click', function(e){
            // console.log(e.currentTarget); // 點擊的元件
            // console.log(e.target); // 選擇器的元件
            if(e.currentTarget == e.target){
                $('.block').css('display','none');
                positionVM.deal_num = 0;
                positionVM.type_id = '';
                positionVM.positions = [];
                positionVM.positions_selected = [];
            }
        });

        function print_order(){
            window.open("{{url('order/OrderCtrl/print_order')}}?id={{$data['singleData']['id']}}", '_blank');
        }
    </script>
@endsection

