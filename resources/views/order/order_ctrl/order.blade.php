@extends('admin.Public.aside')

@section('title')
    訂單管理 - 訂單管理
@endsection

@section('css')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue2-datepicker@3.11.1/index.min.css">
    <style>
        .spin {
            -webkit-animation: rotate 1s normal linear infinite;
            animation: rotate 1s normal linear infinite;
        }

        @keyframes rotate {
            0% {
                -webkit-transform: rotate3d(0, 0, 1, 0deg);
                transform: rotate3d(0, 0, 1, 0deg);
            }
            25% {
                -webkit-transform: rotate3d(0, 0, 1, 90deg);
                transform: rotate3d(0, 0, 1, 90deg);
            }
            50% {
                -webkit-transform: rotate3d(0, 0, 1, 180deg);
                transform: rotate3d(0, 0, 1, 180deg);
            }
            75% {
                -webkit-transform: rotate3d(0, 0, 1, 270deg);
                transform: rotate3d(0, 0, 1, 270deg);
            }
            100% {
                -webkit-transform: rotate3d(0, 0, 1, 360deg);
                transform: rotate3d(0, 0, 1, 360deg);
            }
        }
    </style>
@endsection

@section('content')
    <div class="content order_content">
       <div class="d-flex justify-content-between">
            <div class="d-inline-flex flex-wrap flex-row">
                <ul id="title" class="d-inline-flex brand-menu mr-4">
                    <li><a href="###">I訂單管理</a></li>
                    <li><a href="###" onclick="javascript:location.href='{{url('OrderCtrl/index')}}">訂單管理</a></li>
                    @if($data['state'] == 'Trash')
                        <li><a href="###">垃圾桶訂單</a></li>
                    @endif
                </ul>
                <div class="d-inline-flex searchbox mb-2" style="width:unset;">
                    <form action="{{url('OrderCtrl/import')}}" name="excelForm" method="post" enctype="multipart/form-data" class="searchKeyBox">
                        @csrf
                        <input type="file" name="file" id="file_excel" class="form-control mr-1 mb-1">
                        <button class="btn sendbtn mr-1 mb-1">匯入檔案</button>
                        <a href="/public/static/index/example_order.xlsx?20250106" class="btn clearbtn mb-1  mr-1" download="">下載範本</a>
                    </form>
                </div>

                @if($data['admin_type'] == 'admin')
                    <!-- 只有管理員可以重置訂單 -->
                    <div class="d-inline-flex searchbox mb-2" style="width:unset;">
                        <form action="{{url('/order/order_ctrl/delete_by_import')}}" name="deleteExcelForm" method="post" enctype="multipart/form-data" class="searchKeyBox">
                            @csrf
                            <input type="file" name="file" id="file_excel" class="form-control mr-1 mb-1">
                            <button class="btn btn-danger mr-1 mb-1">刪除訂單</button>
                        </form>
                    </div>
                @endif
            </div>
            <ul class="export-form">
                <li class="mr-2">
                    <button class="cursor-pointer btn export-btn" @click="excel">匯出訂單</button>
                </li>
                <li>
                    <button class="cursor-pointer btn export-btn" @click="group_excel">匯出訂單金額總計</button>
                </li>
            </ul>
       </div>

       @include('order.order_ctrl.order_search')

        <template v-if="'{{$data['state']}}'!='Trash'">
            <ol class="list-unstyled d-flex status">
                <li @click="change_order_ship_status('-1')" :class="[search_data.order_ship_status==-1 ?'active' : '']"><a href="###">全部</a></li>
                <li @click="change_order_ship_status('1')" :class="[search_data.order_ship_status==1 ?'active' : '']"><a href="###">待揀貨</a></li>
                <li @click="change_order_ship_status('2')" :class="[search_data.order_ship_status==2 ?'active' : '']"><a href="###">可揀貨</a></li>
                @if(empty(config('control.close_function_current')['揀貨列表']))
                <li @click="change_order_ship_status('3')" :class="[search_data.order_ship_status==3 ?'active' : '']"><a href="###">待包裝</a></li>
                <li @click="change_order_ship_status('4')" :class="[search_data.order_ship_status==4 ?'active' : '']"><a href="###">可寄出</a></li>
                @endif
                <li @click="change_order_ship_status('5')" :class="[search_data.order_ship_status==5 ?'active' : '']"><a href="###">已出貨</a></li>
            </ol>
        </template>

        <div class="frame d-flex flex-wrap justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button type="button" class="redo-icon mr-2" @click="get_orderforms">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                <div class="tool_item d-flex align-items-center mr-4">
                    <template v-if="['1','2','4'].indexOf(search_data.order_ship_status)!=-1
                                    || '{{$data['state']}}'=='Trash'">
                        <span class="edit" @click="toggle_edit">
                            操作 <i class="bi bi-caret-down-fill"></i>
                        </span>
                        <div :class="['edit-item', show_edit ? '' : 'none']">
                            <!-- onclick fn 再麻煩確認 -->
                            @if(empty(config('control.close_function_current')['揀貨列表']))
                                <a class="mb-2 statusSwitch" @click="move_to_pick_hint" v-if="search_data.order_ship_status==2">
                                    移至揀貨列表
                                </a>
                            @else
                                <a class="mb-2 statusSwitch"  @click="move_to_shipped" v-if="search_data.order_ship_status==2">
                                    移至已出貨
                                </a>
                            @endif
                            <a class="mb-2 statusSwitch"  @click="move_to_shipped" v-if="search_data.order_ship_status==4">
                                移至已出貨
                            </a>
                            <a @click="cnacel_orderforms" v-if="['1','2'].indexOf(search_data.order_ship_status)!=-1">
                                取消 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                            </a>
                            <template v-if="'{{$data['state']}}'=='Trash'">
                                <a @click="delete_orderforms">
                                    刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                                </a>
                            </template>
                        </div>
                    </template>
                </div>
                <template v-if="search_data.order_ship_status==2">
                    庫存狀況(
                        <button type="button" class="redo-icon m-0" @click="check_stock">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    )：
                    <select class="mr-2" v-model="search_data.stock_status" @change="go_search($event)">
                        <option value="">請選擇</option>
                        <option value="0">不足</option>
                        <option value="1">足夠</option>
                    </select>
                </template>
                <template v-if="search_data.order_ship_status==5">
                    回饋狀態：
                    <select class="mr-2" v-model="search_data.do_award_time" @change="go_search($event)">
                        <option value="">請選擇</option>
                        <option value="0">尚未回饋</option>
                        <option value="-1">已回饋</option>
                    </select>
                </template>
                <template v-if="search_resault_text==''">
                    <div class="tool_item">
                        <button class="btn sendbtn font-weight-bold" type="button" data-toggle="modal" data-target="#select">
                            篩選工具
                        </button>
                    </div>
                </template>
                <template v-else>
                    <button type="button" data-toggle="modal" data-target="#select" class="btn sendbtn font-weight-bold activeSendbtn" v-text="search_resault_text">
                    </button>
                    <span class="removebtn" @click="cancel_search"><i class="bi bi-x"></i></span>
                </template>
                <template v-if="search_data.order_ship_status==5">
                    <button class="btn sendbtn font-weight-bold ml-4" @click="batch_do_award">
                        依搜尋參數批次處理回饋
                    </button>
                </template>
            </div>
            <div class="d-flex align-items-center tool_item_right">
                <!-- <button class="btn print-btn font-weight-bold mr-2" @click="printOrders()"><i class="bi bi-printer d-inline-block mr-2"></i>列印此頁訂單</button> -->
                <span class="d-inline-block mr-2" style="color:#757575">共 <span v-text="total_orderform_num"></span> 項</span>
                <span class="d-inline-block mr-2" style="color:#757575">(共 <span v-text="lastPage"></span> 頁)</span>
                <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)-1)"><i class="bi bi-chevron-left"></i></button>
                <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)+1)"><i class="bi bi-chevron-right"></i></button>
            </div>
        </div>
        <div class="edit_form" style="min-width: 1200px;">
            <table class="table table-rwd">
                <thead>
                    <tr>
                        <th style="width:5%">
                            <input type="checkbox" class="activityCheckboxAll" onclick="$('input[class=orderCheckbox]').prop('checked', ($(this).is(':checked')?true:false))" style="vertical-align:middle"><span>次項</span>
                        </th>
                        <th style="width:10%">訂單日期/編號</th>
                        <th style="width:10%">
                            付款方式/狀態
                            <template v-if="total_orderform_unpay_num">
                                <br><span class="not-paid-num" v-text="'未付款：'+total_orderform_unpay_num"></span>
                            </template>
                        </th>
                        <th style="width:10%">運送方式</th>
                        <th style="width:15%">發票</th>
                        <th style="width:10%">姓名</th>
                        <th style="width:15%">備註</th>
                        <th style="width:10%">商品</th>
                        <th style="width:10%">購買數量/總金額</th>
                        <th style="width:5%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td class="loading"><div class="spin"><i class="bi bi-arrow-clockwise" style="font-size: 25px;"></i></div></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr v-for="(vo, vo_idx) in orderforms">
                        <td><input type="checkbox" class="orderCheckbox" :alt="vo.id">
                            <span v-text="((currentPage-1)*page_count) + vo_idx+1"></span>
                        </td>
                        <td>
                            <div v-text="vo.create_date"></div>
                            <a :href="'{{url('order/OrderCtrl/show')}}?id='+vo.id" v-text="vo.order_number" target="_blank"></a>
                        </td>
                        <td>
                            <div v-text="vo.payment_name"></div>
                            <div>
                                <template v-if="vo.receipts_state==1"><span style="color: #757575;font-size:14px;">已付款</span></template>
                                <template v-else><span style="color: #D05050;font-size:14px;">未付款</span></template>
                            </div>
                        </td>
                        <td>
                            <div v-text="vo.transport"></div>
                            <!-- 這部分要等報價確定才做
                                <div>
                                <input type="button" value="產生託運單" class="transport-btn">
                                <input type="button" value="已印製" class="transport-btn already-print">
                            </div> -->
                        </td>
                        <td>
                            <!-- 資料庫可能要新增？
                                發票形式有五種：公司、電子發票、載具、紙本、捐贈
                                發票狀態有兩種：已開立(灰)、未開立(紅)  (請參考付款方式樣式)-->

                            <div v-text="vo.InvoiceStyleText"></div>
                            <div>
                                <span v-if="vo.InvoiceNo" style="color: #757575;font-size:14px;">已開立</span>
                                <span v-else style="color: #D05050;font-size:14px;">未開立</span>
                            </div>
                        </td>
                        <td>
                            <!-- <span v-if="vo.number" v-text="vo.number"></span>
                            <span v-else>-</span>
                            <br> -->
                            <span v-if="vo.name" v-text="vo.name"></span>
                            <span v-else v-text="vo.transport_location_name"></span>
                        </td>
                        <td class="text-left">
                            <a :href="'{{url('order/OrderCtrl/show')}}?id='+vo.id"
                                class="text-left d-inline-block ps"
                                v-text="vo.ps">
                            </a>
                        </td>
                        <td>
                            <!-- 只顯示第一的商品名，若有一個商品以上，則後面加上 more -->
                            <template v-if="show_real_products(vo.product).length>0">
                                <a href="###" class="product" @click="open_product_modal(vo_idx)"
                                   v-text="vo.product[0].name"></a>
                                <a href="###" class="more-btn" @click="open_product_modal(vo_idx)"
                                   v-if="show_real_products(vo.product).length>1">more</a>
                            </template>
                        </td>
                        <td>
                            <div style="color:#757575;" v-text="count_product_num(vo.product)"></div>
                            {{config('extra.shop.dollar_symbol')}}<span v-text="vo.total"></span>
                        </td>
                        <td>
                            <template v-if="'{{$data['state']}}'!='Trash'">
                                <template v-if="['1','2'].indexOf(search_data.order_ship_status)!=-1">
                                    <span class="bi bi-trash" @click="operateCancel(vo.id)"></span>
                                </template>
                                <template v-if="['4'].indexOf(search_data.order_ship_status)!=-1">
                                    <a href="###" class="finish-btn" @click="operateCompleted(vo.id)">完成</a>
                                </template>
                                <template v-if="['-1','3','5'].indexOf(search_data.order_ship_status)!=-1">
                                    -
                                </template>
                            </template>
                            <template v-else>
                                <a href="###" class="btn" @click="operateRestore(vo.id)">恢復訂單</a>
                                <button class="btn btn-danger" @click="operateDelete(vo.id)">完全刪除</button>
                            </template>
                        </td>
                    </tr>
                </tbody>

            <!-- modal productDetail starts-->
                <a id="productDetail_btn" href="#productDetail" data-toggle="modal"></a>
                <div class="modal fade" id="productDetail" tabindex="-1" aria-labelledby="productDetailLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                        <h5 class="modal-title text-center mx-auto" id="productDetailLabel">
                            <span v-text="modal_data.name"></span><br>
                            <span v-text="modal_data.number"></span>
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        </div>
                        <div class="modal-body">
                        <div class="d-flex justify-content-between">
                            <span class="date" v-text="modal_data.create_time_f"></span>
                            <span class="memberNumber" v-text="modal_data.order_number"></span>
                        </div>
                        <div class="detail">
                            <div class="d-flex">
                                <span>商品名稱</span>
                                <span>數量</span>
                            </div>
                            <template v-for="one in show_real_products(modal_data.product)">
                                <div class="d-flex">
                                    <span class="d-flex flex-column">
                                        @if(!isset(config('control.close_function_current')['存放位置管理']))
                                            <span style="color:#757575;font-size:12px;" class="mb-1">
                                                揀貨編碼：
                                                <span v-if="one.position_code" v-text="one.position_code"></span>
                                                <span v-else>尚未揀貨</span>
                                            </span>
                                        @endif
                                        <span v-text="one.name"></span>
                                    </span>
                                    <span v-text="one.num"></span>
                                </div>
                            </template>
                        </div>
                        <div class="d-flex mb-5 total">
                            <span></span><span>總數量：<span v-text="count_product_num(modal_data.product)"></span></span>
                        </div>
                        <div>
                            <h6 class="font-weight-bold">備註</h6>
                            <div class="message" v-text="modal_data.ps"></div>
                        </div>
                        </div>
                        <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">確認
                        </button>
                        </div>
                    </div>
                    </div>
                </div>
            <!-- modal productDetail starts-->
            </table>
        </div>

        <!-- modal hint1 starts-->
        <div class="modal fade modalHint" id="hint1" tabindex="-1" aria-labelledby="hint1Label"  aria-hidden="true">
            <div class="modal-dialog modal-md modal-dialog-centered">
                <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title mx-auto font-weight-bold" id="hint1Label">提示</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    即將把
                    <span v-text="pick_hint.selected_num"></span>
                    筆訂單轉至【揀貨列表】統計揀貨數量
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn font-weight-bold" data-dismiss="modal">取消</button>
                    <button type="button" class="btn font-weight-bold" @click="move_to_pick">確認</button>
                </div>
                </div>
            </div>
        </div>
        <!-- modal hint1 ends-->

        <!-- modal hint2 starts-->
        <div class="modal fade modalHint" id="hint2" tabindex="-1" aria-labelledby="hint2Label" aria-hidden="true">
            <div class="modal-dialog modal-md modal-dialog-centered">
                <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title mx-auto font-weight-bold" id="hint2Label">提示</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <p>
                        已成功拋轉
                        <span v-text="pick_hint.success_num"></span>
                        筆訂單：請至揀貨列表進行揀貨
                    </p>
                    <ul v-if="pick_hint.out_of_stock.length">
                        失敗拋轉
                        <span v-text="pick_hint.out_of_stock.length"></span>
                        筆(庫存不足或已拋轉)：
                        <li v-for="order in pick_hint.out_of_stock"
                            v-text="order">
                        </li>
                    </ul>
                    <ul v-if="pick_hint.instance_code.length">
                        失敗拋轉
                        <span v-text="pick_hint.instance_code.length"></span>
                        筆(只能拋轉依品項編碼的訂單)：
                        <li v-for="order in pick_hint.instance_code"
                            v-text="order">
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <a href="{{url('order/Pick/index')}}" class="btn font-weight-bold">前往揀貨列表</a>
                </div>
                </div>
            </div>
        </div>
        <!-- modal hint2 ends-->

        <!-- modal hint3 starts-->
        <div class="modal fade modalHint" id="hint3" tabindex="-1" aria-labelledby="hint3Label"  aria-hidden="true">
            <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                <h5 class="modal-title mx-auto font-weight-bold" id="hint3Label">提示</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                </div>
                <div class="modal-body text-center">
                    確定要列印此頁尚未列印的托運單嗎 ?
                </div>
                <div class="modal-footer">
                <button type="button font-weight-bold" class="btn" data-dismiss="modal">取消</button>
                <button type="button font-weight-bold" class="btn" data-dismiss="modal" data-toggle="modal">列印</button>
                </div>
            </div>
            </div>
        </div>
        <!-- modal hint3 ends-->
    </div>
@endsection

@section('ownJS')
    @include('order.order_ctrl.order_search_js')
@endsection
