<!DOCTYPE html>
<html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2, user-scalable=0" />
        <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" />
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>

        <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css" />
        <script src="//stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.js"></script>
        <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js"></script>

        <link rel="stylesheet" href="{{__PUBLIC__}}/css/style2023.css?2" />
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.3.2/jquery-confirm.min.css"
        />
        <script src="//cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
        <script src="//html2canvas.hertzen.com/dist/html2canvas.min.js"></script>

        <style>
            body {
                background-color: #ffffff;
            }
            .top-table.table-order-ctrl td {
                width: 100%;
                display: block;
                border: none;
            
            }
            /* 蓋過樣式 */
            @media  (min-width: 375px) and (max-width: 1200px) {
                .top-table.table-order-ctrl td {
                    width: 50%;
                    display: table-cell;
                }
            }

            .top-table.table-order-ctrl td[data-th]:before {
                background-color: transparent;
                padding: 0.5rem 0;
                width: auto;
            }

            .rwd-table thead {
                background: #f6f6f6;
                overflow: hidden;
            }
            .rwd-table tr {
                border: 1px solid black;
            }
            .rwd-table th {
                display: none;
            }
            .rwd-table td {
                display: block;
            }

            .rwd-table td:first-child,
            .rwd-table th:first-child {
                background-color: #f6f6f6;
                border-bottom: 1px solid black;
            }
            .rwd-table td:before {
                content: attr(data-th) " : ";
                width: 3.5em;
                display: inline-block;
            }

            .rwd-table th,
            .rwd-table td {
                text-align: left;
            }

            .rwd-table th,
            .rwd-table td:before {
                color: #000000;
                font-weight: bold;
            }
            /* 大於375px */
            @media (min-width: 375px) {
                .rwd-table td:before {
                    display: none;
                }

                .rwd-table tr {
                    border-left: none;
                    border-right: none;
                }
                .rwd-table th,
                .rwd-table td {
                    display: table-cell;
                    padding: 0.25rem 0.1rem;
                }
                .rwd-table th:first-child,
                .rwd-table td:first-child {
                    padding-left: 0;
                    width: 65%;
                    background-color: transparent;
                }
                .rwd-table th:last-child,
                .rwd-table td:last-child {
                    padding-right: 0;
                }
                .rwd-table td {
                    padding: 0.5rem 0.1rem;
                }
                .rwd-table td:first-child {
                    font-weight: bold;
                }
            }
            .mt-120 {
                margin-top: 120px;
            }
        </style>
        <title>訂單明細</title>
    </head>
    <body>
        <div class="container mt-5" id="content_area">
            <h2 class="text-center">訂單明細</h2>
            <h3 class="text-center mb-4">{{$data['singleData']['order_number']}}</h3>

            <table class="table table-order-ctrl mb-5 top-table" style="box-shadow: none">
                <tbody>
                    <tr>
                        <td data-th="收件人：">{{$data['singleData']['transport_location_name']}}</td>
                        <td data-th="聯絡電話：">{{$data['singleData']['transport_location_phone']}}</td>
                    </tr>
                    <tr>
                        <td data-th="付款方式：">{{$data['singleData']['payment']}}</td>
                        <td data-th="運送方式：">{{$data['singleData']['transport']}}</td>
                    </tr>
                    <tr>
                        <td data-th="收件地址：">{{$data['singleData']['transport_location']}}</td>
                        <td data-th="備註：">{{$data['singleData']['ps2']}}</td>
                    </tr>
                </tbody>
            </table>

            <table class="rwd-table mb-5" style="box-shadow: none; width: 100%;">
                <thead>
                    <tr>
                        <th scope="col">品項</th>
                        <th scope="col">單價</th>
                        <th scope="col">數量</th>
                        <th scope="col">金額</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['singleData']['product'] as $product)
                    <tr>
                        <td data-th="品項">{{$product['name']}}</td>
                        <td data-th="單價">{{$product['price']}}</td>
                        <td data-th="數量">{{$product['num']}}</td>
                        <td data-th="金額">{{$product['total']}}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <div class="text-right border-bottom">
                <p>小計：{{config('extra.shop.dollar_symbol')}}{{$data['singleData']['subtotal']}}</p>
                <p>運費：{{config('extra.shop.dollar_symbol')}}{{$data['singleData']['shipping_fee']}}</p>
                <p>點數折抵：-{{config('extra.shop.dollar_symbol')}}{{$data['singleData']['discount']}}</p>
            </div>

            <div class="text-right">
                <p class="text-right mt-3 mb-0">總計：{{config('extra.shop.dollar_symbol')}}{{$data['singleData']['total']}}</p>
                <span style="font-size: 14px">(此次訂單新增點數：{{$data['singleData']['add_point']}} 點)</span>
            </div>

            <div class="d-flex align-items-center justify-content-center mt-120 mb-5">
                <img src="{{__UPLOAD__}}/{{$data['singleData']['icon']}}" alt="logo" class="mr-2" style="max-width: 150px;" />
                <div class="text-sm-center">
                    <p class="mb-0">{{$data['singleData']['company_name']}}</p>
                    <p class="mb-0">
                        <span>電話：{{$data['singleData']['service_tel']}}</span>
                        <span>信箱：{{$data['singleData']['service_email']}}</span>
                    </p>
                    <p class="mb-0">地址：{{$data['singleData']['service_address']}}</p>
                </div>
            </div>
        </div>
    </body>
</html>

<script type="text/javascript" src="/public/static/admin/js/vue.min.js"></script>
<script>
    // window.jsPDF = window.jspdf.jsPDF;
    // window.onload = function () {
    //     const content = document.getElementById('content_area');
    //     html2canvas(content).then(canvas => {
    //         // canvas
    //         const imgData = canvas.toDataURL('image/png');
    //         // make pdf
    //         const pdf = new jsPDF('p', 'pt', [canvas.width, canvas.height]);
    //         // add image to the pdf
    //         pdf.addImage(imgData, 'PNG', 20, 50);
    //         // download
    //         pdf.save("{{$data['singleData']['order_number']}}" + '.pdf');
    //     });
    // };
</script>