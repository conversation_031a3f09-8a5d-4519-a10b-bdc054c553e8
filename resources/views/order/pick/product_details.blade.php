<script>
  // 供應商切換模組串接-開始
  function init_after_get_distributors(){
  }
  function click_distributor_emit(id){
    content_areaVM.distributors_current = String(id);
    content_areaVM.get_history();
  }
  // 供應商切換模組串接-結束

  const empty_searchform = {
    page: 1, 
    order: 'desc',
  };

  const my_distributor_id = '{{$data["my_distributor_id"]}}';

  const is_num_limit = `{{ empty(config('control.close_function_current')['庫存警示']) }}` == '1' ? true : false;
  const is_stock = `{{ empty(config('control.close_function_current')['存放位置管理']) }}` == '1' ? true : false;
  const stock_warning = `{{ empty(config('control.close_function_current')['庫存警示']) }}` == '1' ? true : false;

  var content_area_data = {
    /*搜尋相關資料*/
    currentPage: 1,
    row_per_page: 20,
    lastPage: 1,

    /*列表顯示相關資料*/
    row_count_top: 0,
    row_count_last: 0,
    total_row_num: 0,
    historyItem: [],
    searchform: JSON.parse(JSON.stringify(empty_searchform)),
    location: window.location.href.split('/')[window.location.href.split('/').length-1].split('.')[0],
  }

  var content_areaVM = new Vue({
    el: '#content_area',
    data: content_area_data,
    computed: {
    },
    methods: {
      change_page(page){
        if(page>0 && page<=this.lastPage){
          this.currentPage = page;
          this.get_history();
        }
      },
      change_order(){
        if ($('#order_arrow').hasClass('active')) {
          $('#order_arrow').removeClass('active');
          $('#order_arrow').html('<i class="bi bi-caret-down-fill"></i>')
          this.searchform.order = 'desc';
        } else {
          $('#order_arrow').addClass('active');
          $('#order_arrow').html('<i class="bi bi-caret-up-fill"></i>')
          this.searchform.order = 'asc';
        }

        this.currentPage = 1;
        this.get_history();
      },
      confirm(n) {
        if(n<0 || n >= this.historyItem.lenght){ 
          Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
        }
        let target = this.historyItem[n];
				let stockValue = stock_warning ? parseInt(target.real_stock, 10) : 999999999;
				let expectedPickValue = parseInt(target.count, 10);
				let pickNumValue = parseInt(target.pick_num, 10);

				//判斷
				if (!isNaN(pickNumValue)) {
					//是數字
          check_real_stock = false;
          if(is_num_limit && is_stock){
            check_real_stock = pickNumValue > stockValue; /*揀貨量 > 實際庫存*/
          }
					if (pickNumValue <= 0 || pickNumValue > expectedPickValue || check_real_stock) {
						let popupHint2Modal = $('#popupHint2');
						popupHint2Modal.modal('show');
						// 在 modal 隱藏後重置 modal 的內容
						popupHint2Modal.on('hidden.bs.modal', function() {
							pickNumElement.value = "";
						});
					} else {
						this.move_to_picked(n);
					}
				} else {
					let popupHint1Modal = $('#popupHint1');
					popupHint1Modal.modal('show');
					// 在 modal 隱藏後重置 modal 的內容
					popupHint1Modal.on('hidden.bs.modal', function() {
						pickNumElement.value = "";
					});
				}
			},
      move_to_picked(n) {
        if(n<0 || n >= this.historyItem.lenght){ 
          Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
        }
        let target = this.historyItem[n];

        if (is_stock) {
          var post_data = {
            'position_code': target.p_code,
            'pick_num': target.pick_num,
          };
        } else {
          var post_data = {
            'position_code': target.type_id,
            'pick_num': target.pick_num,
          };
        }

        response = $.ajax({
          type: "POST",
          dataType: "json",
          headers: {
            'X-CSRF-TOKEN': csrf_token
          },
          data: post_data,
          url: "{{url('order/OrderCtrl/move_to_picked')}}", //產品資訊
        });

        alert('揀貨成功');
        content_areaVM.get_history();
      },
      get_history() {
        this.historyItem = []; 
        post_data = this.searchform;
        post_data.page = this.currentPage;
        post_data.row_per_page = this.row_per_page;
        post_data.location = page_location;
        // console.log(post_data);
        return $.ajax({
          type: "GET",
          dataType: "json",
          data: post_data,
          url: "{{url('order/Pick/get_history')}}", //產品資訊
          success: function (response) {
            console.log(response);
            content_areaVM.currentPage = response.CurrentPage;
            content_areaVM.row_per_page = response.listRows;
            content_areaVM.lastPage = response.lastPage;
            content_areaVM.total_row_num = response.total_history.length;
            for (let idx = 0; idx < response.history_data_item.length; idx++) {
              response.history_data_item[idx].pick_num = '';
            }
            content_areaVM.historyItem = response.history_data_item;
          }
        });
      },
      print() {
        window.print();
      },
    },
  });

  content_areaVM.get_history();
</script>