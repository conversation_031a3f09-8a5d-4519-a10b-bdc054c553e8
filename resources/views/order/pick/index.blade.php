<!--/*串接內容*/-->
@extends('admin.Public.aside')
@section('title')揀貨列表 - 待取庫存@endsection
@section('css')@endsection
@section('content')
<div class="content">
    <ul id="title" class="brand-menu d-flex">
        <li><a href="###">I訂單管理</a></li>
        <li><a href="###">揀貨列表</a></li>
        <li><a href="###">待取庫存</a></li>
     </ul>
    <div class="d-flex justify-content-between mb-3">
        <div class="d-flex flex-column title">
            <h6>待取庫存</h6>
            <ul class="d-flex switch-tabs">
                <li><a href="###" class="active mr-2">待取庫存</a></li>
                <li><a href="/order/pick/history">揀貨歷史紀錄</a></li>
            </ul>
        </div>
        <div class="final-check d-flex align-items-center">
            <p class="mb-0 mr-2">此頁揀貨完畢<br>即可到「可寄出」流程</p>
            <a href="/order/order_ctrl/index?order_ship_status=4">可寄出</a>
        </div>
    </div>     

    <div class="frame d-flex flex-wrap justify-content-between align-items-center">
        <div class="tool_item d-flex align-items-center">  
          <button type="button" class="redo-icon" @click="get_history"><i class="bi bi-arrow-clockwise"></i></button>
        </div>

        <div class="d-flex align-items-center">
            <button class="btn print-btn font-weight-bold mr-2" @click="print();"><i class="bi bi-printer d-inline-block mr-2"></i>列印此頁商品</button>
            <span class="d-inline-block mr-2">共 <span v-text="total_row_num"></span> 項</span>
            <template v-if="total_row_num>0">
                <span v-text="((currentPage-1)*row_per_page)+1"></span>-
            </template>
            <span v-if="currentPage*row_per_page > total_row_num" v-text="total_row_num"></span>
            <span v-else v-text="currentPage*row_per_page"></span>
            列(共 <span v-text="lastPage"></span> 頁)</span>
          
            <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)-1)"><i class="bi bi-chevron-left"></i></button>
            <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)+1)"><i class="bi bi-chevron-right"></i></button>
            </span>
        </div>
    </div>
    <div class="edit_form" style="min-width: 1200px;">
         <table class="table table-rwd">
             <thead>
                 <tr>
                    <th style="width:5%">次項</th>
                    <th style="width:10%">商品條碼</th>
                    @if(empty(config('control.close_function_current')['存放位置管理']))
                    <th style="width:10%">揀貨編碼</th>
                    @endif
                    <th style="width:20%">商品</th>
                    <th style="width:10%">品項</th>
                    
                    @if(empty(config('control.close_function_current')['庫存警示']))
                    @if(empty(config('control.close_function_current')['存放位置管理']))
                    <th style="width:10%">實體庫存</th>
                    @endif
                    @endif
                    <th style="width:10%">
                        需求數量
                        <a id="order_arrow" class="d-inline-block ml-2" href="###" @click="change_order()">
                            <i class="bi bi-caret-down-fill"></i>
                        </a>
                    </th>
                    <th style="width:10%">本次揀貨數量</th>
                    <th style="width:10%">確認</th>
                 </tr>
             </thead>
             <tbody>
                <template v-if="historyItem.length == 0">
                    <tr>
                        <td colspan="9" class="text-center">無資料</td>
                    </tr>
                </template>
                <template v-else>
                    <tr v-for="(vo, vo_idx) in historyItem">
                        <td><span v-text="((currentPage - 1) * row_per_page) + vo_idx + 1"></span></td>
                        <td><span v-text="vo.ISBN"></span></td>
                        @if(empty(config('control.close_function_current')['存放位置管理']))
                        <td><span v-text="vo.p_code"></span></td>
                        @endif
                        <td><span v-text="vo.title"></span></td>
                        <td><span v-text="vo.type_title"></span></td>

                        @if(empty(config('control.close_function_current')['庫存警示']))
                        @if(empty(config('control.close_function_current')['存放位置管理']))
                        <td class="stock"><span v-text="vo.real_stock"></span></td>
                        @endif
                        @endif
                        <td class="expected-num"><span v-text="vo.count"></span></td>
                        <td><input type="text" class="form-control w-50 mx-auto pick-num" v-model="vo.pick_num"></td>
                        <td><button type="button" class="confirm-btn" @click="confirm(vo_idx)">確認</button></td>
                    </tr>
                </template>
             </tbody>
         </table>
     </div>

    <!-- modal popupHint1 starts -->
     <div class="modal fade modalHint" id="popupHint1" tabindex="-1" aria-labelledby="popupHint1Label"  aria-hidden="true">
        <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
            <h5 class="modal-title mx-auto font-weight-bold" id="popupHint1Label">提示</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            </div>
            <div class="modal-body text-center">
                請輸入揀貨數量！
            </div>
            <div class="modal-footer">
                <button type="button" class="btn w-100 font-weight-bold" data-dismiss="modal">確認</button>
            </div>
        </div>
        </div>
    </div> 
    <!-- modal popupHint1 starts -->



    <!-- modal popupHint2 starts -->
    <div class="modal fade modalHint" id="popupHint2" tabindex="-1" aria-labelledby="popupHint2Label"  aria-hidden="true">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title mx-auto font-weight-bold" id="popupHint2Label">提示</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    輸入的數字必須大於 0 且不能超過庫存數量與預計揀貨數量！
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn font-weight-bold w-100" data-dismiss="modal">確認</button>
                </div>
            </div>
        </div>
    </div>
    <!-- modal popupHint2 ends -->
</div>

@endsection
@section('ownJS')
    <script>
        var page_location = 'index';
    </script>
    @include('order.pick.product_details')
@endsection
