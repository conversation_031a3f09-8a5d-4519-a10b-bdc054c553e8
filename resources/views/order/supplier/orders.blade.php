@extends('admin.Public.aside')

@section('title')
  供應商回饋結算 - 訂單管理
@endsection

@section('css')
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue2-datepicker@3.11.1/index.min.css">
  <style>
    .spin {
      -webkit-animation: rotate 1s normal linear infinite;
      animation: rotate 1s normal linear infinite;
    }

    @keyframes rotate {
      0% {
        -webkit-transform: rotate3d(0, 0, 1, 0deg);
        transform: rotate3d(0, 0, 1, 0deg);
      }
      25% {
        -webkit-transform: rotate3d(0, 0, 1, 90deg);
        transform: rotate3d(0, 0, 1, 90deg);
      }
      50% {
        -webkit-transform: rotate3d(0, 0, 1, 180deg);
        transform: rotate3d(0, 0, 1, 180deg);
      }
      75% {
        -webkit-transform: rotate3d(0, 0, 1, 270deg);
        transform: rotate3d(0, 0, 1, 270deg);
      }
      100% {
        -webkit-transform: rotate3d(0, 0, 1, 360deg);
        transform: rotate3d(0, 0, 1, 360deg);
      }
    }
  </style>
@endsection

@section('content')
  <div class="content order_content">
    <div class="d-flex justify-content-between">
      <div class="d-inline-flex flex-wrap flex-row">
        <ul id="title" class="d-inline-flex brand-menu mr-4">
          <li><a href="###">I訂單管理</a></li>
          <li><a href="###" onclick="javascript:location.href='{{url('Supplier/orders')}}">供應商回饋結算</a></li>
        </ul>
      </div>
      <ul class="export-form">
        <li>
          <button class="cursor-pointer btn export-btn mr-2" @click="excel">匯出列表</button>
          <button class="cursor-pointer btn export-btn" @click="group_excel">匯出商品金額總計</button>
        </li>
      </ul>
    </div>

    @include('order.order_ctrl.order_search')

    <div class="frame d-flex flex-wrap justify-content-between align-items-center">
      <div class="d-flex align-items-center flex-wrap">
        <button type="button" class="redo-icon mr-2" @click="get_orderforms">
          <i class="bi bi-arrow-clockwise"></i>
        </button>
        <template>
          <span class="mr-2 d-linline-block">
            <input type="text" v-model="search_data.supplier_text" @blur="go_search($event)" placeholder="供應商姓名/編號/手機">
          </span>
          <span class="mr-2 d-linline-block">
            回饋方式
            <select v-model="search_data.supplier_bonus" @change="go_search($event)">
              <option value="">全部</option>
              <option value="1">增值積分</option>
              <option value="2">現金</option>
            </select>
          </span>
          <span class="mr-2 d-linline-block">
            供應商回饋狀態：
            <select v-model="search_data.do_award_supplier_time" @change="go_search($event)">
              <option value="">請選擇</option>
              <option value="0">尚未回饋</option>
              <option value="-1">已回饋</option>
            </select>
          </span>
          <span class="mr-2 d-linline-block">
            供應商回饋時間：
            <input type="date" v-model="search_data.do_award_supplier_time_s" @change="go_search($event)">~
            <input type="date" v-model="search_data.do_award_supplier_time_e" @change="go_search($event)">
          </span>
        </template>
        <template v-if="search_resault_text==''">
          <div class="tool_item">
            <button class="btn sendbtn font-weight-bold" type="button" data-toggle="modal" data-target="#select">
              篩選工具
            </button>
          </div>
        </template>
        <template v-else>
          <button type="button" data-toggle="modal" data-target="#select" class="btn sendbtn font-weight-bold activeSendbtn" v-text="search_resault_text">
          </button>
          <span class="removebtn" @click="cancel_search"><i class="bi bi-x"></i></span>
        </template>
      </div>
    </div>
    <div class="d-flex flex-wrap justify-content-between">
      <button class="btn clearbtn mb-2" @click="pay_batch">依搜尋結果標記付款</button>
      <div class="d-flex align-items-center tool_item_right">
        <span class="d-inline-block mr-2" style="color:#757575">共 <span v-text="total_orderform_num"></span> 項</span>
        <span class="d-inline-block mr-2" style="color:#757575">(共 <span v-text="lastPage"></span> 頁)</span>
        <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)-1)"><i class="bi bi-chevron-left"></i></button>
        <button type="button" class="page-switch-btn" @click="change_page(Number(currentPage)+1)"><i class="bi bi-chevron-right"></i></button>
      </div>
    </div>
    <div class="edit_form" style="min-width: 1200px;">
      <table class="table table-rwd">
        <thead>
          <tr>
            <th style="width:5%"><span>次項</span></th>
            <th style="width:20%">訂單日期/編號</th>
            <th style="width:25%">商品名稱</th>
            <th style="width:15%">供應商姓名</th>
            <th style="width:10%">回饋方式</th>
            <th style="width:15%">回饋金額(美金)</th>
            <th style="width:10%">回饋日期</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colspan="7" class="loading">
              <div class="spin"><i class="bi bi-arrow-clockwise" style="font-size: 25px;"></i></div>
            </td>
          </tr>
          <tr v-for="(vo, vo_idx) in orderforms">
            <td>
              <span v-text="((currentPage-1)*page_count) + vo_idx+1"></span>
            </td>
            <td>
              <div v-text="vo.create_date"></div>
              <a :href="'{{url('order/OrderCtrl/show')}}?id='+vo.orderform_id" v-text="vo.order_number" target="_blank"></a>
            </td>
            <td><div v-text="vo.name"></div></td>
            <td>
              <span v-text="vo.supplier_name"></span>
              <template v-if="vo.supplier_number">
                <span v-text="'('+vo.supplier_number+')'"></span>
              </template>
            </td>
            <td><div v-text="vo.supplier_bonus_name"></div></td>
            <td><div v-text="vo.price_supplier"></div></td>
            <td>
              <template v-if="vo.do_award_supplier_time">
                <span v-text="vo.do_award_supplier_time_f"></span>
              </template>
              <template v-else>
                <!-- <span v-text="vo.id"></span> -->
                <button class="btn clearbtn" @click="pay(vo.id)">標記付款</button>
              </template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
@endsection

@section('ownJS')
  @include('order.supplier.order_search_js')
@endsection
