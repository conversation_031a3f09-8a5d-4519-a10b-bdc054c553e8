<!-- 引入 vue2-datepicker 的 JavaScript 文件 -->
<script src="https://cdn.jsdelivr.net/npm/vue2-datepicker@3.11.1/index.min.js"></script>
<script>
  // 供應商切換模組串接-開始
  function init_after_get_distributors() {}

  function click_distributor_emit(id) {
    content_areaVM.distributors_current = String(id);
    content_areaVM.get_orderforms();
  }
  // 供應商切換模組串接-結束

  const my_distributor_id = '{{$data["my_distributor_id"]}}';
  const clear_search = {
    searchKey: '',
    searchKey2: '',
    searchKey2_exclude: "",
    payment: '',
    receipts_state: '',
    transport: '',
    transport_location_name: '',
    transport_location_phone: '',
    
    supplier_text: '',
    supplier_bonus: '',
    do_award_supplier_time: '',
    do_award_supplier_time_s: '',
    do_award_supplier_time_e: '',
  };
  var content_area_data = {
    /*搜尋相關資料*/
    distributors_current: '{{$data["distributor_id_search"]}}',
    date: "{{request()->get('date')??''}}", /*預設搜尋購買日期區間用*/
    time: null, /* vue date-picker 綁定用 */
    buy_date_st2: '', /*由 time 拆解*/
    buy_date_en2: '', /*由 time 拆解*/
    searchKey2_exclude_list: [],
    currentPage: 1,
    page_count: Number(document.querySelector('select[name="page_count"]').children[0].value),
    lastPage: 1,
    search_data: {
      state: "{{request()->get('state')??'New'}}",
      order_ship_status: -1,
      searchKey: "{{request()->get('searchKey')??''}}",
      searchKey2: "{{request()->get('searchKey2')??''}}",
      searchKey2_exclude: "",
      payment: "{{request()->get('payment')??''}}",
      receipts_state: "{{request()->get('receipts_state')??''}}",
      transport: "{{request()->get('transport')??''}}",
      transport_location_name: "{{request()->get('transport_location_name')??''}}",
      transport_location_phone: "{{request()->get('transport_location_phone')??''}}",
      
      supplier_text: "{{request()->get('supplier_text')??''}}",
      supplier_bonus: "{{request()->get('supplier_bonus')??''}}",
      do_award_supplier_time: "{{request()->get('do_award_supplier_time')??''}}",
      do_award_supplier_time_s: "{{request()->get('do_award_supplier_time_s')??''}}",
      do_award_supplier_time_e: "{{request()->get('do_award_supplier_time_e')??''}}",
    },

    /*列表顯示相關資料*/
    show_edit: false,
    search_resault_text: '',
    total_orderform_num: 0,
    total_orderform_unpay_num: 0,
    orderforms: [],

    /*搜尋付款方式的選項*/
    payments: [
      @foreach($data['payments'] as $vo)
        @if($vo['sys_status']==1)
          {
            id: "{{$vo['id']}}",
            name: "{{$vo['name']}}"
          },
        @endif
      @endforeach
    ],
    receipts_states: [
      {id: "1", name: "已付款"},
      {id: "0", name: "未付款"},
    ],
    transports: [
      @foreach($data['transports'] as $vo)
        "{{$vo->transport}}",
      @endforeach
    ],
  }

  /*依據篩選的date還原出綁定時間區間選擇器的time*/
  if (content_area_data.date) {
    date = content_area_data.date
    dates = date.split(' ~ ');
    if (dates.length == 2) {
      var time_0 = new Date(dates[0]);
      var time_1 = new Date(dates[1]);
      if (time_0 != 'Invalid Date' && time_1 != 'Invalid Date') {
        content_area_data.time = [time_0, time_1];
        content_area_data.buy_date_st2 = dates[0];
        content_area_data.buy_date_en2 = dates[1];
      }
    }
  }

  var content_areaVM = new Vue({
    el: '#content_area',
    data: content_area_data,
    computed: {},
    updated() {
      //備註字數限制
      let message = document.querySelectorAll("table .ps");
      message.forEach((title) => {
        let trimmedText = title.innerHTML.trim();
        if (trimmedText.length >= 20) {
          let slicedText = trimmedText.slice(0, 20);
          title.innerHTML = slicedText + "...";
        }
      });
    },
    methods: {
      renew_search_resault_text() {
        /*依據篩選的資料組織出search_resault_text*/
        search_resault_text = [];

        date = $('[name="date"]').val();
        date_range = date.split(' ~ ');
        if (date_range.length == 2) {
          search_resault_text.push(date);
        }

        for (let index = 0; index < Object.keys(this.search_data).length; index++) {
          var key = Object.keys(this.search_data)[index];
          var value = this.search_data[key];
          if (value == clear_search[key]) {
            continue;
          }
          if (['state', 'order_ship_status', 'searchKey2_exclude', 'supplier_text', 'supplier_bonus', 'do_award_supplier_time', 'do_award_supplier_time_s', 'do_award_supplier_time_e'].indexOf(key) != -1) {
            continue;
          }
          if (key == 'payment') {
            target = this.payments.filter((item) => item.id == value);
            if (target) {
              search_resault_text.push(target[0].name);
            }
          } else if (key == 'receipts_state') {
            target = this.receipts_states.filter((item) => item.id == value);
            if (target) {
              search_resault_text.push(target[0].name);
            }
          } else if (key == 'searchKey2') {
            if (this.searchKey2_exclude_list.indexOf('排除') != -1) {
              value += "(排除)";
            }
            search_resault_text.push(value);
          } else if (key == 'transport_location_name') {
            search_resault_text.push('收件人姓名：' + value);
          } else if (key == 'transport_location_phone') {
            search_resault_text.push('收件人電話：' + value);
          } else {
            search_resault_text.push(value);
          }
        }

        search_resault_text = search_resault_text.join('、');

        if (search_resault_text) {
          search_resault_text = '搜尋項目 | ' + search_resault_text;
        }

        this.search_resault_text = search_resault_text;
      },
      clear_date() {
        this.time = null;
        $('[name="date"]').val('');
      },
      clear_page_count() {
        this.page_count = document.querySelector('select[name="page_count"]').children[0].value;
      },
      change_order_ship_status(order_ship_status) {
        this.search_data.order_ship_status = order_ship_status;
        if (order_ship_status == 5) {
          this.search_data.state = 'Complete';
        } else {
          this.search_data.state = 'New';
        }
        this.currentPage = 1;
        this.get_orderforms();
      },
      cancel_search($event) {
        /*清除搜尋*/
        if ($event) {
          $event.preventDefault()
        }
        this.search_data = {
          ...this.search_data,
          ...clear_search
        };
        this.clear_date();
        this.clear_page_count();
        this.get_orderforms();
        this.renew_search_resault_text();
      },
      go_search($event) {
        /*搜尋*/
        if ($event) {
          $event.preventDefault()
        }
        this.currentPage = 1;
        this.get_orderforms();
        this.renew_search_resault_text();
        $('#select').modal('hide');
      },
      change_page(page) {
        if (page > 0 && page <= this.lastPage) {
          this.currentPage = page;
          this.get_orderforms();
        }
      },
      get_orderforms() {
        $('.loading').parent().show();
        this.show_edit = false;
        this.orderforms = [];
        post_data = this.get_post_data(true);
        console.log(post_data);
        return $.ajax({
          type: "GET",
          dataType: "json",
          data: post_data,
          url: "{{url('order/Supplier/get_orderforms')}}", //產品資訊
          success: function(data) {
            content_areaVM.total_orderform_num = data.total_orderform.length;
            total_orderform_unpay_num = 0;
            data.total_orderform.forEach(o => {
              if (o.receipts_state == 0) {
                total_orderform_unpay_num += 1;
              }
            });
            content_areaVM.total_orderform_unpay_num = total_orderform_unpay_num;

            content_areaVM.orderforms = data.rowDataItem;
            content_areaVM.currentPage = data.CurrentPage;
            content_areaVM.page_count = data.listRows;
            content_areaVM.lastPage = data.lastPage;

            // content_areaVM.search_data = clear_search;

            $('.loading').parent().hide();
          }
        });
      },
      get_post_data(need_page = false) {
        var post_data = JSON.parse(JSON.stringify(this.search_data));
        post_data.distributor_id = this.distributors_current;
        date_range = $('[name="date"]').val();
        date_range = date_range.split(' ~ ');
        if (date_range.length == 2) {
          post_data.buy_date_st2 = date_range[0];
          post_data.buy_date_en2 = date_range[1];
        }
        post_data.searchKey2_exclude = this.searchKey2_exclude_list.length > 0 ? content_areaVM.searchKey2_exclude_list[0] : '';
        if (need_page) {
          post_data.page = this.currentPage;
          post_data.page_count = this.page_count;
        }
        return post_data;
      },

      excel() {
        get_data = this.get_post_data();
        params = new URLSearchParams(get_data);
        location.href = "{{url('order/Supplier/excel')}}?" + params;
      },
      group_excel() {
        get_data = this.get_post_data();
        params = new URLSearchParams(get_data);
        location.href = "{{url('order/Supplier/group_excel')}}?" + params;
      },

      async pay(orderform_product_id){
        await this.go_batch(orderform_product_id);
      },
      async pay_batch(){
        await this.go_batch();
      },
      async go_batch(orderform_product_id=''){
        var get_data = this.get_post_data();
        get_data.orderform_product_id = orderform_product_id;
        $('#block_block').show();
        var resp = await $.ajax({
          type: "GET",
          dataType: "json",
          data: get_data,
          url: "{{url('order/Supplier/pay_batch')}}", //品資訊產
        });
        var vt_class = resp.code==1 ? vt_success_obj : vt_error_obj;
        Vue.toasted.show(resp.msg, vt_class);
        if(resp.code==1){
          await this.get_orderforms();
        }
        $('#block_block').hide();
      },
    },
    components: {
      DatePicker
    },
  });
  content_areaVM.get_orderforms();
  content_areaVM.renew_search_resault_text();
</script>
<script>

  function getMultiId(renew=true) {
    var multiIdArray = [];
    $('.orderCheckbox').each(function () {
      if($(this).prop("checked")){
        multiIdArray.push($(this).attr('alt'));
        if(renew){
          $(this).prop("checked", false);
        }
      }
    });
    if(renew){
      $('.activityCheckboxAll').prop('checked', false);
    }
    return multiIdArray;
  }
</script>