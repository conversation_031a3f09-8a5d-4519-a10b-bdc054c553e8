@extends('admin.Public.aside')

@section("title")B會員功能管理 - 會員級別設定@endsection

@section("css")
	<style>
		@media (max-width: 1200px) {
			.table-mobile td[data-th]:before {
				content: attr(data-th) "：";
				display: inline-block;
				color: var(--title);
				width: 100%;
				text-transform: uppercase;
				margin-right: .5rem;
			}
		}
	</style>
@endsection

@section("content")
    <div>
		<ul id="title" class="brand-menu">
            <li><a href="###">B會員功能管理 </a></li>
            <li><a href="###">會員級別設定</a></li>
        </ul>
        @if(config('control.control_FirstBuyDiscount')==1)
        <div>
        	<h3 class="main-title">會員首購優惠設定</h3>

        	<table class="table col-lg-6  col-12 table-rwd table-mobile">
				<thead>
					<tr>
						<th style="width:60%">優惠方式</th>
						<th>優惠說明</th>
						<th style="width:160px">更新</th>
					</tr>  
				</thead>
				<tbody>
					<form action="{{url('order/Admin/update_vip_type')}}?id=0" method="post">
						@csrf
						<input type="hidden" name="id" value="0">
						<input type="hidden" name="vip_name" value="會員首購優惠">
						<tr>
							<td data-th="優惠方式">
								<input type="radio" name="type" id="type0_0" value="0" @if($data['fisrt_buy']['type'] == 0) checked @endif>
								<label for="type0_0" class="mr-2">打折</label>
								<input type="radio" name="type" id="type0_1" value="1" @if($data['fisrt_buy']['type'] == 1) checked @endif>
								<label for="type{{$data['fisrt_buy']['id']}}_1" class="mr-4">扣元</label>
								<input type="number" name="discount" class="discount" value="{{$data['fisrt_buy']['discount']}}" step="0.01">
								<br><span class="text-danger">{{Lang::get('如需打85折，請輸入0.85')}}</span>
							</td>
							<td data-th="優惠說明">
								<input type="text" name="note" value="{{$data['fisrt_buy']['note']}}" class="w-100">
							</td>
							<td><button class="btn clearbtn">更改</button></td>
						</tr>    
					 </form>
				</tbody>
            </table>
        </div>
        <hr>
        @endif

        @if(config('control.control_VipDiscount')==1)
        <div>
        	<h3 class="main-title">會員級別</h3>
		   	<form action="{{url('order/Admin/add_vip_type')}}" method="post">
			   @csrf
                <table class="table table-rwd table-mobile">
					<thead>
						<tr>
							<th style="width:10%">等級名稱：</th>
							<!-- <th style="width:25%">優惠方式：</th> -->
							<th style="width:10%" class="text-right">升等條件金額：</th>
							<th>等級說明：</th>
							<th style="width:10%">月分紅加權：</th>
							<th style="width:10%">會員級別燒傷金額(美金)：</th>
							<th style="width:10%">消費圓滿點數抵扣消費比率(%)：</th>
							<th style="width:10%">個人點數責任額(GV)：</th>
							<th style="width: 75px;">新增</th>
						</tr>
					</thead>
                    <tr>
						<td data-th="等級名稱"><input type="text" name="vip_name" class="w-100"></td>
						<!-- <td data-th="優惠方式">
							<input type="radio" name="type" id="type_0" value="0" checked>
							<label for="type_0" class="mr-2">打折</label>
							<input type="radio" name="type" id="type_1" value="1">
							<label for="type_1" class="mr-4">扣元</label>
							<input type="number" name="discount" class="discount" step="0.01">
							<p class="text-danger remark">{{Lang::get('如需打85折，請輸入0.85')}}</p>
						</td> -->
						<td data-th="升等條件金額">
							<input type="number" name="rule" class="w-100 text-right" placeholder="ex:0">
							<input type="hidden" name="type" value="0">
							<input type="hidden" name="discount" value="1">
							<p class="text-danger remark">如設定0元，則只要有消費過即符合</p>
						</td>
						<td data-th="等級說明"><input type="text" name="note" class="w-100"></td>
						<td data-th="月分紅加權"><input type="number" name="dividend_month_weighted" class="w-100 text-right" step="0.1" placeholder="ex:1.1"></td>
						<td data-th="會員級別燒傷金額(美金)"><input type="number" name="burn_cv" class="w-100 text-right" step="0.01" placeholder="ex:1200"></td>
						<td data-th="消費圓滿點數抵扣消費比率(%)"><input type="number" name="discount_ratio" class="w-100 text-right" step="0.01" placeholder="ex:10"></td>
						<td data-th="個人點數責任額(GV)"><input type="number" name="liability_gv" class="w-100 text-right" placeholder="ex:1000"></td>
                        <td><button class="btn sendbtn">新增</button></td>
                    </tr>
                </table>
            </form>

            <table class="table table-rwd table-mobile">
				<thead>
					<tr>
						<th style="width:10%">等級名稱：</th>
						<!-- <th style="width:25%">優惠方式：</th> -->
						<th style="width:10%" class="text-right">升等條件金額：</th>
						<th>等級說明：</th>
						<th style="width:10%">月分紅加權：</th>
						<th style="width:10%">會員級別燒傷金額(美金)：</th>
						<th style="width:10%">消費圓滿點數抵扣消費比率(%)：</th>
						<th style="width:10%">個人點數責任額(GV)：</th>
						<th style="width: 75px;">新增</th>
					</tr>
				</thead>
				<tbody>
					@foreach($data['vip_type'] as $vo)
					<form action="{{url('order/Admin/update_vip_type')}}?id={{$vo['id']}}" method="post">
						@csrf
						<input name="id" type="hidden" value="{{$vo['id']}}">    
						<tr>
							<td data-th="等級名稱">
								<input type="text" name="vip_name" value="{{$vo['vip_name']}}" class="w-100">
								@if($vo['id']== config('extra.skychakra.check_buyer_partner_bonus_type_id'))
									<div class="text-danger position-absolute">
										此級別以下(不含)的會員購買「消費商品」時，才有可能套用「合夥批發回饋」
									</div>
								@endif
							</td>
							<!-- <td data-th="優惠方式">
								<input type="radio" name="type" id="type{{$vo['id']}}_0" value="0" @if($vo['type'] == 0)checked @endif>
								<label for="type{{$vo['id']}}_0" class="mr-2">打折</label>
								<input type="radio" name="type" id="type{{$vo['id']}}_1" value="1" @if($vo['type'] == 1)checked @endif>
								<label for="type{{$vo['id']}}_1" class="mr-4">扣元</label>
								<input type="number" name="discount" class="discount" value="{{$vo['discount']}}" step="0.01">
							</td> -->
							<td data-th="升等條件金額">
								<input type="number" name="rule" value="{{$vo['rule']}}" class="w-100 text-right">
								<input type="hidden" name="type" value="0">
								<input type="hidden" name="discount" value="1">
							</td>
							<td data-th="等級說明"><input type="text" name="note" value="{{$vo['note']}}" class="w-100"></td>
							<td data-th="月分紅加權"><input type="number" name="dividend_month_weighted" value="{{$vo['dividend_month_weighted']}}" class="w-100 text-right" step="0.1"></td>
							<td data-th="會員級別燒傷金額(美金)"><input type="number" name="burn_cv" value="{{$vo['burn_cv']}}" class="w-100 text-right" step="0.01"></td>
							<td data-th="消費圓滿點數抵扣消費比率(%)"><input type="number" name="discount_ratio" value="{{$vo['discount_ratio']}}" class="w-100 text-right" step="0.01"></td>
							<td data-th="個人點數責任額(GV)"><input type="number" name="liability_gv" value="{{$vo['liability_gv']}}" class="w-100 text-right"></td>
							<td>
								<button class="btn clearbtn">更改</button>
								<a class="btn sendbtn" href="javascript:if(confirm('確定刪除?'))location.href='{{url('order/Admin/del_vip_type')}}?id={{$vo['id']}}'">刪除</a>
							</td>
						</tr>
					</form>
					@endforeach
				</tbody>
            </table>
        </div>
        @endif
    </div>
@endsection

@section("ownJS")
@endsection































































