@extends('admin.Public.aside')
@section('title')現金積分總覽 - B會員功能管理@endsection
@section('css')@endsection
@section('content')
  <div id="content">
    <ul id="title" class="brand-menu">
      <li>B會員功能管理</li>
      <li><a href="{:url('Point/point')}">現金積分總覽</a></li>
    </ul>

    <div class="frame" @submit="search_record($event)">
      <div class="d-flex flex-wrap justify-content-between">
        <div>
          <form action="{:url('Point/point')}" method="post">
            <div class="d-inline-block mr-2">
              <span class="name">日期區間：</span>
              <input type="date" v-model="searchform.date_s"> ~ 
              <input type="date" v-model="searchform.date_e">
            </div>
            <div class="d-inline-block mr-2">
              <span class="name">會員姓名/編號：</span>
              <input type="text" v-model="searchform.user_key" placeholder="">
            </div>
            <div class="d-inline-block mr-2">
              <span class="name">說明：</span>
              <input type="text" v-model="searchform.point_msg" placeholder="">
            </div>
            <input class="btn sendbtn" type="submit" value="搜尋">
            <input class="btn" type="submit" value="清除搜尋" @click="clear_search($event)">
          </form>
        </div>
      </div>
    </div>
    <div class="frame pt-0">
      <form method="post" @submit="send_gift($event)">
        <b>現金積分異動：</b>
        目標：<input type="text" class="mr-3" placeholder="會員編號" v-model="sendform.number">
        數量：<input type="number" class="mr-3" placeholder="輸入負數則為扣除" step="1" v-model="sendform.point">
        說明(會員會看到)：<input type="text" class="mr-3" placeholder="管理員贈送點數" v-model="sendform.msg">
        <input type="submit" value="確認" class="btn btn-secondary">
      </form>
    </div>
    <div class="edit_form">
      <table class="table table-rwd" style="min-width:1000px;">
        <thead>
          <tr>
            <th>日期</th>
            <th>會員姓名</th>
            <th>會員編號</th>
            <th>說明</th>
            <th>數量</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="record in point_records">
            <tr>
              <td v-text="record.msg_time"></td>
              <td>
                <a :href="'{{url('order/Index/edit')}}?id='+record.user_id" 
                    v-text="record.name"
                    target="_blank"
                ></a>
              </td>
              <td v-text="record.number"></td>
              <td v-text="record.msg"></td>
              <td v-text="record.points"></td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
    <div class="text-center">
      <crm_index_pages 
        :change_page="change_page"
        :current_page="searchform.page" 
        :count_of_items="records_total" 
        :count_of_page="searchform.count_of_items"
      ></crm_index_pages>
    </div>
  </div>
@endsection

@section('ownJS')
  <script>
    Vue.component('crm_index_pages', {
      template:`
        <ul class="pagination">
          <li class="" v-if="current_page-1 > 0">
            <a href="###" @click="trigger_change_page(current_page-1)">«</a>
          </li> 
          <template v-for="page in pages">
            <li :class="[current_page==page ? 'active' : '']" >
              <a v-if="current_page!=page" href="###" v-text="page" @click="change_page(page)"></a>
              <span class="text-dark" v-else v-text="page"></span>
            </li>
          </template>
          <li class="" v-if="current_page+1 <= computed_page_num">
            <a href="###" @click="trigger_change_page(current_page+1)">»</a>
          </li> 
        </ul>
      `,
      data: function() {
        return {
          pages: [1],
        };
      },
      props: {
        change_page: Function,  /*換頁*/
        current_page: Number,   /*當前頁數*/
        
        count_of_items: Number, /*項目總數(計算總頁數用)*/
        count_of_page: Number,  /*一頁數量(計算總頁數用)*/
        
        total_pages: Number,    /*總頁數*/
      },
      computed: {
        computed_page_num: function(){
          page_num = 1;
          if(this.total_pages){ /*有傳入總頁數*/
            page_num = this.total_pages;
          }else if(this.count_of_items && this.count_of_page){ /*有傳入一頁數量&項目總數*/
            page_num = Math.ceil( this.count_of_items / this.count_of_page);
          }
          return page_num;
        },
      },
      watch: {
        current_page: {
          immediate: true, // 立即执行一次监听器
          handler: function() { this.updatePages(); },
        },
        count_of_items: {
          handler: function() { this.updatePages(); },
        },
        count_of_page: {
          handler: function() { this.updatePages(); },
        },
        total_pages: {
          handler: function() { this.updatePages(); },
        },
      },
      methods: {
        updatePages() { /*根據傳入最大頁數生成新的頁數列表*/
          var pages = [];
          for (var i=-5; i<5; i++) {
            if(i+this.current_page > 0 && i+this.current_page <= this.computed_page_num){
              pages.push(i+this.current_page);
            }
          }
          this.pages = pages;
        },
        trigger_change_page(page){
          if (typeof this.change_page === 'function') {
            if(page > 0 && page <= this.computed_page_num){
              this.change_page(page);
            }
          }
        }
      },
    });

    const empty_searchform = {
      page: 1, /*當前頁數*/
      count_of_items: 20,

      date_s: '',
      date_e: '',
      user_key: '',
      point_msg: '',
    };
    const empty_sendform = {
      number: '',
      point: '',
      msg: '',
    };
    var content_data = {      
      searchform: JSON.parse(JSON.stringify(empty_searchform)),
      point_records: [],
      records_total: 0,

      sendform: JSON.parse(JSON.stringify(empty_sendform)),
    }
    var contentVM = new Vue({
      el: '#content',
      data: content_data,
      created(){
        this.get_records();
      },
      methods: {
        reset_search: function(){
          this.searchform = JSON.parse(JSON.stringify(empty_searchform))
        },
        clear_search: function($event){
          $event.preventDefault();
          this.reset_search();
          this.get_records();
        },
        change_page: function(p){
          if(this.searchform.page!=p){
            this.searchform.page = p;
            this.get_records();
          }
        },
        search_record: function($event){
          $event.preventDefault();
          this.searchform.page = 1;
          this.get_records();
        },
        get_records: async function (){
          Vue.toasted.show("{{Lang::get('載入中')}}",{duration:1500, className: ["toasted-primary", 'bg-success']});
          this.point_records = [];
          try {
            resp = await $.ajax({
              type: 'post',
              dataType: 'json',
              url: "{{url('Point/get_point_data')}}",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              data: this.searchform,
            });
            this.point_records = resp.records_show;
            this.records_total = resp.records_total;

            Vue.toasted.show("{{Lang::get('載入完成')}}",{duration:1500, className: ["toasted-primary", 'bg-success']}); 
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
        },

        send_gift: async function($event){
          $event.preventDefault();
          $confirm_msg = "{{Lang::get('確定異動現金積分')}}?\n";
          $confirm_msg +="{{Lang::get('目標')}}:"+this.sendform.number+"\n";
          $confirm_msg +="{{Lang::get('數量')}}:"+this.sendform.point+"\n";
          if(confirm($confirm_msg)){
            $('#block_block').show();
            await this.gift();
            $('#block_block').hide();
          }
        },
        gift: async function(){
          try {
            var resp = await $.ajax({
              url     : "{{url('order/Index/gift')}}",
              dataType: 'json',
              headers: {
                'X-CSRF-Token': '{{csrf_token()}}' 
              },
              type    : 'POST',
              data    : this.sendform,
              contentType:"application/x-www-form-urlencoded; charset=UTF-8",
            });
            var vt_class = resp.code==1 ? vt_success_obj : vt_error_obj;
            Vue.toasted.show(resp.msg, vt_class);

            if(resp.code==1){
              this.sendform.point = '';
              this.sendform.msg = '';

              this.searchform.page = 1;
              await this.get_records();
            }
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
        },
      },
    });
  </script>
@endsection