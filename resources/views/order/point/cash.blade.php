@extends('admin.Public.aside')
@section('title')積分提現紀錄 - B會員功能管理@endsection
@section('css')@endsection
@section('content')
  <div class="content" id="content">
    <div class="d-flex justify-content-between">
      <div class="d-inline-flex flex-wrap flex-row">
        <ul id="title" class="brand-menu d-flex">
          <li><a href="###">B會員功能管理</a></li>
          <li><a href="###">積分提現紀錄</a></li>
        </ul>
      </div>
      <ul class="export-form">
        <li>
          <button class="cursor-pointer btn export-btn mr-2" @click="excel">匯出列表</button>
          <button class="cursor-pointer btn export-btn" @click="group_excel">匯出會員金額總計</button>
        </li>
      </ul>
    </div>
    <div class="d-flex justify-content-between mb-3">
      <div class="d-flex flex-column title">
        <ul class="d-flex switch-tabs">
          <li><a href="{{url('order/Point/cash')}}?paid=" class="mr-2 {{App\Services\CommonService::compare_return(request()->get('paid'), '', 'active')}}">全部</a></li>
          <li><a href="{{url('order/Point/cash')}}?paid=1" class="mr-2 {{App\Services\CommonService::compare_return(request()->get('paid'), '1', 'active')}}">未給付</a></li>
          <li><a href="{{url('order/Point/cash')}}?paid=2" class="mr-2 {{App\Services\CommonService::compare_return(request()->get('paid'), '2', 'active')}}">完成給付</a></li>
        </ul>
      </div>
    </div>
    <div class="frame">
      <form class="d-flex flex-wrap align-items-center flex-wrap" @submit="search_record($event)">
        @csrf
        <div class="d-inline-flex flex-row align-items-center mb-2 mr-3">
          <input type="text" class="form-control" v-model="searchform.memberKey" style="width: 300px;" placeholder="請輸入會員編號/姓名/信箱/手機">
        </div>
        <div class="d-inline-flex flex-row align-items-center mb-2 mr-3">
          <span class="mr-2">建立時間</span>
          <input name="date_s" type="date" class="form-control" v-model="searchform.date_s" style="width: 150px;">
          ~
          <input name="date_e" type="date" class="form-control" v-model="searchform.date_e" style="width: 150px;">
        </div>
        <div class="d-inline-flex flex-row align-items-center mb-2 mr-3">
          <span class="mr-2">給付時間</span>
          <input name="date_s_pay" type="date" class="form-control" v-model="searchform.date_s_pay" style="width: 150px;">
          ~
          <input name="date_e_pay" type="date" class="form-control" v-model="searchform.date_e_pay" style="width: 150px;">
        </div>
        <button type="submit" class="togo btn mb-2 mr-3">{{Lang::get('搜尋')}}</button>
      </form>
    </div>
    <div class="frame pt-0" v-if="searchform.paid!=2">
      <button class="btn clearbtn" @click="pay_cash_batch">依搜尋結果批次標記付款</button>
    </div>
    <div class="edit_form">
      <table class="table table-rwd" style="min-width:800px;">
        <thead>
          <tr>
            <th>{{Lang::get('日期')}}</th>
            <th>{{Lang::get('會員編號')}}</th>
            <th>{{Lang::get('姓名')}}</th>
            <th>{{Lang::get('幣別')}}</th>
            <th>{{Lang::get('金額')}}</th>
            <th>{{Lang::get('給付日期')}}</th>
          </tr>
        </thead>
        <tbody>
          <tr></tr>
          <template v-for="record in point_records">
            <tr>
              <td data-th="{{Lang::get('日期')}}" v-text="record.time_create_f.slice(0,16)"></td>
              <td data-th="{{Lang::get('會員編號')}}">
                <a :href="'{{url('order/Index/edit')}}?id='+record.user_id" target="_blank"
                   v-text="record.number"
                ></a>
              </td>
              <td data-th="{{Lang::get('姓名')}}" v-text="record.name"></td>
              <td data-th="{{Lang::get('幣別')}}" v-text="record.currency"></td>
              <td data-th="{{Lang::get('金額')}}" v-text="record.num"></td>
              <td data-th="{{Lang::get('給付日期')}}">
                <template v-if="record.time_pay">
                  <span v-text="record.time_pay_f.slice(0,16)"></span>
                </template>
                <template v-else>
                  <a href="###" @click="pay_cash_one(record.id)">
                    <button class="btn clearbtn">標記給付</button>
                  </a>
                </template>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
    <div class="text-center">
      <crm_index_pages 
        :change_page="change_page"
        :current_page="searchform.page" 
        :count_of_items="records_total" 
        :count_of_page="searchform.count_of_items"
      ></crm_index_pages>
    </div>
  </div>
@endsection

@section('ownJS')
  <script>
    Vue.component('crm_index_pages', {
      template:`
        <ul class="pagination">
          <li class="" v-if="current_page-1 > 0">
            <a href="###" @click="trigger_change_page(current_page-1)">«</a>
          </li> 
          <template v-for="page in pages">
            <li :class="[current_page==page ? 'active' : '']" >
              <a v-if="current_page!=page" href="###" v-text="page" @click="change_page(page)"></a>
              <span class="text-dark" v-else v-text="page"></span>
            </li>
          </template>
          <li class="" v-if="current_page+1 <= computed_page_num">
            <a href="###" @click="trigger_change_page(current_page+1)">»</a>
          </li> 
        </ul>
      `,
      data: function() {
        return {
          pages: [1],
        };
      },
      props: {
        change_page: Function,  /*換頁*/
        current_page: Number,   /*當前頁數*/
        
        count_of_items: Number, /*項目總數(計算總頁數用)*/
        count_of_page: Number,  /*一頁數量(計算總頁數用)*/
        
        total_pages: Number,    /*總頁數*/
      },
      computed: {
        computed_page_num: function(){
          page_num = 1;
          if(this.total_pages){ /*有傳入總頁數*/
            page_num = this.total_pages;
          }else if(this.count_of_items && this.count_of_page){ /*有傳入一頁數量&項目總數*/
            page_num = Math.ceil( this.count_of_items / this.count_of_page);
          }
          return page_num;
        },
      },
      watch: {
        current_page: {
          immediate: true, // 立即执行一次监听器
          handler: function() { this.updatePages(); },
        },
        count_of_items: {
          handler: function() { this.updatePages(); },
        },
        count_of_page: {
          handler: function() { this.updatePages(); },
        },
        total_pages: {
          handler: function() { this.updatePages(); },
        },
      },
      methods: {
        updatePages() { /*根據傳入最大頁數生成新的頁數列表*/
          var pages = [];
          for (var i=-5; i<5; i++) {
            if(i+this.current_page > 0 && i+this.current_page <= this.computed_page_num){
              pages.push(i+this.current_page);
            }
          }
          this.pages = pages;
        },
        trigger_change_page(page){
          if (typeof this.change_page === 'function') {
            if(page > 0 && page <= this.computed_page_num){
              this.change_page(page);
            }
          }
        }
      },
    });

    const empty_searchform = {
      page: 1, /*當前頁數*/
      count_of_items: 20,

      paid: "{{request()->get('paid')}}",
      memberKey: '',
      date_s: '',
      date_e: '',
      date_s_pay: '',
      date_e_pay: '',
    };
    var content_data = {      
      searchform: JSON.parse(JSON.stringify(empty_searchform)),
      point_records: [],
      records_total: 0,
    }
    var contentVM = new Vue({
      el: '#content',
      data: content_data,
      created(){
        this.get_records();
      },
      methods: {
        reset_search: function(){
          this.searchform = JSON.parse(JSON.stringify(empty_searchform))
        },
        clear_search: function($event){
          $event.preventDefault();
          this.reset_search();
          this.get_records();
        },
        change_page: function(p){
          if(this.searchform.page!=p){
            this.searchform.page = p;
            this.get_records();
          }
        },
        search_record: function($event){
          $event.preventDefault();
          this.searchform.page = 1;
          this.get_records();
        },
        get_records: async function (){
          Vue.toasted.show("{{Lang::get('載入中')}}",{duration:1500, className: ["toasted-primary", 'bg-success']});
          this.point_records = [];
          try {
            resp = await $.ajax({
              type: 'post',
              dataType: 'json',
              url: "{{url('Point/get_cash_record_data')}}",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              data: this.searchform,
            });
            this.point_records = resp.records_show;
            this.records_total = resp.records_total;

            Vue.toasted.show("{{Lang::get('載入完成')}}",{duration:1500, className: ["toasted-primary", 'bg-success']}); 
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
        },

        excel() {
          params = new URLSearchParams(this.searchform);
          location.href = "{{url('order/Point/cash_excel')}}?" + params;
        },
        group_excel() {
          params = new URLSearchParams(this.searchform);
          location.href = "{{url('order/Point/cash_group_excel')}}?" + params;
        },
        pay_cash_batch: function (){
          this.pay_cash(this.searchform);
        },
        pay_cash_one: function (id){
          this.pay_cash({id: id});
        },
        async pay_cash(post_data){
          if(!confirm('確定標記付款？')){ return; }
          $('#block_block').show();
          try {
            resp = await $.ajax({
              type: 'post',
              dataType: 'json',
              url: "{{url('Point/pay_cash')}}",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              data: post_data,
            });
            var vt_class = resp.code==1 ? vt_success_obj : vt_error_obj;
            Vue.toasted.show(resp.msg, vt_class);
            if(resp.code){
              await this.get_records();
            }
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
          $('#block_block').hide();
        },
      },
    });
  </script>
@endsection