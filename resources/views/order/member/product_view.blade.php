@extends('admin.Public.aside')
@section("title")B會員功能管理 > 會員瀏覽商品設定@endsection
@section("css")
    <link rel="stylesheet" type="text/css" href="{{__PUBLIC__}}/css/daterangepicker.css" />
   
@endsection
@section("content")
    <div id="content">
       
        <ul id="title" class="brand-menu">
            <li><a href="###">B會員功能管理 </a></li>
            <li><a href="###" onclick="javascript:location.href='{{url('order/Member/product_view')}}'">會員瀏覽商品設定</a></li>
            <li v-text="model.search"></li>
        </ul>
        <div class="searchbox">
            <form action="{{url('order/Member/product_view')}}" class="searchKeyBox" name="searchForm" method="get">
                @csrf
                <input type="text" name="searchKey" class="form-control mr-1" placeholder="搜尋活動">
                <input type="hidden" name="type" class="form-control mr-1" value="keyword">
                <a class="btn sendbtn" onclick="searchForm.submit();">搜尋</a>
            </form>
        </div>

        <!--新增與編輯-->
        <div class="frame">
            <a href="###" class="btn clearbtn"  data-toggle="modal" data-target="#addModal"><i class="bi bi-plus-lg add small"></i>  新增</a>
            <div class="d-inline-block position-relative">
                <div class="edit" onclick="Show('.edit-item')">編輯 <span class="bi bi-chevron-down"></span></div>
                <!-- 編輯開始 -->
                <div class="edit-item none">
                    <a @click="multiOnline();">
                        <p class="mb-0">上架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled checked><span class="slider round"></span>
                        </label>
                    </a>
                 
                    <a @click="multiOffline();">
                        <p class="mb-0">下架&nbsp;</p>
                        <label class="switch" name="0">
                            <input type="checkbox" disabled><span class="slider round"></span>
                        </label>
                    </a>
                    <a @click="multiDelete();" class="mt-2 border-top">
                        刪除 <span style="margin-left: 15px;" class="bi bi-trash"></span>
                    </a>
                </div>
                <!-- 編輯結束 -->
            </div>
           
            
        </div>

        <!--表格 開始-->
        <table class="table table-mobile table-rwd">
            <thead>
                <tr>
                    <th style="width: 60px;"><input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=actCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"></th>
                    <th>上下架</th>
                    <th>等級名稱</th>
                    <th>設定瀏覽商品</th>
                    <th>刪除</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item,index) in model.actList" :id="'act_' + item.id">
                    <td ><input type="checkbox" class="actCheckbox" :alt="index"></td>
                    <td data-th="上下架">
                        <label class="switch mb-0 mt-0">
                            <input v-model="item.online" type="checkbox" @change="onlineChange(item)">
                            <span class="slider round"></span>
                        </label>
                    </td>
                    <td data-th="等級名稱"><span v-text="item.name"></span></td>  
                    <td data-th="設定瀏覽商品">
                        @foreach($data['lang'] as $vo)
                            <a :href="'{{url('order/Member/product_view_edit')}}?id=' + item.id" class="mr-4 d-inline-block">{{$vo['lang_word']}}</a>
                        @endforeach
                    </td>             
                    <td><span class="bi bi-trash" @click="delAct(item)"></span></td>                    
                </tr>
            </tbody>
            

            
            
        </table>
        <!--表格 結束-->
        <div class="text-center">
        </div>
    </div>

    <div class="modal fade in main-modal" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel" style="display: inline-block;">新增會員瀏覽商品</h5>
                  
                </div>
                <div class="modal-body row" id="boxModel">
                    <div class="form-group col-12">
                        <label class="col-form-label">名稱</label>
                        <input type="text" class="form-control" v-model="addModal.name">
                    </div>
                </div>
                <div class="modal-footer flex-wrap">
                    <span class="text-danger remark">新增後請設定可瀏覽商品</span>
                    <div class="w-100 d-flex justify-content-center"><button class="btn sendbtn" @click="do_add()">新增</button></div>
                    
                   
                </div>
            </div>
        </div>
    </div>
@endsection
@section("ownJS")
    <script type="text/javascript" src="{{__PUBLIC__}}/js/moment.min.js"></script>  
    <script type="text/javascript" src="{{__PUBLIC__}}/js/daterangepicker.js"></script>
    <script src="{{__PUBLIC__}}/js/action.js"></script>
    
    <script type="text/javascript">
        // 抓取搜尋關鍵字
        var Request = new Object();  
        searchKey = GetRequest() ? GetRequest().trim() : '';
        searchKey = decodeURIComponent(searchKey).trim();;
        function GetRequest() {      
             var url = location.search.replaceAll('+', ''); 
             var theRequest = new Object();      
             if (url.indexOf("?") != -1) {       
                var str = url.substr(1);         
                strs = str.split("&");       
                for(var i = 0; i < strs.length; i++) {       
                   theRequest[strs[i].split("=")[0]]=decodeURI(strs[i].split("=")[1]);       
                }        
             }       
             return theRequest.searchKey;      
        }

        // 初始化vue
        var content_area_data = {
            timeRange: [],
            model:{
                search: "",
                actList: [],
            },
            addModal:{
                name: '',
            },
        };
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            methods: {
                getList: function(searchdata){
                    self = this;
                    $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('order/Member/getActList')}}",
                        data: searchdata,
                        success: function(resp){
                            // console.log(resp);
                            self.model.search = resp.search;
                            self.model.actList = resp.actList;
                            for(var prop in self.model.actList){
                                if (self.model.actList[prop]['online'] == 1){
                                    self.model.actList[prop]['online'] = true;
                                }else{
                                    self.model.actList[prop]['online'] = false;
                                }
                            }
                        },
                    });
                },
                onlineChange: function(item){
                    return $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('order/Member/changeOnline')}}",
                        data: item,
                        success: function(resp){
                            if(resp.code==1){
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            if(resp.code==0){ location.reload(); }
                        },
                    });
                },
                multiOnline: async function() {
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        multiId['online'] = true;
                        await self.onlineChange(multiId);
                    }
                },
                multiOffline: async function() {
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        multiId['online'] = false;
                        await self.onlineChange(multiId);
                    }
                },
                delAct: function(item){
                    return $.ajax({
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url : "{{url('order/Member/delAct')}}",
                        data: item,
                        success: function(resp){
                            if(resp.code==1){
                                $('#act_'+item["id"]).css('display','none');
                                bg_class = 'bg-success';
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                        },
                    });
                },
                multiDelete: async function() {
                    self = this;
                    var multiIdArray = self.getMultiId();
                    for(var i=0;i<multiIdArray.length;i++){
                        multiId = multiIdArray[i];
                        await self.delAct(multiId);
                    }
                    location.reload();
                },
                getMultiId: function() {
                    self = this;
                    var multiIdArray = [];
                    $('.actCheckbox').each(function () {
                        if($(this).prop("checked")){
                            multiIdArray.push(self.model.actList[Number($(this).attr('alt'))]);
                            $(this).prop("checked", false);
                        }
                    });
                    $('.activityCheckboxAll').prop("checked", false);
                    return multiIdArray;
                },

                do_add: function(){
                    self = this;
                    if(!self.addModal.name){alert('請輸入名稱');return;}

                    var postData = Object.assign({}, self.addModal);
                    $.ajax({
                        method: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: 'json',
                        url: "{{url('order/Member/doCreate')}}",
                        data: postData,
                    }).success(function(resp){
                        if(resp.code==1){
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-success"]});
                            setTimeout(()=>{ location.href=resp.url; }, 300)
                        }else{
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", "bg-danger"]});
                        }
                    }).error(function(){
                    })//error
                }, 
            },
        });
        content_areaVM.getList({ searchKey: searchKey })
    </script>

    <script>
        $(function() {
            $(document).click(function() {
                $('.edit-item').fadeOut();
            })
            $('.edit').click(function(event) {
                event.stopPropagation();
            })
        });
        $("input.date").daterangepicker({locale: {format: 'YYYY-MM-DD'}});
    </script>
@endsection