@extends('admin.Public.aside')
@section("title")B會員功能管理-修改會員瀏覽商品設定@endsection

@section("content")
    <div id="content">

    <ul id="title" class="brand-menu">
            <li><a href="###">B會員功能管理 </a></li>
            <li><a href="###">修改會員瀏覽商品設定</a></li>
          
        </ul>
        <a class="back sendbtn" href="{{url('order/member/product_view')}}">
            <span class="bi bi-arrow-left"></span>
        </a>
        <form action="{{url('order/Member/update')}}" name="actForm" method="post" enctype="multipart/form-data">
            @csrf
            <input type="hidden" value="{{$data['singleData']['id']}}" name="id">
            <div class="top-box">
                <div class="item">
                    <p class="name">名稱：</p>
                    <input type="text" name="name" value="{{$data['singleData']['name']}}" class="form-control" class="w-c100">
                </div>
                <a class="btn sendbtn" onclick="actForm.submit();">更新</a>
            </div>
        </form>
        <hr>
        <h3 v-text="lang_word" class="main-title"></h3>
        <div class="frame" style="min-height: 40px;">
            <distributors_area ref="distributors_area" :my_distributor_id="{{$data['my_distributor_id']}}" :need_all="true"></distributors_area>
        </div>

        @include('admin/act/product_selector')
    </div>
@endsection

@section("ownJS")
    <script>
        // 供應商切換模組串接-開始
        function init_after_get_distributors(){
            content_areaVM.getActProd();
            content_areaVM.getList();
        }
        function click_distributor_emit(id){
            content_areaVM.distributors_current = id;
            
            content_areaVM.getList();
        }
        // 供應商切換模組串接-結束
    </script>
    <script>
        var content_area_data = {
            model: {
                location: '0',
                act: {},
                series: [],
                cate2: {},
                cateProd: [],
                actProd: [],
            },
            actId: {{$data['actId']}},
            langId: {{$data['langId']}},
            showDiscType: [false,true,false,false,false],

            lang_word: '',
            distributors_current: "{{$data['my_distributor_id']}}",

            catProd: "",
            currCates: [],
        };
        var content_areaVM = new Vue({
            el: '#content_area',
            data: content_area_data,
            methods: {
                getActProd: function(){
                    self = this;
                    Vue.set(self.model, 'actProd', []);
                    $.ajax({
                        type: "post",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        url : "{{url('order/Member/getActProd')}}",
                        data: {
                            actId:self.actId,
                            langId:self.langId,
                        },
                        success: function(resp){
                            // console.log(resp.actProd);
                            for (var prop in resp.actProd){
                                resp.actProd[prop]['select'] = false;
                                Vue.set(self.model.actProd, prop, resp.actProd[prop]);
                            }
                            self.lang_word = resp.lang ? resp.lang.lang_word : '';
                        }
                    });
                },
                getList: function(){
                    self = this;
                    self.model.act          = {};
                    self.model.series       = [];
                    self.model.cate2        = {};
                    self.model.cateProd     = [];
                    $.ajax({
                        type: "post",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        url : "{{url('admin/Product/getList')}}",
                        data: {
                            actId:self.actId,
                            langId:self.langId,
                            distributor_id: self.distributors_current,
                        },
                        success: function(resp){
                            // console.log(resp);
                            self.model.series = resp.message;
                            self.currCates = [];
                        }
                    });
                },
                changeCate: function(item, index){
                    self = this;
                    self.currCates = self.currCates.slice(0, index);
                    if(index==0){
                        self.getCate(item);
                    }else{
                        self.getCate2(item);
                    }
                },
                getCate: function(item){
                    // console.log(item.id);
                    self = this;
                    self.model.series = [];
                    $.ajax({
                        type: "post",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        url : "{{url('admin/Product/getCate')}}",
                        data: {
                            seriesId:item.id,
                            langId:self.langId,
                            distributor_id: self.distributors_current,
                        },
                        success: function(resp){
                            resp['first'] = true;
                            // console.log(resp)
                            self.getCateProd(resp);

                            self.model.cate2 = resp.cate;
                            self.currCates.push(item);
                            for (var prop in self.model.series){
                                self.model.series[prop]['select'] = false;
                            }
                        }
                    });
                },
                getCate2: function(item){
                    // console.log(item.id);
                    self = this;
                    self.model.cate2 = {};
                    $.ajax({
                        type: "post",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        url : "{{url('admin/Product/getCate2')}}",
                        data: {
                            seriesId:item.id,
                            langId:self.langId,
                        },
                        success: function(resp){
                            self.model.cate2 = resp.cate;
                            self.currCates.push(item);

                            if(resp.getCateProd == 1){
                                self.model.cate2 = {};
                            }

                            self.getCateProd(resp);
                        }
                    });
                },
                getCateProd: function (item){
                    // console.log(seriesId);
                    self = this;
                    $.ajax({
                        type: "post",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        url : "{{url('order/Member/getCateProd')}}",
                        data: {
                            cateId:item.seriesId, 
                            first:item.first,
                            langId:self.langId,
                            actId:self.actId, 
                        },
                        success: function(resp){
                            self.catProd = item.title;
                            for (var prop in resp.productinfo){
                                resp.productinfo[prop]['select'] = false;
                            }
                            self.model.cateProd = resp.productinfo;
                        }
                    });
                },
                selDiscType: function(typeId){
                    self = this;
                    for(var prop in self.showDiscType){
                        self.showDiscType[prop] = false;
                        if (prop == typeId){
                            self.showDiscType[prop] = true;
                        }
                    }
                },
                insertAct: function(){
                    self = this;
                    $('#block_block').show();
                    //console.log(self.model.series);
                    //console.log('--檢查--');
                    for(var prop in self.model.cateProd){
                        self.model.cateProd[prop]['actId'] = self.actId;
                    }
                    $.ajax({
                        type: "post",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        url : "{{url('order/Member/insertAct')}}",
                        data: {
                            actId:self.actId, 
                            langId:self.langId, 
                            actData:JSON.stringify(self.model),
                        },
                        success: function(resp){
                            if (resp.code){
                                Vue.toasted.show('',{duration:1500});
                                bg_class = 'bg-success';
                                self.getActProd();
                                self.getList();
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            $('#block_block').hide();
                        },
                        error: function(){
                            Vue.toasted.show('網路異常', {duration:1500, className: ["toasted-primary", 'bg-danger']});
                            $('#block_block').hide();
                        },
                    });
                },
                delActProd: function(){
                    self = this;
                    $('#block_block').show();
                    for(var prop in self.model.cateProd){
                        self.model.actProd[prop]['actId'] = self.actId;
                    }
                    // console.log(self.model.actProd);
                    // return;
                    $.ajax({
                        type: "post",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        url : "{{url('order/Member/delActProd')}}",
                        data: {
                            actId:self.actId, 
                            langId:self.langId, 
                            cateProd:JSON.stringify(self.model.actProd),
                        },
                        success: function(resp){
                            if (resp.code){
                                Vue.toasted.show('',{duration:1500});
                                bg_class = 'bg-success';
                                self.getActProd();
                                self.getList();
                            }else{
                                bg_class = 'bg-danger';
                            }
                            Vue.toasted.show(resp.msg, {duration:1500, className: ["toasted-primary", bg_class]});
                            $('#block_block').hide();
                        },
                        error: function(){
                            Vue.toasted.show('網路異常', {duration:1500, className: ["toasted-primary", 'bg-danger']});
                            $('#block_block').hide();
                        },
                    });
                },
                selectAll: function(){
                    self = this;
                    for(var prop in self.model.actProd){
                        item = self.model.actProd[prop];
                        item['select'] = true;
                        Vue.set(self.model.actProd, prop, item);
                    }
                },
                selectAllProd: function(){
                    self = this;
                    for(var prop in self.model.cateProd){
                        item = self.model.cateProd[prop];
                        item['select'] = true;
                        Vue.set(self.model.cateProd, prop, item);
                        self.model.cateProd[prop]['select'] = true;
                    }
                    
                },
                change_actProd_check: function(index, item){
                    self = this;
                    item.select = item.select ? false : true;
                    Vue.set(self.model.actProd, index, item);
                },
                change_cateProd_check: function(index, item){
                    self = this;
                    item.select = item.select ? false : true;
                    Vue.set(self.model.cateProd, index, item);
                },

                onFileSelect: function($event){
                    var file = ($event.srcElement || $event.target).files[0];
                    content_areaVM.img = URL.createObjectURL(file)
                },
            },
        });
	</script>
@endsection