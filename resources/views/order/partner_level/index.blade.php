@extends('admin.Public.aside')
@section('title')合夥人等級設定 - B會員功能管理@endsection

@section('css')
@endsection

@section('content')
<div id="content">
    <ul id="title" class="brand-menu">
        <li><a href="###">B會員功能管理</a></li>
        <li><a href="###">合夥人等級設定</a></li>
    </ul>
    <div class="frame">
        <a href="###" class="btn clearbtn mr-3" @click="open_add_modal(-1)"><span class="bi bi-plus-lg add"></span> 新增</a>
    </div>
    <div class="edit_form">
        <table class="table table-rwd" style="min-width:1200px; width:1200px;">
            <thead>
                <tr>
                    <th style="width: 75px;">
                        <!-- <input type="checkbox" class="activityCheckboxAll" onclick="$('.table input[class=productinfoCheckbox]').prop('checked', ($(this).is(':checked')?true:false))"style="cursor:pointer;"> -->
                        序號
                    </th>
                    <th style="width: 150px;">{{Lang::get('名稱')}}</th>
                    <th style="width: 150px;">功德圓滿點數倍率</th>
                    <th style="width: 150px;">累積投資金額</th>
                    <th style="width: 150px;">合夥平級獎勵可回饋比率</th>
                    <th style="width: 150px;">廣告訂單加權</th>
                    <th style="width:150px;">{{Lang::get('操作')}}</th>
                </tr>
            </thead>
            <tbody>
                <tr></tr>
                <tr v-for="(vo, vo_idx) in items" :id="'items_' +vo.id">
                    <td>
                        <!-- <input type="checkbox" class="productinfoCheckbox" :alt="vo.id"> -->
                        <span v-text="vo_idx+1"></span>
                    </td>
                    <td>
                        <a href="###" @click="open_add_modal(vo_idx)">
                            <span v-text="vo.name"></span>
                        </a>
                    </td>
                    <td><span v-text="vo.ratio"></span></a>
                    <td><span v-text="vo.contribution"></span></a>
                    <td><span v-text="vo.partner_bonus_ratio"></span>%</a>
                    <td><span v-text="vo.orderform_ad_weight"></span></a>
                    </td>
                    <td>
                        <button class="btn btn-danger pt-1 pb-1 pl-2 pr-2" @click="del_item(vo_idx)">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- 新增/修改商品分類開始 -->
    <a id="functionModal_btn" data-toggle="modal" data-target="#functionModal" class="d-none">跳出視窗</a>
    <div class="modal main-modal fade" id="functionModal" role="dialog" aria-labelledby="functionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document" id="Box">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title">等級內容</h5>
                </div>
                <form name="boxForm" method="post" target="boxFormIframe" enctype="multipart/form-data">
                    @csrf
                    <div class="modal-body">
                        <div class="row m-0">
                            <div class="col-lg-6 col-12">
                                <div class="item">
                                    等級名稱
                                    <input v-model="detail.name" type="text" class="form-control">
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <hr>
                            </div>
                            <div class="col-lg-12 col-12 mb-3">
                                <div class="input-group"> 
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">功德圓滿點數倍率</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.ratio" placeholder="ex:1.25" min="0">
                                    <!-- <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div> -->
                                </div>
                            </div>
                            <div class="col-lg-12 col-12 mb-0">
                                <div class="input-group"> 
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">累積投資金額</div>
                                    </div>
                                    <input type="number" step="1" class="form-control text-right"
                                        v-model="detail.contribution" placeholder="ex:400" min="0">
                                    <!-- <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div> -->
                                </div>
                            </div>
                            <div class="col-lg-12 col-12 mb-3">
                                <div class="text-danger">(數字愈小排越前，等級也越低)</div>
                            </div>
                            <div class="col-lg-12 col-12 mb-3">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">合夥平級獎勵可回饋比率</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.partner_bonus_ratio" placeholder="ex:25" min="0" max="100">
                                    <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 col-12 mb-3">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">廣告訂單加權</div>
                                    </div>
                                    <input type="number" step="0.01" class="form-control text-right"
                                        v-model="detail.orderform_ad_weight" placeholder="ex:32" min="0">
                                    <!-- <div class="input-group-apend">
                                        <div class="input-group-text">%</div>
                                    </div> -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn sendbtn" @click="form_submit">儲存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- 新增/修改商品分類結束 -->
</div>
@endsection
@section('ownJS')
    <script>
        var empty_detail = {
            id: 0,
            name: '',

            ratio:'',
            contribution:'',
            partner_bonus_ratio:'',
            orderform_ad_weight:'',
        };
        var content_data = {
            items:[],
            detail: JSON.parse(JSON.stringify(empty_detail)),
        };
        var contentVM = new Vue({
            el:'#content',
            data: content_data,
            async created(){
                $('#block_block').show()
                var resp = await this.load_data();
                this.items = resp.db_data;
                $('#block_block').hide();
            },
            methods:{
                load_data(){
                    return $.ajax({
                        type: "GET",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        dataType: "json",
                        data:{
                        },
                        url: "{{url('PartnerLevel/get_data')}}",
                    });
                },
                open_add_modal(idx=-1){
                    if(idx<-1 || idx>=this.items.length){ return; }
                    if(idx==-1){ /*新增*/
                        this.detail = JSON.parse(JSON.stringify(empty_detail));
                    }else{ /*修改*/
                        this.detail = JSON.parse(JSON.stringify(this.items[idx]));
                    }
                    $('#functionModal_btn').click();
                },
                async form_submit(){
                    // console.log(this.detail);
                    $('#block_block').show()
                    try {
                        var resp = await $.ajax({
                            type: "POST",
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            dataType: "json",
                            data:{
                                detail: JSON.parse(JSON.stringify(this.detail)),
                            },
                            url: "{{url('PartnerLevel/save_data')}}",
                        });
                        if(resp.code==1){
                            Vue.toasted.show(resp.msg.msg, vt_success_obj);
                            if(this.detail.id==0){/*新增成功*/
                                var resp = await this.load_data();
                                this.items = resp.db_data;

                                $('#functionModal').modal('hide');
                                this.detail = JSON.parse(JSON.stringify(empty_detail));
                            }else{ /*編輯成功*/
                                // for (let idx = 0; idx < this.items.length; idx++) {
                                //     const element = this.items[idx];
                                //     if(element.id==this.detail.id){
                                //         this.items[idx] = JSON.parse(JSON.stringify(this.detail));
                                //         break;
                                //     }
                                // }
                                var resp = await this.load_data();
                                this.items = resp.db_data;
                            }
                            this.$forceUpdate();
                        }else{
                            Vue.toasted.show(resp.msg, vt_error_obj);
                        }
                    } catch (error) {
                        // console.log(error);
                        Vue.toasted.show(error.statusText, vt_error_obj);
                    }
                    $('#block_block').hide();
                },
                async del_item(idx){
                    if(idx<0 || idx>=this.items.length){ return; }
                    if(!confirm("{{Lang::get('確定刪除嗎')}}")){ return; }
                    var target_id = this.items[idx].id;
                    try {
                        var resp = await $.ajax({
                            type: "POST",
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            dataType: "json",
                            data:{
                            },
                            url: "{{url('PartnerLevel/delete_data')}}",
                            data:{
                                id: target_id,
                            },
                        });
                        if(resp.code==1){
                            this.items.splice(idx, 1);
                            Vue.toasted.show(resp.msg, vt_success_obj);
                        }else{
                            Vue.toasted.show(resp.msg, vt_error_obj);
                        }
                    } catch (error) {
                        // console.log(error);
                        Vue.toasted.show(error.statusText, vt_error_obj);
                    }
                    $('#block_block').hide();
                }
            },
        });
    </script>
@endsection