@extends('home.Public.mainTpl')
@section('title')@endsection

@section('css')
@endsection

@section('mycode')
@endsection

@section('content')
<section class="directoryRow">
    <div class="container">
        <ul>
            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
            <li><a href="{{url('Cart/choose_shop')}}">{{Lang::get('購物車')}}</a></li>
        </ul>
    </div>
</section>
<section class="container max-wideVersion productPublic">
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox" class="shopcarInforBox">
        <div class="shopcarContentBox  memberContentBox">
            <div class="memberTop">
                <div class="titleBox">
                    <div class="title">
                        <h3>{{Lang::get('選擇購物商品')}}</h3>
                    </div>
                </div>
            </div>
            <div class="memberMiddle memberitems">
                <div class="orderDetailsBox shoppingCartBox">                
                    <form name="cartform" action="{{url('cart/do_choose_shop')}}" method="post">
                        @csrf
                        @if (empty($data['cartData_shop']) == false)
                        @foreach($data['cartData_shop'] as $shop)
                            @if($shop['shop'])
                                <div class="name-head">
                                    <a href="{{url('product/distributor')}}?id={{$shop['shop']['id']}}" target="_blank">
                                        @include('home/Public/svg_shop_icon')
                                        {{$shop['shop']['shop_name']}}
                                    </a>
                                </div>
                            @else
                                <div class="name-head">
                                    <a href="{{url('product/product')}}" target="_blank">
                                        @include('home.Public.svg_shop_icon') {{Lang::get('平台')}}
                                    </a>
                                </div>
                            @endif
                            <table class="orderTable table table-striped table-rwd proIntro" id="shop_table_{{$shop['shop']['id'] ?? ''}}">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th style="width: 50px">
                                            <input class="check_arrow select_all" type="checkbox"  shop_id="{{$shop['shop']['id'] ?? ''}}">
                                        </th>
                                        <th style="width: 120px">{{Lang::get('圖片')}}</th>
                                        <th>{{Lang::get('品名')}}</th>
                                        <th style="width: 80px">{{Lang::get('數量')}}</th>
                                        <th style="width: 140px">{{Lang::get('單價')}}</th>
                                        <th style="width: 140px">{{Lang::get('總價')}}</th>
                                        <th style="width: 50px">{{Lang::get('操作')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(empty($shop['products']) == false)
                                    @foreach($shop['products'] as $key => $vo)
                                        <!-- 商品資料 -->
                                        <tr class="exam_tr">
                                            <td data-th="{{Lang::get('選擇')}}">
                                                <input class="check_arrow" type="checkbox" name="select_prods[{{$vo['key']}}]" value="{{$vo['num']}}">
                                                <!-- {{$vo['type_product_id']}} 編號 -->
                                            </td>
                                            <td data-th="{{Lang::get('圖片')}}">
                                                <div class="smallProImg">
                                                    <img src="{{__PUBLIC__}}{{$vo['info_pic1']}}" alt="">
                                                    @if(!empty(config('control.close_function_current')['網紅列表']) && substr($vo['key_type'],0,3)== 'kol')
                                                        <span class="prod_tag kol_tag">{{Lang::get('網紅推薦')}}</span>
                                                    @endif
                                                    @if(!empty(config('control.close_function_current')['加價購設定']) && substr($vo['key_type'],0,3)== 'add')
                                                        <span class="prod_tag add_tag">{{Lang::get('加價購')}}</span>
                                                    @endif
                                                </div>
                                            </td>
                                            <td data-th="{{Lang::get('品名')}}">
                                                <span class="productNameBox">
                                                    <span class="productName" style="pointer-events: none;">
                                                        {{$vo['info_title']}}
                                                        @if($vo['type_title'] != '')
                                                        - {{$vo['type_title']}}
                                                        @endif
                                                    </span>
                                                    @if(config('control.thirdpart_money')==1 && config('control.control_card_pay')==1 && $vo['card_pay']==0)
                                                    <span class="productName" style="pointer-events: none; color:red"
                                                        disabled>&nbsp;&nbsp;{{Lang::get('不可刷卡')}}</span>
                                                    @endif
                                                </span>
                                            </td>
                                            
                                            <td data-th="{{Lang::get('數量')}}">
                                                <input type="number" id="examinee_{{$vo['type_id']}}_num" class="num" min="1"
                                                       value="{{$vo['num']}}"
                                                       onchange="cart_change_num_choose_shop(this, '{{$vo['type_id']}}')">
                                            </td>
                                            <td data-th="{{Lang::get('單價')}}" class="">{{config('extra.shop.dollar_symbol')}}<span>{{$vo['countPrice']}}</span></td>
                                            <td data-th="{{Lang::get('總價')}}" class="price sum_price">
                                                {{config('extra.shop.dollar_symbol')}}<span id="cart_tol_{{$vo['type_id']}}" value="{{$vo['countPrice']}}">{{$vo['countPrice'] * $vo['num']}}</span>
                                            </td>
                                            <!-- id="cart_tol_{{$vo['type_id']}}" -->
                                            <td data-th="{{Lang::get('操作')}}">
                                                <a class="deleteBtn" onclick="deleteCtrl_choose_shop(this, '{{$vo['type_id']}}');"><i class="icon-bin"></i></a>
                                            </td>
                                        </tr>                
                                    @endforeach
                                    @endif
                                </tbody>
                            </table>
                        @endforeach
                        @endif

                        <div class="text-right">
                            <span class="mr-4">
                                {{Lang::get('已勾選商品金額')}}：
                                {{config('extra.shop.dollar_symbol')}}
                                <span id="selected_total" class="selected_total"></span>
                            </span>
                            <button class="modalBtn submitBtn border-0 cursor-pointer">{{Lang::get('前往結帳')}}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
</section>
@endsection

@section('Modal')
@endsection


@section('ownJS')
    <script>
        var cart_session = 'cart_all';
        function cart_change_num_choose_shop($this, product_id){
            console.log($this);
            cart_change_num($this, product_id).then(()=>{
                init_selected_total();
            });
        }
        function deleteCtrl_choose_shop($this, product_id){
            deleteCtrl($this, product_id).then(()=>{
                init_selected_total();
            });
        }
    </script>
    @include('home.cart.cart_ctrl_js')

    <script type="text/javascript">
        $('.select_all').on('click', (e)=>{
            var target = e.currentTarget;
            setTimeout(()=>{
                const val = $(target).prop('checked');
                const shop_id = $(target).attr('shop_id');
                child_checks = $('#shop_table_'+shop_id).find('tbody input[type="checkbox"]');
                for (var i = 0; i < child_checks.length; i++) {
                    $(child_checks[i]).prop('checked', val);
                }
                init_selected_total();
            }, 10);
        });
        $('form[name="cartform"] tbody tr').find('input[type="checkbox"]').on('click', ()=>{
            init_selected_total();
        })
        $('input[type="number"]').on('change', ()=>{ 
            setTimeout(()=>{init_selected_total();},300)
        });
        function init_selected_total() {
            const products_tr = $('form[name="cartform"] tbody tr');
            let selected_total = 0;
            for (var i = 0; i < products_tr.length; i++) {
                val = $(products_tr[i]).find('input[type="checkbox"]').prop('checked');
                if(val){
                    price = $(products_tr[i]).find('td.sum_price span').html();
                    price = Number(price);
                    selected_total += price;
                }
            }
            $('#selected_total').html(selected_total);
        }
        init_selected_total();
    </script>
@endsection

