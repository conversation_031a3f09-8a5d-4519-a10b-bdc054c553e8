@extends('home.Public.mainTpl')
@section('title'){{$data['frontend_menu']['findorder']['name']}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
    <style type="text/css">
        .findorderForm .item_row .form-group{
            display: flex;
            align-items: start;
            flex-direction: column;
        }
        .findorderForm .item_row .form-group:last-child{
            justify-content: center;
        }
    </style>
@endsection

@section('content')
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['findorder']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}/{{$data['frontend_menu']['findorder']['pic']}});">
            <div class="container">
                <h2 class="page-title" style="color:{$data['frontend_menu']['findorder']['text_color']}">
                    {{$data['frontend_menu']['findorder']['name']}}</h2>
                <!-- <span class="enText">{{$data['frontend_menu']['findorder']['en_name']}}</span> -->
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Findorder/findorder')}}">{{$data['frontend_menu']['findorder']['name']}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion min-heightVersion aboutUsBox">
        <div class="tabRow">
            <div class="tab-pane tabContent" id="contactUs">
                <div class="content contact">
                    <form class="findorderForm" id="findorderForm">
                        @csrf
                        <div class="memberMiddle">
                            <div class="row">
                                <div class="form-group col-md-4 col-12">
                                    <label :for="user_name" class="col-form-label">
                                        {{Lang::get('姓名')}}<span class="smallText">*</span>
                                    </label>
                                    <input type="text" class="form-control" v-model="user_name" :id="user_name">
                                </div>
                                <div class="form-group col-md-4 col-12">
                                    <label :for="user_phone" class="col-form-label">
                                        {{Lang::get('手機')}}<span class="smallText">*</span>
                                    </label>
                                    <input type="text" class="form-control" v-model="user_phone" :id="user_phone">
                                </div>
                                <div class="form-group col-md-4 col-12">
                                    <label :for="user_email" class="col-form-label">
                                        {{Lang::get('信箱')}}<span class="smallText">*</span>
                                    </label>
                                    <input type="email" class="form-control" v-model="user_email" :id="user_email">
                                </div>
                            </div>
                            <div class="item_row row" v-for="(product, index) in products">
                                <div class="form-group col-md-3 col-6">
                                    <label :for="'pro_name_' + index" class="col-form-label">
                                        {{Lang::get('品名')}}<span class="smallText">*</span>
                                    </label>
                                    <input type="text" class="form-control" v-model="product.name" :id="'pro_name_' + index">
                                    <span class="smallText">{{Lang::get('請務必提供詳細的商品名稱')}}</span>
                                </div>
                                <div class="form-group col-md-1 col-3">
                                    <label :for="'pro_unit_' + index" class="col-form-label">
                                        {{Lang::get('單位')}}
                                    </label>
                                    <input type="text" class="form-control " v-model="product.unit" :id="'pro_unit_' + index">

                                </div>
                                <div class="form-group col-md-1 col-3">
                                    <label :for="'pro_num_' + index" class="col-form-label">
                                        {{Lang::get('數量')}}<span class="smallText">*</span>
                                    </label>
                                    <input type="number" class="form-control" min="1" v-model="product.num" :id="'pro_num_' + index">
                                    <span class="smallText">{{Lang::get('數量不可小於1')}}</span>
                                </div>
                                <div class="form-group col-md-3 col-12">
                                    <label :for="'pro_img_' + index" class="col-form-label">{{Lang::get('圖片')}}</label>
                                    <div class="row">
                                        <img class="col-4 w-100" :src="product.img">
                                        <input class="col-8" type="file" @change="chage_img(index)" :id="'pro_img_' + index">
                                    </div>
                                </div>
                                <div class="form-group col-md-3 col-9">
                                    <label :for="'pro_note_' + index" class="col-form-label">
                                        <div class="formJustify">{{Lang::get('備註')}}</div>
                                    </label>
                                    <textarea class="form-control" rows="3" v-model="product.note" :id="'pro_note_' + index"></textarea>
                                </div>
                                <div class="form-group col-md-1 col-3">
                                    <button type="button" class="btn btn-danger" @click="del_p(index)">{{Lang::get('刪除')}}</button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group col-12">
                                    <a class="btn use-btn" @click="add_p">{{Lang::get('添加商品')}}</a>
                                    <span class="smallText">{{Lang::get('最多詢問10個商品')}}</span>
                                </div>

                                <div class="form-group col-12">
                                    <label for="su_verifyCode">{{Lang::get('驗證碼')}}</label>
                                    <div class="verification" id="verification_contact_prod"></div>
                                </div>
                                <div class="form-group justify-content-center">
                                    <a id="check_btn_contact_prod" class="btn use-btn">{{Lang::get('送出')}}</a>
                                </div>
                                <span class="smallText text-center w-100">{{Lang::get('詢問後可至會員後台查看店家回覆及歷史紀錄')}}</span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection



@section('ownJS')
    <script src="https://static.runoob.com/assets/jquery-validation-1.14.0/dist/jquery.validate.min.js"></script>
    <script src="https://static.runoob.com/assets/jquery-validation-1.14.0/dist/localization/messages_zh.js"></script>
    <script type="text/javascript">
        // Vue.toasted.show('修改成功',{
        //   duration:1500, /*存續時間*/
        //   className:['toasted-primary','bg-success'], /*控制class*/
        // });
    </script>

    <script type="text/javascript">
        findorderForm_data = {
            empty_p: {
                name: "",
                unit: "",
                num: 1,
                img: "",
                note: "",
            },
            user_name: "{{$data['user_name']}}",
            user_phone: "{{$data['user_phone']}}",
            user_email: "{{$data['user_email']}}",
            products:[  
            ],
        };
        var findorderFormVM = new Vue({
            el: '#findorderForm',
            data: findorderForm_data,
            watch: {
                products: {
                    handler: function (val) {
                        for (var i = 0; i < val.length; i++) {
                            if( val[i].num < 1) {
                                Vue.toasted.show("{{Lang::get('數量不可小於1')}}", vt_error_obj);
                                this.products[i].num = 1;
                            }
                        }
                    },
                    deep: true,
                },
            },
            methods: {
                add_p: function(){
                    if(this.products.length<10){
                        new_p = Object.assign({}, this.empty_p);
                        this.products.push(new_p);
                    }else{
                        Vue.toasted.show("{{Lang::get('最多詢問10個商品')}}", vt_error_obj);
                    }
                },
                del_p: function(index){
                    if(this.products.length>1){
                        this.products.splice(index, 1);
                    }else{
                        Vue.toasted.show("{{Lang::get('最少詢問1個商品')}}", vt_error_obj);
                    }
                },
                chage_img: function(index){
                    self = this;
                    const [file] = event.currentTarget.files
                    if (file) {
                        var reader = new FileReader();
                        reader.onload = function (data) {
                            self.products[index].img = data.target.result;
                            console.log(data.target.result);
                        };
                        reader.readAsDataURL(file);
                    }
                },
                submit: function(){
                    self = this;
                    if(self.user_name==""){
                        Vue.toasted.show("{{Lang::get('請輸入姓名')}}", vt_error_obj);
                        return;
                    }
                    if(self.user_phone==""){
                        Vue.toasted.show("{{Lang::get('請輸入手機')}}", vt_error_obj);
                        return;
                    }
                    if(self.user_email==""){
                        Vue.toasted.show("{{Lang::get('請輸入信箱')}}", vt_error_obj);
                        return;
                    }
                    for (var i = 0; i < self.products.length; i++) {
                        if(self.products[i].name==""){
                            Vue.toasted.show("{{Lang::get('有商品未填寫品名')}}", vt_error_obj);
                            return;
                        }
                        if(self.products[i].num==""){
                            Vue.toasted.show("{{Lang::get('有商品未填寫數量')}}", vt_error_obj);
                            return;
                        }   
                    }

                    $.ajax({
                        type: "POST",
                        headers: {
                            'X-CSRF-Token': csrf_token 
                        },
                        async: true,
                        datatype: 'json',
                        data: {
                            user_name: self.user_name,
                            user_phone: self.user_phone,
                            user_email: self.user_email,
                            products: self.products,
                        },
                        url: "{{url('Findorder/doFindorder')}}",
                        success: function (result) {
                            bg_class_obj = result.code == 1 ? 'vt_success_obj' : 'vt_error_obj';
                            Vue.toasted.show(result.msg, bg_class_obj);
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        },
                        error: function (xhr) {
                            Vue.toasted.show("{{Lang::get('操作失敗')}}", vt_error_obj);
                            console.error(xhr);
                            return;
                        }
                    });
                },
            },
        });
        findorderFormVM.add_p();
    </script>
    <script type="text/javascript">
        /*幫我找貨驗證碼*/
        $('#verification_contact_prod').codeVerify({
            type: 1,
            // arith:24,
            width: '100%',
            height: '40px',
            fontSize: '30px',
            codeLength: 4,
            btnId: 'check_btn_contact_prod',
            ready: function () {},
            success: function () {
                // alert('驗證成功');
                findorderFormVM.submit();
            },
            error: function () {
                Vue.toasted.show("{{Lang::get('驗證失敗')}}", vt_error_obj);
            }
        });
    </script>
@endsection