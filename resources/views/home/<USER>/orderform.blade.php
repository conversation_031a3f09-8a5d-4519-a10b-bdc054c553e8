@extends('home.Public.mainTpl')
@section('title'){{Lang::get('訂單列表')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Orderform/orderform')}}">{{Lang::get('訂單列表')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('訂單查看')}}</h3>
                                </div>
                            </div>
                            <ul class="nav tabNavBox justify-content-center">
                                <li class="nav-item active">
                                    <a href="{{url('Orderform/orderform')}}">{{Lang::get('訂單')}}</a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{url('Orderform/history')}}">{{Lang::get('歷史紀錄')}}</a>
                                </li>
                           
                            </ul>
                        </div>
                        <div class="memberMiddle">
                            <form action="{{url('Orderform/orderform')}}" method="get" enctype="multipart/form-data">
                                @csrf
                                <div class="time-select-box">
                                    <p class="mb-2">時間搜尋</p>
                                    <div class="d-flex align-items-center mb-4">
                                        <input name="date_s" type="date" class="form-control col-lg-3" value="{{request()->get('date_s')}}">
                                        ~
                                        <input name="date_e" type="date" class="form-control col-lg-3" value="{{request()->get('date_e')}}">
                                        <button type="submit" class="togo btn ml-2 ml-lg-2 mt-0 mt-lg-0">{{Lang::get('搜尋')}}</button>
                                    </div>
                                </div>
                            </form>
                            <div class="couponBox">
                                <table class="orderTable table table-striped table-bordered table-rwd">
                                    <thead>
                                        <tr class="tr-only-hide">
                                            <th>{{Lang::get('購買日期')}}</th>
                                            <th>{{Lang::get('訂單編號')}}</th>
                                            <th>{{Lang::get('總價')}}</th>
                                            <th style="width: 75px;">{{Lang::get('付款方式')}}</th>
                                            <th style="width:180px;">{{Lang::get('收款狀態')}}</th>
                                            <th style="width: 75px;">{{Lang::get('出貨狀態')}}</th>
                                            <th style="width:200px;">{{Lang::get('賣家備註')}}</th>
                                            <th style="width: 90px;">{{Lang::get('操作')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(empty($data['orderform']) == false)
                                        @foreach($data['orderform'] as $vo)
                                            <tr>
                                                <td data-th="{{Lang::get('購買日期')}}">{{date('Y/m/d H:i:s', $vo->create_time)}}</td>
                                                <td data-th="{{Lang::get('訂單編號')}}"><a href="{{url('Orderform/orderform_c') . '?id=' . $vo->order_number}}">{{$vo->order_number}}</a></td>
                                                <td data-th="{{Lang::get('總價')}}">{{config('extra.shop.dollar_symbol')}}{{number_format($vo->total)}}</td>
                                                <td data-th="{{Lang::get('付款方式')}}">
                                                    @if(isset($data['pay_fee_dict']['k_'.$vo->payment]))
                                                        {{$data['pay_fee_dict']['k_'.$vo->payment]['name']}}
                                                    @else
                                                        {{$vo->payment}}
                                                    @endif
                                                </td>
                                                <td data-th="{{Lang::get('收款狀態')}}">
                                                    <form action="{{url('Cart/redirect3Next')}}" method="post" class="d-inline-block">
                                                        @csrf
                                                        @if($vo->payment==2)
                                                            @if($vo->receipts_state == 1 || $vo->report_check_time != '')
                                                                {{$vo->report}}&nbsp;&nbsp;&nbsp;{{Lang::get('已確認')}}
                                                            @else
                                                                @if($vo->report == '')
                                                                    <div class="input-group transferBox">
                                                                        <div class="custom-file">
                                                                            <input type="text" class="form-control" placeholder="{{Lang::get('回報後五碼或姓名')}}" id="pc_{{$vo->id}}" onfocus="javascript:this.parentElement.parentNode.className+=' write';" onblur="javascript:this.parentElement.parentNode.classList.remove('write');" style="font-size: 12px;">
                                                                        </div>
                                                                        <div class="input-group-append" style="align-items: center">
                                                                            <button class="btn more" type="button" alt="pc_{{$vo->id}}" onclick="setReportNumber(this);">{{Lang::get('送出')}}</button>
                                                                        </div>
                                                                    </div>
                                                                @else
                                                                    {{$vo->report}}&nbsp;&nbsp;&nbsp;{{Lang::get('待確認')}}
                                                                @endif
                                                            @endif

                                                        @else
                                                            {{config('extra.order.RECEIPTS_STATE')[$vo->receipts_state]}}
                                                        @endif
                                                        <!-- //////////// -->
                                                        <input type="hidden" name="id" value="{{$vo->id}}"/>
                                                        <input type="hidden" name="total" value="{{$vo->total}}"/>
                                                        <input type="hidden" name="order_number" value="{{$vo->order_number}}"/>
                                                        <input type="hidden" name="pay_way" value="{{$vo->payment}}"/>
                                                        <!-- //////////// -->
                                                        @if(config('control.thirdpart_money')==1 and $vo->status=='New')
                                                            @if(in_array($vo->payment, [3,4,5]) AND ($vo->receipts_state == '0'))
                                                                <button class="btn more" type="submit">{{Lang::get('補單')}}</button>
                                                            @endif
                                                        @endif
                                                    </form>
                                                </td>
                                                <td data-th="{{Lang::get('出貨狀態')}}">{{config('extra.order.TRANSPORT_STATE')[$vo->transport_state]}}</td>
                                                <td data-th="{{Lang::get('賣家備註')}}">{{$vo->ps2}}</td>
                                                <td data-th="{{Lang::get('操作')}}">
                                                    @if(($vo->transport_state != 1 ) and $vo->status=='New')
                                                        <a class="submitBtn2 cancel" order_number="{{$vo->order_number}}">{{Lang::get('取消')}}</a>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- //////////////////////////////////// -->
                        <div class="row paginationBox">
                            <div class="col-12 boxCenter">
                                {{$data['orderform']->links('pagination::default')}}
                            </div>
                        </div>
                        <!-- //////////////////////////////////// -->

                        <div class="memberBottom">
                            <div>
                                {!!$data['consent_other']!!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('ownJS')
    <script>
        function setReportNumber($this){
            $.ajax({
                url: "{{url('Orderform/setReportNumber')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data:{
                    id: $($this).attr('alt').split("_")[1],
                    reportNumber: $("#" + $($this).attr('alt')).val()
                },
                success: function(resp) {
                    if(resp.code){
                        Vue.toasted.show(resp.msg, vt_success_obj);
                        $($this).parent().html("{{Lang::get('待確認')}}");
                    }else{
                        Vue.toasted.show(resp.msg, vt_error_obj);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                }
            });
        }

        $('.cancel').on('click', function(e){
            if(confirm("{{Lang::get('確定取消嗎')}}")){
                $('#body_block').show();
                $.ajax({
                    url: "{{url('Orderform/cancel')}}",
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    dataType: "json",
                    data: { order_number: $(this).attr('order_number') },
                    success: function (response) {
                        alert(response.msg);
                        if (response.url) {
                            location.href = response.url;
                        } else {
                            location.href="{{url('Orderform/orderform')}}"
                        }
                        $('#body_block').hide();
                    },
                    error: function (xhr) {
                        console.log(xhr);
                        Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                        $('#body_block').hide();
                    },
                });
            }
        });
    </script>
@endsection

