<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>Card</title>

		<style type="text/css">
			*{
				margin: 0;
			}
			.scratch_container{
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 320px;
			}
			img{
				position: absolute;
				max-width: 320px;
				width: fit-content;
				height: auto;
			}
			canvas{
				position: absolute;
			  	cursor: pointer;
			  	z-index: 3;
			}
		</style>
	</head>
	<body>
		<div class="scratch_container">
			<img src="{{__PUBLIC__}}/{{$data['draw_record']['gift_pic']}}" alt="{{$data['draw_record']['gift_name']}}">
			@if($data['draw_record']['show']==0"}
				<canvas id="top"></canvas>
			@endif
		</div>
	</body>

	<script type="text/javascript">
		var draw_record_id = '{{$data["draw_record"]["id"]}}';
		var gift_name = '{{$data["draw_record"]["gift_name"]}}';

		var useNumber=0,
		    mousedown,
		    w = 320,
		    h = 320;

		var topCanvas = document.querySelector('#top');
		topCanvas.width  = w;
		topCanvas.height = h;
		
		var ctxTop = topCanvas.getContext('2d');
		ctxTop.canvas.style.opacity = 1;
	    ctxTop.fillStyle = 'gray';
	    ctxTop.fillRect(0, 0, w, h);
	    ctxTop.globalCompositeOperation = 'destination-out';

		//鼠標移動開始刮圖層
		topCanvas.addEventListener('touchstart', eventDown);
		topCanvas.addEventListener('touchend', eventUp);
		topCanvas.addEventListener('touchmove', eventMove);
		topCanvas.addEventListener('mousedown', eventDown);
		document.addEventListener('mouseup', eventUp);
		topCanvas.addEventListener('mousemove', eventMove);
		function eventDown(ev){
		    ev = ev || event;
		    ev.preventDefault();
		    mousedown=true;
		}
		function eventUp(ev){
		    ev = ev || event;
		    ev.preventDefault();
		    mousedown=false;
		}
		function eventMove(ev){
		    ev = ev || event;
		    ev.preventDefault();
		    if(mousedown) {
		        if(ev.changedTouches){
		            ev=ev.changedTouches[ev.changedTouches.length-1];
		        }
		        var x = ev.pageX - this.offsetLeft;
		        var y = ev.pageY - this.offsetTop;
		        ctxTop.beginPath();
		        ctxTop.arc(x, y, 50, 0, Math.PI * 2);
		        ctxTop.fill();
		        alertInfo();
		    }
		}

		var hide_resault = true;
		// 判斷刮開區域大於60%時，頂層canvas消失，顯示底層數據
		function alertInfo(){
		    var data = ctxTop.getImageData(0,0,w,h).data;
		    var n = 0 ;
		    for (var i = 0; i < data.length; i++) {
		      if (data[i] == 0) {
		        n++;
		      };
		        };
		    if (n >= data.length * 0.9) { /*檢查刮刮樂刮除比例*/
		        // ctxTop.globalCompositeOperation = 'destination-over';
		        // ctxTop.canvas.style.opacity = 0;
		        
		        if(hide_resault){ /*還未公開結果*/
			        hide_resault = false; /*設定成已公開結果*/
			        mousedown = false; /*停止刮除動作*/
			        
			        /*公布結果時要處理的事*/
			        parent.show_draw_result(gift_name, draw_record_id);
		        }
		    }
		}
	</script>
</html>