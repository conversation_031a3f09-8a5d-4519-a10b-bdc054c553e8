@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['share_article']['name']}} | {{$data['seo'][0]['title']}}@endsection

@section("css")
  <style>
    .newsList .list .img{
      padding-top: 60%;
    }
  </style>
@endsection

@section("content")
  @if(empty(config('control.close_function_current')['banner管理']))
    @if($data['frontend_menu']['share_article']['pic'])
    <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu']['share_article']['pic']}});">
      <div class="container">
        <h2 class="page-title" style="color:{{$data['frontend_menu']['share_article']['text_color']}}">
          {{$data['frontend_menu']['share_article']['name']}}</h2>
        <!-- <span class="enText">{{$data['frontend_menu']['share_article']['en_name']}}</span> -->
      </div>
    </section>
    @endif
  @endif
  <section class="directoryRow">
    <div class="container">
      <ul>
        <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
        <li><a href="{{url('ShareArticle/index')}}">{{$data['frontend_menu']['share_article']['name']}}</a></li>
      </ul>
    </div>
  </section>
  <section class="container productPublic">
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox">
      <div id="rightContentBox" class="innerPageBox">
        <div class="proBrandZone container">
          <div class="newsList row">
            @foreach($data['share_article'] as $vo)
              <div class="col-lg-4 col-md-6 col-12 list w-100 mb-4">
                 <a href="{{url('ShareArticle/detail')}}?id={{$vo->id}}" visitorId>
                  <div class="img" style="background-image: url({{$vo->img}});"></div>
                  <p class="time mb-1"></i> {{$vo->create_time_f}}</p>
                  <div class="text">
                    <h3 class="mb-1">{{$vo->name}}</h3>
                  </div>
                 </a>
              </div>
            @endforeach
          </div>
          <!-- /////////////////////////////////// -->
          <div class="row paginationBox">
            <div class="col-12 boxCenter">
              {{$data['share_article']->links('pagination::default')}}
            </div>
          </div>
          <!-- /////////////////////////////////// -->
        </div>
        <!-- announcement end -->
      </div>
    </div>
    <!-- /////////////////////////////////////////// -->
  </section>
@endsection

@section("ownJS")
@endsection