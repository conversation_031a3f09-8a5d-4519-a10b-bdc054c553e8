    
    <div class="container-fluid mt-4" style="overflow-y: auto;">
        <div class="row">
            @foreach($data['graph_datas'] as $graph_k => $graph)
                <div id="graph_{{$graph_k}}_{{$data['rand']}}" class="graph col-lg-6 col-12" style="min-width: 449px; height:425px;"></div>
            @endforeach
        </div>
    </div>

    <script type="text/javascript">
        function create_graph(
            target="1", 
            name="test",
            column=['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            data=[
                {title:'Mon', value:120}, 
                {title:'Tue', value:155},
                {title:'Wed', value:150},
                {title:'Thu', value:80},
                {title:'Fri', value:70},
                {title:'Sat', value:110},
                {title:'Sun', value:130},
            ],
            y_max = null,
        ){
            option = {
                title: {
                    text: name
                },
                xAxis: {
                    type: 'category',
                    data: column,
                    axisLabel: { interval: 0, rotate: 30 },
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1,
                    max: y_max,
                },
                series: [
                    {
                        data: data,
                        type: 'bar',
                    }
                ],
            };

            $('#graph_'+target).attr('_echarts_instance_', "");

            var myChart = echarts.init($('#graph_'+target)[0]);
            myChart.setOption(option);
            myChart.on('click', function (event) {
                // console.log(event.data);
                var users = event.data.users;
                var users_string = users.length>0 ? users.join(', ') : "({{Lang::get('無填寫者')}})";
                Swal.fire({
                    title: "{{Lang::get('無填寫者')}}",
                    text: users_string,
                    icon: '',
                    confirmButtonText: "{{Lang::get('關閉')}}",
                })
            });
        }

        function clean_graph(){
            $('.graph').html("");
            $('.graph').attr('_echarts_instance_', "");
        }
    </script>