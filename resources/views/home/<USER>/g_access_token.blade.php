<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Google Auth</title>

    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
  </head>
  <body>
    <script src="https://accounts.google.com/gsi/client" async defer
            onload="this.onload=function(){};GoogleClientInitGetInfo()"
            onreadystatechange="if (this.readyState === 'complete') this.onload()"
    ></script>
    <script type="text/javascript">
      function GoogleClientInitGetInfo() {
        google.accounts.id.initialize({
          client_id: "{{config('extra.social_media.Google_appId')}}",
          callback: '',
        });

        /*解析參數*/
        params_str = location.href.split('#');
        if(params_str.length==1){ location.href = '/'; }
        params_str = params_str[1];
        // console.log(params_str);
        params_str = params_str.split('&');
        params = {};
        for (var i = 0; i < params_str.length; i++) {
          params_strs = params_str[i].split('=');
          params[params_strs[0]] = decodeURI(params_strs[1]);
          if(params_strs[0]=='state'){
            params[params_strs[0]] = JSON.parse(params[params_strs[0]]);
          }
        }
        // console.log(params);
        if(typeof(params.access_token)=='undefined'){ location.href = '/'; }

        /*取得用戶資料*/
        var xhr = new XMLHttpRequest();
        xhr.open('GET',
          'https://www.googleapis.com/oauth2/v1/userinfo?' +
          'access_token=' + params.access_token);
        xhr.onload = function (e) {
          if(!xhr.response){ return; }
          // console.log(xhr.response);
          info = JSON.parse(xhr.response);
          // console.log(info);
          $.ajax({
            url     : "{{url('Login/g_login')}}",
            dataType: 'json',
            headers: {
              'X-CSRF-Token': '{{csrf_token()}}' 
            },
            type    : 'POST',
            data : { 
              email: info.email, /*mail*/
              name: info.name,   /*name*/
              open : params.state.open,
            },
            contentType:"application/x-www-form-urlencoded; charset=UTF-8",
            success: function(result){
              if(result.code == 1){
                if(params.state.open == 0){
                  location.href = decodeURIComponent(params.state.redirect);
                }else{
                  alert("{{Lang::get('成功合併，請重新登入')}}");
                  google.accounts.id.disableAutoSelect();
                  location.href="{{url('Login/Logout')}}";
                } 
              }else{
                alert(result.msg);
                history.back();
              }
            }
          });
        };
        xhr.send(null);
      }
    </script>
  </body>
</html>