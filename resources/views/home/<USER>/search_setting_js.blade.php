
<script type="text/javascript">
    $(`#product_sort option[value="{{$data['sort']}}"]`).attr("selected", true);
    $(`#product_price_range option[value="{{$data['price_range']}}"]`).attr("selected", true);
    function change_sort(){
        new_href = location.href;
        /*處理排序*/
        var product_sort = $('#product_sort');
        if(new_href.split('?').length > 1){
            if(new_href.split('sort=').length > 1){
                new_href = new_href.split('sort=')[0] + 'sort=' + $(product_sort).val();
            }else{
                new_href = new_href + '&sort=' + $(product_sort).val();
            }
        }else{
            new_href = new_href + '?sort=' + $(product_sort).val();
        }

        /*處理價格區間*/
        var product_price_range = $('#product_price_range');
        if(new_href.split('?').length > 1){
            if(new_href.split('price_range=').length > 1){
                new_href = new_href.split('price_range=')[0] + 'price_range=' + $(product_price_range).val();
            }else{
                new_href = new_href + '&price_range=' + $(product_price_range).val();
            }
        }else{
            new_href = new_href + '?price_range=' + $(product_price_range).val();
        }

        location.href = new_href;
    }
</script>