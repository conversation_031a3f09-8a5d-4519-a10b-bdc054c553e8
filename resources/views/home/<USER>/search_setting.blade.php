
	<span>
        <select id="product_sort" onchange="change_sort()" class="mr-3">
            <option value="">{{Lang::get('熱門推薦')}}</option>
            <option value="price_asc">{{Lang::get('價格：由低至高')}}</option>
            <option value="price_desc">{{Lang::get('價格：由高至低')}}</option>
            <option value="createtime_desc">{{Lang::get('上架時間：由新到舊')}}</option>
            <option value="createtime_asc">{{Lang::get('上架時間：由舊到新')}}</option>
        </select>
        @if(empty(config('control.close_function_current')['價格搜尋設定']))
            @if(!empty($data['product_price_searchs']))
                <select id="product_price_range" onchange="change_sort()">
                    <option value="">{{Lang::get('價格區間')}}</option>
                    @foreach($data['product_price_searchs'] as $option)
                        <option value="{{$option['content']}}">{{$option['title']}}</option>
                    @endforeach
                </select>
            @endif
        @endif
    </span>