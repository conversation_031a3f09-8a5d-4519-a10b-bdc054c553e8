@extends('home.Public.mainTpl')
@section('title'){{Lang::get('購物車')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
    <style>
        .modal{
            overflow: scroll !important;
        }
    
        #contWrap {
            width: 100%;
        }

        .orderTable.table td.examinee_top{
            padding: 0px;
        }
        .orderTable.table-rwd.proIntro td.examinee_top:before{
            display: none;
        }
        .orderTable .examinee_table.table-rwd tr{
            padding: 0px;
        }

        @media only screen and (max-width: 1280px){
            .orderTable.table-rwd.proIntro td:before{
                width: auto;
            }
        }

        .add_prod img{
           max-width:80px;
           max-height:80px;
        }
        .add_prod>div{
            width: calc(100% - 80px);
        }
        .add_prod span.btn{
            font-size: 14px;
            line-height: 14px;
            padding: 5px;
        }
        .lineThrough{
            text-decoration: line-through;
        }
        .num{
            color: #ff4300;
        }

        .cart-items .spinner input[type=number]::-webkit-outer-spin-button, 
        .cart-items .spinner input[type=number]::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }
        .cart-items input[type=number]::-webkit-outer-spin-button, 
        .cart-items input[type=number]::-webkit-inner-spin-button {
            -webkit-appearance: auto;
        }
    </style>
@endsection
@section('mycode')
<script>
    $(function() {
        var mdwBtn = $('.modalBtn');
        mdwBtn.on('click', function(e) {
            e.preventDefault();
            var setMdw = $(this),
                setHref = setMdw.attr('href');
                console.log(setHref)
            if(setHref != undefined && setHref != ""){
                $('#boxModel').html('<iframe id="contWrap"></iframe></div>');
                $('#contWrap').attr('src', setHref);
            }
        });
    });
</script>
@endsection

@section('content')
<section class="directoryRow">
    <div class="container">
        <ul>
            <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
            <li><a href="{{url('Cart/choose_shop')}}">{{Lang::get('購物車')}}</a></li>
        </ul>
    </div>
</section>
<section class="container max-wideVersion productPublic">
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox" class="shopcarInforBox">
        <div class="shopcarContentBox  memberContentBox">
            <div class="memberTop">
                <div class="titleBox">
                    <div class="title">
                        <h3>{{Lang::get('購物車')}}</h3>
                    </div>
                </div>
            </div>
            <div class="memberMiddle memberitems">
                <div class="orderDetailsBox shoppingCartBox">

                    @if(config('control.control_register')==1)
                    <ul class="nav tabNavBox justify-content-end">
                        <li class="nav-item">
                            <a class="process_btn" data-toggle="modal" data-target="#descriptionContModel">{{Lang::get('流程說明')}}</a>
                        </li>
                    </ul>
                    @endif
            
                    <form name="cartform" id="cartform" action="{{url('Cart/buy')}}" method="post">
                        @csrf
                        <div class="cart-items" id="cart_table">
                            <h4>{{Lang::get('商品內容')}}
                                （ <span v-text="'{{Lang::get('共 XX 項')}}'.replace('XX', cartData.length)"></span> ）
                            </h4>
                            <table id="cart_table" class="orderTable table table-rwd">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th style="width: 170px">{{Lang::get('圖片')}}</th>
                                        <th>{{Lang::get('品名')}}</th>
                                        <th style="width: 170px">{{Lang::get('數量')}}</th>
                                        <th style="width: 150px">{{Lang::get('單價')}}</th>
                                        <th>{{Lang::get('總價')}}</th>
                                        <th style="width: 150px">{{Lang::get('圓滿點折抵')}}</th>
                                        <th style="width: 80px">{{Lang::get('刪除')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <template v-for="(vo, vo_key) in cartData">
                                        <!-- 商品資料 -->
                                        <tr class="exam_tr">
                                            <td data-th="{{Lang::get('圖片')}}">
                                                <div class="smallProImg position-relative"
                                                    :style="{'background-image': 'url('+'{{__PUBLIC__}}'+vo.info_pic1+')'}">
                                                    <span class="prod_tag kol_tag" 
                                                        v-if="!closeKol && vo.key_type.slice(0,3)=='kol'">{{Lang::get('網紅推薦')}}</span>
                                                    <span class="prod_tag add_tag"
                                                        v-if="!closeAddPrice && vo.key_type.slice(0,3)=='add'">{{Lang::get('加價購')}}</span>
                                                </div>
                                            </td>
                                            <td data-th="{{Lang::get('品名')}}">
                                                <span class="productNameBox" tabindex="0" 
                                                    data-toggle="tooltip" :title="vo.info_title+' - '+vo.type_title">
                                                    <span class="productName">
                                                        <span v-text="vo.info_title"></span>
                                                        <span v-if="vo.type_title" v-text="' - '+vo.type_title"></span>
                                                    </span>
                                                    <span class="productName text-danger"
                                                          v-if="(thirdpart_money==1) && control_card_pay==1 && vo.card_pay==0">
                                                          &nbsp;&nbsp;{{Lang::get('不可刷卡')}}
                                                    </span>
                                                </span>
                                                <a class="btn btn-success btn-sm text-white"
                                                v-if="control_register==1 && vo.is_registrable==1"
                                                @click="add_examinee(vo.type_id)">{{Lang::get('填寫報名資料')}}
                                                </a>
                                            </td>
                                            <td data-th="{{Lang::get('數量')}}">
                                                <div class="d-inline-block">
                                                    <div class="input-group spinner">
                                                        <div class="input-group-prepend">
                                                            <button
                                                                class="btn text-monospace"
                                                                type="button"
                                                                @click="changeNum(-1, vo.type_id)"
                                                            >
                                                                <i class="bi bi-dash-lg"></i>
                                                            </button>
                                                        </div>
                                                        <input
                                                            type="number"
                                                            class="count form-control cart-count"
                                                            min="1"
                                                            :max="vo.limit_num"
                                                            step="1"
                                                            v-model="vo.num"
                                                            :id="'count_' + vo.type_id"
                                                            style="text-align: center;"
                                                            @blur="realChangeNum(vo.num, vo.type_id)"
                                                        />
                                                        <div class="input-group-append">
                                                            <button
                                                                class="btn text-monospace"
                                                                type="button"
                                                                @click="changeNum(1, vo.type_id)"
                                                            >
                                                                <i class="bi bi-plus-lg"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td data-th="{{Lang::get('單價')}}">
                                                {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}<span v-text="formatPrice(toNumber(vo.countPrice))"></span>
                                            </td>
                                            <td data-th="{{Lang::get('總價')}}">
                                                {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}<span v-text="formatPrice(toNumber(vo.countPrice*vo.num))"></span>
                                                <template v-if="vo.product_cate!=1">
                                                    <br>
                                                    ({{Lang::get('折抵上限')}}:USD$
                                                    <span v-text="cartData_deduct[vo.type_id].max_deduct"></span>)
                                                </template>
                                            </td>
                                            <td data-th="{{Lang::get('圓滿點折抵')}}">
                                                <div class="d-inline-flex flex-wrap flex-row" v-if="vo.product_cate==1">
                                                    {{Lang::get('不可使用')}}
                                                </div>
                                                <div class="d-inline-flex flex-wrap flex-row" v-else>
                                                    <div class="mr-2 mb-2 d-flex flex-wrap">
                                                        <div>{{Lang::get('功德點')}}：</div>
                                                        <div>(USD$)</div>
                                                        <input type="number" class="border text-right" min="0"
                                                               v-model="cartData_deduct[vo.type_id].deduct_invest" 
                                                               @change="set_contribution_deduct(vo.type_id, 'deduct_invest')">
                                                    </div>
                                                    <div class="mr-2 mb-2 d-flex flex-wrap">
                                                        <div>{{Lang::get('消費點')}}：</div>
                                                        <div>
                                                            ({{Lang::get('折抵上限')}}:USD$<span v-text="formatPrice(cartData_deduct_consumption[vo.type_id])"></span>)
                                                        </div>
                                                        <input type="number" class="border text-right" min="0"
                                                               v-model="cartData_deduct[vo.type_id].deduct_consumption"
                                                               @change="set_contribution_deduct(vo.type_id, 'deduct_consumption')">
                                                    </div>
                                                </div>
                                            </td>
                                            <td data-th="{{Lang::get('刪除')}}">
                                                <a class="deleteBtn" href="###" @click="deleteCtrl(vo.type_id)">
                                                    <i class="icon-bin"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <template v-if="control_register==1 && vo.is_registrable==1">
                                            <!-- 考生資料 -->
                                            <tr class="examinee_tr" :id="'examinee'+vo.type_id">
                                                <td colspan="8" class="examinee_top">
                                                    <div class="container p-0 overflow-x-scroll">
                                                        <table class="orderTable table-striped table-bordered table-rwd examinee_table">
                                                            <thead>
                                                                <tr class="tr-only-hide text-dark">
                                                                    <th><small>{{Lang::get('序號')}}</small></th>
                                                                    <th v-for="(field) in vo.fields">
                                                                        <small v-text="field.title"></small>
                                                                    </th>
                                                                    <th><small>{{Lang::get('刪除')}}</small></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr v-for="(exinfo, index) in vo.examinee" class="cursor-pointer">
                                                                    <td data-th="{{Lang::get('序號')}}" 
                                                                        @click="edit_examinee(vo.type_id, exinfo.id)">
                                                                        <a><small v-text="index + 1"></small></a>
                                                                    </td>
                                                                    <td v-for="field in vo.fields" :data-th="field.title"
                                                                        @click="edit_examinee(vo.type_id, exinfo.id)">
                                                                        <span v-if="register_data(index, vo.type_id)['field_id_'+field.id]">
                                                                            <small v-if="types_need_checked.indexOf(field.type) != -1" 
                                                                                v-text="register_data(index, vo.type_id)['field_id_'+field.id].join()"></small>
                                                                            <small v-if="['file'].indexOf(field.type) != -1" 
                                                                                v-text="register_data(index, vo.type_id)['field_id_'+field.id]['file_name']"></small>
                                                                            <small v-if="types_need_checked.indexOf(field.type)==-1 && ['file'].indexOf(field.type)==-1" 
                                                                                v-text="register_data(index, vo.type_id)['field_id_'+field.id]"></small>
                                                                        </span>
                                                                    </td>
                                                                    <td data-th="{{Lang::get('刪除')}}"><small @click="del_examinee(index, vo.type_id)">{{Lang::get('刪除')}}</small></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </td>
                                            </tr>
                                        </template>
                                    </template>
                                </tbody>
                            </table>
                            <div class="subTotal">小計： {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}} <span v-text="formatPrice(toNumber(total))"></span> </div>
                        </div>

                        @if(empty(config('control.close_function_current')['加價購設定']) && count($data['addprice_group'])>0)
                        <h3 class="subtitle">加價購商品</h3>
                        <div class="container">
                            <div class="row">
                                @if(empty($data['addprice_group']) == false)
                                @foreach($data['addprice_group'] as $addprice)
                                <div class="col-xl-4 col-lg-4 col-md-6 col-12 add_prod">
                                    <img src="{{__PUBLIC__}}{{$addprice['pic'][0]}}">
                                    <div>
                                        <h3 class="title">{{$addprice['pi_title']}}-{{$addprice['title']}}</h3>
                                        <div class="priceBox">
                                            <p class="originalPrice mr-1">
                                                {{Lang::get('定價')}}{{$addprice['price']}}&nbsp;&nbsp;{{Lang::get('售價')}}{{config('extra.shop.dollar_symbol')}}{{$addprice['count']}}
                                            </p>
                                            <p class="offerPrice">
                                                {{Lang::get('加購價')}}{{config('extra.shop.dollar_symbol')}}
                                                <span class="price">{{$addprice['adp_dis']}}</span>
                                            </p>
                                            <span class="btn addCart more ml-2" onclick="addprice_to_cart(`{{$addprice['cart_id']}}`)">{{Lang::get('加入購物車')}}</span>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                                @endif
                            </div>
                        </div>
                        @endif
                        <br>
                        <!-- /////////////// -->
                        <div class="row cart-form">
                            <div class="col-lg-6">
                                <div class="mb-4">
                                    <h4>{{Lang::get('選擇配送方式')}}</h4>
                                    <div class="form-box">
                                        <div class="form-group" id="shipping_div">
                                            <label for="delivery">{{Lang::get('送貨方式')}} <span class="required">*</span></label>
                                            <select class="form-control" name="send_way" onchange="send_way_infos(this.value)">
                                                <option value="">--{{Lang::get('請選擇')}}--</option>
                                                <template v-for="item in type_decode">
                                                    <option v-if="item.price == 0" :value="item.shipping_fee_id" :key="'label_send_way_'+item.shipping_fee_id" v-text="item.name + ' |  {{Lang::get('免運費')}}'"></option>
                                                    <option v-else-if="item.free_rule > 0" :value="item.shipping_fee_id" :key="'label_send_way_'+item.shipping_fee_id">
                                                        <span v-text="item.name + ' |  {{Lang::get('運費')}} {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + item.price"></span>
                                                        <span v-if="item.free_rule!=999999999" v-text="'、{{Lang::get('免運金額')}} {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + item.free_rule"></span>
                                                    </option>
                                                    <option v-else :value="item.shipping_fee_id" :key="'label_send_way_'+item.shipping_fee_id" v-text="item.name + ' |  {{Lang::get('運費')}} {{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + item.price"></option>
                                                </template>
                                            </select>
                                        </div>
                                        @if(empty(config('control.close_function_current')['會員管理']))
                                        @if($data['user']['id'] > 0)
                                        <div class="mb-2 float-left shipping_blank" style="display: none;" id="sync_member">
                                            <a class="submitBtn" style="border-radius: 5px;" onclick="assign_memeber_data()" href="###">
                                                {{Lang::get('同會員資料')}}
                                            </a>
                                        </div>
                                        @endif
                                        @endif
                                        <div class="form-group shipping_blank" style="display: none;">
                                            <label for="transport_location_name">{{Lang::get('收件人姓名')}} <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="transport_location_name" name="transport_location_name" placeholder="{{Lang::get('請填入收件人真實姓名，以確保順利收件')}}">
                                        </div>
                                        <div class="form-group shipping_blank" style="display: none;">
                                            <label for="transport_location_phone">{{Lang::get('收件人行動電話')}} <span class="required">*</span></label>
                                            <input type="text" class="form-control" id="transport_location_phone" name="transport_location_phone" placeholder="{{Lang::get('請輸入有效手機號碼')}}">
                                        </div>
                                        <div class="form-group shipping_blank" style="display: none;">
                                            <label for="transport_email">{{Lang::get('收件人電子信箱')}} <span class="required">*</span></label>
                                            <input type="email" class="form-control" id="transport_email" name="transport_email" placeholder="{{Lang::get('請輸入電子信箱')}}">
                                        </div>
                                        <div id="cz_select" class="row use-row shipping_blank" style="display: none;">
                                            <div class="col-12 use-col mb-2">
                                                <label for="addr">{{Lang::get('寄送地址')}} <span class="required">*</span></label>
                                            </div>
                                            <div class="col-sm-4 col-12 use-col mb-2">
                                                <select name="F_I_CNo" id="myCity" class="form-control">
                                                    <option value="">{{Lang::get('請選擇縣市')}}</option>
                                                    @foreach($data['city'] as $vo)
                                                        <option value="{{$vo['AutoNo']}}">{{$vo['Name']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-sm-4 col-12 use-col mb-2">
                                                <select name="F_I_TNo" id="myTown" class="form-control">
                                                    <option value="">{{Lang::get('請選擇鄉鎮區')}}</option>
                                                </select>
                                            </div>
                                            <div class="col-sm-4 col-12 use-col mb-2">
                                                <input type="text" class="form-control" id="myZip" name="F_S_NH_Zip" size="3" placeholder="{{Lang::get('郵遞區號')}}" readonly="ture">
                                            </div>
                                            <div class="col-12 use-col mb-2">
                                                <input type="text" class="form-control" id="transport_location" name="transport_location" placeholder="{{Lang::get('請輸入到貨地址')}}">
                                            </div>
                                        </div>
                                        @if(config('control.thirdpart_logistic')==1)
                                        <div class="form-group shipping_blank" style="display: none;" id="store_pickup">
                                            <label for="store">{{Lang::get('取貨門市')}} <span class="required">*</span></label>
                                            <input type="text" class="form-control mb-2" style="cursor: auto;" name="store" id="store" placeholder="{{Lang::get('請點選下方按鈕選擇門市')}}" readonly>
                                            <button type="button" class="select-branch-btn" onclick="selectPlace()">{{Lang::get('選擇門市')}}</button>
                                            <input type="hidden" name="MerchantTradeNo">
                                            <input type="hidden" name="LogisticsSubType">
                                            <input type="hidden" name="CVSStoreID">
                                            <input type="hidden" name="CVSStoreName">
                                            <input type="hidden" name="CVSAddress">
                                            <input type="hidden" name="CVSTelephone">
                                            <input type="hidden" name="CVSOutSide">
                                            <input type="hidden" name="ExtraData">
                                        </div>
                                        @else
                                        <div class="form-group shipping_blank" style="display: none;" id="store_pickup">
                                            <label for="store">{{Lang::get('取貨門市')}} <span class="required">*</span></label>
                                            <div class="d-flex mb-2" style="gap: 0.5rem;">
                                                <input type="text" class="form-control" style="cursor: auto;" id="storeNo" placeholder="{{Lang::get('請填入店鋪號')}}" maxlength="8">
                                                <input type="text" class="form-control" style="cursor: auto;" id="storeName" placeholder="{{Lang::get('請填入店鋪名')}}" maxlength="10">
                                            </div>
                                            <input type="text" class="form-control mb-2" style="cursor: auto;" id="storeAddr" placeholder="{{Lang::get('請填入店鋪地址')}}" maxlength="35">
                                            <button type="button" class="select-branch-btn" id="mart_url" onclick="selectPlace()">{{Lang::get('門市資訊查詢')}}</button>
                                        </div>
                                        @endif
                                        <input type="hidden" name="addrC" />

                                        <div class="form-group">
                                            <label for="ps">{{Lang::get('其他備註')}}</label>
                                            <textarea id="ps" class="form-control" rows="5" name="ps"></textarea>
                                        </div>
                                    </div>

                                </div>
                                <div class="mb-4" id="paying_div">
                                    <h4>{{Lang::get('選擇付款方式')}}</h4>
                                    <div class="form-group form-box">
                                        <label>{{Lang::get('付款方式')}} <span class="required">*</span></label>
                                        <select class="form-control" name="pay_way" v-model="pay_way">
                                          <option value="">--{{Lang::get('請選擇')}}--</option>
                                          <option v-for="item in type_decode" :key="'label_pay_way_'+item.pay_fee_id" :value="item.pay_fee_id" v-text="item.name"></option>
                                        </select>
                                    </div>
                                    @if(config('control.thirdpart_money'))
                                        <template v-if="[3,4].indexOf(pay_way)!=-1">
                                            <p>{{Lang::get('如果刷卡失敗請至會員專區>訂單查詢內「補單」')}}</p>
                                        </template>
                                        @if(config('control.control_card_pay'))
                                            <template v-if="[3,4].indexOf(pay_way)!=-1 && !can_card_pay">
                                                <p>{{Lang::get('此筆訂單含有不可刷卡之商品，若欲使用線上刷卡，請刪除該商品並重新整理頁面')}}</p>
                                            </template>
                                        @endif
                                    @endif
                                </div>
                                <div>
                                    <h4>{{Lang::get('發票設定')}}</h4>
                                    <div class="form-group form-box">
                                        <label for="invoice">{{Lang::get('請選擇發票開立方式')}} <span class="required">*</span></label>
                                        <select class="form-control mb-2" name="invoice_style" onchange="showInputs(this.value)">
                                          <option value="">--{{Lang::get('請選擇')}}--</option>
                                          <option value="1">{{Lang::get('個人實體紙本發票')}}</option>
                                          @if(config('control.thirdpart_invoice')==1)
                                            <option value="2">{{Lang::get('個人電子郵件寄送發票')}}</option>
                                            <option value="3">{{Lang::get('個人共通性載具')}}</option>
                                          @endif
                                          <option value="4">{{Lang::get('公司戶發票')}}</option>
                                          <option value="5">{{Lang::get('捐贈')}}</option>
                                        </select>
                                        @if(config('control.thirdpart_invoice')==1)
                                            <span class="required invoice-input" style="display: none;" id="emailNote">{{Lang::get('發票將會寄至您的 Email，進入連結即可列印出紙本發票')}}</span>
                                        @endif
                                        <div class="form-row invoice-input" style="display: none;">
                                            <div class="form-group col-lg-6">
                                                <label for="carrier_type">{{Lang::get('載具類型')}}</label>
                                                <select name="CarrierType" id="CarrierType" class="form-control" onchange="carrierNumLenChange();">
                                                    <option value="3">{{Lang::get('手機條碼')}}</option>
                                                    <option value="2">{{Lang::get('自然人憑證')}}</option>
                                                </select>
                                              </div>
                                            <div class="form-group col-lg-6">
                                              <label for="carrier_number">{{Lang::get('載具編號')}}</label>
                                              <input type="text" class="form-control" name="CarrierNum" id="CarrierNum" value="" placeholder="">
                                            </div>
                                        </div>
                                        <div class="form-row invoice-input" style="display: none;">
                                            <div class="form-group col-md-6">
                                              <label for="company_name">{{Lang::get('公司抬頭')}}</label>
                                              <input type="text" class="form-control" id="company_title"
                                              name="company_title" placeholder="">
                                            </div>
                                            <div class="form-group col-md-6">
                                              <label for="gui_number">{{Lang::get('統一編號')}}</label>
                                              <input type="text" class="form-control" id="uniform_numbers"
                                              name="uniform_numbers" placeholder="" maxlength="8">
                                            </div>
                                        </div>
                                        <div class="form-row invoice-input" id="loveCode_div" style="display: none;">
                                            <div class="form-group col-lg-6">
                                                <label for="donate-target">{{Lang::get('捐贈對象')}}</label>
                                                <select id="LoveCode_select" class="form-control" @change="changeLoveCode()">
                                                    <optgroup class="main" label="{{Lang::get('捐贈單位')}}">
                                                        <option v-for="item in type_decode" :value="item.code" v-text="item.name"></option>
                                                    </optgroup>
                                                    <optgroup label="{{Lang::get('其他')}}">
                                                        <option value="">{{Lang::get('其他')}}</option>
                                                    </optgroup>
                                                </select>
                                              </div>
                                            <div class="form-group col-lg-6">
                                              <label for="donate_number">{{Lang::get('捐贈碼')}}</label>
                                              <input id="LoveCode" type="text" class="form-control" name="LoveCode" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-4">
                                    <h4>{{Lang::get('付款金額')}}</h4>
                                    <div class="form-box" id="finalPart">
                                        <div id="myAccordion" class="mb-2">
                                            @if(empty(config('control.close_function_current')['會員管理']))
                                                @if($data['user']['id'] != 0)
                                                    <!-- 因目前未有推薦會員後的相關消費行為，故先註解 -->
                                                    <!-- <div class="form-check mb-2" id="referral">
                                                        <input class="form-check-input" type="radio" name="discount_selected" value="referralCode" id="referral_code">
                                                        <label class="form-check-label" for="referral_code" >
                                                            使用推薦碼
                                                        </label>
                                                        <div id="referralCode" class="mb-2 d-none myAccordion_blank" >
                                                            <div class="d-flex justify-content-between">
                                                                <input type="text" class="form-control" name="referralCode_input">
                                                                <button type="button" class="cursor-pointer">{{Lang::get('確認')}}</button>
                                                            </div>
                                                        </div>
                                                    </div> -->
                                                    <template v-if="discount!=null">
                                                        @if(empty(config('control.close_function_current')['會員優惠設定']))
                                                            @if(config('control.control_FirstBuyDiscount')==1)
                                                                <template v-if="discount.discountData.firstBuyDiscount.can_use==1">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input" type="radio" name="discount_selected" value="firstbuy_select" id="firstbuy_select" v-model="discountSelect">
                                                                        <label class="form-check-label" for="firstbuy_select">
                                                                            {{Lang::get('會員首購優惠')}}
                                                                            <span v-text="discount.discountData.firstBuyDiscount.note"></span>
                                                                        </label>
                                                                    </div>
                                                                </template>
                                                            @endif
                                                            @if(config('control.control_VipDiscount')==1)
                                                                <!-- <template v-if="discount.discountData.vipDiscount.can_use==1">
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input" type="radio" name="discount_selected" value="vipdiscount_select" id="vipdiscount_select" v-model="discountSelect">
                                                                        <label class="form-check-label" for="vipdiscount_select">
                                                                            {{Lang::get('會員等級優惠')}}
                                                                            <span v-text="discount.discountData.vipDiscount.note"></span>
                                                                        </label>
                                                                    </div>
                                                                </template> -->
                                                            @endif
                                                        @endif
                                                    </template>

                                                    @if(empty(config('control.close_function_current')['點數設定']))
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="radio" name="discount_selected" value="points_0" id="point_discount" v-model="discountSelect">
                                                        <label class="form-check-label" for="point_discount">
                                                            {{Lang::get('使用紅利點數')}}
                                                        </label>
                                                        <div id="pointDiscount" class="mb-2 d-none">
                                                            <span>
                                                                {{Lang::get('持有現金積分')}}：
                                                                <span class="num mr-3" style="color: var(--mark);" v-text="Math.floor(available_points)"></span>
                                                                {{Lang::get('請輸入欲使用的積分數')}}：
                                                            </span>
                                                            <div class="d-flex justify-content-between mt-2">
                                                                <input type="number" class="form-control" step="1" v-model="point_input">
                                                                <button type="button" class="cursor-pointer" @click="set_discount_selected('points_0')">{{Lang::get('確認')}}</button>
                                                            </div>
                                                            <div class="point-response" v-if="point_error_msg">
                                                                <span v-html="point_error_msg"></span>
                                                            </div>
                                                            <div class="mt-2 use-point point-response" v-if="point_success_info">                                                       
                                                                <div class="d-flex align-items-center">
                                                                    <div class="d-flex flex-column">
                                                                        <span class="note" v-html="point_success_info"></span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @endif
                                                    @if(empty(config('control.close_function_current')['直接輸入型優惠券']))
                                                        @if($data['user']['id'] != 0)
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="discount_selected" value="directcoupon" id="discount_code" v-model="discountSelect">
                                                            <label class="form-check-label" for="discount_code" >
                                                                {{Lang::get('使用折扣碼')}}
                                                            </label>
                                                            <div id="discountCode" class="mb-2 d-none">
                                                                <div class="d-flex justify-content-between">
                                                                    <input type="text" class="form-control" name="directcoupon_input">
                                                                    <button type="button" class="cursor-pointer" @click="set_discount_selected('directcoupon')">{{Lang::get('確認')}}</button>
                                                                </div>
                                                                <div class="code-response" v-if="code_error_msg">
                                                                    <span style="color: var(--mark);" v-html="code_error_msg"></span>
                                                                </div>
                                                                <div class="mt-2 use-code code-response" v-if="code_success_name">                                                       
                                                                    <div class="d-flex align-items-center">
                                                                        <span class="check-icon"><i class="bi bi-check-lg" style="margin-top: 2px;"></i></span>
                                                                        <div class="mb-2">
                                                                            <span class="mr-2" v-html="code_success_name"></span>
                                                                            <span class="note" v-html="code_success_info"></span>
                                                                        </div>
                                                                    </div>
                                                                    <span class="remove-icon cursor-pointer" @click="set_discount_selected('none_discount')">
                                                                        <i class="bi bi-x-lg"></i>
                                                                        {{Lang::get('取消套用折扣碼')}}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @endif
                                                    @endif

                                                    @if(empty(config('control.close_function_current')['優惠券專區']))
                                                        <!-- 屬於會員優惠券 -->
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" type="radio" name="discount_selected" value="coupon" id="coupon_member" v-model="discountSelect">
                                                            <label class="form-check-label" for="coupon_member">
                                                                {{Lang::get('會員優惠券')}}
                                                                <template v-if="discount!=null">
                                                                    <span class="">(</span>
                                                                    {{Lang::get('可以使用會員優惠券')}}：
                                                                    <span v-text="discount.coupon_c"></span>
                                                                    <span class="mr-2">)</span>
                                                                </template>
                                                                <a href="{{url('Product/coupon')}}" target="_blank">
                                                                    {{Lang::get('領取優惠券')}}
                                                                </a>
                                                            </label>
                                                            <div class="mb-2 d-none">
                                                                <template v-if="discount!=null">
                                                                    <template v-for="vo in discount.discountData.coupon">
                                                                        <a href="###"
                                                                        :class="['conpon_select_radio d-block p-1', coupon_pool_id==vo.coupon_pool_id ? 'conpon_selected' : '']" 
                                                                        v-text="vo.coupon_title"
                                                                        @click="select_coupon(vo.coupon_pool_id)"
                                                                        ></a>
                                                                    </template>
                                                                </template>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endif
                                            @endif
                                            @if(empty(config('control.close_function_current')['活動優惠']))
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="radio" name="discount_selected" value="acts_0" id="acts_0" v-model="discountSelect">
                                                    <label class="form-check-label" for="acts_0">
                                                        {{Lang::get('活動優惠')}}
                                                        ({{Lang::get('符合條件活動')}}:<template v-if="discount!=null"><span v-text="discount.acts_c"></span></template>)
                                                    </label>
                                                    <div class="mb-2 d-none">
                                                        <div class="optionsOfferBox act_select_context">
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                            @if($data['marketing'] > 0)
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="discount_selected" value="none_discount" id="cancel_discount" v-model="discountSelect">
                                                    <label class="form-check-label" for="cancel_discount">
                                                    {{Lang::get('取消任何折扣')}}
                                                    <!-- ({{Lang::get('累積紅利點數，立馬省商品金額將被排除')}}) -->
                                                    </label>
                                                </div>
                                            @endif
                                        </div>
                                        <!-- 計算優惠價格用 start -->
                                        <input type="hidden" name="discount" value="none_discount"/>
                                        <input type="hidden" name="point" value=""/>
                                        <input type="hidden" name="directcoupon_code" value=""/>
                                        <input type="hidden" name="contribution_deduct" value=""/>
                                        <!-- 計算優惠價格用 end -->
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>{{Lang::get('商品小計')}}</span>
                                            <span id="finalSubtotal" v-text="'{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalsubTotal))"></span>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>{{Lang::get('折扣')}}</span>
                                            <span id="finalDiscount" style="color: var(--mark);" v-text="'-{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalDiscount) + toNumber(final_contribution_deduct))"></span>
                                            <input type="hidden" name="discountAmount" :value="'{{Lang::get("扣")}}' + '{{config('extra.shop.dollar_symbol')}}' + finalDiscount">
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>{{Lang::get('運費')}}</span>
                                            <span id="finalShipping" v-text="'{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalShipping))"></span>
                                        </div>
                                        <div class="d-flex flex-row-reverse mb-2">
                                            <span v-if="finalShippingDiscountDiff>0" style="color: var(--mark); font-size: 0.8rem;">{{Lang::get("離獲得免運資格尚差")}}： {{config('extra.shop.dollar_symbol')}}<span style="color: var(--mark); font-size: 0.8rem;" v-text="formatPrice(toNumber(finalShippingDiscountDiff))"></span></span>
                                            <span class="d-none" id="discountOver" style="color: var(--success); font-size: 0.8rem;">{{Lang::get("您已獲得免運資格")}}</span>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between total">
                                            <span>{{Lang::get('應付合計')}}</span>
                                            <span id="finalTotal" v-text="'{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}' + formatPrice(toNumber(finalsubTotal) - toNumber(finalDiscount) - toNumber(final_contribution_deduct) + toNumber(finalShipping))"></span>
                                        </div>
                                    </div>   
                                </div>
                                <div class="mb-4">
                                    <h4>{{Lang::get('購物條款')}}</h4>
                                    <div class="form-box">
                                        <div>{!! $data['consent']['shopping'] !!}</div>
                                        <hr>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="accept_policy">
                                            <label class="form-check-label" for="accept_policy">
                                            {{Lang::get('同意購物條款')}} <span class="required">(*{{Lang::get('必勾選')}})</span>
                                            </label>
                                        </div>
                                        <p class="mb-3">※{{Lang::get('請您再次確認以上購買資料是否無誤，並勾選「同意購物條款」後按下「確定訂單」')}}</p>

                                        @if(empty(config('control.close_function_current')['會員管理']))
                                            @if($data['user']['id'] == 0)
                                            <div class="form-check">
                                                <input type="hidden" name="upline_user" value="{{$data['productinfo_recommend']}}">
                                                <input id="defaultCheck2" class="form-check-input" type="checkbox" value="1" name="become_member">
                                                <label class="form-check-label" for="defaultCheck2">
                                                    <span class="terms">{{Lang::get('以購買資料註冊會員')}}</span>
                                                </label>
                                            </div>
                                            <p class="smallText mb-2">
                                                ※{{Lang::get('勾選後將利用所填資料註冊會員，下次購物即可開始使用會員相關優惠(預設密碼為:1234)')}}
                                            </p>
                                            @endif
                                        @endif
                                    </div>
                                    
                                </div>
                                <button type="button" class="confirm-btn" id="confirm_form">{{Lang::get('確定訂單')}}</button>
                                <button class="d-none" id="confirm_form_hidden">{{Lang::get('確定訂單')}}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
</section>
@endsection

@section('Modal')
    <!-- Modal -->
    <a id="model_btn" class="visibility:hidden" data-toggle="modal" data-target="#model"></a>
    <div class="modal large_modal fade shoppingCart" id="model" tabindex="-1" role="dialog" aria-labelledby="modelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="modelTitle">{{Lang::get('選擇優惠方式')}}</h5>
                </div>
                <div class="modal-body" id="boxModel">
                </div>
            </div>
        </div>
    </div>
    <!-- /////////////////////////////////////////// -->

    <!-- descriptionContModel start-->
    <div class="modal fade " id="descriptionContModel" tabindex="-1" role="dialog" aria-labelledby="descriptionContModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content ">
                <div class="modal-header">
                    <h5 class="modal-title" id="descriptionContModelTitle">{{Lang::get('報名流程說明')}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>{!!$data['consent']['examination']!!}</p>
                </div>
            </div>
        </div>
    </div>
    <!-- descriptionContModel end-->

    <!-- confirm modal -->
    <div id="confirmModal_btn" class="d-none" data-toggle="modal" data-target="#confirmModal"></div>
    <div class="modal fade" id="confirmModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
                <div class="title-box">
                    <h5 class="modal-title" id="confirmModalLabel">{{Lang::get('購物明細')}}</h5>
                </div>
                <div class="all-box">
                    <h3>{{Lang::get('商品內容')}}（<span id="finalModal_itemLength"></span>）</h3>
                    <div class="cart-list" id="itemContent"></div>
                    <div class="detail">
                        <div><span>{{Lang::get('商品金額')}}：</span><span id="finalModal_subTotal"></span></div>
                        <div><span>{{Lang::get('折扣')}}：</span><span id="finalModal_discount" style="color: var(--mark);"></span></div>
                        <div><span>{{Lang::get('付款方式')}}：</span><span id="finalModal_payWayName"></span></div>
                        <div><span>{{Lang::get('運送方式')}}：</span><span id="finalModal_sendWayName"></span></div>
                        <div><span>{{Lang::get('運費')}}：</span><span id="finalModal_shipping"></span></div>
                    </div>

                    <div class="total">
                        <div>
                            <span id="finalModal_itemCount"></span>
                            <span>，</span>
                        </div>
                        <div class="d-flex">
                            <span>{{Lang::get('總計')}}：</span>
                            <span id="finalModal_total"></span>
                        </div>        
                    </div>
                </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="send-btn" data-dismiss="modal" id="confirm_order" onclick="go_buy()">{{Lang::get('確認送出')}}</button>
            </div>
          </div>
        </div>
      </div>
    <!-- confirm modal -->

    <!-- error modal -->
    <div id="errorModal_btn" class="d-none" data-toggle="modal" data-target="#errorModal"></div>
    <div class="modal fade" id="errorModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-body">
                <div class="title-box">
                    <h5 class="modal-title" id="errorModalLabel">錯誤訊息</h5>
                </div>
                <div class="all-box">
                    <div class="detail" id="errorDetail"></div>
                </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="send-btn" data-dismiss="modal">確定</button>
            </div>
          </div>
        </div>
      </div>
    <!-- confirm modal -->
@endsection


@section('ownJS')
    <!-- ////////////////////////////////////////////////////// -->
    <script>
        function shaveEllipsis(h) {
            // shave(".productName", h, {
            //     classname: "classname",
            //     character: '......'
            // });
        };

        $(window).on("resize", function() {
            var width1281 = Modernizr.mq('(min-width: 1281px)');
            if (width1281) {
                shaveEllipsis(80);
            } else {
                shaveEllipsis(20);
            }
        }).resize();

        $(function() {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>

    @include('home.cart.cart_ctrl_js')

    <script>
        /*根據不同配送方式顯示不同資訊*/
        function send_way_infos(value=null) {
            $('.shipping_blank').hide();

            if (!value) {
                return;
            }

            let shipping_type = maskVM.type_decode;
            let name, free_rule, shipping_price;

            for (let i = 0; i < shipping_type.length; i++) {
                if (shipping_type[i].shipping_fee_id == value) {
                    name = shipping_type[i].name;
                    free_rule = shipping_type[i].free_rule;
                    shipping_price = shipping_type[i].price;
                    final_priceVM.currentShippingType = i;

                    break;
                }
            }

            final_priceVM.finalShipping = 0;
            final_priceVM.finalShippingDiscountDiff = 0;
            $('#discountOver').addClass('d-none');
            if(shipping_price > 0){
                final_priceVM.finalShipping = Number(shipping_price);
                if (final_priceVM.finalsubTotal < free_rule) { /*未達免運條件*/
                    if (free_rule != 999999999){
                        final_priceVM.finalShippingDiscountDiff = free_rule - final_priceVM.finalsubTotal;
                    }
                }else{
                    final_priceVM.finalShipping = 0;
                    if (free_rule != 999999999){
                        $('#discountOver').removeClass('d-none');
                    }
                }
            }

            switch (name) {
                case "{{Lang::get('宅配')}}":
                    $('#sync_member').show();
                    $('#transport_location_name').parent().show();
                    $('#transport_location_phone').parent().show();
                    $('#transport_email').parent().show();
                    $('#cz_select').show();
                    break;
                case "{{Lang::get('到店取貨')}}":
                    $('#sync_member').show();
                    $('#transport_location_name').parent().show();
                    $('#transport_location_phone').parent().show();
                    $('#transport_email').parent().show();
                    $('#pickUpRemind_btn').click();
                    break;
                default:
                    $('#sync_member').show();
                    $('#transport_location_name').parent().show();
                    $('#transport_location_phone').parent().show();
                    $('#transport_email').parent().show();    
                    $('#store_pickup').show();
                    break;
            }
        }

        function carrierNumLenChange() {
            const carrierType = Number($('select[name="CarrierType"]').val());
            $('#CarrierNum').val('');

            if (carrierType == 3) {
                $('#CarrierNum').attr('maxlength', 8);
            } else {
                $('#CarrierNum').attr('maxlength', 16);
            }
        }

        function assign_memeber_data(){
            $('#transport_location_name').val(`{{$data['user']['name']}}`);
            $('#transport_location_phone').val(`{{$data['user']['phone']}}`);
            $('#transport_email').val(`{{$data['user']['email']}}`);
        }

        $('#confirm_form').on('click', async function() {
            if (cart_tableVM.cartData.length == 0) {
                swal_fire("{{Lang::get('購物車內無商品')}}");
                return;
            }


            let error_arr = [];
            const send_way_value = $('select[name="send_way"]').val(), pay_way_value = $('select[name="pay_way"]').val();
            let send_way_name, pay_way_name;

            if (send_way_value) {
                send_way_name = $('option:selected', 'select[name="send_way"]').text().split(' | ')[0];
            } else {
                swal_fire("{{Lang::get('請選擇')}}" + "{{Lang::get('選擇配送方式')}}");
                return;
            }

            if (pay_way_value) {
                pay_way_name = $('option:selected', 'select[name="pay_way"]').text();
            } else {
                swal_fire("{{Lang::get('請選擇')}}" + "{{Lang::get('選擇付款方式')}}");
                return;
            }

            if ($('select[name="invoice_style"]').val() == '') {
                swal_fire("{{Lang::get('請選擇發票開立方式')}} ");
                return;
            }

            /* --- Test Start --- */
            if (use_thirdpart_logistic && send_way_name == "{{Lang::get('宅配')}}" && pay_way_name == "{{Lang::get('貨到付款')}}") {
                swal_fire("{{Lang::get('宅配無法使用貨到付款')}}");
                return;
            }

            if (send_way_name == "{{Lang::get('宅配')}}" && (pay_way_name.includes("匯款") || pay_way_name.includes("轉帳"))) {
                swal_fire("{{Lang::get('宅配請於線上完成付款')}}");
                return;
            }

            const subTotal = parseInt($('.subTotal span').text());
            if (ecpay_shippable_supermarket.indexOf(send_way_name) != -1 && subTotal > 20000) {
                swal_fire("{{Lang::get('超商取貨無法運送金額超過20000元之商品')}}");
                return;
            }

            if ($('#transport_location_name').val() == '') {
                error_arr.push("{{Lang::get('請輸入姓名')}}");
            }

            let regex;
            regex = /^09[0-9]{8}$/;
            if ($('#transport_location_phone').val() == '') {
                error_arr.push("{{Lang::get('請輸入手機')}}");
            } else {
                if ($('#transport_location_phone').val().match(regex) == false) {
                    error_arr.push("{{Lang::get('請輸入09開頭的10位手機號碼')}}");
                }
            }

            regex = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g;
            if ($('#transport_email').val() == '') {
                error_arr.push("{{Lang::get('請輸入信箱')}}");
            } else {
                if (regex.test($('#transport_email').val()) == false) {
                    error_arr.push("{{Lang::get('email格式錯誤')}}");
                }
            }

            let city_town;
            if (["{{Lang::get('宅配')}}", "{{Lang::get('到店取貨')}}"].includes(send_way_name) == true) {
                if (send_way_name == "{{Lang::get('宅配')}}") {
                    if ($('#myCity').val() == '') {
                        error_arr.push("{{Lang::get('請選擇縣市')}}");
                    }
        
                    if ($('#myTown').val() == '') {
                        error_arr.push("{{Lang::get('請選擇鄉鎮區')}}");
                    }

                    if ($('#transport_location').val() == '') {
                        error_arr.push("{{Lang::get('請輸入地址')}}");
                    }

                    city_town = get_city_town($('#myCity').val(), $('#myTown').val());

                    if (city_town.code != 200) {
                        error_arr.push(city_town.result);
                    }
                }
            } else {
                if (use_thirdpart_logistic) {
                    if ($('#store').val() == '') {
                        error_arr.push("{{Lang::get('請選擇門市')}}");
                    }
                } else {
                    if ($('#storeNo').val() == '' || $('#storeName').val() == '' || $('#storeAddr').val() == '') {
                        error_arr.push("{{Lang::get('門市資訊不完整')}}");
                    } else {
                        let regex = /^[0-9]{4,}$/;
                        if ($('#storeNo').val().match(regex) === null) {
                            error_arr.push("{{Lang::get('店鋪號碼格式錯誤')}}");
                        }
                    }
                }
            }

            switch ($('select[name="invoice_style"]').val()) {
                // 個人共通性載具
                case '3':
                    if ($('#CarrierType').val() == '3') {
                        if ($('#CarrierNum').val() == '') {
                            error_arr.push("請輸入載具編號");
                        } else {
                            let regex = /^\/[0-9A-Z\.\+\-]{7}$/;
                            if ($('#CarrierNum').val().match(regex)===null) {
                                error_arr.push("載具編號格式錯誤");
                            }
                            // 可能會暫時性關閉，因財政部系統可能會進行不預期的維護作業
                            $.ajax({
                                url: "{{url('Cart/invoice_carrier_check')}}",
                                type: 'GET',
                                async: false,
                                data: {
                                    CarrierNum: $('[name="CarrierNum"]').val(),
                                },
                                success: function(response) {
                                    if (response.code == 0) {
                                        error_arr.push(response.msg);
                                    }
                                },
                                error: function(response) {
                                    error_arr.push('載具編號碼驗證有誤');
                                }
                            });
                        }
                    } else if ($('#CarrierType').val() == '2') {
                        if ($('#CarrierNum').val() == '') {
                            error_arr.push("請輸入自然人憑證號碼");
                        }else {
                            regex = /^[A-Z]{2}\d{14}$/;
                            if (!$('#CarrierNum').val().match(regex)) {
                                error_arr.push("自然人憑證號碼格式錯誤");
                            }
                        }
                    } else {
                        error_arr.push("請選擇載具類型");
                    }

                    break;
                // 公司戶發票(三聯發票)
                case '4':
                    if ($('#company_title').val() == '') {
                        error_arr.push("{{Lang::get('請輸入')}}{{Lang::get('公司抬頭')}}");
                    }

                    if ($('#uniform_numbers').val() == '') {
                        error_arr.push("{{Lang::get('請輸入')}}{{Lang::get('統一編號')}}");
                    } else {
                        regex = /^\d{8}$/;
                        if (!$('#uniform_numbers').val().match(regex)) {
                            error_arr.push("{{Lang::get('統一編號格式錯誤')}}");
                        }
                    }

                    break;
                // 捐贈
                case '5':
                    if ($('#LoveCode').val() == '') {
                        error_arr.push("請輸入捐贈碼");
                    } else {
                        // 可能會暫時性關閉，因財政部系統可能會進行不預期的維護作業
                        $.ajax({
                            url: "{{url('Cart/invoice_lovecode_check')}}",
                            type: 'GET',
                            async: false,
                            data: {
                                LoveCode: $('[name="LoveCode"]').val(),
                            },
                            success: function(response) {
                                if (response.code == 0) {
                                    error_arr.push(response.msg);
                                }
                            },
                            error: function(response) {
                                error_arr.push('捐贈碼驗證有誤');
                            }
                        });
                    }

                    break;
                default:
                    break;
            }

            if (final_priceVM.discountSelect != 'none_discount') {
                if (final_priceVM.discountSelect == 'directcoupon') {
                    if ($('input[name="directcoupon_input"]').val() == '') {
                        error_arr.push("{{Lang::get('請輸入活動優惠券代碼')}}");
                    } else {
                        if (final_priceVM.code_success_name=='' && final_priceVM.code_success_info=='') {
                            error_arr.push("{{Lang::get('活動優惠券代碼未套用')}}");
                        }
                    }
                }
            }

            if ($('#accept_policy').prop('checked') == false) {
                error_arr.push("{{Lang::get('未勾選購買須知')}}");
            }
            /* --- Test End --- */

            if (error_arr.length > 0) {
                let error_html = '';
                error_arr.forEach((error) => {
                    error_html += `<span>${error}</span><br>`;
                });
                $('#errorDetail').html(error_html);
                $('#errorModal_btn').click();
            } else {
                if (["{{Lang::get('宅配')}}", "{{Lang::get('到店取貨')}}"].includes(send_way_name) == true) {
                    if (send_way_name == "{{Lang::get('宅配')}}") {
                        $('input[name="addrC"]').val($('#myZip').val() + ' ' + city_town.result + $('#transport_location').val());
                    }
                } else {
                    if (use_thirdpart_logistic) {
                        $('input[name="addrC"]').val($('input[name="CVSStoreID"]').val() + ' ' + $('input[name="CVSStoreName"]').val() + ' ' + $('input[name="CVSTelephone"]').val());
                    } else {
                        $('input[name="addrC"]').val($('#storeNo').val() + ' ' +  $('#storeName').val() + ' ' + $('#storeAddr').val());
                    }
                }

                let itemLength = 0, itemContent = '';
                for (item of cart_tableVM.cartData) {
                    itemLength += Number(item.num);
                    var item_name = item.info_title;
                    item_name += item.type_title ? (' - '+item.type_title) : '';
                    itemContent += `
                        <div class="item d-flex justify-content-between align-items-center">
                            <div class="d-flex flex-wrap align-items-center">
                                <div class="img mr-2" style="background-image: url(&quot;/public/static/index/${item.info_pic1}&quot;);"></div>
                                <p class="mb-0">${item_name}</p>
                            </div>
                            <div class="d-flex flex-column align-items-end">
                                <span class="mb-2">x<span>${item.num}</span></span>
                                <span>{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}${item.countPrice * item.num}</span>
                            </div>
                        </div>
                    `;
                }

                let itemLength_text = "{{Lang::get('共 XX 件')}}".replace('XX', itemLength);
                $('#finalModal_itemLength').html(itemLength_text);
                $('#itemContent').html(itemContent);
                $('#finalModal_subTotal').html(`{{config('extra.shop.dollar_symbol')}}` + final_priceVM.finalsubTotal);
                $('#finalModal_discount').html(`-{{config('extra.shop.dollar_symbol')}}` + (Number(final_priceVM.finalDiscount) + Number(final_priceVM.final_contribution_deduct)));
                $('#finalModal_payWayName').html(pay_way_name);
                $('#finalModal_sendWayName').html(send_way_name);
                $('#finalModal_shipping').html(`{{config('extra.shop.dollar_symbol')}}` + final_priceVM.finalShipping);
                $('#finalModal_total').html(`{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}` + (Number(final_priceVM.finalsubTotal) - Number(final_priceVM.finalDiscount) - Number(final_priceVM.final_contribution_deduct) + Number(final_priceVM.finalShipping)));                
                let item_count = 0;
                for (let i = 0; i < document.getElementsByClassName('cart-count').length; i++) {
                    item_count += parseInt(document.getElementsByClassName('cart-count')[i].value);
                }

                let item_count_text = "{{Lang::get('共 XX 件商品')}}".replace('XX', item_count);
                $('#finalModal_itemCount').html(item_count_text);
                $('#confirmModal_btn').click();
            }
        });

        function swal_fire(message) {
            Swal.fire({
                title: message,
                icon: 'error',
                confirmButtonText:"{{Lang::get('確認')}}",
                confirmButtonColor: 'var(--btn-mainlink)',
            });
        }
    </script>

    <!-- 付款法功能 -->
    <script>
        var payingData = {
                type_decode: [],
                pay_way: '',
                cartData:[],
            }
        var payingVM = new Vue({
            el: '#paying_div', 
            data: payingData,
            computed: {
                can_card_pay(){
                    for (let idx = 0; idx < this.cartData.length; idx++) {
                        const element = this.cartData[idx];
                        if(element.card_pay==0){
                            return false;
                        }
                    }
                    return true;
                },
            },
            methods: {
            },
        });
        /* 初始化勾選付法 */
        function init_paying(){
            var paying_type = payingVM.type_decode;

            if (paying_type.length == 0) {
                $('#confirm_form').hide();

                Swal.fire({
                    title: "{{Lang::get('目前購買之商品無可用付款方式，無法下單')}}",
                    icon: 'error',
                    content:'',
                    confirmButtonText:"{{Lang::get('確認')}}",
                    confirmButtonColor: 'var(--btn-mainlink)',
                });
            }
        }
    </script>
    <!-- 運費功能 -->
    <script type="text/javascript">
        var shippingData = {
                type_decode: [],
                send_way: 0,
            }
        var maskVM = new Vue({
            el: '#shipping_div', 
            data: shippingData,
            computed: {
            },
            methods: {
            },
        });
        /* 初始化勾選運費 */
        function init_shipping(){
            var shipping_type = maskVM.type_decode;

            if(shipping_type.length>0){
                maskVM.send_way = shipping_type[0]['shipping_fee_id'];
            }else{
                $('#confirm_form').hide();

                Swal.fire({
                    title: "{{Lang::get('目前購買之商品無可用配送方式，無法下單')}}",
                    icon: 'error',
                    content:'',
                    confirmButtonText:"{{Lang::get('確認')}}",
                    confirmButtonColor: 'var(--btn-mainlink)',
                });
            }
        }

        function get_order_form_data(){
            $('input[name="contribution_deduct"]').val(JSON.stringify(cart_table_data.cartData_deduct));
            let order_data = {};
            let data = $('#cartform').serialize();
            data = decodeURIComponent(data);
            data = data.split('&');
            for (let i = 0; i < data.length; i++) {
                let temp = data[i].split('=');
                order_data[temp[0]] = temp[1];
            }

            cartform_obj = new FormData(cartform);
            order_data['become_member'] = cartform_obj.get('become_member') ?? '0';
            
            return order_data;
        }

        function selectPlace() {            
            if (use_thirdpart_logistic) {
                let order_data = get_order_form_data();
                let selectPlace_url ="{{url('Cart/selectPlace')}}" + '?' + 'Order_Data=' + JSON.stringify(order_data);
                location.href = selectPlace_url;
            } else {
                let send_way_name = $('option:selected', 'select[name="send_way"]').text().split(' | ')[0];
                switch (send_way_name) {
                    case "{{Lang::get('7-11取貨')}}":
                        window.open('https://emap.pcsc.com.tw/');
                        break;
                    case "{{Lang::get('全家取貨')}}":
                        window.open('https://www.family.com.tw/Marketing/Map');
                        break;
                    case "{{Lang::get('萊爾富取貨')}}":
                        window.open('https://www.hilife.com.tw/storeInquiry_street.aspx');
                        break;
                    case "{{Lang::get('OKmart取貨')}}":
                        window.open('https://www.okmart.com.tw/convenient_shopSearch');
                        break;
                    default:
                        break;
                }
            }
        }
    </script>
    <!-- 捐贈碼功能 -->
    <script>
        var loveCodeData = {
                type_decode: [],
                lovecode: 0,
            }
        var loveCodeVM = new Vue({
            el: '#loveCode_div', 
            data: loveCodeData,
            computed: {
            },
            methods: {
                changeLoveCode: function() {
                    var code = $('#LoveCode_select').val();

                    if (code == '') {
                        $('#LoveCode').val('');
                    } else {
                        $('#LoveCode').val(code);
                    }                
                },
            },
        });
        /* 初始化捐贈碼 */
        function init_love_code(){
            var loveCode_type = loveCodeVM.type_decode;
            if (loveCode_type.length > 0) {
                loveCodeVM.lovecode = loveCode_type[0]['id'];
                $('#LoveCode_select').val(loveCode_type[0]['code']);
                $('#LoveCode').val(loveCode_type[0]['code']);
            }
        }
    </script>
    <!-- 加價購功能 -->
    <script type="">
        function addprice_to_cart(cart_id){
            $.ajax({
                url: "{{url('Cart/cartCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                datatype: 'json',
                data: {
                    cmd: 'increase',
                    num: 1,
                    product_id: cart_id,
                    cart_session: 'cart',
                },
                error: function (xhr) {
                    console.error(xhr);
                },
                success: function (response) {
                    if (response.code) {
                        Swal.fire({
                            title: "{{Lang::get('操作成功')}}",
                            icon: 'success',
                            // content:'',
                            confirmButtonText:"{{Lang::get('確認')}}",
                            confirmButtonColor: 'var(--btn-mainlink)',
                        }).then((result) => {
                            location.reload();
                        });
                    }else {
                        Swal.fire({
                            title: response.msg,
                            icon: 'error',
                            // content:'',
                            confirmButtonText:"{{Lang::get('確認')}}",
                            confirmButtonColor: 'var(--btn-mainlink)',
                        });
                    }
                }
            });
        }
    </script>
    <!-- 縣市下拉選 -->
    <script>
        //利用jQuery的ajax把縣市編號(CNo)傳到town_ajax.php把相對應的區域名稱回傳後印到選擇區域(鄉鎮)下拉選單
        if ($('#myCity').val()) {
            var CNo = $('#myCity').val();
            $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                url: "{{url('Login/town_ajax')}}",
                cache: false,
                data: { CNo: CNo },
                error: function() {
                },
                success: function(data) {
                    $('#myTown').html(`<option value="">{{Lang::get('請選擇鄉鎮區')}}</option>` + data);
                    $('#myZip').val(""); //避免重新選擇縣市後郵遞區號還存在，所以在重新選擇縣市後郵遞區號欄位清空
                }
            });
        }

        $('#myCity').change(function() {
            var CNo = $('#myCity').val();
            $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                url: "{{url('Login/town_ajax')}}",
                cache: false,
                data: { CNo: CNo },
                error: function() {
                },
                success: function(data) {
                    $('#myTown').html(`<option value="">{{Lang::get('請選擇鄉鎮區')}}</option>` + data);
                    $('#myZip').val(""); //避免重新選擇縣市後郵遞區號還存在，所以在重新選擇縣市後郵遞區號欄位清空
                }
            });
        });

        //利用jQuery的ajax把縣市編號(CNo)傳到town_ajax.php把相對應的區域名稱回傳後印到選擇區域(鄉鎮)下拉選單
        $('#myTown').change(function() {
            var TNo = $('#myTown').val();
            $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                url: "{{url('Login/zip_ajax')}}",
                cache: false,
                data: { TNo: TNo },
                error: function() {
                },
                success: function(data) {
                    $('#myZip').val(data);
                },
            });
        });

        function get_city_town(city, town) {
            let result;

            $.ajax({
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                url: "{{url('Login/city_town_ajax')}}",
                cache: false,
                async: false,
                data: { city: city, town: town },
                error: function() {
                },
                success: function(data) {
                    result = data;
                },
            });

            return result;
        }
    </script>

    <script type="text/javascript">
        const cart_session = 'cart';
        const no_addr_ship = "{{Lang::get('到店取貨')}}";  /*免填地址的運送方式 到店取貨*/
        const dont_need_select_ct = [
            no_addr_ship, 
            "{{Lang::get('全家取貨')}}", 
            "{{Lang::get('7-11取貨')}}", 
            "{{Lang::get('萊爾富取貨')}}", 
            "{{Lang::get('OKmart取貨')}}"
        ]; /*免選擇縣市的運送方式*/
        const ecpay_shippable_supermarket = JSON.parse("{{json_encode(config('extra.ecpay.shippable_supermarket'))}}".replace(/&quot;/g, '"').trim());  /*綠界物流有物流功能的 超商 運送方式*/
        const use_thirdpart_logistic = "{{config('control.thirdpart_logistic')}}"=="1"  /*是否有使用第三方物流功能*/;
        const use_thirdpart_invoice = "{{config('control.thirdpart_invoice')}}" == "1"  /*是否有使用綠界電子發票功能*/;
        const closeMember = "{{config('control.close_function_current')['會員管理'] ?? ''}}"; /*關閉會員功能*/

        let final_prices = {
            discountSelect: "none_discount",
            
            exchange_rate: 1,
            discount: null,
            available_points: 0,
            finalsubTotal: 0,
            finalDiscount: 0,
            final_contribution_deduct: 0,
            finalShipping: 0,
            finalShippingDiscountDiff: 0,
            currentShippingType: 0,

            point_error_msg: '',
            point_success_info: '',
            point_input: 0,

            code_error_msg: '',
            code_success_name: '',
            code_success_info: '',

            coupon_pool_id: 0,
        }

        const final_priceVM = new Vue({
            el: '#finalPart',
            data: final_prices,
            watch: {
                discountSelect: {
                    handler(v_new, v_old){
                        if(v_new==v_old){ return; }
                        this.set_total(v_new);
                    },
                },
            },
            methods: {
                formatPrice(price) {
                    return price.toLocaleString();
                },
                toNumber(num) {
                    return Number(num);
                },
                set_contribution_deduct(type_id, input_name){ /*圓滿點抵扣*/
                    let total_deduct = 0;
                    let sum_deduct_invest = 0;
                    let sum_deduct_consumption = 0;
                    var array = Object.keys(cart_table_data.cartData_deduct);

                    $not_allowed = false;
                    for (let idx = 0; idx < array.length; idx++) {
                        const key = array[idx];
                        const key_item = cart_table_data.cartData_deduct[key];
                        let deduct_invest = key_item.deduct_invest ? key_item.deduct_invest : 0;
                        let deduct_consumption = key_item.deduct_consumption ? key_item.deduct_consumption : 0;
                        deduct_invest = Number(deduct_invest);
                        deduct_consumption = Number(deduct_consumption);
                        if(deduct_consumption > cart_table_data.cartData_deduct_consumption[key]){
                            Vue.toasted.show(
                                "{{Lang::get('超過消費圓滿點數使用上限')}}:" + Math.floor(cart_table_data.cartData_deduct_consumption[key]), 
                                vt_error_obj
                            );
                            $not_allowed = true;
                            break;
                        }
                        else if(deduct_invest + deduct_consumption > key_item.max_deduct){
                            Vue.toasted.show(
                                key_item['info_title'] + " {{Lang::get('超出折抵上限')}}", 
                                vt_error_obj
                            );
                            $not_allowed = true;
                            break;
                        }
                        else if(sum_deduct_invest + deduct_invest > cart_table_data.increasing_limit_invest){
                            Vue.toasted.show(
                                "{{Lang::get('您僅擁有功德圓滿點數')}}:" + Math.floor(cart_table_data.increasing_limit_invest),
                                vt_error_obj
                            );
                            $not_allowed = true;
                            break;
                        }
                        else if(sum_deduct_consumption + deduct_consumption > cart_table_data.increasing_limit_consumption){
                            Vue.toasted.show(
                                "{{Lang::get('您僅擁有消費圓滿點數')}}:" + Math.floor(cart_table_data.increasing_limit_consumption), 
                                vt_error_obj
                            );
                            $not_allowed = true;
                            break;
                        }
                        else if(this.total_lt_zero(this.finalDiscount, (deduct_invest+deduct_consumption) * this.exchange_rate)){
                            Vue.toasted.show("{{Lang::get('訂單金額不可小於0')}}", vt_error_obj);
                            $not_allowed = true;
                            break;
                        }
                        total_deduct += deduct_invest;
                        sum_deduct_invest += deduct_invest;
                        total_deduct += deduct_consumption;
                        sum_deduct_consumption += deduct_consumption;
                    }
                    if($not_allowed){
                        cart_table_data.cartData_deduct[type_id][input_name] = 0;
                        cart_tableVM.$forceUpdate();
                    }
                    this.final_contribution_deduct = total_deduct * this.exchange_rate;
                },
                select_coupon(coupon_pool_id){
                    this.coupon_pool_id = coupon_pool_id;
                    this.set_discount_selected('coupon');
                },
                set_discount_selected(new_discountSelect){
                    this.discountSelect = new_discountSelect;
                    this.set_total(new_discountSelect);
                },
                set_total: async function(new_discountSelect){
                    var orderdata_discount = new_discountSelect;
                    var orderdata_point = 0;
                    var orderdata_directcoupon_code = '';

                    if(new_discountSelect=='firstbuy_select'){ /*會員首購優惠*/
                        if(this.total_lt_zero(this.discount.discountData.firstBuyDiscount.discount, this.final_contribution_deduct)){
                            Vue.toasted.show("{{Lang::get('訂單金額不可小於0')}}", vt_error_obj);
                            this.finalDiscount = 0;
                            orderdata_discount = 'none_discount';
                        }else{
                            this.finalDiscount = this.discount.discountData.firstBuyDiscount.discount;
                        }
                    }
                    else if(new_discountSelect=='vipdiscount_select'){ /*VIP會員優惠*/
                        if(this.total_lt_zero(this.discount.discountData.vipDiscount.discount, this.final_contribution_deduct)){
                            Vue.toasted.show("{{Lang::get('訂單金額不可小於0')}}", vt_error_obj);
                            this.finalDiscount = 0;
                            orderdata_discount = 'none_discount';
                        }else{
                            this.finalDiscount = this.discount.discountData.vipDiscount.discount;
                        }
                    }
                    else if(new_discountSelect=='points_0'){ /*紅利點數優惠*/
                        orderdata_discount = 'none_discount';
                        if (!this.point_input || this.point_input == 0) {
                            this.finalDiscount = 0;
                        }else{
                            this.point_input = Math.round(this.point_input);
                            if (this.point_input > this.finalsubTotal + this.finalShipping) {                           
                                this.point_error_msg = `您最多可使用<span class="num" style="color: var(--mark);">${Math.floor(this.finalsubTotal + this.finalShipping)}</span>點`;
                                this.point_success_info = ``;

                                this.finalDiscount = 0;
                                Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                            }
                            else if (this.point_input > this.discount.points_limit) {                            
                                this.point_error_msg = `您最多可使用<span class="num" style="color: var(--mark);">${Math.floor(this.discount.points_limit)}</span>點`;
                                this.point_success_info = ``;
                                
                                this.finalDiscount = 0;
                                Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                            }
                            else {
                                this.point_error_msg = ``;
                                this.point_success_info = `此次使用積分數<span class="num" style="color: var(--mark);">${this.point_input}</span>`;
                                this.point_success_info +=`，折抵<span class="num" style="color: var(--mark);">${this.point_input * this.exchange_rate}</span>元。`;

                                orderdata_point = this.point_input;
                                if(this.total_lt_zero(this.point_input * this.exchange_rate, this.final_contribution_deduct)){
                                    Vue.toasted.show("{{Lang::get('訂單金額不可小於0')}}", vt_error_obj);
                                    this.finalDiscount = 0;
                                    orderdata_discount = 'none_discount';
                                }else{
                                    this.finalDiscount = this.point_input * this.exchange_rate;
                                    orderdata_discount = 'points_' + this.point_input;
                                }
                            }
                        }
                    }
                    else if(new_discountSelect=='directcoupon'){ /*直接輸入型優惠券*/
                        orderdata_discount = 'none_discount';
                        var resp = await this.get_direct_coupon_discount();
                        // console.log(resp);
                        if(resp.user_code){
                            if (resp.status == 0) {
                                this.code_error_msg = `{{Lang::get('請重新檢查代碼是否填寫有誤或購買條件是否達成')}}`;
                                this.code_success_name = ``;
                                this.code_success_info = ``;

                                this.finalDiscount = 0;
                                Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                            }else{
                                this.code_error_msg = ``;
                                this.code_success_name = resp.name;
                                this.code_success_info = `折抵${resp.discount}元，此折扣碼${resp.end}。`;

                                if(this.total_lt_zero(Number(resp.discount), this.final_contribution_deduct)){
                                    Vue.toasted.show("{{Lang::get('訂單金額不可小於0')}}", vt_error_obj);
                                    this.finalDiscount = 0;
                                    orderdata_discount = 'none_discount';
                                }else{
                                    this.finalDiscount = Number(resp.discount);
                                    orderdata_directcoupon_code = resp.user_code;
                                    orderdata_discount = 'directcoupon_' + Number(resp.id);
                                }
                            }
                        }else{
                            this.code_error_msg = resp.msg;
                            this.code_success_name = ``;
                            this.finalDiscount = 0;
                            Vue.toasted.show("{{Lang::get('無法套用')}}", vt_error_obj);
                        }
                    }
                    else if(new_discountSelect=='coupon'){ /*會員優惠券*/
                        use_coupon = false;
                        if(this.coupon_pool_id){
                            for (let idx = 0; idx < this.discount.discountData.coupon.length; idx++) {
                                const coupon = this.discount.discountData.coupon[idx];
                                if(coupon.coupon_pool_id==this.coupon_pool_id){
                                    if(this.total_lt_zero(coupon.discount, this.final_contribution_deduct)){
                                        Vue.toasted.show("{{Lang::get('訂單金額不可小於0')}}", vt_error_obj);
                                        this.finalDiscount = 0;
                                        orderdata_discount = 'none_discount';
                                    }else{
                                        use_coupon = true;
                                        this.finalDiscount = coupon.discount;
                                        orderdata_discount = 'coupon_' + this.coupon_pool_id;
                                    }
                                }
                            }
                        }
                        if(!use_coupon){
                            orderdata_discount='none_discount';
                            this.finalDiscount = 0;
                        }
                    }
                    else if(new_discountSelect=='acts_0'){ /*活動優惠*/
                        let act_discount = this.finalsubTotal - this.discount.discountData.acts.sum;
                        if(act_discount>0){
                            if(this.total_lt_zero(act_discount, this.final_contribution_deduct)){
                                Vue.toasted.show("{{Lang::get('訂單金額不可小於0')}}", vt_error_obj);
                                this.finalDiscount = 0;
                                orderdata_discount = 'none_discount';
                            }else{
                                this.finalDiscount = act_discount;
                            }
                        }else{
                            orderdata_discount='none_discount';
                            this.finalDiscount = 0;
                        }
                    }
                    else{
                        this.discountSelect='none_discount';

                        this.point_error_msg = '';
                        this.point_success_info = '';

                        this.code_success_name = '';
                        this.code_success_info = '';

                        this.finalDiscount = 0;
                    }

                    $('input[name="discount"]').val(orderdata_discount);
                    $('input[name="point"]').val(orderdata_point);
                    $('input[name="directcoupon_code"]').val(orderdata_directcoupon_code);
                },
                async get_direct_coupon_discount() {
                    var user_code = $('input[name="directcoupon_input"]').val();
                    if(!user_code){ 
                        this.code_error_msg = ``;
                        this.code_success_name = ``;
                        this.code_success_info = ``;
                        return {};
                    }
                    return await $.ajax({
                        url: "{{url('Coupondirect/get_discount')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': csrf_token
                        },
                        data: {
                            user_code: encodeURI(user_code),
                        },
                    });
                },
                total_lt_zero(finalDiscount=0, final_contribution_deduct=0){
                    var total = this.toNumber(this.finalsubTotal) + this.toNumber(this.finalShipping);
                    total -= this.toNumber(finalDiscount);
                    total -= this.toNumber(final_contribution_deduct);
                    return total<0;
                },
            },
        });

        let cart_table_data = {
            cartData: [],
            discount: null,
            types_need_checked: [],
            types_need_limit: [],
            types_need_option: [],
            total: 0,

            exchange_rate: 1,
            cartData_deduct: {},
            cartData_deduct_consumption: {},
            increasing_limit_invest: 0,
            increasing_limit_consumption: 0,
            
            control_card_pay: "{{config('control.control_card_pay')}}",
            control_register: "{{config('control.control_register')}}",
            thirdpart_money: "{{config('control.thirdpart_money')}}",
            closeKol:"{{config('control.close_function_current')['網紅列表'] ?? ''}}", /*關閉網紅功能*/
            closeAddPrice:"{{config('control.close_function_current')['加價購設定'] ?? ''}}", /*關閉加價購功能*/
        }

        function get_cart_data(need_init=false){
            $.ajax({
                type: 'post',
                dataType: 'json',
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                url: "{{url('Cart/ajax_cart_data')}}",
                data: { cart_session: cart_session },
                success: function(resp){
                    // console.log(resp);
                    if(resp.cartData.length==0){
                        $('#confirm_form').hide();
                    }
                    
                    cart_table_data.exchange_rate = resp.exchange_rate;
                    final_priceVM.exchange_rate = resp.exchange_rate;

                    cart_table_data.cartData_deduct_consumption = resp.discount[0].discountData.ContributionDeduct.cartData_deduct_consumption;
                    /*初始化功德點數折抵*/
                    let cartData_deduct = resp.discount[0].discountData.ContributionDeduct.cartData_deduct;
                    for (let idx = 0; idx < resp.cartData.length; idx++) {
                        const element = resp.cartData[idx];
                        if(typeof(cart_table_data.cartData_deduct[element.type_id])=='undefined'){
                            cart_table_data.cartData_deduct[element.type_id] = {}
                        }
                        cart_table_data.cartData_deduct[element.type_id].info_title = element.info_title;
                        cart_table_data.cartData_deduct[element.type_id].max_deduct = cartData_deduct[element.type_id];
                        if(typeof(cart_table_data.cartData_deduct[element.type_id].deduct_invest)=='undefined'){
                            cart_table_data.cartData_deduct[element.type_id].deduct_invest = '';
                        }
                        if(typeof(cart_table_data.cartData_deduct[element.type_id].deduct_consumption)=='undefined'){
                            cart_table_data.cartData_deduct[element.type_id].deduct_consumption = '';
                        }
                    }
                    cart_table_data.increasing_limit_invest = resp.discount[0].discountData.ContributionDeduct.increasing_limit_invest;
                    cart_table_data.increasing_limit_consumption = resp.discount[0].discountData.ContributionDeduct.increasing_limit_consumption;

                    payingVM.cartData = resp.cartData;
                    cart_table_data.cartData = resp.cartData;
                    cart_table_data.discount = resp.discount[0];
                    cart_table_data.types_need_checked = resp.types_need_checked;
                    cart_table_data.types_need_limit = resp.types_need_limit;
                    cart_table_data.types_need_option = resp.types_need_option;
    
                    cart_table_data.total = 0;
                    resp.cartData.forEach((value) => {
                        cart_table_data.total += Number(value.countPrice) * Number(value.num);
                    });
    
                    final_priceVM.finalsubTotal = cart_table_data.total;
                    final_priceVM.discount = resp.discount[0];
                    final_priceVM.available_points = resp.discount[0].discountData.points[0].point;
                        
                    payingData.type_decode = resp.paying;
                    shippingData.type_decode = resp.shipping;
                    loveCodeData.type_decode = resp.love_code_list;
                    
                    send_way_infos($('[name="send_way"]').val());

                    if(need_init){
                        init_paying();
                        init_shipping();
                        init_love_code();
                        setTimeout(() => {
                            let OrderData = `{{$data['OrderData']}}`.replace(/&quot;/g, '"').trim();
                            if (OrderData) {
                                OrderData = JSON.parse(OrderData);
                                console.log(OrderData);
                                for (let key in OrderData) {
                                    if (key == 'selectPlace') {
                                        if (OrderData[key]['CVSStoreID'] && OrderData[key]['CVSStoreName']){
                                            $('#store').val(OrderData[key]['CVSStoreID'] + ' ' + OrderData[key]['CVSStoreName']);
                                        }
                                        for (let key2 in OrderData[key]) {
                                            $('input[name="' + key2 + '"]').val(OrderData[key][key2]);
                                        }
                                    } else {
                                        if (['ps', ].includes(key)) {
                                            $('textarea[name="' + key + '"]').val(OrderData[key]);
                                        }
                                        else if (['send_way', 'F_I_CNo', 'F_I_TNo', 'pay_way', 'invoice_style', 'CarrierType', ].includes(key) == false) {
                                            $('input[name="' + key + '"]').val(OrderData[key]);
                                        }
                                        else {
                                            $('select[name="' + key + '"]').val(OrderData[key]);
        
                                            if (key == 'send_way') {
                                                send_way_infos(OrderData[key]);
                                            } else if (key == 'invoice_style') {
                                                showInputs(OrderData[key]);
                                            }
                                        }
                                    }
                                }
                            }
                        }, 500);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                }
            });
        }
        get_cart_data(true);

        const cart_tableVM = new Vue({
            el: '#cart_table',
            data: cart_table_data,
            methods: {
                set_contribution_deduct(type_id, input_name){
                    final_priceVM.set_contribution_deduct(type_id, input_name);
                },

                get_target_product: function(type_id){
                    self = this;
                    for (var i = 0; i < self.cartData.length; i++) {
                        const item = self.cartData[i];
                        if(item.type_id==type_id){
                            return [i, item];
                        }
                    }
                    return [-1, {}];
                },
                get_total: function() {
                    self = this;

                    if (self.cartData.length == 0) {
                        $('#confirm_form').hide();
                        swal_fire("{{Lang::get('購物車內無商品')}}");
                        return;
                    }

                    self.total = 0;

                    self.cartData.forEach((value) => {
                        self.total += Number(value.countPrice) * Number(value.num);
                    });

                    final_priceVM.finalsubTotal = self.total;

                    if ($('select[name="send_way"]').val()) {
                        if (maskVM.type_decode[final_priceVM.currentShippingType].free_rule >= 0 && final_priceVM.finalsubTotal < maskVM.type_decode[final_priceVM.currentShippingType].free_rule && maskVM.type_decode[final_priceVM.currentShippingType].price > 0) {
                            final_priceVM.finalShipping = Number(maskVM.type_decode[final_priceVM.currentShippingType].price);
                            final_priceVM.finalShippingDiscountDiff = maskVM.type_decode[final_priceVM.currentShippingType].free_rule - final_priceVM.finalsubTotal;
                            $('#discountDiff').removeClass('d-none');
                            $('#discountOver').addClass('d-none')
                        } else {
                            if (maskVM.type_decode[final_priceVM.currentShippingType].free_rule >= 0) {
                                final_priceVM.finalShipping = 0;
                                final_priceVM.finalShippingDiscountDiff = 0;
                                $('#discountDiff').addClass('d-none');
                                $('#discountOver').removeClass('d-none');
                            } else {
                                final_priceVM.finalShipping = maskVM.type_decode[final_priceVM.currentShippingType].price;
                                final_priceVM.finalShippingDiscountDiff = 0;
                                $('#discountDiff').addClass('d-none');
                                $('#discountOver').addClass('d-none');
                            }
                        }
                    }
                },
                changeNum: async function(direction, type_id) {
                    self = this;
                    let amount = Number($('#count_' + type_id).val());

                    if (direction == 1) {
                        amount = amount + 1;
                    } else if (direction == -1) {
                        amount = amount - 1
                    }

                    $('#count_' + type_id).val(amount);

                    self.realChangeNum(amount, type_id);
                },
                realChangeNum: async function(amount, type_id) {
                    self = this;
                    [index, item] = self.get_target_product(type_id);

                    if (index > -1) {
                        item.num = amount;
                        num = amount;

                        if (num != 0) {
                            await cart_cartCtrl(num, type_id, 'assign');
                        } else {
                            self.cartData.splice(index, 1);
                            await cart_cartCtrl(num, type_id, 'delete');
                        }
                        get_cart_data();
                        final_priceVM.set_discount_selected('none_discount');
                        self.get_total();

                        return;
                    }
                },
                deleteCtrl: async function(type_id){
                    self = this;
                    [index, item] = self.get_target_product(type_id);
                    if(index!=-1){
                        self.cartData.splice(index, 1);
                        await cart_cartCtrl(0, type_id, 'delete');
                        get_cart_data();
                        final_priceVM.set_discount_selected('none_discount');
                        self.get_total();

                        return;
                    }
                },
                formatPrice(price) {
                    return price.toLocaleString();
                    // return price;
                },
                toNumber(num) {
                    return Number(num);
                },
                /*解code 填寫資料*/
                register_data: function(examinee_index, type_id){
                    self = this;
                    [index, item] = self.get_target_product(type_id);
                    if(index!=-1){
                        data = item.examinee[examinee_index]['register_data'];
                        if(typeof(data)=='string'){
                            data = JSON.parse(data)
                        }
                    }else{
                        data = {};
                    }
                    return data;
                },
                /*開啟填寫報名資料畫面(新增)*/
                add_examinee: function(type_id){
                    self = this;
                    /*檢查填寫的報名資料數量*/
                    [index, item] = self.get_target_product(type_id);
                    if(index!=-1){
                        if(item.examinee.length>=item.num){
                            Vue.toasted.show("{{Lang::get('報名資料已填寫完畢')}}", vt_warning_obj);
                            return;
                        }
                        self.open_examinee_panel(type_id, examinee_id=0);
                    }
                },
                /*開啟填寫報名資料畫面(修改)*/
                edit_examinee: function(type_id, examinee_id){
                    self = this;
                    self.open_examinee_panel(type_id, examinee_id);
                },
                /*打開填寫報名資料面板*/
                open_examinee_panel: function(type_id, examinee_id){
                    self = this;
                    [index, item] = self.get_target_product(type_id);
                    if(index!=-1){
                        $('#model_btn').click();
                        $('#modelTitle').html("{{Lang::get('填寫報名資料')}}");
                        $.ajax({
                            url: "{{url('Examination/examinee_panel')}}",
                            type: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': csrf_token
                            },
                            data: {
                                vm_name         : 'cart_tableVM',
                                title           : item.info_title+' - '+item.type_title,
                                type_id         : item.type_id,
                                type_product_id : item.type_product_id,
                                examinee_id     : examinee_id,
                            },          
                            success: function(re) {
                                $('#boxModel').html(re);
                            },
                        });
                    }
                },
                /*新增、修改報名資料*/
                save_exinfo: function(examinee_id, register_data, type_id){
                    self = this;
                    [index, item] = self.get_target_product(type_id);
                    if(index!=-1){
                        $('#body_block').show();
                        $.ajax({
                            url: "{{url('Examination/examinee_save')}}",
                            type: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': csrf_token
                            },
                            data: {
                                type_id         : item.type_id,
                                type_product_id : item.type_product_id,
                                examinee_id     : examinee_id,
                                register_data   : register_data,
                            },          
                            success: function(res) {                          
                                if(res.code == 1){
                                    examinee_data = JSON.parse(res.msg);
                                    examinee_data.id = examinee_data.examinee_id;
                                    delete examinee_data.examinee_id;

                                    if(examinee_id==0){/*新增*/
                                        self.cartData[index].examinee.push(examinee_data);
                                    }
                                    else{/*修改*/
                                        for (var i = 0; i < self.cartData[index].examinee.length; i++) {
                                            if(self.cartData[index].examinee[i].id == examinee_id){
                                                
                                                Vue.set(self.cartData[index].examinee, i, examinee_data);
                                            }
                                        }
                                    }
                                    $('#model span[aria-hidden="true"]').click();
                                    Vue.toasted.show("{{Lang::get('操作成功')}}", {duration:1500, className:"bg-success"});
                                }else{
                                    Vue.toasted.show(res.msg, {duration:1500, className:"bg-danger"});
                                }
                                $('#body_block').hide();
                            },
                            error: function(e){
                                $('#body_block').hide();
                            },
                        });
                    }
                },
                /*刪除報名資料*/
                del_examinee: function(examinee_index, type_id){
                    self = this;
                    [index, item] = self.get_target_product(type_id);
                    if(index!=-1){
                        const examinee_id = item.examinee[examinee_index].id;
                        $.ajax({
                            url: "{{url('Examination/examinee_delete')}}",
                            type: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': csrf_token
                            },
                            data: {
                                examinee_id: examinee_id,
                            },          
                            success: function(res){
                                // console.log(res)
                                if(res.code == 1){
                                    self.cartData[index].examinee.splice(examinee_index, 1);
                                    Vue.toasted.show(res.msg, {duration:1500, className:"bg-success"});
                                }else{
                                    Vue.toasted.show(res.msg, {duration:1500, className:"bg-danger"});
                                }
                            },
                        });
                    }
                },
            },
            updated: function(){ $('[data-toggle="tooltip"]').tooltip(); },
        })
        window['cart_tableVM'] = cart_tableVM;
    </script>
<!-- 發票設定 -->
<script type="text/javascript">
    function showInputs(type) {
        $('.invoice-input').hide();
        $('#needUniform').val('0');
        $('#uniform_numbers').val('');
        $('#company_title').val('');
        $('#CarrierType').val('3');
        carrierNumLenChange();
        $('#CarrierNum').val('');
        $('#LoveCode_select').val($('#LoveCode_select .main option').first().val());
        $('#LoveCode').val($('#LoveCode_select .main option').first().val());
        
        switch (type) {
            case '2':
                $('#emailNote').show();
                break;
            case '3':
                $('#CarrierType').parent().parent().show();
                break;
            case '4':
                $('#company_title').parent().parent().show();
                break;
            case '5':
                $('#LoveCode_select').parent().parent().show();
                break;
            default:
                break;
            }
    }

    showInputs($('#invoice_style').val());
</script>
<script type="text/javascript">
    $(function() {
        let accordionBtns = $('#myAccordion input[type="radio"]');
        accordionBtns.click(function() {
            let btn = $(this);
            if (btn.next().next().hasClass("d-none")) {
                btn.next().next().removeClass("d-none");
            }

            accordionBtns.each(function(index, otherBtn) {
                if (btn.is(otherBtn) == false) {
                    $(otherBtn).next().next().addClass("d-none");
                }
            });
        });

        document.getElementById('cartform').addEventListener('keydown', function(e) {
            if (e.keyCode == 13) {
                e.preventDefault();
                return false;
            }
        });
    });

    async function go_buy(){
        $('#body_block').show();
        let order_data = get_order_form_data();
        // console.log(order_data);
        if(order_data.become_member==1){
            resp = await $.ajax({
                url: "{{url('Login/order_create_account')}}",
                type: 'POST',
                datatype: 'json',
                data: order_data,
            });
            if(resp.code==0){
                Vue.toasted.show(resp.msg, vt_error_obj);
                $('#body_block').hide();
                e.preventdefault();
                return;
            }
        }
        $('#cartform').submit();
    }
</script>
@endsection
