@extends('home.Public.mainTpl')
@section('title'){{$data['act']['name']}}@endsection
@section('css')@endsection
@section('content')
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['activity']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu']['activity']['pic']}});">
            <div class="container">
                <h2 class="page-title" style="color:{{$data['frontend_menu']['activity']['text_color']}}">
                    {{$data['frontend_menu']['product']['second_menu']['activity']['name']}}</h2>
                <!-- <span class="enText">{{$data['frontend_menu']['product']['en_name']}}</span> -->
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a class="cursor-initial">{{$data['frontend_menu']['product']['second_menu']['activity']['name']}}</a></li>
                <li>{{$data['act']['name']}}</li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                <!-- Side Product Menu -->
                @include('home.Public.productMenu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox">
                <!-- announcement start -->
                @include('home.Public.newsLink')
                <!-- announcement end -->

                <div class="titleBox">
                    <div class="title">
                        <h3>{{$data['frontend_menu']['product']['second_menu']['activity']['name']}}
                            <!-- <span class="enText">{{$data['frontend_menu']['product']['second_menu']['activity']['en_name']}}</span> -->
                        </h3>
                    </div>
                </div>
                <div class="titleBrandBox">
                    <div class="leftBox">
                        <div class="d-flex flex-wrap justify-content-between align-items-center ml-2 mr-2">
                            <span>
                                {{Lang::get('商品數')}}： {{$data['rowCount']}} 
                                @if(!isset(config('control.close_function_current')['會員瀏覽商品設定']))({{Lang::get('請留意您的瀏覽權限')}})@endif
                            </span>
                            @include('home.product.search_setting')
                        </div>
                    </div>
                </div>
                <div class="mb-2"></div>
                @if(empty($data['acts']) == false)
                @foreach($data['acts'] as $act)
                    @if($act->img!='')
                        <a href="{{url('Product/activity')}}?id={{$act->id}}">
                            <img src="{{__PUBLIC__}}{{$act->img}}">
                        </a>
                    @endif
                    <!-- proBrandZone start -->
                    <div class="proBrandZone announcementRow m-0 mb-4">
                        <div class="subtitleBox announcementBox">
                            <div class="d-flex align-items-center">
                                <h3 class="subtitle">{{App\Services\CommonService::fat_index_text($act->name, 15)}}</h3>
                                <p class="info">{{$act->content}}</p>
                            </div>
                            @if(count($data['acts'])>1)
                                <a href="{{url('Product/activity')}}?id={{$act->id}}" class="more pb-2">
                                    MORE 
                                    <i aria-hidden="true" class="bi bi-chevron-right"></i>
                                </a>
                            @endif
                        </div>

                        <!-- ///////////////////////////////////////////////////////// -->
                        @if(count($act->productinfo->items())==0)
                            <div class="proItem"><h1>{{Lang::get('無資料')}}</h1></div>
                        @endif
                        <div class="proBox proItemBox">
                            @if (empty($act->productinfo) == false)
                            @foreach($act->productinfo->items() as $k => $productvo)
                                <div class="item">
                                    <div class="img_container">
                                        <div class="img" style="background-image: url({{__PUBLIC__}}{{$productvo->pic1}});"></div>
                                        @if(in_array($productvo->id, $data['store_products']))
                                            <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(0, {{$productvo->id}})">
                                                <i class="bi bi-heart-fill"></i>
                                            </a>
                                        @else
                                            <a class="btn store_btn store_book mr-1" href="javascript:priceOptionVM.set_store_product_list(1, {{$productvo->id}})">
                                                <i class="bi bi-heart"></i>
                                            </a>
                                        @endif
                                        @if(!empty($data['coupon_button'][$productvo->id]))
                                            <a class="couponLabel" {{$data['coupon_button'][$productvo->id] ?? ""}}>
                                                <p><span>{{Lang::get('優惠券')}}</span></p>
                                            </a>
                                        @endif
                                    </div>
                                    <div class="img_container addcart_area">
                                        <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                            <div class="img"></div>
                                        </a>
                                        <div class="addcarBox d-none d-lg-block">
                                            @if(!empty($productvo->show[0]['idtype']))
                                                <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                    {{Lang::get('加入購物車')}}
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="textBox">
                                        <h3>
                                            <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                                {{App\Services\CommonService::fat_index_text($productvo->title, 40)}}
                                            </a>
                                            @if(!empty($data['act_button'][$productvo->id]['act_data']['link']))
                                                <span class="activityLabel">
                                                    <a {{$data['act_button'][$productvo->id]['act_data']['link'] ?? ''}}>{{$data['act_button'][$productvo->id]['act_data']['type_name']}}</a>
                                                </span>
                                            @endif
                                        </h3>
                                        <a href="{{url('Product/productinfo')}}?id={{$productvo->id}}">
                                            <div class="priceBox">
                                                <span class="originalPrice">{{$productvo->show[0]['originalPrice']}}</span>
                                                <span class="offerPrice">{!!$productvo->show[0]['offerPrice']!!}</span>
                                            </div>
                                        </a>
                                        <div class="addcarBox d-block d-lg-none">
                                            @if(!empty($productvo->show[0]['idtype']))
                                                <a class="btn store_btn mr-1" href="javascript:priceOptionVM.set_price_sets_and_add_cart({{$productvo->id}})">
                                                    {{Lang::get('加入購物車')}}
                                                </a>
                                             @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            @endif
                        </div>
                        <!-- /////////////////////////////////// -->
                        <!-- /////////////////////////////////// -->
                        @if(count($data['acts'])==1)
                            <div class="row paginationBox">
                                <div class="col-12 boxCenter">
                                    {{$act->productinfo->links('pagination::default')}}
                                </div>
                            </div> 
                        @endif
                    </div>
                @endforeach
                @endif
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
    @include('home.product.search_setting_js')
@endsection