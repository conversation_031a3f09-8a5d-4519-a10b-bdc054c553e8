@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['activity']['name']}} | {{$data['seo'][0]['title']}}@endsection

@section("css")@endsection

@section("content")
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['activity']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}/{{$data['frontend_menu']['activity']['pic']}});">
            <div class="container">
                <h2 class="page-title" style="color:{{$data['frontend_menu']['activity']['text_color']}}">
                    {{$data['frontend_menu']['activity']['name']}}</h2>
                <!-- <span class="enText">{{$data['frontend_menu']['activity']['en_name']}}</span> -->
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Activity/activity')}}">{{$data['frontend_menu']['activity']['name']}}</a></li>
            </ul>
        </div>
    </section>
    

    <section class="container max-wideVersion productPublic">
        <div id="itemBox">
            <?php /*
                <div id="leftBox">
                    <!-- /////////////////////////////////////////// -->
                    <!-- Side Product Menu -->
                    @include('home.Public.productMenu')
                    <!-- /////////////////////////////////////////// -->
                </div>
            */ ?>
            <div id="rightContentBox" class="innerPageBox">
                <!-- announcement start -->
                @include('home.Public.newsLink')
                <!-- announcement end -->
                <div class="liveStreamBox activityAreaBox">
                    <div class="activityGrid" >
                        <!-- item strat -->
                        @foreach($data['activity'] as $vo)
                        <div class="activityGridItem cursor-pointer" onclick='location.href="{{$vo->url}}";'>
                            <div class="bgColor">
                                <div class="activityBgImg" style="background-image: url({{__PUBLIC__}}{{$vo->pic}});"></div>
                                <div class="content">
                                    <h3>{!!$vo->title!!}</h3>
                                    <p>{!!$vo->content!!}</p>
                                </div>
                                <div class="moreBox">
                                    <a href="{{$vo->url}}" class="more">{{Lang::get('更多資訊')}}<i class="icon-right"></i></a>
                                </div>
                            </div>
                        </div>
                        @endforeach
                        <!-- item end -->
                    </div>
                </div>
                <div class="row paginationBox">
                    <div class="col-12 boxCenter">
                        {{$data['activity']->links('pagination::default')}}
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/masonry-pkgd-min.js"></script>
    <script src="{{__PUBLIC__}}/js/imagesloaded-pkgd.js"></script>
    <script>
        function activityGrid() {
            var $grid = $('.activityGrid').masonry({
                itemSelector: '.activityGridItem',
                percentPosition: true,
                // gutter: 5
            });
            $grid.imagesLoaded().progress( function() {
                $grid.masonry();
            });
        };
        activityGrid();
    </script>
@endsection



