@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['findorder']['name']}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Buyform/buyform')}}">{{$data['frontend_menu']['findorder']['name']}}</a></li>
            </ul>
        </div>
    </section>

    <section class="buyform container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">
                            <div class="titleBox">
                            <div class="title">
                                <h3>{{$data['frontend_menu']['findorder']['name']}}</h3>
                            </div>
                        </div>
                        </div>
                        <div class="memberMiddle"> 
                            <h3 class="subtitle">{{Lang::get('詢問內容')}}</h3>
                            <div class="buyformBox">
                                <table class="w-100">
                                    @if(empty($data['contact_log']) == false)
                                    @foreach($data['contact_log'] as $key => $vo)
                                        <tr>
                                            <td class="ask border p-4">
                                                <div>
                                                    @if(empty($vo['ask']) == false)
                                                    @foreach($vo['ask'] as $ask)
                                                        <p>
                                                            {{$key + 1}}.
                                                            {{Lang::get('品名')}}: {{$ask['name']}}, 
                                                            {{Lang::get('單位')}}: {{$ask['unit']}}, 
                                                            {{Lang::get('數量')}}: {{$ask['num']}},
                                                            {{Lang::get('圖片')}}: 
                                                            @if($ask['img'])
                                                                <a href="{{$ask['img']}}" target="_blank">
                                                                    <img class='small_pic' src="{{$ask['img']}}">
                                                                </a>,
                                                            @else
                                                                {{Lang::get('無')}},
                                                            @endif
                                                            {{Lang::get('備註')}}:{{$ask['note']}}
                                                        </p>
                                                    @endforeach
                                                    @endif
                                                </div>
                                                @if($vo['resp'])
                                                <div class="ans">
                                                    {{str_replace("\n", "<br>", $vo['resp'])}}
                                                </div>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('ownJS')
@endsection