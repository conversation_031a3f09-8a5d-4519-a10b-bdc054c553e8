
<tr class="tr-only-hide">
    <td>
        @if($data['edit_able'])
        <input type="checkbox" class="activityCheckboxAll" 
                                onclick="$('.edit_form input[class=productinfoCheckbox]').prop('checked', ($(this).is(':checked')?true:false))">
        @endif
        <small>{{Lang::get('序號')}}</small>
    </td>
    <td><small>{{Lang::get('報名狀態')}}</small></td>

    @if(config('control.control_pre_buy')==1 && empty(config('control.close_function_current')['庫存警示']))
    @if($data['productinfo']['pre_buy'])
        <td><small>{{Lang::get('候補狀態')}}</small></td>
    @endif
    @endif
    
    @if($data['productinfo']['is_roll_call']==1)
        <td><small>{{Lang::get('點名')}}</small></td>
    @endif
    @foreach($data['register_fields'] as $field)
        <td><small>{{$field['title']}}</small></td>
    @endforeach
</tr>
@foreach($data['examinees'] as $ex_k => $vo_e)
    <tr id="examinee{{$vo_e['id']}}" class="cursor-pointer"
        @if($vo_e['cancel']==1)style="opacity:0.7"@endif
        ondblclick="open_examinee_panel('examinee{{$vo_e['id']}}_VM', '{{$vo_e['id']}}', '{{$vo_e['type_id']}}')">
        <td data-th="{{Lang::get('序號')}}">
            @if($data['edit_able'])
            <input class="productinfoCheckbox" type="checkbox" alt="{{$vo_e['id']}}">
            @endif
            <small>{{$ex_k}}</small>
        </td>
        <td data-th="{{Lang::get('報名狀態')}}">
            <small>
                @if($vo_e['cancel']==1)
                    {{Lang::get('已取消')}}
                @else
                    {{Lang::get('完成')}}
                @endif
            </small>
        </td>

        @if(config('control.control_pre_buy')==1 && empty(config('control.close_function_current')['庫存警示']))
        @if($data['productinfo']['pre_buy'])
            <td data-th="{{Lang::get('候補狀態')}}">
                <small>
                    @if($vo_e['reg_status']==1)
                        {{Lang::get('正取')}}
                    @elseif ($vo_e['reg_status']==0)
                        {{Lang::get('候補')}}
                    @elseif ($vo_e['reg_status']==2)
                        {{Lang::get('已補上')}}
                    @elseif ($vo_e['reg_status']==3)
                        {{Lang::get('無法錄取')}}
                    @endif
                </small>
            </td>
        @endif
        @endif

        @if($data['productinfo']['is_roll_call']==1)
            <td data-th="{{Lang::get('點名')}}">
                <small>
                    @if($data['edit_able'])
                        @if($vo_e['roll_call']==0)
                            <select class="roll_call_{{$vo_e['id']}}" @change="update_examinee('{{$vo_e['id']}}', 'roll_call', $event)">
                                <option value="0">{{Lang::get('請選擇')}}</option>
                                <option value="2" {{App\Services\CommonService::compare_return($vo_e['roll_call'],"2","selected")}}>{{Lang::get('到')}}</option>
                                <option value="1" {{App\Services\CommonService::compare_return($vo_e['roll_call'],"1","selected")}}>{{Lang::get('未到')}}</option>
                            </select>
                        @endif
                    @endif
                    <span class="roll_call_time">
                        {{App\Services\CommonService::compare_return($vo_e['roll_call'],"0",Lang::get('未確認'))}}
                        {{App\Services\CommonService::compare_return($vo_e['roll_call'],"1",Lang::get('未到'))}}
                        {{App\Services\CommonService::compare_return($vo_e['roll_call'],"2",Lang::get('到'))}}
                        @if($vo_e['roll_call_time'])
                            {{$vo_e['roll_call_time']}}
                        @endif
                    </span>
                </small>
            </td>
        @endif
        <td v-for="field in fields" :data-th="field.title">
            <span v-if="register_data(0)[field.field_id]">
                <small v-if="types_need_checked.indexOf(field.type) != -1" 
                       v-text="register_data(0)[field.field_id].join()"></small>
                <small v-if="['file'].indexOf(field.type) != -1" 
                       v-text="register_data(0)[field.field_id]['file_name']"></small>
                <small v-if="types_need_checked.indexOf(field.type)==-1 && ['file'].indexOf(field.type)==-1" 
                       v-text="register_data(0)[field.field_id]"></small>
            </span>
        </td>
    </tr>
@endforeach

<script>
    function open_examinee_panel(vm_name, examinee_id, type_id){
        $('#model_btn').click();

        $('#modelTitle').html("{{Lang::get('填寫報名資料')}}");
        $.ajax({
            url: "{{url('index/Examination/examinee_panel')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            data: {
                vm_name         : vm_name,
                type_id         : type_id,
                examinee_id     : examinee_id,
            },          
            success: function(re) {
                $('#boxModel').html(re);
            },
        });
    }

    @if(empty($data['examinees']) == false)
    @foreach($data['examinees'] as $vo_e)
        var examinee{{$vo_e['id']}}_data = { 
            types_need_checked: JSON.parse(`{{json_encode($data['types_need_checked'])}}`.replace(/&quot;/g, '"').trim()),

            vm_name: "examinee{{$vo_e['id']}}_VM",
            type_id: "{$vo_e.type_id}", 
            type_product_id: "{$vo_e.prod_id}",
            fields: [
                @foreach($data['register_fields'] as $vo_f)
                    {field_id:"field_id_{{$vo_f['id']}}", title:"{{$vo_f['title']}}", type:"{{$vo_f['type']}}" },
                @endforeach
                //
            ],
            examinee: [
                {examinee_id:"{{$vo_e['id']}}", register_data:JSON.parse(`{{json_encode($vo_e['register_data'])}}`.replace(/&quot;/g, '"').trim())},
            ],
        }
        window['examinee{{$vo_e['id']}}_VM_init'] = function(){
            return new Vue({
                el: '#examinee{{$vo_e['id']}}',
                data: examinee{{$vo_e['id']}}_data,
                computed:{
                },
                methods:{
                    /*解code 填寫資料*/
                    register_data: function(index){
                        self = this;
                        data = self.examinee[index]['register_data']
                        if(typeof(data)=='string'){
                            data = JSON.parse(data)
                        }
                        return data;
                    },
                    /*新增、修改報名資料*/
                    save_exinfo: function(examinee_id, register_data){
                        self = this;
                        $('#body_block').show();
                        $.ajax({
                            url: "{{url('Examination/examinee_save')}}",
                            type: 'POST',
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            data: {
                                type_id         : self.type_id,
                                type_product_id : self.type_product_id,
                                examinee_id     : examinee_id,
                                register_data   : register_data,
                            },          
                            success: function(res) {                          
                                if(res.code == 1){
                                    if(examinee_id==0){/*新增*/
                                        self.examinee.push(JSON.parse(res.msg));
                                    }
                                    else{/*修改*/
                                        for (var i = 0; i < self.examinee.length; i++) {
                                            if(self.examinee[i].examinee_id == examinee_id){
                                                Vue.set(self.examinee, i, JSON.parse(res.msg));
                                            }
                                        }
                                    }
                                    $('#model span[aria-hidden="true"]').click();
                                    Vue.toasted.show("{{Lang::get('操作成功')}}", {duration:1500, className:"bg-success"});
                                }else{
                                    Vue.toasted.show(res.msg, {duration:1500, className:"bg-danger"});
                                }
                                $('#body_block').hide();
                            },
                            error: function(e){
                                $('#body_block').hide();
                            },
                        });
                    },

                    /*修改點名資料*/
                    update_examinee: function(id, col, $event){
                        value = $($event.currentTarget).val();
                        $.ajax({
                            type: 'POST',
                            headers: {
                                'X-CSRF-Token': csrf_token 
                            },
                            dataType: 'json',
                            url: "{{url('Examination/update_examinee')}}",
                            data: {
                                id:id,
                                col:col,
                                value:value,
                            },
                            success: function(resp) {
                                if(resp.code){
                                    data = resp.msg;
                                    if(data.roll_call && data.roll_call!=0){
                                        target = $('.roll_call_'+id)
                                        target.hide();
                                        target.next().html(
                                            $('.roll_call_'+id+' option:selected').html() + " " + data.roll_call_time
                                        )
                                    }
                                    Vue.toasted.show("{{Lang::get('操作成功')}}",{duration:1500, className: ["toasted-primary", "bg-success"]});
                                }else{
                                    Vue.toasted.show(resp.msg,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                                }
                            },
                            error: function(xhr) {
                                Vue.toasted.show(xhr,{duration:1500, className: ["toasted-primary", "bg-danger"]});
                            }
                        });
                    },
                },
            });
        }
        var examinee{{$vo_e['id']}}_VM = typeof(Vue)!='undefined' ? window['examinee{{$vo_e['id']}}_VM_init']() : "";
    @endforeach
    @endif
</script>
