@extends('home.Public.mainTpl')

@section('title')
{{$data['experience']['title']}} - {{$data['frontend_menu']['experience']['name']}} | {{$data['seo'][0]['title']}}
@endsection

@section('css')
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Experience/experience')}}">{{$data['frontend_menu']['experience']['name']}}</a></li>
                <li><a class="cursor-initial" href="">{{$data['experience']['title']}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox">
            <?php /*
                <div id="leftBox">
                    <!-- /////////////////////////////////////////// -->
                    <!-- Side Product Menu -->
                    @include('home.Public.productMenu')
                    <!-- /////////////////////////////////////////// -->
                </div>
            */ ?>
            <div id="rightContentBox" class="innerPageBox">
                <!-- announcement start -->
                @include('home.Public.newsLink')            
                <!-- announcement end -->
                <div class="titleBox">
                    <div class="title">
                        <h3>{{$data['frontend_menu']['experience']['name']}}
                            <!-- <span class="enText">{{$data['frontend_menu']['experience']['en_name']}}</span> -->
                        </h3>
                    </div>
                </div>
                <div class=" liveStreamBox liveStreamIntroBox">
                    <h3 class="title">{{$data['experience']['title']}}</h3>
                    <div class="bgImg"><img src="{{__PUBLIC__}}{{$data['experience']['pic']}}" alt=""></div>
                    <div class="content">
                        <p>{!! $data['experience']['content'] !!}</p>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item previous">
                                <a class="" 
                                @if($data['pagedown'] != null)
                                href="{{url('Experience/experience_c')}}?id={{$data['pagedown']->id}}" 
                                @else 
                                style="opacity:0.5;" 
                                @endif>
                                    <i class="bi bi-chevron-left"></i> {{Lang::get('上一頁')}}
                                </a>
                            </li>
                            <li class="page-item return">
                                <a class="" href="javascript:history.back();">{{Lang::get('返回')}}</a>
                            </li>
                            <li class="page-item next">
                                <a class="" 
                                    @if($data['pageup'] != null )
                                        href="{{url('Experience/experience_c')}}?id={{$data['pageup']->id}}" 
                                    @else 
                                        style="opacity:0.5;" 
                                    @endif>
                                    {{Lang::get('下一頁')}} <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
@endsection