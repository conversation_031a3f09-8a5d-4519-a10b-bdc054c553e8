@extends('home.Public.mainTpl')
@section('title'){{Lang::get('個人資訊')}} - {{Lang::get('網紅後台')}}@endsection
@section('css')
    <style>
        .hide{
            display: none;
        }

        .memberContentBox .bindingBox a.bindingBtn.use-btn:hover{
            background-color: #ff7300;
            cursor: default;
        }
    </style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Kol/kol_data')}}">{{Lang::get('網紅後台')}}</a></li>
                <li><a href="{{url('Kol/kol_data')}}">{{Lang::get('個人資訊')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.kol.kol_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop p-top-0-ipt">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('個人資訊')}}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="memberMiddle memberitems">
                            <h4 class="subtitle">{{Lang::get('基本資料')}}</h4>
                            <div class="row">
                                <div class="form-group col-sm-6 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('帳號')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['email']}}" readonly>
                                </div>
                                <div class="form-group col-sm-6 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('密碼')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['password']}}" readonly>
                                </div>

                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('網紅名')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['kol_name']}}" readonly>
                                </div>
                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('姓名')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['real_name']}}" readonly>
                                </div>
                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('暱稱')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['password']}}" readonly>
                                </div>

                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('分類')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['english_name']}}" readonly>
                                </div>
                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('電話')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['phone']}}" readonly>
                                </div>
                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('手機')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['mobile']}}" readonly>
                                </div>

                                <div class="form-group col-sm-8 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('地址')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['address']}}" readonly>
                                </div>
                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('備註')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['address_memo']}}" readonly>
                                </div>

                                <div class="form-group col-sm-6 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('匯款銀行')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['bank_name']}}" readonly>
                                </div>
                                <div class="form-group col-sm-6 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('匯款帳號')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['bank_account']}}" readonly>
                                </div>

                                <div class="form-group col-sm-4 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('身份証')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['id_no']}}" readonly>
                                </div>
                                <div class="form-group col-sm-8 col-12">
                                    <label for="" class="col-form-label">{{Lang::get('備註')}}</label>
                                    <input type="text" class="form-control "  placeholder="" value="{{$data['kol']['memo']}}" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
@endsection

