@extends('home.Public.mainTpl')
@section('title'){{Lang::get('訂單資訊')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
    <style>
        .orderfrom_text{
            text-align: right;
        }
        @media (max-width: 1280px) {
            .orderfrom_text{
                text-align: left;
            }
        }
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
            }
            .noPrint {
                display: none;
            }
            .breakPage {
                break-after: page;
            }
        }
    </style>
@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                @if($data['user']['id'] != '0')
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                @endif
                <li><a>{{Lang::get('訂單資訊')}}</a></li>
            </ul> 
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{Lang::get('訂單資訊')}}</h3>
                            </div>
                        </div>
                        <button class="btn more float-right noPrint" type="button" onclick="window.print();">列印訂單</button>
                        <ul class="nav tabNavBox justify-content-center noPrint">
                            @if($data['user']['id'] != '0')
                                <li class="nav-item">
                                    <a href="{{url('Orderform/orderform')}}">{{Lang::get('訂單')}}</a>
                                </li>
                                <li class="nav-item active">
                                    <a href="{{url('Orderform/history')}}">{{Lang::get('歷史紀錄')}}</a>
                                </li>
                            @endif
                            <li class="nav-item">
                                <a href="{{url('About/about_contact')}}?order_number={{$data['orderform'][0]['order_number']}}" target="_blank">{{Lang::get('訂單問題')}}
                                </a>
                            </li>
                        </ul> 
                    </div>
                    <div class="memberMiddle">
                        <div class="headingBox">
                            <h3 class="subtitle"><span>{{Lang::get('訂單狀態')}}</span></h3>
                        </div>
                        <div class="couponBox">
                            <table class="orderTable table table-striped table-bordered table-rwd vertical_baseline">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th>{{Lang::get('購買日期')}}</th>
                                        <th>{{Lang::get('訂單編號')}}</th>
                                        <th>{{Lang::get('總價')}}</th>
                                        <th style="width: 75px;">{{Lang::get('付款方式')}}</th>
                                        <th style="width:180px;">{{Lang::get('收款狀態')}}</th>
                                        <th style="width: 75px;">{{Lang::get('出貨狀態')}}</th>
                                        <th style="width: 50px;">{{Lang::get('操作')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(empty($data['orderform']) == false)
                                    @foreach($data['orderform'] as $vo)
                                        <tr>
                                            <td data-th="{{Lang::get('購買日期')}}">{{date('Y/m/d H:i:s',$vo['create_time'])}}</td>
                                            <td data-th="{{Lang::get('訂單編號')}}"><a href="{{url('Orderform/orderform_c')}}?id={{$vo['order_number']}}">{{$vo['order_number']}}</a></td>
                                            <td data-th="{{Lang::get('總價')}}">{{config('extra.shop.dollar_symbol')}}{{number_format($vo['total'])}}</td>
                                            <td data-th="{{Lang::get('付款方式')}}">
                                                @if(!empty($data['pay_fee_dict']['k_'.$vo['payment']]))
                                                    {{$data['pay_fee_dict']['k_'.$vo['payment']]['name']}}
                                                @else
                                                    {{$vo['payment']}}
                                                @endif
                                            </td>
                                            <td data-th="{{Lang::get('收款狀態')}}">
                                                <form name="order_form" action="{{url('Cart/redirect3Next')}}" method="post" class="d-inline-block">
                                                    @csrf
                                                    @if($vo['payment']==2 && $vo['status']=='New')
                                                        @if($vo['receipts_state'] == 1 || $vo['report_check_time'] != '')
                                                            {{$vo['report']}}&nbsp;&nbsp;&nbsp;{{Lang::get('已確認')}}
                                                        @else
                                                            @if($vo['report'] == '')
                                                                <div class="input-group transferBox">
                                                                    <div class="custom-file">
                                                                        <input type="text" class="form-control" placeholder="{{Lang::get('回報後五碼或姓名')}}" id="pc_{{$vo['id']}}" onfocus="javascript:this.parentElement.parentNode.className+=' write';" onblur="javascript:this.parentElement.parentNode.classList.remove('write');" style="font-size: 12px;">
                                                                    </div>
                                                                    <div class="input-group-append" style="align-items: center">
                                                                        <button class="btn more" type="button" alt="pc_{{$vo['id']}}" onclick="setReportNumber(this);">{{Lang::get('送出')}}</button>
                                                                    </div>
                                                                </div>
                                                            @else
                                                                {{$vo['report']}}&nbsp;&nbsp;&nbsp;{{Lang::get('待確認')}}
                                                            @endif
                                                        @endif
                                                    @else
                                                        {{App\Services\CommonService::RECEIPTS_STATE($vo['receipts_state'])}}
                                                    @endif

                                                    <!-- //////////// -->
                                                    <input type="hidden" name="id" value="{{$vo['id']}}"/>
                                                    <input type="hidden" name="total" value="{{$vo['total']}}"/>
                                                    <input type="hidden" name="order_number" value="{{$vo['order_number']}}"/>
                                                    <input type="hidden" name="pay_way" value="{{$vo['payment']}}"/>
                                                    <!-- //////////// -->
                                                    @if(config('control.thirdpart_money')==1 && $vo['status'] =='New')
                                                        @if(in_array($vo['payment'], [3,4,5]) && ($vo['receipts_state'] == '0'))
                                                            <button class="btn more" type="submit">{{Lang::get('補單')}}</button>
                                                        @endif
                                                    @endif
                                                </form>
                                            </td>
                                            <td data-th="{{Lang::get('出貨狀態')}}">{{App\Services\CommonService::TRANSPORT_STATE($vo['transport_state'])}}</td>
                                            <td data-th="{{Lang::get('操作')}}">
                                                @if($vo['transport_state'] != 1 && $vo['status']=='New')
                                                    <a class="use-btn cancel" order_number="{{$vo['order_number']}}">{{Lang::get('取消')}}</a>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        <hr class="breakPage">
                        <div class="orderDetailsBox">
                            <h3 class="title mb-2">
                                {{Lang::get('訂單編號')}}<span>{{$data['singleData']['order_number']}}</span>
                            </h3>
                            <div class="order-top">
                                <div class="order-time">
                                    <span>
                                        {{Lang::get('訂購時間')}}：
                                        <span>{{date('Y/m/d H:i',$data['singleData']['create_time'])}}</span>
                                    </span>
                                </div>
                                <div class="order-email">
                                    <span>
                                        {{Lang::get('訂單信箱')}}：
                                        <span>{{$data['singleData']['transport_email']}}</span>
                                    </span>
                                </div>
                            </div>
                            <table class="orderTable table table-striped table-bordered table-rwd proIntro">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th>{{Lang::get('品項')}}</th>
                                        <th style="width: 75px">{{Lang::get('單價')}}</th>
                                        <th style="width: 75px">{{Lang::get('數量')}}</th>
                                        <th style="width: 75px">{{Lang::get('總價')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($data['singleData']['product'] as $product)
                                        <tr>
                                            <td data-th="{{Lang::get('品項')}}">
                                                <div class="d-flex flex-wrap align-items-center">
                                                    @if(!empty($product['url2']))
                                                    <div class="smallProImg mr-4" style="background-image: url({{request()->server('REQUEST_SCHEME')}}://{{$product['url2']}}); position: relative;">
                                                        @if(empty(config('control.close_function_current')['網紅列表']) && isset($product['key_type']))
                                                        @if( substr($product['key_type'],0,3)== 'kol')
                                                            <span class="prod_tag kol_tag">
                                                                {{Lang::get('網紅推薦')}}
                                                            </span>
                                                        @endif
                                                        @endif

                                                        @if(empty(config('control.close_function_current')['加價購設定']) && isset($product['key_type']))
                                                        @if($product['key_type']== 'add')
                                                            <span class="prod_tag add_tag">
                                                                {{Lang::get('加價購')}}
                                                            </span>
                                                        @endif
                                                        @endif
                                                    </div>
                                                    @endif
                                                    <div class="proContent">
                                                        {{$product['name']}}
                                                        @if(!empty($product['pre_buy']))
                                                            【{{Lang::get('超額購買')}}】
                                                        @endif
                                                        @if(config('control.control_register')==1 && isset($product['is_registrable']))
                                                            @if($product['is_registrable']=='1')
                                                                <a class="btn btn-success btn-sm text-white show_info" 
                                                                   onclick="get_examinees_info('{{$data['singleData']['order_number']}}', '{{$product['type_id']}}_{{$product['key_type']}}')"
                                                                >{{Lang::get('查看報名資料')}}</a>
                                                            @endif
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td data-th="{{Lang::get('單價')}}">{{config('extra.shop.dollar_symbol')}}{{number_format($product['price'])}}</td>
                                            <td data-th="{{Lang::get('數量')}}">{{$product['num']}}</td>
                                            <td data-th="{{Lang::get('總價')}}">{{config('extra.shop.dollar_symbol')}}{{number_format($product['total'])}}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            <div class="row">
                                @foreach($data['singleData']['discount'] as $discount)
                                    <div class="col-lg-12 mb-1">
                                        <span>【{{$discount['type']}}】</span>{{$discount['name']}} : <span>{{$discount['count']}}</span>
                                    </div>
                                @endforeach
                                <div class="col-lg-12 mb-1">
                                    @if($data['singleData']['freediscount'] != 0)
                                        <span>【{{Lang::get('立馬省')}}】</span>
                                        <span>{{Lang::get('購物現折')}} : </span>
                                        <span>{{config('extra.shop.dollar_symbol')}}{{number_format($data['singleData']['freediscount'])}}</span>
                                    @endif
                                </div>
                                <div class="col-lg-6 mb-1">
                                    <span>{{Lang::get('圓滿點折抵')}} : </span>
                                    <span>{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}{{number_format($data['singleData']['contribution_deduct'] * config('extra.skychakra.exchange_rate'))}}</span>

                                    <!-- @if(empty(config('control.close_function_current')['點數設定']))
                                        <span>{{Lang::get('本訂單增加紅利')}} : <span>{{number_format($data['singleData']['add_point'])}}</span></span>
                                    @endif -->
                                </div>
                                <div class="col-lg-6 mb-1">
                                    <span>
                                        {{Lang::get('總金額')}} : 
                                        <span>{{config('extra.shop.dollar')}}{{config('extra.shop.dollar_symbol')}}{{number_format($data['singleData']['total'])}}</span>
                                    </span>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                @if(config('control.thirdpart_logistic'))
                                    @if($data['singleData']['ecpay_shippable'])
                                        <p class="col-lg-12 mb-1">
                                            <a class="use-btn" href="###" data-toggle="modal" data-target="#logisticModel" style="width: 120px;">
                                                {{Lang::get('查看物流狀態')}}
                                            </a>
                                        </p>
                                    @endif
                                @endif
                                <p class="col-lg-6 mb-1">{{Lang::get('購買人')}}：<span>{{$data['singleData']['name']}}</span></p>
                                <p class="col-lg-6 mb-1">{{Lang::get('收件人')}}：<span>{{$data['singleData']['transport_location_name']}}</span></p>
                                @if($data['singleData']['transport'] != Lang::get('到店取貨'))
                                <p class="col-lg-6 mb-1">{{Lang::get('出貨地址')}}：<span>{{$data['singleData']['transport_location']}}</span></p>
                                @endif
                                <p class="col-lg-6 mb-1">{{Lang::get('手機')}}：<span>{{$data['singleData']['transport_location_phone']}}</span></p>
                                <p class="col-lg-6 mb-1">{{Lang::get('電話')}}：<span>{{$data['singleData']['transport_location_tele']}}</span></p>
                                <p class="col-lg-6 mb-1">{{Lang::get('付款方式')}}：
                                    <span>
                                        @if(isset($data['pay_fee_dict']['k_'.$data['singleData']['payment']]))
                                            {{$data['pay_fee_dict']['k_'.$data['singleData']['payment']]['name']}}
                                        @else
                                            {{$data['singleData']['payment']}}
                                        @endif
                                    </span>
                                </p>
                            </div>
                            <hr>
                            <div class="row">
                                <p class="col-lg-12">發票開立方式：<span>{{$data['singleData']['InvoiceStyleText']}}</span></p>

                                @if($data['singleData']['InvoiceStyle'] == '2')
                                    <p class="col-lg-12">發票寄送電子信箱：<span>{{$data['singleData']['transport_email']}}</span></p>
                                @elseif($data['singleData']['InvoiceStyle'] == '3')
                                    @switch($data['singleData']['CarrierType'])
                                        @case("3")
                                            <p class="col-lg-6">{{Lang::get('載具類型')}}：<span>手機條碼</span></p>
                                            <p class="col-lg-6">{{Lang::get('編號')}}：<span>{{$data['singleData']['CarrierNum']}}</span></p>
                                        @break
                                        @case("2")
                                            <p class="col-lg-6">{{Lang::get('載具類型')}}：<span>自然人憑證條碼</span></p>
                                            <p class="col-lg-6">{{Lang::get('編號')}}：<span>{{$data['singleData']['CarrierNum']}}</span></p>
                                        @break
                                    @endswitch
                                @elseif($data['singleData']['InvoiceStyle'] == '4')
                                    <p class="col-lg-6">{{Lang::get('統一編號')}}：<span>{{$data['singleData']['uniform_numbers']}}</span></p>
                                    <p class="col-lg-6">{{Lang::get('公司抬頭')}}：<span>{{$data['singleData']['company_title']}}</span></p>
                                @elseif($data['singleData']['InvoiceStyle'] == '5')
                                    <p class="col-lg-6">捐贈單位：<span>{{$data['singleData']['LoveCodeFoundation']}}</span></p>
                                    <p class="col-lg-6">{{Lang::get('捐贈碼')}}：<span>{{$data['singleData']['LoveCode']}}</span></p>
                                @endif

                                @if($data['singleData']['status'] == 'Cancel')
                                    <p class="col-lg-12">{{Lang::get('發票狀態')}}：<span>{{Lang::get('訂單')}}{{Lang::get('已取消')}}</span></p>
                                @elseif($data['singleData']['InvoiceNo'] == '')
                                    <p class="col-lg-12">{{Lang::get('發票狀態')}}：<span>{{Lang::get('尚未開立發票')}}</span></p>
                                @else
                                    <p class="col-lg-6">{{Lang::get('發票號碼')}}：<span>{{$data['singleData']['InvoiceNo']}}</span></p>
                                    <p class="col-lg-6">{{Lang::get('發票日期')}}：<span>{{$data['singleData']['InvoiceDate']}}</span></p>
                                    <p class="col-lg-6">{{Lang::get('發票隨機碼')}}：<span>{{$data['singleData']['RandomNumber']}}</span></p>

                                    <p class="col-lg-6 tourist">{{Lang::get('發票列印網址')}}：
                                    @if($data['singleData']['Print'] == '1')
                                        <span><a href="{{$data['singleData']['InvoiceHtml']}}" target="_blank">列印發票</a></span>
                                    @else
                                        <span>發票免列印</span>
                                    @endif
                                    </p>
                                @endif
                            </div>
                            <hr class="noPrint">
                            <div class="row">
                                <p class="col-lg-12 mb-1">{{Lang::get('備註')}}：<span>{{$data['singleData']['ps']}}</span></p>
                                <p class="col-lg-12 mb-1">{{Lang::get('賣家備註')}}：<span>{{$data['singleData']['ps2']}}</span></p>
                            </div>
                            <hr>
                            @if(empty(config('control.close_function_current')['會員管理']))
                            @if(($data['user']['id'] != '0'))
                                <div class="col-12 text-center">
                                    <a href="{{url('Orderform/orderform')}}" class="use-btn">{{Lang::get('返回列表')}}</a>
                                </div>
                            @endif
                            @endif
                        </div>
                    </div>
                    <!-- //////////////////////////////////// -->
                    <div class="memberBottom">
                        <div>
                            {!!$data['consent_other']!!}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- logisticModel start-->
        <div class="modal fade " id="logisticModel" tabindex="-1" role="dialog" aria-labelledby="processModelTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content ">
                    <div class="modal-header">
                        <h5 class="modal-title" id="processModelTitle">{{Lang::get('物流狀態')}}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p>{{Lang::get('取貨方式')}}：<span id="logist_type"></span></p>
                        <table class="table table-striped table-bordered table-rwd">
                            <thead>
                                <tr>
                                    <th>{{Lang::get('狀態說明')}}</th>
                                    <th>{{Lang::get('時間')}}</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- logisticModel end-->

        <a id="regiModal_btn" class="invisible" data-toggle="modal" data-target="#regiModal"></a>
        <div class="modal fade" id="regiModal" tabindex="-1" role="dialog" aria-labelledby="regiModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="regiModalLabel">{{Lang::get('報名資料')}}</h5>
                        <button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="overflow-x-scroll">
                            <table class="orderTable table table-striped table-bordered table-rwd examinee_table">
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                    </div>
                </div>
            </div>
        </div>

        <!-- cancelModal start-->
        <a id="cancelModal_btn" class="invisible" data-toggle="modal" data-target="#cancelModal"></a>
        <div class="modal fade " id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="cancelModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
          <div class="modal-dialog modal-dialog-centered" role="document">
              <div class="modal-content ">
                  <div class="modal-header d-flex justify-content-center">
                      <h3 class="modal-title" id="cancelModalLabel">提示訊息</h3>
                      <!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                          <span aria-hidden="true">&times;</span>
                      </button> -->
                  </div>
                  <div class="modal-body d-flex justify-content-center">
                    <h4>訂單取消中，請稍候</h4>
                  </div>
              </div>
          </div>
      </div>
      <!-- cancelModal end-->

        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
    <script>
        function setReportNumber($this){
            $.ajax({
                url: "{{url('Orderform/setReportNumber')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                data:{
                    id: $($this).attr('alt').split("_")[1],
                    reportNumber: $("#" + $($this).attr('alt')).val()
                },
                success: function(resp) {
                    if(resp.code){
                        Vue.toasted.show(resp.msg, vt_success_obj);
                        $($this).parent().html("{{Lang::get('待確認')}}");
                    }else{
                        Vue.toasted.show(resp.msg, vt_error_obj);
                    }
                },
                error: function(xhr) {
                    console.log(xhr);
                    Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                }
            });
        }

        function get_logistic_record(){
            $.ajax({
                url: "{{url('Orderform/ajax_logistic_status')}}?id={{$data['orderform'][0]['id']}}",
                type: 'GET',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                dataType: 'json',
                success: function(response) {
                    console.log(response);
                    $("#logist_type").html("{{$data['orderform'][0]['transport']}}");
                    record_html = "";
                    for(var i =0; i<response.length; i++){
                        record_html = record_html + '<tr><td>'+response[i]['message']+'</td><td>'+response[i]['time']+'</td></tr>';
                    }
                    $('#logisticModel tbody').html(record_html);
                },
                error: function(xhr) {
                    console.log(xhr);
                    Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                }
            });
        }
        get_logistic_record();

        $('.cancel').on('click', function(e){
            if(confirm("{{Lang::get('確定取消嗎')}}")){
                $('#cancelModal_btn').click();
                $.ajax({
                    url: "{{url('Orderform/cancel')}}",
                    type: "POST",
                    headers: {
                        'X-CSRF-Token': csrf_token 
                    },
                    dataType: "json",
                    data: { order_number: $(this).attr('order_number') },
                    success: function (response) {
                        alert(response.msg);
                        // 關閉會員管理則回到首頁
                        if ("{{$data['memberFunction']}}" == '0') {
                          location.href="{{url('Index/index')}}";
                        } else {
                          location.href="{{url('Orderform/orderform')}}";
                        }
                    },
                    error: function (xhr) {
                        console.log(xhr);
                        Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                        $('#cancelModal').modal('hide');
                    },
                });
            }
        });

        function get_examinees_info(order_number, type_id){
            $.ajax({
                url: "{{url('Examination/examinee_list_table')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                datatype: 'json',
                data: {
                    type_id: type_id,
                    order_number: order_number,
                },
                success: function (response) {
                    console.log(response);
                    $('#regiModal .modal-body .overflow-x-scroll table').html(response);
                    $('#regiModal_btn').click();
                }
            });
        }
    </script>
@endsection

