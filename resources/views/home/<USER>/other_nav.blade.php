
								@if(!isset(config('control.close_function_current')['關於我們']))
                                    <!-- 關於我們 -->
                                    <li class="aboutUsLink top_nav"><a href="{{url('About/about_story')}}">{{$data['frontend_menu']['about']['name']}}</a></li>
                                
                                @endif

                                @if(empty(config('control.close_function_current')['客戶來函']))
                                    <!-- 聯絡我們 -->
                                    <li class="aboutUsLink top_nav"><a href="{{url('About/about_contact')}}">{{$data['frontend_menu']['about']['second_menu']['about_contact']['name']}}</a></li>
                                @endif

                                @if(empty(config('control.close_function_current')['最新消息']))
                                    <!-- 最新消息 -->
                                    <li class="aboutUsLink top_nav"><a href="{{url('News/news')}}">{{$data['frontend_menu']['news']['name']}}</a></li>
                                @endif  
                                
                                @if(empty(config('control.close_function_current')['常見問題']))
                                
                                    <!-- 常見問題 -->
                                    <li class="top_nav"><a href="{{url('Qa/qa')}}">{{$data['frontend_menu']['qa']['name']}}</a></li>
                                @endif  
                                
                                @if(empty(config('control.close_function_current')['經銷據點']))
                                
                                    <!-- 經銷據點 -->
                                    <li class="top_nav"><a href="{{url('Distribution/distribution')}}?id={{$data['stronghold']['id']}}">{{$data['frontend_menu']['distribution']['name']}}</a></li>
                                @endif  

                                @if(empty(config('control.close_function_current')['訂單管理']))                                
                                    <!-- 訂單查詢 -->
                                    <!-- <li class="top_nav"><a href="{{url('Orderform/tracking')}}">{{Lang::get('訂單查詢')}}</a></li> -->
                                @endif  
                                
                                @if(empty(config('control.close_function_current')['訂單管理']))                                
                                    @if(config('control.control_register')==1) 
                                        <!-- 報名資料查詢 -->
                                        <li class="top_nav"><a href="{{url('Examination/tracking_registration')}}">{{Lang::get('報名資料查詢')}}</a></li>
                                    @endif
                                @endif

                                @if(empty(config('control.close_function_current')['會員管理']))
                                    @if(empty(config('control.close_function_current')['找貨回函']))                              
                                    
                                        <!-- 找貨回函 -->
                                        @if($data['user']['id'] != '0')
                                            <li class="top_nav"><a href="{{url('Findorder/findorder')}}">{{$data['frontend_menu']['findorder']['name']}}</a></li>
                                        @else
                                            <li class="top_nav">
                                                <a class="checkoutBtn" href="" data-toggle="modal" data-target="#memberLogin"
                                                    onclick="$('#bonus_info').hide()">
                                                    <span>{{$data['frontend_menu']['findorder']['name']}}</span>
                                                </a>
                                            </li>
                                        @endif
                                    @endif
                                @endif