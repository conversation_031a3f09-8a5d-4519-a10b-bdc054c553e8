@extends('home.Public.mainTpl')
@section('title'){{Lang::get('忘記密碼')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css"> -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.0/js/bootstrap.min.js"></script>
    <style>
        .jumbotron {
            background-color: #fffbed;
        }
        .grid_2{
            display: none;

        }
    </style>
@endsection

@section('content')
    @if($data['adminData']==null)
        <div class="jumbotron text-center border border-warning">
        <h1>{{Lang::get('資料有誤')}}</h1>
        <p>{{Lang::get('如果有任何問題，請來電由客服人員幫您處理')}}</p>
            <a href="###" onclick="history.back()">{{Lang::get('回上一頁')}}</a>
        </div>
    @else
        <div class="jumbotron text-center border border-warning">
        <h1>{{Lang::get('已寄信至')}}：<a class="text-danger">
            {{$data['adminData']['email']}}</a>
            {{Lang::get('，請查看信箱並修改密碼。')}}
        </h1>
        <p>{{Lang::get('如果有任何問題，請來電由客服人員幫您處理')}}</p>
            <a href="###" onclick="history.back()">{{Lang::get('回上一頁')}}</a>
        </div>
    @endif
@endsection

@section('ownJS')
    <script>
        $('.LoginTab').addClass('thispage');
    </script>
@endsection
