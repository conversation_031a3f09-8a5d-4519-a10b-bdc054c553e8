<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>LINE Auth</title>

    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
</head>
<body>
    <script src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    <script type="text/javascript">
        $(document).ready(function(){
            var liffID = '{{$data["LIFF_ID"]}}';
            if(liffID){
                liff.init({
                    liffId: liffID
                }).then(function() {
                    console.log('LIFF init');

                    // 這邊開始寫使用其他功能
                    var context = liff.getContext();
                    userId = context.userId;

                    if(userId){
                        $.ajax({
                            url     : "{{url('Login/line_login')}}",
                            dataType: 'json',
                            headers: {
                                'X-CSRF-Token': '{{csrf_token()}}' 
                            },
                            type    : 'POST',
                            data : { U3: userId, ig:""},
                            contentType:"application/x-www-form-urlencoded; charset=UTF-8",
                            success: function(result){
                                if(result.code==1){
                                    localStorage.setItem('redirect', "");
                                    location.href = '{{$data["redirect_url"]}}';
                                }
                                else{
                                    alert(result.msg);
                                    liff.closeWindow();
                                }
                            }
                        });
                    }else{
                        location.href = url('Login/login');
                    }

                }).catch(function(error) {
                    console.log(error);
                });
            }
        })
    </script>
</body>