@extends('home.Public.mainTpl')
@section('title'){{Lang::get('商品註冊')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Member/reg_product')}}">{{Lang::get('商品註冊')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                @include('home.Public.member_menu')
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="titleBox">
                        <div class="title">
                            <h3>{{Lang::get('商品註冊')}}</h3>
                        </div>
                        <ul class="nav tabNavBox justify-content-center">
                            <li class="nav-item active">
                                <a href="/Orderform/orderform">{{Lang::get('商品列表')}}</a>
                            </li>
                            <li class="nav-item">
                                <a href="javascript:register_product(0)" class="bg-danger text-light">{{Lang::get('我要註冊')}}</a>
                            </li>
                        </ul>
                    </div>
                    <div class="memberMiddle">
                        <div class="couponBox">
                            <table class="orderTable table table-striped table-bordered table-rwd">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th>{{Lang::get('註冊日期')}}</th>
                                        <th>{{Lang::get('產品名稱')}}</th>
                                        <th>{{Lang::get('機身號碼')}}</th>
                                        <th>{{Lang::get('購買日期')}}</th>
                                        <th>{{Lang::get('發票號碼')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(empty($data['userD'])==false)
                                    @foreach($data['userD'] as $vo)
                                    <tr>
                                        <td data-th="{{Lang::get('註冊日期')}}">{{$vo->regtime}}</td>
                                        <td data-th="{{Lang::get('產品名稱')}}">{{$vo->product_name}}</td>
                                        <td data-th="{{Lang::get('機身號碼')}}">{{$vo->product_code}}</td>
                                        <td data-th="{{Lang::get('購買日期')}}">{{$vo->buytime}}</td>
                                        <td data-th="{{Lang::get('發票號碼')}}">{{$vo->tax_ID_number}}</td>
                                    </tr>
                                    @endforeach
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        <div class="row paginationBox">
                            <div class="col-12 boxCenter">
                                {{$data['userD']->links('pagination::default')}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
@endsection