@extends('home.Public.mainTpl')
@section('title'){{$data['article']->name}} - {{$data['frontend_menu']['share_article']['name']}} | {{$data['seo'][0]['title']}}@endsection

@section("css")@endsection

@section("content")
  @if(empty(config('control.close_function_current')['banner管理']))
    @if($data['frontend_menu']['share_article']['pic'])
    <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu']['share_article']['pic']}});">
      <div class="container">
        <h2 class="page-title">{{$data['frontend_menu']['share_article']['name']}}</h2>
      </div>
    </section>
    @endif
  @endif
  <section class="directoryRow">
    <div class="container">
      <ul>
        <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
        <li><a href="{{url('ShareArticle/index')}}">{{$data['frontend_menu']['share_article']['name']}}</a></li>
        <li><a href="{{url('ShareArticle/detail')}}?id={{$data['article']->id}}">{{$data['article']->name}}</a></li>
      </ul>
    </div>
  </section>
  <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
  <section class="container productPublic">
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox">
      <div id="rightContentBox" class="innerPageBox">
        <div class="titleBox">
          <div class="title">
            <!-- 
            <h3>{{$data['frontend_menu']['share_article']['name']}}</h3>
            <span class="enText">{{$data['frontend_menu']['share_article']['en_name']}}</span>
            -->
            <h3 class="title">{{$data['article']->name}}</h3>
          </div>
        </div>
        <div class="newsIntroBox">
          <div class="time d-flex flex-wrap justify-content-between align-items-center">
            <span><i class="bi bi-calendar-minus"></i> {{$data['article']->create_time_f}}</span>
            <span>
              <a href="###" class="text-danger mr-4" id="love_article_btn">
                @if($data['love_article']==-1)
                  <i class="bi bi-heart"></i>
                @else
                  <i class="bi bi-heart-fill"></i>
                @endif
              </a>
              <button class="btn btn-secnodary" data-toggle="modal" data-target="#reportModal">檢舉<i class="icon-horn ml-3"></i></button>
            </span>
          </div>
          <div class="content">
            <p>{!! $data['article']->content !!}</p>
          </div>
          <nav aria-label="Page navigation">
            <ul class="pagination">
              <li class="page-item return"><a class="" href="{{url('ShareArticle/index')}}">{{Lang::get('返回列表')}}</a></li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
    <!-- /////////////////////////////////////////// -->
  </section>
@endsection

@section("Modal")
  <div class="modal fade phoneSearch smallMOdel" id="reportModal" tabindex="-1" role="dialog" aria-labelledby="reportModalTitle" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="reportModalTitle">{{Lang::get('我要檢舉')}}</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form action="{{url('ShareArticle/report')}}" method="post" name="reportForm">
            @csrf
            <input type="hidden" name="article_id" value="{{$data['article']->id}}">
            {{Lang::get('檢舉說明')}}
            <textarea rows="6" name="note" class="form-control mb-3"></textarea>

            @if(config('extra.shop.google_recaptcha_sitekey'))
              <div class="w-100 d-flex justify-content-center mb-3">
                <div class="g-recaptcha text-center" data-callback="captcha_onclick" 
                     data-sitekey="{{config('extra.shop.google_recaptcha_sitekey')}}">
                </div>
                <input type="hidden" id="recaptchaValidator" />
              </div>
            @endif
          </form>
          <div class="text-center">
            <button class="btn use-btn" onclick="report();">{{Lang::get('送出檢舉')}}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection

@section("ownJS")
  @if(config('extra.shop.google_recaptcha_sitekey'))
    <!-- 機器人驗證 -->
    <script type="text/javascript">
      function captcha_onclick() {
        document.getElementById('recaptchaValidator').value = grecaptcha.getResponse();
      }
      window.addEventListener('pageshow', (event) => {
        if(grecaptcha.getResponse()){
          grecaptcha.reset();
        }
      });
    </script>
  @endif
  <script>
    /*檢舉文章*/
    function report(){
      if($('[name="reportForm"] [name="note"]').val()==''){
        Vue.toasted.show("{{Lang::get('請輸入檢舉說明')}}", vt_error_obj); return;
      }else if(confirm("{{Lang::get('確定檢舉文章嗎')}}")){
        reportForm.submit();
      }
    }

    /*載入喜歡文章狀態*/
    $(document).ready(function(){
      try {
        /*載入愛心狀態*/
        get_love_status();
        /*觸發瀏覽紀錄*/
        article_interact(1);
      } catch (error) {
      }
    });
    async function get_love_status(){
      visitorId = await get_visitorId(); /*定義在 mainTpl.blade.php 中*/
      result = await $.ajax({
        type: "POST",
        headers: {
          'X-CSRF-Token': csrf_token 
        },
        dataType: "json",
        data:{
          visitorId: visitorId,
          article_id: "{{$data['article']->id}}",
          act_type: 2,
        },
        url: "{{url('ShareArticle/get_interact_status')}}",
      });
      if(result.code==1){
        let love_value = result.msg.length ? result.msg[0].value : -1;
        set_love_article_btn(love_value);
      }
    }
    /*喜歡或不喜歡文章*/
    $('#love_article_btn').on('click', async(e)=>{
      result = await article_interact(2);
      if(result){
        set_love_article_btn(result.msg);
      }
    });

    async function article_interact(act_type){
      try {
        visitorId = await get_visitorId(); /*定義在 mainTpl.blade.php 中*/
        let result = await $.ajax({
          type: "POST",
          headers: {
            'X-CSRF-Token': csrf_token 
          },
          dataType: "json",
          data:{
            visitorId: visitorId,
            article_id: "{{$data['article']->id}}",
            act_type: act_type,
          },
          url: "{{url('ShareArticle/interact')}}",
        });
        return result;
      } catch (error) {
      }
    }
    /*依喜歡狀態調整喜歡按鈕樣式*/
    function set_love_article_btn(love_value){
      if(love_value==-1){ /*結果是取消*/
        $('#love_article_btn i').removeClass("bi-heart-fill");
        $('#love_article_btn i').addClass("bi-heart");
      }else{
        $('#love_article_btn i').addClass("bi-heart-fill");
        $('#love_article_btn i').removeClass("bi-heart");
      }
    }
  </script>
@endsection

