@extends('home.Public.mainTpl')

@section('title')網紅後台-銷售統計@endsection

@section('css')

<style>
    .hide{
        display: none;
    }

    .memberContentBox .bindingBox a.bindingBtn.use-btn:hover{
        background-color: #ff7300;
        cursor: default;
    }

    .orderTable td, .orderTable th{ text-align: center; }
    .orderTable img{
        width: 50px;
    }
</style>

@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">首頁</a></li>
                <li><a href="{{url('Kol/kol_data')}}">網紅後台</a></li>
                <li><a href="{{url('Kol/sale_record')}}">銷售統計</a></li>
            </ul>
        </div>
    </section>

    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->

        <div id="itemBox" class="memberInforBox">

            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.kol.kol_menu')
                <!-- /////////////////////////////////////////// -->
            </div>

            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>銷售統計</h3>
                                </div>
                            </div>
                        </div>

                        <div class="memberMiddle">
                                <table class="orderTable table table-striped table-bordered table-rwd">
                                    <thead>
                                        <tr class="tr-only-hide">
                                            <th>開賣日</th>
                                            <th>商品名</th>
                                            <th>商品圖</th>
                                            <th>售價</th>
                                            <th>己結算銷售數量</th>
                                            <th>己結算銷售總額</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="product in sale_record">
                                            <td data-th="開賣日" v-text="product.s_time"></td>
                                            <td data-th="商品名"><a target="_blank" :href="'{{request()->server('REQUEST_SCHEME')}}://'+product.product.url" v-text="product.product.name"></a></td>
                                            <td data-th="商品圖"><img :src="'{{request()->server('REQUEST_SCHEME')}}://'+product.product.url2"></td>
                                            <td data-th="售價" v-text="product.product.price"></td>
                                            <td data-th="己結算銷售數量" v-text="product.num"></td>
                                            <td data-th="己結算銷售總額" v-text="product.total"></td>
                                        </tr>
                                    </tbody>
                                </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
    <script type="text/javascript">
        var itemData = { sale_record: [] }
        var rightContentBoxVM = new Vue({
            el: '#rightContentBox', 
            data: itemData,
            methods: {
            }
        });

        $.ajax({
            url: "{{url('Kol/get_sale_record')}}",
            type: 'POST',
            headers: {
                'X-CSRF-Token': csrf_token 
            },
            datatype: 'json',
            data: {
                type: 2,
            },
            error: function (xhr) {
                alert('失敗');
                console.error(xhr);
            },
            success: function (response) {
                rightContentBoxVM.sale_record = response;
            },
        });
    </script>

@endsection

