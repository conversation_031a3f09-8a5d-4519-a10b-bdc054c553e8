@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['consumption']['second_menu']createpay->name} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Consumption/create_pay')}}">{{$data['frontend_menu']['consumption']['second_menu']createpay->name}</a></li>
            </ul>
        </div>
    </section>

    <section class="buyform container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                        <div class="title">
                            <h3>{{$data['frontend_menu']['consumption']['second_menu']createpay->name}</h3>
                        </div>
                    </div>
                    </div>
                    <div class="memberMiddle"> 
                        <div class="buyformBox">
                            <form action="{{url('Consumption/do_create_pay')}}" method="post">
                                @csrf
                                <label class="form-group">{{Lang::get('請輸入金額')}}</label>
                                <input class="form-control" type="number" name="price">
                                <input class="form-control" type="hidden" name="distributor_id" value="{request()->get('distributor_id') ?? 0}">
                            
                                <div class="d-flex justify-content-center mt-4">
                                    <button class="col-6 btn btn-success m-4">{{Lang::get('送出')}}</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('ownJS')
@endsection