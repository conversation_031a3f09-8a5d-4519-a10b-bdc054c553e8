@extends('home.Public.mainTpl')
@section('title'){{Lang::get('修改報名資料')}} @if($data['user']['id'] != 0)- {{Lang::get('會員專區')}}@endif | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
	<section class="directoryRow">
		<div class="container">
			<ul>
				<li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
				@if($data['user']['id'] != '0')
				<li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
				@endif
				<li><a href="{{url('Examination/examination')}}">{{Lang::get('修改報名資料')}}</a></li>
			</ul>
		</div>
	</section>
	<section class="container max-wideVersion productPublic member_nofooterimg table_align_left examination memberTop_normalmargin">
	    <!-- /////////////////////////////////////////// -->
	    <!-- /////////////////////////////////////////// -->
	    <!-- /////////////////////////////////////////// -->
	    <div id="itemBox" class="memberInforBox">
	        <div id="leftBox">
	            <!-- /////////////////////////////////////////// -->
	            @include('home.Public.member_menu')
	            <!-- /////////////////////////////////////////// -->
	        </div>
	        <div id="rightContentBox" class="innerPageBox memberContentBox">
	            <div class="paddingSpacing">
	                <div class="memberTop">
						<div class="titleBox">
							<div class="title">
								<h3>{{Lang::get('修改報名資料')}}</h3>
							</div>
						</div>
	                </div>
	                <div class="memberMiddle">
						<table class="orderTable table table-striped table-bordered table-rwd">
							<thead>
							<tr class="tr-only-hide">
								<th>{{Lang::get('報名名稱')}}</th>
								<th>{{Lang::get('開始日期')}}</th>
							</tr>
							</thead>
							@foreach($data['re'] as $vo)
								<tr>
									<td data-th="{{Lang::get('報名名稱')}}">
										<a href="{{url('Examination/examinee_list'). '?type_id=' .$vo['tinfo_id']}}">
											{{$vo['in_title']}}-{{$vo['area_title']}}
										</a>
									</td>
									<td data-th="{{Lang::get('開始日期')}}">
										@if($vo['act_time']=='')
											{{Lang::get('無時間')}}
										@else
											{{str_replace("T", " ", $vo['act_time'])}}
										@endif
									</td>
								</tr>
							@endforeach
						</table>
	                </div>
	                <div class="memberBottom">
	                    <div>
	                        {!! $data['consent_other'] !!}
	                    </div>
	                </div>
	            </div>
	        </div>
	    </div>
	    <!-- /////////////////////////////////////////// -->
	    <!-- /////////////////////////////////////////// -->
	    <!-- /////////////////////////////////////////// -->
		<div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
		  <div class="modal-dialog" role="document">
			<div class="modal-content">
			  <div class="modal-header">
				<h5 class="modal-title" id="exampleModalLabel">{{Lang::get('詳細內容')}}</h5>
				<button type="button" class="close eeeeeee" data-dismiss="modal" aria-label="Close">
				  <span aria-hidden="true">&times;</span>
				</button>
			  </div>
			  <div class="modal-body" id="boxModel">
				...
			  </div>
			  <div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
			  </div>
			</div>
		  </div>
		</div>
	</section>
@endsection
@section('ownJS')
@endsection

