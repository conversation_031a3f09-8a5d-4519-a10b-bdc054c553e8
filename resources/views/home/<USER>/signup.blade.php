@extends('home.Public.mainTpl')
@section('title'){{Lang::get('會員註冊')}} | {{$data['seo'][0]['title']}}@endsection

@if($data['recommend_user'])
<!--社群分享SEO-->
    @section('ogtitle'){{$data['recommend_user']['share_title']}}@endsection
    @section('ogdescription'){{$data['recommend_user']['share_text']}}@endsection
    @section('ogurl'){{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}{{request()->server('REQUEST_URI')}}@endsection
    @section('ogimage'){{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}{{$data['recommend_user']['share_pic']}}@endsection
@endif

@section('css')
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li id="aboutDirectoryText"><a href="{{url('Login/signup')}}">{{Lang::get('會員註冊')}}</a></li>
            </ul>
        </div>
    </section>

    <section class="container max-wideVersion min-heightVersion aboutUsBox">
        @include('home.Public.newsLink')
        <div class="titleBox">
            <div class="title">
                <h3>{{Lang::get('會員註冊')}}</h3>
            </div>
        </div>

        <div class="memberContentBox">
            @if(config('control.control_down_line')==1)
                @if($data['recommend_user'])
                    <div class="col-12 mb-4">
                        {!! $data['recommend_user']['recommend_content'] !!}
                    </div>
                @endif
            @endif

            <form action="{{url('Login/dosignup')}}" method="post" name="signupForm" id="signupForm" enctype="multipart/form-data">
                @csrf
                <div class="memberMiddle memberitems">
                    @if(config('control.control_platform')==1)

                    @endif
                    <div class="row">
                        @if(config('control.control_down_line')==1)
                            <div class="form-group col-sm-6 col-12">
                                <label for="su_name" class="col-form-label">{{Lang::get('推薦者')}}({{Lang::get('會員編號')}})</label>
                                <input type="text" class="form-control" name="upline_user" placeholder="{{Lang::get('請輸入推薦人編號')}}" id="upline_user" value="{{$data['recommend_user_number']}}">
                            </div>
                            <div class="form-group col-sm-6 col-12"></div>
                        @endif

                        <div class="form-group col-sm-6 col-12">
                            <label for="su_email" class="col-form-label"><span class="text-danger">*</span> {{Lang::get('帳號')}}</label><span class="smallText">{{Lang::get('請輸入手機號碼')}}</span>
                            <input type="text" class="form-control " name="phone" id="su_email" placeholder="{{Lang::get('手機為您的帳號')}}">
                        </div>
                        <div class="form-group col-sm-6 col-12">
                            <label for="su_password" class="col-form-label"><span class="text-danger">*</span> {{Lang::get('密碼')}}</label><span class="smallText">{{Lang::get('密碼需包含英文及數字')}}</span>
                            <input type="password" class="form-control " name="password" id="su_password" placeholder="{{Lang::get('請輸入密碼')}}">
                        </div>
                        <div class="form-group col-sm-6 col-12">
                            <label for="su_passwordB" class="col-form-label"><span class="text-danger">*</span> {{Lang::get('再確認密碼')}}</label>
                            <input type="password" class="form-control" name="passwordB" id="su_passwordB" placeholder="{{Lang::get('再確認密碼')}}">
                        </div>

                        @if(empty(config('control.close_function_current')['會員瀏覽商品設定']))
                            <div class="form-group col-sm-6 col-12">
                                <label for="" class="col-form-label">{{Lang::get('商品瀏覽權限')}}</label>
                                <select class="form-control" name="product_view_id">
                                    @foreach($data['product_view'] as $vo)
                                        <option value="{{$vo['id']}}">{{$vo['name']}}</option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        <template v-if="user_type_radio==1">
                            <div class="form-group col-sm-6 col-12">
                                <label class="col-form-label">{{Lang::get('店鋪名稱')}}</label>
                                <input type="text" class="form-control" name="shop_name">
                            </div>
                            <div class="form-group col-sm-6 col-12"></div>
                            <div class="form-group col-sm-6 col-12">
                                <label class="col-form-label">{{Lang::get('公司登記文件')}}</label>
                                <input type="file" class="form-control" name="file_company">
                            </div>
                            <div class="form-group col-sm-6 col-12">
                                <label class="col-form-label">{{Lang::get('個人身份文件')}}</label>
                                <input type="file" class="form-control" name="file_person">
                            </div>
                            <div class="form-group col-sm-12 col-12">
                                <label class="col-form-label">{{Lang::get('收款帳號')}}</label>
                                <div class="row m-0">
                                    <div class="col-sm-2">
                                        <input type="text" class="form-control" name="bank" placeholder="{{Lang::get('銀行名稱')}}">
                                    </div>
                                    <div class="col-sm-2">
                                        <input type="text" class="form-control" name="bank_code" placeholder="{{Lang::get('分行代號')}}">
                                    </div>
                                    <div class="col-sm-2">
                                        <input type="text" class="form-control" name="bank_account_name" placeholder="{{Lang::get('戶名')}}">
                                    </div>
                                    <div class="col-sm-6">
                                        <input type="text" class="form-control" name="bank_account_code" placeholder="{{Lang::get('銀行帳號')}}">
                                    </div>
                                </div>
                            </div>
                        </template>

                        @if(config('extra.shop.google_recaptcha_sitekey'))
                        <div class="w-100 d-flex justify-content-center mb-3">
                            <div class="g-recaptcha text-center" data-callback="captcha_onclick" 
                                 data-sitekey="{{config('extra.shop.google_recaptcha_sitekey')}}">
                            </div>
                            <input type="hidden" name="recaptcha" id="recaptchaValidator" />
                        </div>
                        @endif

                        <hr>
                        <div class="form-group col-12 text-center">
                            <div class="form-check term-error-box">
                                <input class="form-check-input" type="checkbox" id="su_agreement" name="term" value="" checked>
                                <label class="form-check-label" for="su_agreement">
                                    <span>{{Lang::get('我已閱讀並同意遵守本網站之條款')}}
                                        <a href="###" style="" data-toggle="modal" data-target="#memberRuleModel">({{Lang::get('查看')}})</a>
                                    </span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group col-12 text-center">
                            <label class="use-btn" style="cursor: pointer;">
                                {{Lang::get('立馬註冊')}}
                                <input type="submit" value="{{Lang::get('立馬註冊')}}" id="check_btn_signupForm">
                            </label> 
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
@endsection

@section('Modal')
    <!-- Modal -->
    <div class="modal fade shoppingCart" id="memberRuleModel" tabindex="-1" role="dialog" aria-labelledby="memberRuleModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="memberRuleModelTitle">{{Lang::get('會員條款')}}</h5>
                </div>
                <div class="modal-body">
                    {!!$data['consent']['member']!!}
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.2/localization/messages_zh.min.js"></script>

    <script type="text/javascript">
        signupForm_data = {
            user_type_radio: 0,
        }
        var signupFormVM = new Vue({
            el: '#signupForm',
            data: signupForm_data,
            method: {
            },
        })
    </script>

    <script>
        var codeVerifyCheck_signupForm  = true; /*配合mainTpl送表單檢查*/

        $.validator.addMethod(
            "regex",
            function(value, element, regexp) {
                var re = new RegExp(regexp);
                return this.optional(element) || re.test(value);
            },
            "Please check your input."
        );

        $("#signupForm").validate({
            submitHandler: function() {
                $('#body_block').show();
                setTimeout(() => {
                    submitForm('signupForm');
                }, 100);
            },
            rules: {
                password: {
                    required: true
                },
                passwordB: {
                    required: true,
                    equalTo: "#su_password"
                },
                phone: {
                    remote: {
                        url: "{{url('Ajax/ckaccount')}}",
                        type: "post",
                        headers: {
                            'X-CSRF-Token': csrf_token
                        }
                    },
                    required: true,
                    number: true
                },
                term: {
                    required: true
                },

                file_company: {
                    required: true
                },
                file_person: {
                    required: true
                },
                bank: {
                    required: true
                },
                bank_code: {
                    required: true
                },
                bank_account_name: {
                    required: true
                },
                bank_account_code: {
                    required: true
                },
            },
            messages: {
                password: {
                    required: "{{Lang::get('密碼不得為空')}}",
                },
                passwordB: {
                    required: "{{Lang::get('密碼不得為空')}}",
                    equalTo: "{{Lang::get('密碼不一致')}}"
                },
                phone: {
                    remote: "{{Lang::get('帳號已經存在')}}",
                    required: "{{Lang::get('手機不得為空')}}",
                    number: "{{Lang::get('手機只能是數字')}}"
                },
                term: {
                    required: "{{Lang::get('請勾選')}}"
                },

                file_company: {
                    required: "{{Lang::get('請選擇公司登記文件')}}"
                },
                file_person: {
                    required: "{{Lang::get('請選擇個人身份文件')}}"
                },
                bank: {
                    required: "{{Lang::get('請輸入銀行名稱')}}"
                },
                bank_code: {
                    required: "{{Lang::get('請輸入分行代號')}}"
                },
                bank_account_name: {
                    required: "{{Lang::get('請輸入戶名')}}"
                },
                bank_account_code: {
                    required: "{{Lang::get('請輸入銀行帳號')}}"
                },
            }
        });
    </script>

    @if(config('extra.shop.google_recaptcha_sitekey'))
        <!-- 機器人驗證 -->
        <script type="text/javascript">
            function captcha_onclick() {
                document.getElementById('recaptchaValidator').value = grecaptcha.getResponse();
            }
            window.addEventListener('pageshow', (event) => {
                if(grecaptcha.getResponse()){
                    grecaptcha.reset();
                }
            });
        </script>
    @endif
@endsection

