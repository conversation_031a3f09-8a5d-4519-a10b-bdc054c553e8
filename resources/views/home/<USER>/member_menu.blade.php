<div class="w-100">
    @if($data['user']['id'] != '0' || !isset(config('control.close_function_current')['會員管理']))
        <!-- 有登入 -->
        <a href="{{url('Cart/choose_shop')}}" class="goCartBox mb-2 d-block">
            <div class="cartBox">
                <i class="icon-shopping_cart"></i><span class="counter prodNum mainbtn">{{($data['cartCount']) ?? "0"}}</span>
                <span class="memberName">{{Lang::get('前往購物車')}}</span>
            </div>
        </a>
    @else
        <!-- 未登入 -->
        <a href="" class="goCartBox mb-2 d-block"  data-toggle="modal" data-target="#shoppingCart">
            <div class="cartBox">
                <i class="icon-shopping_cart"></i><span class="counter prodNum mainbtn">{{$data['cartCount'] ?? "0"}}</span>
                <span class="memberName">{{Lang::get('前往購物車')}}</span>
            </div>
        
        </a>
    @endif

    @if(($data['user']['id'] != '0'))
        <div class="memberToggle memberNumberBox border" style="border-radius:10px">
            <div class="photo"><i class="bi bi-person"></i></div>
            <div>
                <h3 class="memberName mb-1">{{$data['user']['name']}}</h3>
                @if(config('control.control_VipDiscount')==1)
                    <p class="lavel">{{Lang::get('課程等級')}}：
                        @if($data['user']['vip_id']==0)
                            {{Lang::get('無')}}
                        @else
                            {{$data['user']['vip_name']}}
                        @endif
                    </p>
                @endif
            </div>
        </div>
        <div class="memberMwnu">
            <ul class="row">
                <li class="col-md-4 col-6">
                    <a class="member" href="{{url('Member/member')}}">{{Lang::get('個人資訊')}}</a>
                </li>
                <li class="col-md-4 col-6">
                    <a class="my_status" href="{{url('Dividend/my_status')}}">{{Lang::get('會員積分查看')}}</a>
                </li>
                <li class="col-md-4 col-6">
                    <a class="point_increasable" href="{{url('Points/point_increasable')}}">{{Lang::get('增值積分紀錄')}}</a>
                </li>
                @if(!isset(config('control.close_function_current')['點數設定']))
                    <li class="col-md-4 col-6">
                        <a class="points" href="{{url('Points/points')}}">{{Lang::get('紅利點數紀錄')}}</a>
                    </li>
                @endif
                <li class="col-md-4 col-6">
                    <a class="cash" href="{{url('Dividend/cash')}}">{{Lang::get('積分提現')}}</a>
                </li>
                @if(config('control.control_platform')==1 && $data['user']['user_type']==1)
                    <!-- <li class="col-md-4 col-6">
                        <a class="" href="{{url('/distribution/Index/index')}}" target="_blank">{{Lang::get('賣家管理後台')}}</a>
                    </li> -->
                @endif
                <li class="col-md-4 col-6">
                    <a class="product_store" href="{{url('Member/product_store')}}">{{Lang::get('我的收藏')}}</a>
                </li>
                <li class="col-md-4 col-6">
                    <a class="orderform" href="{{url('Orderform/orderform')}}">{{Lang::get('訂單列表')}}</a>
                </li>

                @if(config('control.control_register')==1)
                <li class="col-md-4 col-6">
                    <a class="examination" href="{{url('examination/examination')}}">{{Lang::get('修改報名資料')}}</a>
                </li>
                @endif

                @if(!isset(config('control.close_function_current')['消費抽抽樂']))
                    <li class="col-md-4 col-6">
                        <a class="scratch_history" href="{{url('Consumption/scratch_history')}}">{{$data['frontend_menu']['consumption']['second_menu']['luckdraw']['name']}}</a>
                    </li>
                @endif

                @if(!isset(config('control.close_function_current')['消費累積兌換']))
                    <li class="col-md-4 col-6">
                        <a class="consumption_exchange" href="{{url('Consumption/exchange')}}">{{$data['frontend_menu']['consumption']['second_menu']['gift']['name']}}</a>
                    </li>
                @endif
                
                @if(!isset(config('control.close_function_current')['優惠券專區']))
                    <li class="col-md-4 col-6">
                        <a class="coupon" href="{{url('Coupon/coupon')}}">{{Lang::get('會員優惠券')}}</a>
                    </li>
                @endif

                @if(!isset(config('control.close_function_current')['註冊商品回函']))
                    <li class="col-md-4 col-6">
                        <a class="reg_product" href="{{url('Member/reg_product')}}">{{Lang::get('商品註冊')}}</a>
                    </li>
                @endif

                @if(!isset(config('control.close_function_current')['找貨回函']))
                    <li class="col-md-4 col-6">
                        <a class="buyform" href="{{url('Buyform/buyform')}}">{{$data['frontend_menu']['findorder']['name']}}紀錄</a>
                    </li>
                @endif

                @if(!isset(config('control.close_function_current')['詢價回函']))
                    <li class="col-md-4 col-6">
                        <a class="askprice" href="{{url('Askprice/askprice')}}">{{Lang::get('商品詢價紀錄')}}</a>
                    </li>
                @endif
                
                @if(!isset(config('control.close_function_current')['會員文章分享']))
                <li class="col-md-4 col-6">
                    <a class="share_article" href="{{url('Member/share_article')}}">{{Lang::get('文章分享')}}</a>
                </li>
                @endif
                @if(config('control.control_down_line')==1)
                    <li class="col-md-4 col-6">
                        <a class="share_content" href="{{url('Member/share_content')}}">{{Lang::get('招募EDM管理')}}</a>
                    </li>
                    <li class="col-md-4 col-6">
                        <a class="down_line" href="{{url('Member/down_line')}}">{{Lang::get('招募會員列表')}}</a>
                    </li>
                @endif

                <li class="col-md-4 col-6">
                    <a href="{{url('Login/logout')}}">{{Lang::get('登出')}}</a>
                </li>
            </ul>
        </div>
    @endif

    <script src="{{__PUBLIC__}}/js/memberToggle.js?1234"></script>
    <script type="text/javascript">
        var mebermenu_active = '.'+'{{$data["mebermenu_active"]}}';
        $(mebermenu_active).addClass('active');
    </script>

</div>
