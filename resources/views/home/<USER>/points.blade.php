@extends('home.Public.mainTpl')
@section('title'){{Lang::get('紅利點數紀錄')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
  <section class="directoryRow">
    <div class="container">
      <ul>
        <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
        <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
        <li><a href="{{url('Points/points')}}">{{Lang::get('紅利點數紀錄')}}</a></li>
      </ul>
    </div>
  </section>
  <section class="container max-wideVersion productPublic">
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox" class="memberInforBox">
      <div id="leftBox">
        <!-- /////////////////////////////////////////// -->
        @include('home.Public.member_menu')
        <!-- /////////////////////////////////////////// -->
      </div>
      <div id="rightContentBox" class="innerPageBox memberContentBox">
        <div class="paddingSpacing">
          <div class="pack">
            <div class="memberTop">
              <div class="titleBox">
                <div class="title">
                  <h3>{{Lang::get('紅利點數紀錄')}}</h3>
                </div>
              </div>
              <ul class="nav tabNavBox justify-content-end">
                <li class="nav-item">
                  <a data-toggle="modal" data-target="#descriptionContModel">{{Lang::get('使用說明')}}</a>
                </li>
              </ul>
            </div>
            <div class="memberMiddle">
              <div>
                <h3 class="subtitle">{{Lang::get('點數統計')}}</h3>
                <p>
                  {{Lang::get('目前累計紅利')}}：
                  <span class="bonusNum">
                    <span v-text="current_points"></span>
                  </span>

                  @if(config('control.control_point_duration') != 99)
                    <span style="padding-left: 150px;">
                      {{Lang::get('將到期的點數')}}
                      ({{date('Y').'-'.config('control.control_point_duration_date')}})：
                      <span>{{$data['expiring_points']}}</span>
                    </span>
                  @endif
                </p>
              </div>
              <hr>
              <div class="couponBox">
                <h3 class="subtitle">{{Lang::get('歷史紀錄')}}</h3>
                <table class="orderTable table table-striped table-bordered table-rwd">
                  <thead>
                    <tr class="tr-only-hide">
                      <th width="100">{{Lang::get('日期')}}</th>
                      <th width="200">{{Lang::get('說明')}}</th>
                      <th width="100" class="text-lg-right text-left">{{Lang::get('數量')}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <template v-for="record in point_records">
                      <tr>
                        <td data-th="{{Lang::get('日期')}}" v-text="record.msg_time.slice(0, 16)"></td>
                        <td data-th="{{Lang::get('說明')}}" v-text="record.msg"></td>
                        <td data-th="{{Lang::get('數量')}}" v-text="record.points" class="text-lg-right text-left"></td>
                      </tr>
                    </template>
                  </tbody>
                </table>
              </div>
              <div class="text-center">
                <crm_index_pages 
                :change_page="change_page"
                :current_page="searchform.page" 
                :count_of_items="records_total" 
                :count_of_page="searchform.count_of_items"
              ></crm_index_pages>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- descriptionContModel start-->
    <div class="modal fade " id="descriptionContModel" tabindex="-1" role="dialog" aria-labelledby="descriptionContModelTitle" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content ">
          <div class="modal-header">
            <h5 class="modal-title" id="descriptionContModelTitle">{{Lang::get('使用說明')}}</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <p>{!! $data['consent'] !!}</p>
          </div>
        </div>
      </div>
    </div>
    <!-- descriptionContModel end-->
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
  </section>
@endsection

@section('ownJS')
  <script>
    Vue.component('crm_index_pages', {
      template:`
        <ul class="pagination">
        <li class="" v-if="current_page-1 > 0">
          <a href="###" @click="trigger_change_page(current_page-1)">«</a>
        </li> 
        <template v-for="page in pages">
          <li :class="[current_page==page ? 'active' : '']" >
          <a v-if="current_page!=page" href="###" v-text="page" @click="change_page(page)"></a>
          <span class="text-dark" v-else v-text="page"></span>
          </li>
        </template>
        <li class="" v-if="current_page+1 <= computed_page_num">
          <a href="###" @click="trigger_change_page(current_page+1)">»</a>
        </li> 
        </ul>
      `,
      data: function() {
        return {
        pages: [1],
        };
      },
      props: {
        change_page: Function,  /*換頁*/
        current_page: Number,   /*當前頁數*/
        
        count_of_items: Number, /*項目總數(計算總頁數用)*/
        count_of_page: Number,  /*一頁數量(計算總頁數用)*/
        
        total_pages: Number,    /*總頁數*/
      },
      computed: {
        computed_page_num: function(){
        page_num = 1;
        if(this.total_pages){ /*有傳入總頁數*/
          page_num = this.total_pages;
        }else if(this.count_of_items && this.count_of_page){ /*有傳入一頁數量&項目總數*/
          page_num = Math.ceil( this.count_of_items / this.count_of_page);
        }
        return page_num;
        },
      },
      watch: {
        current_page: {
        immediate: true, // 立即执行一次监听器
        handler: function() { this.updatePages(); },
        },
        count_of_items: {
        handler: function() { this.updatePages(); },
        },
        count_of_page: {
        handler: function() { this.updatePages(); },
        },
        total_pages: {
        handler: function() { this.updatePages(); },
        },
      },
      methods: {
        updatePages() { /*根據傳入最大頁數生成新的頁數列表*/
        var pages = [];
        for (var i=-5; i<5; i++) {
          if(i+this.current_page > 0 && i+this.current_page <= this.computed_page_num){
          pages.push(i+this.current_page);
          }
        }
        this.pages = pages;
        },
        trigger_change_page(page){
        if (typeof this.change_page === 'function') {
          if(page > 0 && page <= this.computed_page_num){
          this.change_page(page);
          }
        }
        }
      },
    });

    const empty_searchform = {
      page: 1, /*當前頁數*/
      count_of_items: 20,

      point_type: 'point',

      date_s: '',
      date_e: '',
      user_key: '',
      point_msg: '',
    };
    var content_data = {      
      searchform: JSON.parse(JSON.stringify(empty_searchform)),
      point_records: [],
      records_total: 0,

      current_points: '',
    }
    var contentVM = new Vue({
      el: '#rightContentBox',
      data: content_data,
      created(){
        this.get_records();
      },
      methods: {
        reset_search: function(){
          this.searchform = JSON.parse(JSON.stringify(empty_searchform))
        },
        clear_search: function($event){
          $event.preventDefault();
          this.reset_search();
          this.get_records();
        },
        change_page: function(p){
          if(this.searchform.page!=p){
            this.searchform.page = p;
            this.get_records();
          }
        },
        search_record: function($event){
          $event.preventDefault();
          this.searchform.page = 1;
          this.get_records();
        },
        get_records: async function (){
          Vue.toasted.show("{{Lang::get('載入中')}}",{duration:1500, className: ["toasted-primary", 'bg-success']});
          this.point_records = [];
          try {
            resp = await $.ajax({
            type: 'post',
            dataType: 'json',
            url: "{{url('Points/get_point_data')}}",
            headers: {
              'X-CSRF-Token': csrf_token 
            },
            data: this.searchform,
            });
            this.point_records = resp.records_show;
            this.records_total = resp.records_total;
            this.current_points = resp.current_points;

            Vue.toasted.show("{{Lang::get('載入完成')}}",{duration:1500, className: ["toasted-primary", 'bg-success']}); 
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
        },
      },
    });
  </script>
@endsection