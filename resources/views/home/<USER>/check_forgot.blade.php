@extends('home.Public.mainTpl')
@section('title'){{Lang::get('忘記密碼')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.0/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.0/umd/popper.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.0/js/bootstrap.min.js"></script>
  <style>
  .jumbotron {
      background-color: #fffbed;
  }
  .grid_2{
    display: none;

  }
  </style>
@endsection

@section('content')
    <div class="jumbotron text-center border border-warning">
        <form action='{{url("Login/change_forgot")}}' method="POST">
            @csrf
            <div class="form-group row">
                <label for="staticEmail" class="col-sm-2 col-form-label">{{Lang::get('帳號')}}</label>
                <div class="col-sm-10">
                    <input type="text" name="id" readonly class="form-control-plaintext" id="staticEmail" value="{{$data['id']}}">
                    <input type="hidden" name="code" readonly value="{{$data['code']}}">
                </div>
            </div>
            <div class="form-group row">
                <label for="inputPassword" class="col-sm-2 col-form-label">{{Lang::get('新密碼')}}</label>
                <div class="col-sm-10">
                    <input type="password" name="password" class="form-control" id="inputPassword" placeholder="Password">
                    <small id="emailHelp" class="form-text text-muted text-left">{{Lang::get('密碼需包含英文及數字')}}</small>
                </div>
            </div>
            <div class="form-group row">
                <label for="inputPassword" class="col-sm-2 col-form-label">{{Lang::get('再確認密碼')}}</label>
                <div class="col-sm-10">
                    <input type="password" name="passwordB" class="form-control" id="inputPassword" placeholder="Password">
                </div>
            </div>
            <button type="submit" class="btn btn-danger mb-2 mt-4 px-4">{{Lang::get('確認')}}</button>
        </form>
    </div>
@endsection

@section('ownJS')
    <script>
        $('.LoginTab').addClass('thispage');
    </script>
@endsection
