@extends('home.Public.mainTpl')
@section('title'){{$data['news']->title}} - {{$data['frontend_menu']['news']['name']}} | {{$data['seo'][0]['title']}}@endsection

@section("css")@endsection

@section("content")
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['news']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}{{$data['frontend_menu']['news']['pic']}});">
            <div class="container">
                <h2 class="page-title">{{$data['frontend_menu']['news']['name']}}</h2>
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('News/news')}}">{{$data['frontend_menu']['news']['name']}}</a></li>
                <li><a href="{{url('News/news_c')}}?id={{$data['news']->id}}">{{$data['news']->title}}</a></li>
            </ul>
        </div>
    </section>
    <!-- /////////////////////////////////////////////////////////////////////////////////////////////// -->
    <section class="container productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox">
            <div id="rightContentBox" class="innerPageBox">
                <!-- announcement start -->
                <!-- @include('home.Public.newsLink') -->        
                <!-- announcement end -->
                <!-- <div class="titleBox">
                    <div class="title">
                        <h3>{{$data['frontend_menu']['news']['name']}}
                        </h3>
                    </div>
                </div> -->
                <!-- <span class="enText">{{$data['frontend_menu']['news']['en_name']}}</span> -->
                <div class="newsIntroBox">
                    <h3 class="title">{{$data['news']->title}}</h3>
                    <span class="time"><i class="bi bi-calendar-minus"></i> {{$data['news']->time}}</span>
                    <div class="content">
                        <p>{!! $data['news']->content !!}</p>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <li class="page-item previous">
                                <a class="" 
                                    @if($data['pageup'] != null)
                                        href="{{url('News/news_c')}}?id={{$data['pageup']['id']}}"
                                    @else
                                        style="opacity:0.5;" 
                                    @endif
                                >
                                    <i class="bi bi-chevron-left"></i> {{Lang::get('上一篇')}}
                                </a>
                            </li>
                            <li class="page-item return"><a class="" href="{{url('News/news')}}">{{Lang::get('返回')}}</a></li>
                            <li class="page-item next">
                                <a class=""
                                    @if($data['pagedown'] != null)
                                        href="{{url('News/news_c')}}?id={{$data['pagedown']['id']}}"
                                    @else
                                        style="opacity:0.5;"
                                    @endif
                                >
                                    {{Lang::get('下一篇')}} <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section("ownJS")
@endsection

