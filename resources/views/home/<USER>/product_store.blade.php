@extends('home.Public.mainTpl')

@section('title'){{Lang::get('我的收藏')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Member/favorites_product')}}">{{Lang::get('我的收藏')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                @include('home.Public.member_menu')
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{Lang::get('我的收藏')}}</h3>
                            </div>
                        </div>
                        <div class="memberMiddle">
                            <div class="couponBox">
                                <h3 class="subtitle">{{Lang::get('商品列表')}}</h3>
                                <ul class="wishlist_box">
                                    @if(empty($data['products'])==false)
                                    @foreach($data['products'] as $vo)
                                    <li>
                                        <a href="{{url('Product/productinfo')}}?id={{$vo->id}}" target="_blank">
                                            <div class="img" style="background-image: url('{{__PUBLIC__}}{{$vo->pic}}');">
                                                
                                            </div>
                                            <p class="name">{{$vo->title}}</p>
                                        </a>
                                        <span class="close">
                                            <a href="javascript:store_record('{{$vo->id}}')" class="cursor-pointer">
                                                <svg width="32" height="32" viewBox="0 0 24 24"><path fill="currentColor" d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"></path></svg>
                                            </a>
                                        </span>
                                    </li> 
                                    @endforeach
                                    @endif                                         
                                </ul>
                                
                            </div>
                            <div class="row paginationBox">
                                <div class="col-12 boxCenter">
                                    {{$data['products']->links('pagination::default')}}
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
    <script type="text/javascript">
        function store_record(prodInfoId){
            $.ajax({
                method : "post",
                url : "{{url('Ajax/store_record')}}",
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                data: {
                    prodInfoId:prodInfoId, 
                    status:"0",
                },
            }).success(function(data){
                if(data.code == '1'){
                    Swal.fire({
                        title:"{{Lang::get('操作成功')}}",
                        icon: 'success',
                        content:'',
                        confirmButtonText:"{{Lang::get('確認')}}",
                        confirmButtonColor: 'var(--btn-mainlink)',
                    }).then((result) => {
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title:"{{Lang::get('操作失敗')}}",
                        icon: 'error',
                        content:'',
                        confirmButtonText:"{{Lang::get('確認')}}",
                        confirmButtonColor: 'var(--btn-mainlink)',
                    });
                }
            }).error(function(res){
                // $('#body_block').hide();
            })//error
        }
    </script>
@endsection