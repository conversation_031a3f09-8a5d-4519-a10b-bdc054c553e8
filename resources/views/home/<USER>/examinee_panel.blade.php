<head>
	<style type="text/css">
		.time_area, .date_area .one_day{
			width: 60px;
			min-width: 60px;
		}
		.time_block, .date_name{
			background: #f3f3f3;
		}
		.time_block .time_label{;
			width: 100%;
			top: -13px;
			left: 0;
		}
		.date_area{
			overflow-y: hidden;
		}
		.time_option_label{
			background-color: #a2ffff;
			opacity: 0.5;
			border-top: 1px solid #dee2e6;
			border-bottom: 1px solid #dee2e6;
		}
		.time_option:checked~.time_option_label{
			opacity: 0.7;
			background-color: #00693e;
		}
	</style>
</head>

<section class="max-wideVersion productPublic w-100" >
	<div id="itemBox" class="memberInforBox examinee_box p-0">
		<div class="memberMiddle">
            <div class="orderDetailsBox shoppingCartBox">
                <div class="row m-0">
                    <div class="w-100 memberTop d-flex flex-wrap align-items-center justify-content-between mb-2">
                        <div class="title">
                            <h3>{{$data['title']}}</h3>
                        </div>
                        @if($['user']['id'] ?? 0 != '0'))
                            <div>
                                <a class="send_btn float-right" onclick="assign_memeber_data_ex();">{{Lang::get('同會員資料')}}</a>
                            </div>
                        @endif
                    </div>
                    <input type="hidden" name="type_id" value="{{$data['type_id']}}">
                    <input type="hidden" name="type_product_id" value="{{$data['type_product_id']}}">
                    <input type="hidden" name="examinee_id" value="{{$data['examinee_id']}}">

                    <div id="question_container" class="container-fluid"  style="max-height: 60vh; overflow-x: hidden;">
                        <div class="row">
                            @foreach($register_fields as $vo)
                                @if($vo['type']!='checkbox_time')
                                <div class="col-lg-6 col-12 form-group mb-2">
                                        <label class="col-form-label">{{$vo['title')}}</label>
                                        @if($vo['required']==1)
                                            <span class="smallText">{{Lang::get('必填')}}</span>
                                        @endif

                                        <!-- 輸入設定 -->
                                        @switch($vo['type'])
                                            @case(text)
                                            @case(number)
                                            @case(date)
                                                    <input type="{{$vo['type']}}" class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}"
                                                                value="{{$vo['ans']}}">
                                            @break
                                            @case(textarea)
                                                <textarea class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}">{{$vo['ans')}}</textarea>
                                            @break
                                            @case(radio)
                                            @case(checkbox)
                                                <div class="w-100">
                                                @foreach($vo['options'] as $o_k => $option)
                                                    <div class="d-inline-block mr-2">
                                                        <input type="{{$vo['type']}}" id="field_id_{{$vo['id']}}_{{$o_k}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}" value="{{$option}}"
                                                    @if(in_array($option, $vo['ans']))checked@endif
                                                        >
                                                        <label for="field_id_{{$vo['id']}}_{{$o_k}}">{{$option}}</label>
                                                    </div>
                                                @endforeach
                                                </div>
                                            @break
                                            @case(select)
                                                <select class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}">
                                                <option value="">{{Lang::get('請選擇')}}</option>
                                                @foreach($vo['options'] as $o_k => $option)
                                                    <option value="{{$option}}" @if($option==$vo['ans'])selected@endif>{{$option}}</option>
                                                @endforeach
                                                </select>
                                            @break
                                            @case(file)
                                                <input type="{{$vo['type']}}" class="form-control" id="field_id_{{$vo['id']}}" name="field_id_{{$vo['id']}}" data="{{$vo['title']}}" accept="{{$vo['limit']}}">
                                                {{Lang::get('已上傳檔案')}}：
                                                    @if($vo['ans']->data)
                                                        <a id="field_id_{{$vo['id']}}_link" href="{{$vo['ans']->data}}" target="_blank">
                                                            {{$vo['ans']->file_name}}
                                                        </a>
                                                    @endif
                                                &nbsp;&nbsp;&nbsp;&nbsp;
                                                <a href="javascript:cancel_file('field_id_{{$vo['id']}}')">{{Lang::get('取消上傳')}}</a>
                                            @break
                                            @default
                                    @endswitch

                                    @if(strip_tags($vo['discription']))
                                        <div class="w-100"><label class="col-form-label">{{$vo['discription']}}</label></div>
                                    @endif
                                </div>
                            @endif
                            @endforeach
                        </div>
                        @if($data['checkbox_time_count']>0)
                            <div class="mb-2">
                                <!-- 時間選表格 -->
                                <div class="d-flex align-items-center">
                                    {{Lang::get('請點擊下方藍色區塊以選擇時間']}
                                    (<span class="d-inline-block time_option_label" style="width:1rem; height: 1rem;"></span>)
                                </div>
                                <div class="d-inline-flex mb-3 position-relative" style="max-width: 100%; width: calc(60px * {{$data['checkbox_time_count']+1}});">
                                    <div class="time_area d-flex flex-column p-0" style="z-index: 2">
                                        <div class="time_block border pb-3">
                                            <span class="invisible">{{Lang::get('日期')}}<br>{{Lang::get('高度')}}</span>
                                        </div>
                                        <!-- 時間點 -->
                                        @foreach($data['time_area_data'] as $index => $vo)
                                            @if($index != count($data['time_area_data']))
                                                <div class="time_block p-1 border position-relative d-inline-block w-100" style="height: 46px;">
                                                    <div class="time_label text-center position-absolute">{{$vo}}</div>
                                                </div>
                                            @else
                                                <div class="time_block border position-relative pb-3">
                                                    <div class="time_label text-center position-absolute">{{$vo}}</div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                    <div class="time_area d-flex flex-column p-0 position-absolute" style="width: inherit;">
                                        <div class="border pb-3">
                                            <span class="invisible">{{Lang::get('日期')}}<br>{{Lang::get('高度')}}</span>
                                        </div>
                                        <!-- 時間點底線(同時間點，但去 time_block 、 time_label) -->
                                        @foreach($data['time_area_data'] as $index => $vo)
                                            @if( $index != count($data['time_area_data']))
                                                <div class="p-1 border position-relative d-inline-block w-100" style="height: 46px;">
                                                </div>
                                            @else
                                                <div class="border position-relative pb-3">
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                    <div class="date_area d-flex" style="z-index: 3;">
                                        <!-- 一天 -->
                                        @foreach($register_fields as $vo)
                                            @if($vo['type']=='checkbox_time' && isset($vo['title_format']))
                                            <div class="one_day border">
                                                <div class="date_name text-center pb-3 pl-2 pr-2">
                                                    <span id="field_id_{{$vo['id']}}_title">{$vo['title_format')}}</span>
                                                </div>
                                                @foreach($vo['options'] as $o_k => $option)
                                                    @if(isset($vo['options_start'][$o_k-1]) && $vo['options_duration'][$o_k-1])
                                                    <div class="date_options position-relative">
                                                        <input class="time_option d-none" type="checkbox" 
                                                                    name="field_id_{{$vo['id']}}" id="field_id_{{$vo['id']}}_{{$o_k}}" data="{{$vo['title']}}"
                                                                    value="{{$option}}" @if(in_array($option, $vo['ans']))checked@endif>
                                                        <label class="time_option_label m-0 w-100 position-absolute cursor-pointer" 
                                                                    for="field_id_{{$vo['id']}}_{{$o_k}}" 
                                                                    style="top: calc( 46px * {{$vo['options_start'][$o_k-1]}} ); 
                                                                            height: calc( 46px * {{$vo['options_duration'][$o_k-1]}} );">
                                                        </label>
                                                    </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                                <div class="mb-5">
                                    <p>{{Lang::get('您已勾選')}}：<br><span id="time_option_clicked"></span></p>
                                </div>
                            </div>
                        @endif
                            <div class="memberBottom">
                                <div>
                                    <p>{{Lang::get('其他規定')}}<br>
                                        {!!$data['consent']!!}
                                    </p>
                                </div>
                            </div>
                    </div>
                </div>
            </div>
		</div>
	</div>
</section>

<div id="bg" class="col-12">
	<div id="pay">
		<a id="post_exinfo" class="btn btn-success btn-sm text-white send_clr arrow w-100 p-1">{{Lang::get('確認')}}</a>
	</div>
</div>
<script>
	var vm_name = "{{request()->post('vm_name')}}";
	var examinee_id = "{{request()->post('examinee_id')}}";
	var type_id = "{{request()->post('type_id')}}";
	var fields = [];
	$.ajax({
		url: '{{ url('Examination/get_fields_by_prod_id') }}',
		methods: 'post',
        headers: {
            'X-CSRF-Token': csrf_token 
        },
		data: {prod_id: '{$type_product_id}'},
		success: function(res){
			fields = res;
		}
	});

	function assign_memeber_data_ex(){
		$('input[data="{{Lang::get('姓名']}"]').val(`{{$data['user']['name'] ?? ''}}`);
		$('input[data="{{Lang::get('地址']}"]').val(`{{$data['home'] ?? ''}}`);
		$('input[data="{{Lang::get('手機']}"]').val(`{{$data['user']['phone'] ?? ''}}`);
		$('input[data="{{Lang::get('電話']}"]').val(`{{$data['user']['tele'] ?? ''}}`);
		$('input[data="{{Lang::get('信箱']}"], input[data="email"], input[data="Email"]').val(`{{$data['user']['email']} ?? ''}`);
	}

	/*取消上傳檔案*/
	window['delete_file'] = [];
	function cancel_file(target){
		$('#' + target + '_link').hide();
		$('#' + target).val("");
		window['delete_file'][target + '_delete'] = true;
	}

	$('#post_exinfo').click(function () {
		var register_data = {};
		var types_need_checked = JSON.parse(`{{json_encode($types_need_checked)}}`.replace(/&quot;/g, '"').trim());
		/*組織答案列*/
		for (var i = 0; i < fields.length; i++) {
			if(types_need_checked.indexOf(fields[i]['type']) != -1){ /*選項類型*/
				var checkexd = $('[name="' + fields[i]['name'] + '"]:checked');
				var ans = [];
				for (var x = 0; x < checkexd.length; x++) {
					ans.push($(checkexd[x]).attr('value'));
				}
			}
			else if(['file'].indexOf(fields[i]['type']) != -1){ /*檔案類型*/
				var t_input = $('[name="' + fields[i]['name'] + '"]')[0];

				var base64 ="";
				var file_name ="";
				ans = {
                    file_name: file_name,
                    data: base64,
                };

				if(t_input.files[0]){ /*有上傳檔案*/
					var file = t_input.files[0];

                    var reader = new FileReader();
                    reader.onload = (function(theFile){
						var input_index = i;
						var fileName = theFile.name;
						return function(e){
							// console.log(input_index);
							// console.log(fileName);
							// console.log(e.target.result);
							register_data['field_id_' + fields[input_index]['id']].file_name = fileName;
							register_data['field_id_' + fields[input_index]['id']].data = e.target.result;
						};
					})(file);
					reader.readAsDataURL(file);
				}
				else if(window['delete_file']['field_id_' + fields[i]['id'] + '_delete']){ /*有設定取消*/
					ans = {
                        file_name: "",
                        data: "delete",
                    };
				}
			}
			else{ /*文字類型*/
				var ans = $('[name="' + fields[i]['name'] + '"]').val();
			}

			register_data[fields[i]['name']] = ans;
		}
		// console.log(register_data);return;

		setTimeout(function(){
			window[vm_name].save_exinfo(examinee_id, register_data, type_id);
		}, 500);
	});
</script>
<script type="text/javascript">
	function renew_time_option_clicked(){
		var clicked_obj = {};
		var options = $('.time_option:checked');
		for (var i = 0; i < options.length; i++) {
			var option = $(options[i]);
			date = $('#'+option.attr('name')+'_title').html().replaceAll(/(<([^>]+)>)/gi, "");;
			time = '<span class="d-inline-block">' + option.val() + '</span>';

			if(Object.keys(clicked_obj).indexOf(date)==-1){
				clicked_obj[date] = time;
			}else{
				clicked_obj[date] += (', '+time);
			}
		}

		var clicked_list = [];
		var keys = Object.keys(clicked_obj);
		for (var i = 0; i < keys.length; i++) {
			clicked_list.push(keys[i] + "：" + clicked_obj[keys[i]]);
		}
		$("#time_option_clicked").html(clicked_list.join(";<br>"));
	}
	function init_checkbox_time(){
		$(document).ready(function(){
			$('.time_option').on('click', function(e){
				renew_time_option_clicked();
			});
			renew_time_option_clicked();
		});
	}
	init_checkbox_time();
</script>