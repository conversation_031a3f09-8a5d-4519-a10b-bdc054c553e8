<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>LINE Auth</title>

    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
</head>
<body>
    <script>
        var url = 'https://api.line.me/oauth2/v2.1/token';
        $.ajax({
            url     : url,
            dataType: 'json',
            headers: {
                'X-CSRF-Token': '{{csrf_token()}}' 
            },
            type    : 'POST',
            data : { 
                grant_type : 'authorization_code',       
                code : '{{$data['code']}}',       
                redirect_uri : '{{config('extra.social_media.line_url')}}',       
                client_id : '{{config('extra.social_media.client_id')}}',       
                client_secret :'{{config('extra.social_media.client_secret')}}'                          
            },
            contentType:"application/x-www-form-urlencoded; charset=UTF-8",
            success: function(data){
                profile(data.access_token);
            }
        });

        function profile(access_token){
            var url = 'https://api.line.me/v2/profile';
            $.ajax({
                url     : url,
                dataType: 'json',
                type    : 'GET',
                headers:{'Content-Type':'application/json;charset=utf8','Authorization':'Bearer '+access_token},
                success: function(data) {
                    $.ajax({
                        url: "{{url('Login/line_login')}}",
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': '{{csrf_token()}}' 
                        },
                        datatype: 'json',
                        data: { U3: data.userId,ig:data.displayName},
                        error: function (xhr) {
                            alert("{{Lang::get('操作失敗')}}");
                            console.error(xhr);
                        },
                        success: function (result) {
                            if(result.code==1){
                                if(result.msg){
                                    location.href = result.msg;
                                }else{
                                    var redirect = localStorage.getItem('redirect');
                                    if(redirect){
                                        localStorage.setItem('redirect', "");
                                        location.href = redirect;
                                    }else{
                                        location.href="{{url('index/index')}}";
                                    }
                                }
                            }
                            else{
                                alert(result.msg);
                                if(result.url){
                                    location.href = result.url;
                                }
                            }
                        },
                    });
                }
            });
        }
    </script>
</body>