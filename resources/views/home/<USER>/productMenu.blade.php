<section class="course_block w-100">
    <h3 class="cate_title"><span>menu</span>課程分類</h3>
    <div class="accordion" id="proAccordion">
         <!-- 產品選單 -->
        <template v-if="window_innerWidth() < 1200">
            <!-- 手機版 -->
            <div class="navbar-collapse product-menu-scroll">
                <ul class="nav d-flex flex-nowrap">
                    <li v-for="item in sideProductMenu" class="nav-item">
                        <a :href="'/index/product/'+ item.action +'?id=' + item.id" class="nav-link">
                            <div class="d-flex align-items-center pro-menu-title">
                                <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + item.pic" v-if="item.pic">
                                <span v-text="item.title"></span>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
        </template>
        <template v-else>
            <!-- 電腦版 -->
            <div class="item-title product_menu_phone" aria-expanded="false"
                data-toggle="collapse" data-target="#cardBox_area">
            <h4 class="">
                @if(isset($data['title_array']))
                    {{$data['frontend_menu'][strtolower($data['controller'])]['name']}}
                    @foreach($data['title_array'] as $ta)
                        / {{$ta['title']}}
                    @endforeach
                @else
                    {!! Lang::get('產品分類<span>menu</span>') !!}
                @endif
            </h4>
                <i class="position-relative"></i>
            </div>
            <div class="cardBox_area" id="cardBox_area">
                <div class="cardBox" v-for="item in sideProductMenu">
                    <div class="cardBox-header">
                        <a href="javascript:void(0);" aria-expanded="true"
                        data-toggle="collapse" :data-target="'#collapse' + item.id"
                        v-if="item.subType.length>0" :class="['protitle', item.show ? '' : 'collapsed']">
                            <div class="d-flex align-items-center pro-menu-title">
                                <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + item.pic" v-if="item.pic">
                                <span v-text="item.title"></span>
                            </div>
                        </a>
                        <a :href="'/index/product/'+ item.action +'?id=' + item.id" v-if="item.subType.length==0">
                            <div class="d-flex align-items-center pro-menu-title">
                                <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + item.pic" v-if="item.pic">
                                <span v-text="item.title"></span>
                            </div>
                        </a>
                    </div>
                    <div :id="'collapse' + item.id" :class="['collapse', item.show]" data-parent="#leftBox">
                        <ul class="navList">
                            <li v-for="prop in item.subType">
                                <a :href="'/index/product/typeinfo?id=' + prop.id">
                                    <img class="icon_pic mr-2" :src="'{{__PUBLIC__}}/' + prop.pic" v-if="prop.pic">
                                    <span v-text="prop.title"></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>

        <!-- 活動專區選單 -->
        <!-- <div class="cardBox" v-for="item in activityMenu">
            <div class="cardBox-header">
                <a :href="item.url">
                    <div class="d-flex align-items-center">
                        <span v-text="item.title"></span>
                    </div>
                </a>
            </div>
        </div> -->
    </div>
</section>
