@extends('home.Public.mainTpl')
@section('title'){{Lang::get('招募EDM管理')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
    <style>
        .hide{
            display: none;
        }
        .ke-dialog img{
            width: initial;
        }
    </style>
@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Member/share_content')}}">{{Lang::get('招募EDM管理')}}</a></li>
            </ul>
        </div>
    </section>

    <section class="container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('招募EDM管理')}}</h3>
                                </div>
                            </div>
                        </div>


                        <div class="memberMiddle">
                            <div class="headingBox">
                                <h3 class="subtitle"><span>{{Lang::get('推廣內容')}}</span></h3>
                            </div>
                            <div class="row">
                                <form  class="form-group" id="setdataform" action="{{url('Member/update_share_content')}}" method="post" enctype="multipart/form-data">
                                    @csrf
                                    <div class="container-fluid row">
                                        <div class="col-md-6 col-12 mb-2">
                                            <label for="" class="col-form-label">{{Lang::get('分享圖片')}}</label>
                                            <input id="imgInp" type="file" class="form-control" name="share_pic" accept="image/gif, image/jpeg, image/png">
                                            <img id="preview" style="width: auto; max-width: 100%;" src="{{$data['userD']['share_pic']}}">
                                        </div>
                                        <div class="col-md-6 col-12 mb-2">
                                            <label for="" class="col-form-label">{{Lang::get('分享標題')}}</label>
                                            <input class="form-control mb-2" type="text" name="share_title" value="{{$data['userD']['share_title']}}">
                                            <br>
                                            <label for="" class="col-form-label">{{Lang::get('分享內容')}}</label>
                                            <textarea  class="form-control col-12 w-100" rows="3" name="share_text">{{$data['userD']['share_text']}}</textarea>
                                        </div>
                                        <div class="col-12">
                                            <label for="" class="col-form-label">{{Lang::get('介紹文字')}}</label>
                                            <textarea  class="form-control col-12 w-100" rows="6" name="recommend_content" id="editor">{{$data['userD']['recommend_content']}}</textarea>
                                        </div>
                                    </div>
                                </form>

                                <div class="form-group col-12 justify-content-center">
                                    <a class="use-btn"  onclick="javascript:document.getElementById('setdataform').submit();">{{Lang::get('儲存')}}</a>
                                </div>
                            </div>


                            <hr class="col-12 p-0">
                            <div class="headingBox">
                                <h3 class="subtitle"><span>{{Lang::get('分享註冊網址')}}</span></h3>
                            </div>
                            <div class="row">
                                <div class="form-group col-md-12 col-12">
                                    <img class="w-100" style="max-width:175px;" 
                                         src="{{$data['userD']['share_link_qrcode']}}">
                                </div>
                                <div class="form-group col-md-6 col-12">
                                    <div class="w-100 d-flex">
                                        <input id="copy_url" class="form-group w-100 m-0" value="{{$data['userD']['share_link']}}" readonly>
                                        <a class="use-btn" href="###" onclick="javascript:copyUrl();">{{Lang::get('複製連結')}}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/kindeditor.js"></script>
    <script charset="utf-8" src="/public/static/admin/js/kindeditor/lang/zh_TW.js"></script>
    <script type="text/javascript">
        var editor;
        KindEditor.ready(function(K) { //插入影片功能 items裡加入 'code'
            editor = K.create('#editor', {
                afterBlur: function(){this.sync();},
                langType : 'zh_TW',
                items: [
                    'source', '|', 'hr', 'forecolor', 'fontsize', 'bold', 'italic', 'underline', '|',
                    'image', 'link', 'unlink','|',
                    'justifyleft', 'justifycenter', 'justifyright','|','emoticons',
                ],
                width:'100%',
                height:'500px',
                resizeType:0,
            });
        });

        function copyUrl(){
            var Url2=document.getElementById("copy_url");
            Url2.select(); // 選擇物件
            document.execCommand("Copy"); // 執行瀏覽器複製命令
            Vue.toasted.show("{{Lang::get('操作成功')}}", vt_success_obj);
        }
    </script>

    <!-- 預覽圖片 -->
    <script type="text/javascript">
        $("#imgInp").change(function(){
            //當檔案改變後，做一些事 
            readURL(this);   // this代表<input id="imgInp">
        });
        function readURL(input){
          if(input.files && input.files[0]){
            var reader = new FileReader();
            reader.onload = function (e) {
               $("#preview").attr('src', e.target.result);
            }
            reader.readAsDataURL(input.files[0]);
          }
        }
    </script>
@endsection

