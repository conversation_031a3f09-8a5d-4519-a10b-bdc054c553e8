@extends('home.Public.mainTpl')
@section('title'){{Lang::get('我的商品')}} - {{Lang::get('網紅後台')}}@endsection
@section('css')
    <style>
        .hide{
            display: none;
        }

        .memberContentBox .bindingBox a.bindingBtn.use-btn:hover{
            background-color: #ff7300;
            cursor: default;
        }

        .orderTable td, .orderTable th{ text-align: center; }
        .orderTable img{
            width: 50px;
        }

        .copyurl{
            width: 100%;
            margin-bottom: 5px;
        }

        .printBtn{
            border: none;
            border-radius: 5px;
            padding: 7.5px 20px;
            cursor: pointer;
            margin: 0 auto;
        }
        .printBtn:hover{
            background-color: #333333;
            color: #fff;
        }

        @media only screen and (max-width: 1280px){
            .copyurl{
                width: calc(100% - 100px);
            }
        }
    </style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Kol/kol_data')}}">{{Lang::get('網紅後台')}}</a></li>
                <li><a href="{{url('Kol/selling_product')}}">{{Lang::get('我的商品')}}</a></li>
            </ul>
        </div>
    </section>

    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->

        <div id="itemBox" class="memberInforBox">

            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.kol.kol_menu')
                <!-- /////////////////////////////////////////// -->
            </div>

            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('我的商品')}}</h3>
                                </div>
                            </div>
                        </div>

                        <div class="memberMiddle">
                            <table class="orderTable table table-striped table-bordered table-rwd">
                                <thead>
                                    <tr class="tr-only-hide">
                                        <th>{{Lang::get('商品名')}}</th>
                                        <th>{{Lang::get('商品圖')}}</th>
                                        <th>{{Lang::get('代銷網址')}}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="product in selling_product">
                                        <td data-th="{{Lang::get('商品名')}}" v-text="product.title"></td>
                                        <td data-th="{{Lang::get('商品圖')}}"><img :src="product.pic"></td>
                                        <td data-th="{{Lang::get('代銷網址')}}">
                                            <input class="copyurl" type="text" readonly
                                                :value="'http://'+'{{request()->server('HTTP_HOST')}}'+'/Product/productinfo?id='+ product.id +'&kol='+ product.kol_id">
                                            <br />
                                            <input class="printBtn" type="button" value="{{Lang::get('查看商品')}}" @click="open_page(product.id, product.kol_id)">
                                            <input class="printBtn" type="button" value="{{Lang::get('複製網址')}}" @click="copy_url(this)">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
    <script type="text/javascript">
        var itemData = { selling_product: [] }
        var rightContentBoxVM = new Vue({
            el: '#rightContentBox', 
            data: itemData,
            methods: {
                open_page: function(prod_id, kol_id){
                    url = "{{request()->server('REQUEST_SCHEME')}}://{{request()->server('HTTP_HOST')}}/Product/productinfo?id="+ prod_id +"&kol="+ kol_id;
                    window.open(url);
                },
                copy_url: function(item){
                    target = $(event.target);
                    var copyText = target.prev().prev().prev();
                    console.log(copyText);
                    copyText.select();
                    document.execCommand("Copy");
                    Vue.toasted.show("{{Lang::get('操作成功')}}", vt_success_obj);
                },
            }
        });

        $.ajax({
            url: "{{url('Kol/get_selling_product')}}",
            type: 'GET',
            datatype: 'json',
            error: function (xhr) {
                Vue.toasted.show("{{Lang::get('發生錯誤')}}", vt_error_obj);
                console.error(xhr);
            },
            success: function (response) {
                rightContentBoxVM.selling_product = response;
            },
        });
    </script>

@endsection

