@extends('home.Public.mainTpl')
@section('title')
    {{Lang::get('修改報名資料')}} 
    @if($data['user']['id'] != '0')
        - {{Lang::get('會員專區')}}
    @endif 
    | {{$data['seo'][0]['title']}}
@endsection
@section('css')@endsection
@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                @if($data['user']['id'] != 0)
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                @endif
                <li><a href="{{url('Examination/examination')}}">{{Lang::get('修改報名人員')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic member_nofooterimg table_align_left examination memberTop_normalmargin">
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="title">
                            <h3>
                                {{Lang::get('修改報名人員')}}
                                <span class="text-danger" style="font-size:1rem">({{Lang::get('請雙擊要修改資料的人員')}})</span>
                            </h3>
                        </div>
                    </div>
                    <div class="memberMiddle">
                        <table class="orderTable table table-striped table-bordered table-rwd examinee_table">
                            {!! $data['examinee_list_table'] !!}
                        </table>
                        
                        <div class="col-12 text-center">
                            <a href="{{url('Examination/examination')}}" class="use-btn">{{Lang::get('返回列表')}}</a>
                        </div>
                    </div>
                    <div class="memberBottom">
                        <div>
                            {!! $data['consent_other'] !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('Modal')
	<!-- Modal -->
    <a id="model_btn" class="visibility:hidden" data-toggle="modal" data-target="#model"></a>
    <div class="modal fade shoppingCart" id="model" tabindex="-1" role="dialog" aria-labelledby="modelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="modelTitle">{{Lang::get('填寫報名資料')}}</h5>
                </div>
                <div class="modal-body" id="boxModel">
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script>
        @if(empty($data['examinees']) == false)
        @foreach($examinees as $vo_e)
            examinee{{$vo_e['id']}}_VM = window['examinee{{$vo_e['id']}}_VM_init']();
        @endforeach
        @endif
    </script>
@endsection