@extends('home.Public.mainTpl')
@section('title'){{Lang::get('訂單查詢')}} @if($data['user']['id'] != '0')- {{Lang::get('會員專區')}}@endif | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                @if(($data['user']['id'] != '0'))
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                @endif
                <li><a href="{{url('Orderform/tracking')}}">{{Lang::get('訂單查詢')}}</a></li>
            </ul>
        </div>
    </section>
    <section class="container max-wideVersion productPublic">
        <!-- /////////////////////////////////////////// -->
        <div id="itemBox" class="memberInforBox">
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="pack">
                        <div class="memberTop">                                       
                            <div class="titleBox">
                                <div class="title">
                                    <h3>{{Lang::get('訂單查詢')}}</h3>
                                </div>    
                            </div>
                        </div>
                        <div class="memberMiddle">
                            <h3 class="subtitle">{{Lang::get('請輸入訂單編號')}}</h3>
                            <div class="form-group">
                                    <input class="form-control" type="text" name="orderNum" placeholder="">
                                    <a href="###" onclick="orderTraking()" class="use-btn">{{Lang::get('送出')}}</a>
                            </div>
                        </div>
                        <div class="memberBottom">
                            <div>
                                {!! $data['consent_other'] !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
        <!-- /////////////////////////////////////////// -->
    </section>
@endsection

@section('ownJS')
    <script type="text/javascript">
        function orderTraking(){
            $.ajax({
                type : "post",
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                url : "{{url('Orderform/orderTracking')}}",
                data: {orderNum: $('input[name="orderNum"]').val()},
            }).success(function(resp){
                if (resp.code){
                    location.href = "{{url('Orderform/orderform_c')}}?id="+resp.msg   
                }else{
                    Vue.toasted.show(resp.msg, vt_error_obj);
                }
            }).error(function(){
            })//error
        }
    </script>
@endsection