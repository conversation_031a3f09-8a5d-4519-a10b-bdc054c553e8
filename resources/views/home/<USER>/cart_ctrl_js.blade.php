
	<script type="text/javascript">
        function cart_change_num($this, product_id){
            if($($this).val()==0)$($this).parent().parent().remove();
            return cart_cartCtrl($($this).val(), product_id, 'assign');
        }
        function deleteCtrl($this, product_id) {
            $($this).parent().parent().remove();
            return cart_cartCtrl($($this).val(), product_id, 'delete');
        }
        function cart_cartCtrl(num, product_id, cmd) {
            return $.ajax({
                url: "{{url('Cart/cartCtrl')}}",
                type: 'POST',
                headers: {
                    'X-CSRF-Token': csrf_token 
                },
                datatype: 'json',
                data: {
                    cart_session: cart_session,
                    cmd: cmd,
                    num: Number(num),
                    product_id: product_id
                },
                error: function (xhr) {
                    console.error(xhr);
                },
                success: function (resp) {
                    // console.log(resp)
                    if (resp.code) {
                        Swal.fire({
                            title: "{{Lang::get('操作成功')}}",
                            icon: 'success',
                            content:'',
                            confirmButtonText:"{{Lang::get('確認')}}",
                            confirmButtonColor: 'var(--btn-mainlink)',
                        });
                        $('#cartCount').text(resp.msg);
                        $('#cart_tol_'+product_id).empty();
                        $('#cart_tol_'+product_id).append($('#cart_tol_'+product_id).attr('value')*Number(resp.num));
                    }else {
                        console.log('error');
                        Swal.fire({
                            title: resp.msg,
                            icon: 'error',
                            content:'',
                            confirmButtonText:"{{Lang::get('確認')}}",
                            confirmButtonColor: 'var(--btn-mainlink)',
                        });
                    }
                }
            });
        }
    </script>