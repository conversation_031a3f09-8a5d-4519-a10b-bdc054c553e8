@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['consumption']['second_menu']['luckdraw']['name']}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
    <style type="text/css">
        .table th, .table td{
            vertical-align: middle;
        }
    </style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Consumption/scratch_history')}}">
                        {{$data['frontend_menu']['consumption']['second_menu']['luckdraw']['name']}}
                    </a>
                </li>
            </ul>
        </div>
    </section>

    <section class="buyform container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{$data['frontend_menu']['consumption']['second_menu']['luckdraw']['name']}}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="memberMiddle"> 
                        <table class="orderTable table table-striped table-bordered table-rwd">
                            <thead>
                                <tr class="tr-only-hide">
                                    <th style="width: 160px;">{{Lang::get('取得日期')}}</th>
                                    <th>{{Lang::get('中獎結果')}}</th>
                                    <th style="width: 160px;">{{Lang::get('領取')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($data['draw_records'] as $vo)
                                <tr>
                                    <td data-th="{{Lang::get('取得日期')}}">{{($vo['createdate'])}}</td>
                                    <td data-th="{{Lang::get('中獎結果')}}">
                                        @if($vo['show'] == 0) 
                                            <a href="javascript:open_scratch('{{$vo['id']}}')">{{Lang::get('尚未刮出')}}</a>
                                        @else
                                            @if($vo['gift_pic'])
                                                <img class="mr-4" src="{{$vo['gift_pic']}}" style="width: 75px;">
                                            @endif
                                            <spen>{{$vo['gift_name']}}</spen>
                                        @endif

                                    </td>
                                    <td data-th="{{Lang::get('領取')}}">
                                        @if($vo['ex_date'] == '' && $vo['show'] == 1) 
                                            <button class="btn btn-warning" onclick="get_gift({{$vo['id']}})">{{Lang::get('領取')}}</button>
                                        @endif
                                        @if($vo['ex_date'] != '') 
                                            {{ date('Y/m/d H:i',$vo['ex_date'])}}
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('Modal')
    <a id="scratchModel_btn" class="d-none" data-toggle="modal" data-target="#scratchModel"></a>
    <div class="modal fade shoppingCart" id="scratchModel" tabindex="-1" role="dialog" aria-labelledby="scratchModelTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                    <h5 class="modal-title" id="scratchModelTitle">{{Lang::get('刮刮樂')}}</h5>
                </div>
                <div class="modal-body">
                    <div class="scratch_container col-12 d-flex justify-content-center mt-4">
                        <div class="scratch_card p-2">
                            <h3 class="p-2 mb-3 bg-white">{{Lang::get('刮刮樂')}}</h3>
                            <iframe src="/index/consumption/scratch?draw_record_id=0" width="320" height="320"></iframe>
                        </div>
                    </div>
                    <div class="col-6 offset-3">
                        <button id="get_gift_btn" class="btn btn-warning w-100 mb-3" onclick="get_gift()" style="display:none">{{Lang::get('領取')}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script type="text/javascript">
        var current_card = ""

        /*領獎*/
        function get_gift(scratch_id=""){
            if(scratch_id){ current_card = scratch_id; }
            if(!current_card){ alert("{{Lang::get('請先刮出結果')}}"); return; }

            $.ajax({
                url: "{{url('Consumption/draw_result_save')}}",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                data:{
                    'draw_record_id': current_card,
                    'column': 'ex_date', 
                },
                success: function(resp){
                    alert(resp.msg);
                    if(resp.code==1){
                        $('#get_gift_btn').hide();
                        location.reload();
                    }
                },
            })
        }

        /*開啟刮刮樂*/
        function open_scratch(scratch_id){
            current_card = scratch_id;
            $('#scratchModel iframe').attr('src', "/consumption/scratch?draw_record_id=" + scratch_id);
            $('#scratchModel_btn').click();
        }

        /*展示中獎結果*/
        function show_draw_result(gift_name, draw_record_id){
            alert("{{Lang::get('恭喜刮中')}}：" + gift_name);
            current_card = draw_record_id;
            $('#get_gift_btn').show();

            $.ajax({
                url: "{{url('Consumption/draw_result_save')}}",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                data:{
                    'draw_record_id': draw_record_id,
                    'column': 'show', 
                },
                success: function(resp){
                },
            });
        }

        $('#scratchModel').on('hidden.bs.modal', function (e) {
            location.reload();
        });
    </script>
@endsection