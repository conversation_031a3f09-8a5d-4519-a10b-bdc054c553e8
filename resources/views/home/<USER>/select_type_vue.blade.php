// <script type="text/javascript">
    computed:{
        price: function(){
            return this.price_json ? JSON.parse(this.price_json) : [];
        },
        show_type: function(){
            show_types = [];
            for (var i = 0; i < this.price.length; i++) {
                if(this.price[i]['title']){
                    titles = this.price[i]['title'].split('_');
                    for (var x = 0; x < titles.length; x++) {
                        if(!show_types[x]) show_types[x] = [];

                        if(show_types[x].indexOf(titles[x])==-1){
                            show_types[x].push(titles[x]);
                        }
                    }
                }
            }
            return show_types;
        },
        current_price: function () {
            if(this.priceSelect==0){
                return {};
            }else{
                for (var i = 0; i < this.price.length; i++) {
                    if(this.price[i].id == this.priceSelect){
                        return this.price[i];
                    }
                }
            }
        },
        show_price: function(){
            if(this.productinfo.has_price == 0){
                return false;
            }
            else if(this.price.length == 0){
                return false;
            }
            else if(this.current_price['count'] == 0){
                return false;
            }
            return true;
        },
    },
    methods: {
        /* 品項功能 ----------------------------*/
        changeNum: function(num){
            if(this.itemcounter + num < 1){
                Vue.toasted.show("{{Lang::get('數量不可小於1')}}", vt_error_obj);
            // }else if(this.itemcounter + num > this.limit_num){
            //     Vue.toasted.show("{{Lang::get('超出數量上限')}}", vt_error_obj);
            }else{
                this.itemcounter = this.itemcounter + num;
            }
        },
        changePic: function(index){
            item = $('#carousel02 div.item');
            if(index >=1 & index <= item.length){
                item[index-1].click();
                item[index-1].click();
            }
        },
        changeTypes: function(shouldChangePic = true){
            self = this;
            setTimeout(function(){
                if(!self.selectTypes){ return; }
                for (var i = 0; i <  self.selectTypes.length; i++) {
                    if(!self.selectTypes[i]){ /*有空值則跳出*/
                        return;
                    }
                }

                teyp_name = self.get_selectTypes_name();
                tarag_label = $('.price_option[data="' + teyp_name + '"]');
                if(tarag_label.length>0){
                    $(tarag_label[0]).click();
                    if (shouldChangePic) {
                        pic_index = $(tarag_label[0]).attr('pic_index');
                        self.changePic(pic_index);
                    }
                }else if(self.price_json!="[]"){
                    Vue.toasted.show("{{Lang::get('無此組合品項')}}", vt_error_obj);
                    self.priceSelect = 0;
                    // self.resetTypes();
                }

                /*初始化ui radio_price_option*/
                $(".radio_price_option").checkboxradio();
                /*監聽radio變化時修改樣式*/
                $(".radio_price_option").off("change");
                $(".radio_price_option").on("change", function(event){
                    targt_input = $($(this).next().context);
                    value = targt_input.val();
                    radios = targt_input.attr('radios');
                    $('.' + radios).removeClass('ui-state-active');
                    $('input.radio_price_option[value="' + value + '"]').prev().addClass('ui-state-active');
                });
            }, 150);
        },
        resetTypes: function(){
            resetType = [];
            if(this.show_type && this.productinfo.has_price==1){
                for (var i = 0; i < this.show_type.length; i++) {
                    resetType.push(this.show_type[i][0]);
                }
                this.changeTypes(false);
            }
            this.selectTypes = resetType;
        },
        get_selectTypes_name: function(str='_') {
            return this.selectTypes.join(str);
        },

        /*依商品id取得品項 並加入購物車*/
        set_price_sets_and_add_cart: function(prod_id){
            self = this;

            $.ajax({
                    url: "{{url('Product/aj_get_productinfo_and_price')}}",
                    type: 'POST',
                    headers: {
                        'X-CSRF-Token': csrf_token
                    },
                    datatype: 'json',
                    data: {
                        prod_id: prod_id
                    },
                    error: function (xhr) {
                        console.error(xhr);
                    },
                    success: function (res) {
                        if(res.code==1){
                            data = JSON.parse(res.msg);
                            self.productinfo.has_price = data.has_price;
                            self.productinfo.card_pay = data.card_pay;
                            self.price_json = JSON.stringify(data.price);
                            self.priceSelect = 0;
                            self.itemcounter = 1;
                            self.limit_num = 10;
                            self.selectTypes = [];
                            self.resetTypes();

                            setTimeout(function(){
                                select_type('cartCtrl'); /*加入購物車*/
                            }, 150);
                        }else{
                            Vue.toasted.show(res.msg, vt_error_obj);
                        }
                    }
            });
        },

        /*我的收藏功能*/
        set_store: function(status, prodInfoId=null) {
            self = this;
            // $('#block_block').show(); 難看死了...
            prodInfoId = self.prodInfoId ? self.prodInfoId : prodInfoId;
            $.ajax({
                type : "post",
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                url : "{{url('Ajax/store_record')}}",
                data: {
                    prodInfoId:prodInfoId,
                    status:status
                },
            }).success(function(data){
                if(data.code == '1'){
                    self.store = status;
                    msg_text = status==1 ? "{{Lang::get('收藏成功')}}" : "{{Lang::get('取消收藏成功')}}";
                    Vue.toasted.show(msg_text, vt_success_obj);
                    self.store_num = data.msg;
                    // $('#block_block').hide();
                }else{
                    Vue.toasted.show(data.msg, vt_error_obj);
                }
            }).error(function(res){
                Vue.toasted.show(res.msg, vt_error_obj);
                // $('#block_block').hide();
            })//error
        },
        /*非商品頁加入收藏功能*/
        set_store_product_list: function(status, prodInfoId=null) {
            self = this;

            // // 顯示載入中
            // $('#body_block').show(); //難看死了

            $.ajax({
                type : "post",
                headers: {
                    'X-CSRF-Token': csrf_token
                },
                url : "{{url('Ajax/store_record')}}",
                data: {
                    prodInfoId: prodInfoId,
                    status: status
                },
            }).success(function(data){
                if(data.code == '1'){
                    // 更新愛心圖標
                    self.updateHeartIcon(prodInfoId, status);

                    // 顯示成功訊息
                    msg_text = status==1 ? "{{Lang::get('收藏成功')}}" : "{{Lang::get('取消收藏成功')}}";
                    Vue.toasted.show(msg_text, vt_success_obj);

                    // $('#body_block').hide();
                }else{
                    Vue.toasted.show(data.msg, vt_error_obj);
                    // $('#body_block').hide();
                }
            }).error(function(res){
                Vue.toasted.show("操作失敗", vt_error_obj);
                // $('#body_block').hide();
            });
        },

        /*更新愛心圖標*/
        updateHeartIcon: function(prodInfoId, status) {
            // 找到所有該商品的愛心按鈕
            $('a[href*="set_store_product_list"][href*="' + prodInfoId + '"]').each(function() {
                var $btn = $(this);
                var $icon = $btn.find('i');

                if (status == 1) {
                    // 收藏：更新為實心愛心
                    $icon.removeClass('bi-heart').addClass('bi-heart-fill');
                    $btn.attr('href', 'javascript:priceOptionVM.set_store_product_list(0, ' + prodInfoId + ')');
                } else {
                    // 取消收藏：更新為空心愛心
                    $icon.removeClass('bi-heart-fill').addClass('bi-heart');
                    $btn.attr('href', 'javascript:priceOptionVM.set_store_product_list(1, ' + prodInfoId + ')');
                }
            });
        },

        /*詢價功能*/
        open_askprice_model: function(){
            self = this;
            $('#askpriceModel input[name="product_id"]').val(self.prodInfoId);
            $('#askpriceModel_product_name').html(self.prodInfoName);
            $('#askpriceModel input[name="product_name"]').val(self.prodInfoName);

            let product_type_id = self.current_price ? self.current_price.id : '';
            if(!product_type_id){
                product_type_id = self.price ? self.price[0].id : '';
            }
            $('#askpriceModel input[name="product_type_id"]').val(product_type_id);
            let product_type_name = self.current_price ? self.current_price.title : '';
            if(!product_type_name){
                product_type_name = self.price ? self.price[0].title : '';
            }
            $('#askpriceModel_product_type_name').html(product_type_name);
            $('#askpriceModel input[name="product_type_name"]').val(product_type_name);
            $('#askpriceModel_btn').click();
        },

        /*複製專書推廣網址*/
        copy_product_share_link: function(prodInfoId) {
            if("{{$data['user']['id']}}"!='0' || "{{empty(config('control.close_function_current')['會員管理'])}}"==""){
                let product_share_link = "{{url('product/productinfo')}}?id=" + prodInfoId;
                product_share_link += '&recommend=' + "{{$data['user']['number'] ?? ''}}"
                var Url = $(this.$refs.mylink);
                Url.val(product_share_link);
                Url.select();
                document.execCommand("copy");
                Vue.toasted.show("{{Lang::get('操作成功')}}", vt_success_obj);
            }else{
                Vue.toasted.show("{{Lang::get('請先登入會員')}}", vt_error_obj);
                $('#go_cart').click(); //會員登入畫面
            }
        },
    },
