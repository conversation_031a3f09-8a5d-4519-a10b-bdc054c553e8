@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['consumption']['second_menu']luckdraw->name} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
@endsection

@section('content')
    <div class="container">
        <div class="row pt-4 pb-4">
            <h2 class="col-12">{{Lang::get('恭喜您有剩餘刮刮樂機會')}}：{{count($draw_records)}}</h2>
            <div class="scratch_container col-12 d-flex justify-content-center mt-4">
                @foreach($data['draw_records'] as $index => $draw_record)
                <div class="scratch_card p-2" id="scratch_{{$draw_record['id']}}"
                    style="">
                    <h3 class="p-2 mb-3 bg-white">{{Lang::get('刮刮樂')}}</h3>
                    <iframe src="/index/consumption/scratch?draw_record_id={{$draw_record['id']}}" width="320" height="320"></iframe>
                </div>
                @endforeach
                <!-- transform: translate({$index-1|bcmul=15}px, {$index-1|bcmul=15}px); -->
            </div>
            <div class="col-6 offset-3">
                <button id="get_gift_btn" class="btn btn-warning w-100 mb-3" onclick="get_gift()" style="display:none">{{Lang::get('兌獎')}}</button>
                <button id="next_btn" class="btn btn-success w-100" onclick="hide_scratch_card()">{{Lang::get('換下一張')}}</button>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script type="text/javascript">
        var current_card = ""

        function change_btn(){
            var num = $('.scratch_card').length;
            if( num == 1 ){
                $('#next_btn').text("{{Lang::get('結束刮刮樂')}}");
            }
            else if( num == 0){
                location.href = "{{url('Consumption/scratch_history')}}";
            }
        }
        change_btn();

        function show_draw_result(gift_name, draw_record_id){
            alert("{{Lang::get('恭喜刮中')}}：" + gift_name);
            current_card = draw_record_id;
            $('#get_gift_btn').show();

            $.ajax({
                url: "{{url('Consumption/draw_result_save')}}",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                method: "POST",
                data:{
                    'draw_record_id': draw_record_id,
                    'column': 'show', 
                },
                success: function(resp){
                },
            });
        }

        /*兌獎*/
        function get_gift(){
            if(!current_card){ alert("{{Lang::get('請先刮出結果')}}"); return; }

            $.ajax({
                url: "{{url('Consumption/draw_result_save')}}",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': csrf_token
                },
                data:{
                    'draw_record_id': current_card,
                    'column': 'ex_date', 
                },
                success: function(resp){
                    if(resp.code==1){
                        $('#get_gift_btn').hide();
                    }
                    alert(resp.msg);
                },
            })
        }

        /*換下一張*/
        function hide_scratch_card(){
            if(!current_card){ alert("{{Lang::get('請先刮出結果')}}"); return; }

            $('#scratch_' + current_card).toggle();
            $('#scratch_' + current_card).remove();
            change_btn();
            current_card = "";
        }
    </script>
@endsection