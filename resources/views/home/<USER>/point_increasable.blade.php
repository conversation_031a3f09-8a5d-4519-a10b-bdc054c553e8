@extends('home.Public.mainTpl')
@section('title'){{Lang::get('增值積分紀錄')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')@endsection
@section('content')
  <section class="directoryRow">
    <div class="container">
      <ul>
        <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
        <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
        <li><a href="{{url('Points/point_increasable')}}">{{Lang::get('增值積分紀錄')}}</a></li>
      </ul>
    </div>
  </section>
  <section class="container max-wideVersion productPublic">
    <!-- /////////////////////////////////////////// -->
    <div id="itemBox" class="memberInforBox">
      <div id="leftBox">
        <!-- /////////////////////////////////////////// -->
        @include('home.Public.member_menu')
        <!-- /////////////////////////////////////////// -->
      </div>
      <div id="rightContentBox" class="innerPageBox memberContentBox">
        <div class="paddingSpacing">
          <div class="pack">
            <div class="memberTop">
              <div class="titleBox">
                <div class="title">
                  <h3>{{Lang::get('增值積分紀錄')}}</h3>
                </div>
              </div>
              <ul class="nav tabNavBox justify-content-end">
                <li class="nav-item">
                  <a class="invisible" data-toggle="modal" data-target="#descriptionContModel">{{Lang::get('使用說明')}}</a>
                </li>
              </ul>
            </div>
            <div class="memberMiddle">
              <div>
                <h3 class="subtitle">{{Lang::get('點數統計')}}</h3>
                <p>
                  {{Lang::get('目前累計紅利')}}：
                  <span class="bonusNum">
                    <span v-text="current_points"></span>
                  </span>
                </p>
              </div>
              <hr>
              <div class="couponBox">
                <h3 class="subtitle">{{Lang::get('歷史紀錄')}}</h3>
                <div>
                  <form method="post" @submit="go_transfer($event)">
                    <b>{{Lang::get('增值積分轉移')}}：</b>
                    <div class="d-inline-block mb-2">
                      {{Lang::get('目標')}}：
                      <input type="text" class="form-control d-inline-block mr-3" placeholder="{{Lang::get('會員編號')}}" v-model="transferform.to" style="width: 200px">
                    </div>
                    <div class="d-inline-block mb-2">
                      {{Lang::get('轉移數量')}}：
                      <input type="number" class="form-control d-inline-block mr-3" placeholder="{{Lang::get('請輸入正整數')}}" step="1" min="1" v-model="transferform.num" style="width: 150px">
                    </div>
                    <input type="submit" value="{{Lang::get('轉移')}}" class="btn btn-secondary mb-2">
                  </form>
                </div>
                <hr>
                <div>
                  <form method="post" @submit="go_point_increasable_to_point($event)">
                    <b>{{Lang::get('增值積分轉現金積分')}}：</b>
                    <div class="d-inline-block mb-2">
                      {{Lang::get('轉移數量')}}：<input type="number" class="form-control d-inline-block mr-3" placeholder="{{Lang::get('請輸入正整數')}}" step="1" min="1" v-model="to_point_num" style="width: 150px">
                    </div>
                    <input type="submit" value="{{Lang::get('轉移')}}" class="btn btn-secondary mb-2">
                  </form>
                </div>
                <table class="orderTable table table-striped table-bordered table-rwd">
                  <thead>
                    <tr class="tr-only-hide">
                      <th width="100">{{Lang::get('日期')}}</th>
                      <th width="200">{{Lang::get('說明')}}</th>
                      <th width="100" class="text-lg-right text-left">{{Lang::get('數量')}}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <template v-for="record in point_records">
                      <tr>
                          <td data-th="{{Lang::get('日期')}}" v-text="record.create_time_f.slice(0, 16)"></td>
                          <td data-th="{{Lang::get('說明')}}" v-text="record.msg"></td>
                          <td data-th="{{Lang::get('數量')}}" v-text="record.num" class="text-lg-right text-left"></td>
                      </tr>
                    </template>
                  </tbody>
                </table>
              </div>
              <div class="text-center">
                <crm_index_pages 
                  :change_page="change_page"
                  :current_page="searchform.page" 
                  :count_of_items="records_total" 
                  :count_of_page="searchform.count_of_items"
                ></crm_index_pages>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
    <!-- /////////////////////////////////////////// -->
  </section>
@endsection

@section('ownJS')
  <script>
    Vue.component('crm_index_pages', {
      template:`
        <ul class="pagination">
          <li class="" v-if="current_page-1 > 0">
            <a href="###" @click="trigger_change_page(current_page-1)">«</a>
          </li> 
          <template v-for="page in pages">
            <li :class="[current_page==page ? 'active' : '']" >
              <a v-if="current_page!=page" href="###" v-text="page" @click="change_page(page)"></a>
              <span class="text-dark" v-else v-text="page"></span>
            </li>
          </template>
          <li class="" v-if="current_page+1 <= computed_page_num">
            <a href="###" @click="trigger_change_page(current_page+1)">»</a>
          </li> 
        </ul>
      `,
      data: function() {
        return {
          pages: [1],
        };
      },
      props: {
        change_page: Function,  /*換頁*/
        current_page: Number,   /*當前頁數*/
        
        count_of_items: Number, /*項目總數(計算總頁數用)*/
        count_of_page: Number,  /*一頁數量(計算總頁數用)*/
        
        total_pages: Number,    /*總頁數*/
      },
      computed: {
        computed_page_num: function(){
          page_num = 1;
          if(this.total_pages){ /*有傳入總頁數*/
            page_num = this.total_pages;
          }else if(this.count_of_items && this.count_of_page){ /*有傳入一頁數量&項目總數*/
            page_num = Math.ceil( this.count_of_items / this.count_of_page);
          }
          return page_num;
        },
      },
      watch: {
        current_page: {
          immediate: true, // 立即执行一次监听器
          handler: function() { this.updatePages(); },
        },
        count_of_items: {
          handler: function() { this.updatePages(); },
        },
        count_of_page: {
          handler: function() { this.updatePages(); },
        },
        total_pages: {
          handler: function() { this.updatePages(); },
        },
      },
      methods: {
        updatePages() { /*根據傳入最大頁數生成新的頁數列表*/
          var pages = [];
          for (var i=-5; i<5; i++) {
            if(i+this.current_page > 0 && i+this.current_page <= this.computed_page_num){
              pages.push(i+this.current_page);
            }
          }
          this.pages = pages;
        },
        trigger_change_page(page){
          if (typeof this.change_page === 'function') {
            if(page > 0 && page <= this.computed_page_num){
              this.change_page(page);
            }
          }
        }
      },
    });

    const empty_searchform = {
      page: 1, /*當前頁數*/
      count_of_items: 20,

      point_type: 'point_increasable',

      date_s: '',
      date_e: '',
      user_key: '',
      point_msg: '',
    };
    const empty_transferform = {
      from: '',
      to: '',
      num: '',
      msg: '',
    }
    var content_data = {      
      searchform: JSON.parse(JSON.stringify(empty_searchform)),
      point_records: [],
      records_total: 0,

      transferform: JSON.parse(JSON.stringify(empty_transferform)),
      current_points: '',

      to_point_num: '',
    }
    var contentVM = new Vue({
      el: '#rightContentBox',
      data: content_data,
      created(){
        this.get_records();
      },
      methods: {
        reset_search: function(){
          this.searchform = JSON.parse(JSON.stringify(empty_searchform))
        },
        clear_search: function($event){
          $event.preventDefault();
          this.reset_search();
          this.get_records();
        },
        change_page: function(p){
          if(this.searchform.page!=p){
            this.searchform.page = p;
            this.get_records();
          }
        },
        search_record: function($event){
          $event.preventDefault();
          this.searchform.page = 1;
          this.get_records();
        },
        get_records: async function (){
          Vue.toasted.show("{{Lang::get('載入中')}}",{duration:1500, className: ["toasted-primary", 'bg-success']});
          this.point_records = [];
          try {
            resp = await $.ajax({
              type: 'post',
              dataType: 'json',
              url: "{{url('Points/get_point_data')}}",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              data: this.searchform,
            });
            this.point_records = resp.records_show;
            this.records_total = resp.records_total;
            this.current_points = resp.current_points;

            Vue.toasted.show("{{Lang::get('載入完成')}}",{duration:1500, className: ["toasted-primary", 'bg-success']}); 
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
        },

        go_transfer: async function($event){
          $event.preventDefault();
          $confirm_msg = "{{Lang::get('確定轉移增值積分')}}?\n";
          // $confirm_msg +="{{Lang::get('來源')}}:"+this.transferform.from+"\n";
          $confirm_msg +="{{Lang::get('目標')}}:"+this.transferform.to+"\n";
          $confirm_msg +="{{Lang::get('轉移數量')}}:"+this.transferform.num+"\n";
          if(confirm($confirm_msg)){
            $('#body_block').show();
            await this.transfer_point_increasable();
            $('#body_block').hide();
          }
        },
        transfer_point_increasable: async function(){
          try {
            resp = await $.ajax({
              type: 'post',
              dataType: 'json',
              url: "{{url('Points/transfer_point_increasable')}}",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              data: this.transferform,
            });
            var vt_class = resp.code==1 ? vt_success_obj : vt_error_obj;
            Vue.toasted.show(resp.msg, vt_class);
            if(resp.code==1){
              this.transferform.num = '';
              this.transferform.msg = '';

              this.searchform.page = 1;
              await this.get_records();
            }
          } catch (error) {
            Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
          }
        },


        go_point_increasable_to_point: async function($event){
          $event.preventDefault();
          $confirm_msg = "{{Lang::get('確定將增值積分轉換成現金積分')}}?\n";
          $confirm_msg +="{{Lang::get('轉移數量')}}:"+this.to_point_num+"\n";
          if(confirm($confirm_msg)){
            $('#body_block').show();
            try {
              resp = await $.ajax({
                type: 'post',
                dataType: 'json',
                url: "{{url('Points/point_increasable_to_point')}}",
                headers: {
                  'X-CSRF-Token': csrf_token 
                },
                data: {
                  to_point_num: this.to_point_num,
                },
              });
              var vt_class = resp.code==1 ? vt_success_obj : vt_error_obj;
              Vue.toasted.show(resp.msg, vt_class);
              if(resp.code==1){
                this.to_point_num = '';

                this.searchform.page = 1;
                await this.get_records();
              }
            } catch (error) {
              Vue.toasted.show(error,{duration:1500, className: ["toasted-primary", 'bg-danger']});
            }
            $('#body_block').hide();
          }
        },
      },
    });
  </script>
@endsection