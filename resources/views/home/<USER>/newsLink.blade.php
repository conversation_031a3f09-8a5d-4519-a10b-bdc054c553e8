
@if(empty(config('control.close_function_current')['最新消息']))
    <section id="newLink_area" class="announcementRow container" v-if="newList.length>0">
        <div class="announcementBox">
            <div id="announcementCarousel">
                <div v-for="item in newList">
                    <a :href="'{{url('News/news_c')}}?id=' + item.id">
                        <span v-text="item.time" class="news-time"></span>
                        <span v-text="item.title"></span>
                    </a>
                </div>
            </div>
            <a class="more" href="{{url('News/news')}}">{{Lang::get('更多')}} <i class="bi bi-chevron-right"></i></a>
        </div>
    </section>

    <script src="{{__PUBLIC__}}/js/announcementCarousel.js"></script>
    <script type="text/javascript">
        $( document ).ready(function() {
            /*最新消息*/
            var data_newLink_area = { newList:[], };
            var newLink_areaVM = new Vue({
                el: '#newLink_area',
                data: data_newLink_area,
                methods:{
                    newsLink: function(){
                        $.ajax({
                            type: "GET",
                            dataType: "json",
                            url: "{{url('Ajax/newslink')}}",
                            success: function (data) {
                                for (i in data) {
                                    newLink_areaVM.newList.push({
                                        link: "{{url('News/news_c')}}?id=" + data.id, //link
                                        id: data[i].id,
                                        title: data[i].title,
                                        time: data[i].time,
                                    })
                                }
                            }
                        });
                    },
                },
            });
            newLink_areaVM.newsLink();
        });
    </script>
@endif


