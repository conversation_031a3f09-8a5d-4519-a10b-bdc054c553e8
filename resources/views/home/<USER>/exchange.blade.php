@extends('home.Public.mainTpl')

@section('title'){{$data['frontend_menu']['consumption']['second_menu']['gift']['name']}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection

@section('css')
    <style type="text/css">
        .table th, .table td{
            vertical-align: middle;
        }
    </style>
@endsection

@section('content')
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
                <li><a href="{{url('Consumption/exchange')}}">{{$data['frontend_menu']['consumption']['second_menu']['gift']['name']}}</a></li>
            </ul>
        </div>
    </section>

    <section class="buyform container max-wideVersion productPublic">
        <div id="itemBox" class="memberInforBox">
            <div id="leftBox">
                <!-- /////////////////////////////////////////// -->
                @include('home.Public.member_menu')
                <!-- /////////////////////////////////////////// -->
            </div>
            <div id="rightContentBox" class="innerPageBox memberContentBox">
                <div class="paddingSpacing">
                    <div class="memberTop">
                        <div class="titleBox">
                            <div class="title">
                                <h3>{{$data['frontend_menu']['consumption']['second_menu']['gift']['name']}}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="memberMiddle"> 
                        <div>
                            <h3 class="subtitle">{{Lang::get('目前累積消費')}}：
                                <span class="bonusNum">                                           
                                    {{config('extra.shop.dollar_symbol')}}<span>{{$data['total_dollar']}}</span>
                                </span>

                                <button id="scan_btn" style="display:none" class="btn btn-success ml-3">{{Lang::get('掃code')}}</button>
                            </h3>
                        </div>
                        <table class="orderTable table table-striped table-bordered table-rwd">
                            <thead>
                                <tr class="tr-only-hide">
                                    <th>{{Lang::get('贈品圖片')}}</th>
                                    <th>{{Lang::get('贈品說明')}}</th>
                                    <th>{{Lang::get('需求累積消費')}}</th>
                                    <th style="width: 160px;">{{Lang::get('領取')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($data['datas'] as $vo)
                                    @if($vo)
                                        <tr>
                                            <td data-th="{{Lang::get('贈品圖片')}}">
                                                @if($vo['pic'])
                                                    <img src="{{__PUBLIC__}}/{{$vo['pic']}}" style="width: 75px;">
                                                @endif
                                            </td>
                                            <td data-th="{{Lang::get('贈品說明')}}">{{$vo['name']}}</td>
                                            <td data-th="{{Lang::get('需求累積消費')}}">{{config('extra.shop.dollar_symbol')}}{{$vo['price']}}</td>
                                            <td data-th="{{Lang::get('領取')}}">
                                                @if($vo['ex_date'] == '')
                                                    @if($data['total_dollar'] >= $vo['price']) 
                                                        <button class="btn btn-warning" onclick="get_gift({{$vo['id']}})">{{Lang::get('領取')}}</button>
                                                    @else
                                                        <button class="btn btn-warning disabled">{{Lang::get('領取')}}</button>
                                                    @endif
                                                @else
                                                    {{$vo['ex_date']}}
                                                @endif
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('Modal')
@endsection

@section('ownJS')
    <script type="text/javascript">
        /*領取*/
        function get_gift(exchange_id=""){
            if(!exchange_id){ alert("{{Lang::get('請選擇要領取的贈品')}}"); return; }

            $.ajax({
                url: "{{url('Consumption/get_exchange_gift')}}",
                method: "POST",
                data:{
                    'exchange_id': exchange_id,
                },
                success: function(resp){
                    alert(resp.msg);
                    if(resp.code==1){
                        location.reload();
                    }
                },
            })
        }
    </script>
    <script src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    <script type="text/javascript">
        $(document).ready(function(){
            var liffID = "{{$data['LIFF_ID']}}";
            if(liffID){
                liff.init({
                    liffId: liffID
                }).then(function() {
                    console.log('LIFF init');

                    // 這邊開始寫使用其他功能
                    $('#scan_btn').css('display', 'inline-block');
                    $('#scan_btn').on('click', function(){
                        liff.scanCode().then(function(res) {
                            // for (var i = 0; i < Object.keys(res).length; i++) {
                            //     alert(Object.keys(res)[i]);
                            //     alert(res[Object.keys(res)[i]]);
                            // }
                            if(res.value){
                                location.href= res.value;
                            }
                        })
                        .catch(function(error) {
                            console.log(error);
                            alert("{{Lang::get('如欲使用掃code功能，請利用店家line@帳戶進入此頁面')}}");
                            $('#scan_btn').css('display', 'none');
                        });
                    });

                }).catch(function(error) {
                    console.log(error);
                });
            }
        })
    </script>
@endsection