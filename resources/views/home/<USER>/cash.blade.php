@extends('home.Public.mainTpl')
@section('title'){{Lang::get('招募會員列表')}} - {{Lang::get('會員專區')}} | {{$data['seo'][0]['title']}}@endsection
@section('css')
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.css" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
  <style type="text/css">
    #progress-container {
      position: relative;
      width: 100%;
      height: 10px;
      background-color: #D9D9D9;
      border-radius: 20px;
      overflow: hidden;
    }
    #progress-bar {
      height: 100%;
      background-color:var(--sub-color);
      width: 0;
      border-radius:20px;
      transition: width 0.5s;
    }
    #progress-text {
      font-weight: bold;
      margin-bottom: 5px;
      font-size: 1.2em;
    }
    #progress-text span{ color:#707070;font-weight:400;}
    #progress-text .current_price{font-size:18px;color: rgba(0, 0, 0, .87);font-weight: 600;}
    .total_range span{color:var(--sub-color);font-size:16px; font-weight:600;}
    
  </style>
@endsection

@section('content')
  <section class="directoryRow">
    <div class="container">
      <ul>
        <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
        <li><a href="{{url('Member/member')}}">{{Lang::get('會員專區')}}</a></li>
        <li><a href="{{url('Member/down_line')}}">{{Lang::get('積分提現')}}</a></li>
      </ul>
    </div>
  </section>
  <section class="container max-wideVersion productPublic">
    <div id="itemBox" class="memberInforBox">
      <div id="leftBox">
        <!-- /////////////////////////////////////////// -->
        @include('home.Public.member_menu')
        <!-- /////////////////////////////////////////// -->
      </div>
      <div id="rightContentBox" class="innerPageBox memberContentBox">
        <div class="paddingSpacing">
          <div class="pack">
            <div class="memberTop">
              <div class="titleBox">
                <div class="title">
                  <h3>{{Lang::get('積分提現')}}</h3>
                </div>
              </div>
              <ul class="nav tabNavBox justify-content-end">
                <li class="nav-item">
                  <a class="invisible" data-toggle="modal" data-target="#descriptionContModel">{{Lang::get('使用說明')}}</a>
                </li>
              </ul>
            </div>
            <div class="memberMiddle">
              <div>
                <h3 class="subtitle">{{Lang::get('點數統計')}}</h3>
                <p>
                  {{Lang::get('目前累計紅利')}}：
                  <span class="bonusNum">
                    <span>{{number_format($data['userD']['point'], 3)}}</span>
                  </span>
                </p>
              </div>
              <hr>
              <!-- //////////////////////////////////// -->
            
              <div class="row member_point_box">
                <div class="col-lg-12 col-12">
                  <div class="headingBox">
                    <h3 class="subtitle">
                      <span class="mr-3">{{Lang::get('提領紀錄')}}</span>
                      <button class="togo btn ml-2 ml-lg-2 mt-0 mt-lg-0" @click="open_cash_modal">{{Lang::get('我要提現')}}</button>
                  </h3>
                  </div>
                  <div class="mb-4">
                    <form action="{{url('Dividend/cash')}}" method="get" enctype="multipart/form-data">
                      @csrf
                      <div class="time-select-box">
                        <p class="mb-2">時間搜尋</p>
                        <div class="d-flex align-items-center mb-4">
                          <input name="date_s" type="date" class="form-control col-lg-3" value="{{request()->get('date_s')}}">
                          ~
                          <input name="date_e" type="date" class="form-control col-lg-3" value="{{request()->get('date_e')}}">
                          <button type="submit" class="togo btn ml-2 ml-lg-2 mt-0 mt-lg-0">{{Lang::get('搜尋')}}</button>
                        </div>
                      </div>
                    </form>
                    <table class="orderTable table table-striped table-bordered table-rwd">
                      <thead>
                        <tr class="tr-only-hide">
                          <th>{{Lang::get('日期')}}</th>
                          <th>{{Lang::get('幣別')}}</th>
                          <th>{{Lang::get('金額')}}</th>
                          <th>{{Lang::get('給付日期')}}</th>
                        </tr>
                      </thead>
                      <tbody>
                        @if(empty($data['cash_record']) == false)
                        @foreach($data['cash_record'] as $vo)
                          <tr>
                            <td data-th="{{Lang::get('日期')}}">{{date('Y-m-d H:i', $vo->time_create)}}</td>
                            <td data-th="{{Lang::get('幣別')}}">{{$vo->currency}}</td>
                            <td data-th="{{Lang::get('金額')}}">{{number_format($vo->num)}}</td>
                            <td data-th="{{Lang::get('給付日期')}}">
                              @if($vo->time_pay)
                                {{date('Y-m-d H:i', $vo->time_pay)}}
                              @endif
                            </td>
                          </tr>
                        @endforeach
                        @endif
                      </tbody>
                    </table>
                    <div class="row paginationBox">
                      <div class="col-12 boxCenter">
                        {{$data['cash_record']->links('pagination::default')}}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        
          <!-- 新增提現紀錄 -->
          <a id="cashModel_btn" data-toggle="modal" data-target="#cashModel" class="d-none"></a>
          <div class="modal fade shoppingCart smallMOdel cashModel" id="cashModel" tabindex="-1" role="dialog" aria-labelledby="cashModelTitle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
              <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-header">
                  <h5 class="modal-title" id="cashModelTitle">{{Lang::get('我要提現')}}</h5>
                </div>
                <div class="modal-body">
                  <div>
                    提醒，現金積分轉換成現金時，將：
                    <ul>
                      <li class="text-danger">1. 扣除代扣稅費: {{$data['charge_tax_txt']}}</li>
                      <li class="text-danger">2. 扣除轉入資金池: {{$data['charge_pool_txt']}} (回歸資金池，讓增值金分升值)</li>
                      <li>3. 實領金額為扣除上數比率後取整數</li>
                    </ul>
                  </div>
                  <div class="form-group">
                    請輸入要轉換的現金積分數量：
                    <input type="number" step="1" min="1" class="form-control text-right" v-model="num">
                  </div>
                  <div class="text-center">
                    <button class="togo btn ml-2 ml-lg-2 mt-0 mt-lg-0" @click="create_record">{{Lang::get('送出')}}</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </section>
@endsection

@section('ownJS')
  <script>
    var rightContentBox_data = {      
      num: 0,
    };
    var rightContentBoxVM  = new Vue({
      el: '#rightContentBox',
      data: rightContentBox_data,
      watch:{
        currency: function(nv, ov){
          if(nv!=ov){
            keys = Object.keys(this.exchange_rate_set);
            if(keys.indexOf(nv)!=-1){
              if(location.search){
                redirect_url = location.href + '&currency='+nv;
              }else{
                redirect_url = location.href + '?currency='+nv;
              }
              location.href = redirect_url;
            }
          }
        },
      },
      methods:{
        open_cash_modal(){
          $('#cashModel_btn').click();
        },
        async create_record(){
          if(!confirm('確定提現?\n使用積分數:'+this.num)){ return; }
          $('#body_block').show();
          try {
            resp = await $.ajax({
              type: "POST",
              headers: {
                'X-CSRF-Token': csrf_token 
              },
              dataType: "json",
              url: "{{url('Dividend/create_record')}}",
              data: {
                num: this.num,
              },
          });
          } catch (error) {
            $('#body_block').hide();return;
          }
          vt_class = resp.code ? vt_success_obj : vt_error_obj;
          Vue.toasted.show(resp.msg, vt_class);
          if(resp.code){
            setTimeout(() => {
              location.reload();
            }, 250);
          }else{
            $('#body_block').hide();
          }
        },
      },
    });
  </script>
@endsection