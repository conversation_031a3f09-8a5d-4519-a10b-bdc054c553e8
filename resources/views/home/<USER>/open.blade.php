<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>LINE Auth</title>

    <script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
</head>
<body>
    <script>
        var url = 'https://api.line.me/oauth2/v2.1/token';
        $.ajax({
            url     : url,
            dataType: 'json',
            headers: {
                'X-CSRF-Token': '{{csrf_token()}}' 
            },
            type    : 'POST',
            data : { 
                grant_type : 'authorization_code',
                code : '{{$data["code"]}}',
                redirect_uri : '{{$data["line_url_open"]}}',
                client_id : '{{$data["client_id"]}}',
                client_secret :'{{$data["client_secret"]}}',
            },
            contentType:"application/x-www-form-urlencoded; charset=UTF-8",
            success: function(data){
                profile(data.access_token);
            }
        });

        function profile(access_token){
            var url = 'https://api.line.me/v2/profile';
            $.ajax({
                url     : url,
                dataType: 'json',
                type    : 'GET',
                headers:{'Content-Type':'application/json;charset=utf8','Authorization':'Bearer '+access_token},
                success: function(data) {
                    $.ajax({
                        url     : "{{url('Login/line_open')}}",
                        dataType: 'json',
                        headers: {
                            'X-CSRF-Token': '{{csrf_token()}}' 
                        },
                        type    : 'POST',
                        data : { U3: data.userId,ig:data.displayName},
                        contentType:"application/x-www-form-urlencoded; charset=UTF-8",
                        success: function(result){
                            if(result.code==1){
                                alert("{{Lang::get('成功合併，請重新登入')}}");
                                location.href="{{url('Login/Logout')}}";
                            }else{
                                alert(result.msg);
                                location.href="{{url('Member/member')}}";
                            }
                        }
                    });
                }
            });
        }
    </script>
</body>