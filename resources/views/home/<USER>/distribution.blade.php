@extends('home.Public.mainTpl')
@section('title'){{$data['frontend_menu']['distribution']['name']}} | {{$data['seo'][0]['title']}}@endsection

@section("css")
    <style type="text/css">
        img.sub_pic{
            width: auto;
        }
    </style>
@endsection

@section("mycode")
<!-- js-code -->
@endsection

@section('content')
    @if(empty(config('control.close_function_current')['banner管理']))
        @if($data['frontend_menu']['distribution']['pic'])
        <section class="page-banner" style="background-image: url({{__PUBLIC__}}/$data['frontend_menu']['distribution']['pic']}});">
            <div class="container">
                <h2 class="page-title" style="color:{{$data['frontend_menu']['distribution']['text_color']}}">
                    {{$data['frontend_menu']['distribution']['name']}}</h2>
                <!-- <span class="enText">{{$data['frontend_menu'][$data['controller']]['en_name']}}</span> -->
            </div>
        </section>
        @endif
    @endif
    <section class="directoryRow">
        <div class="container">
            <ul>
                <li><a href="{{url('Index/index')}}">{{Lang::get('首頁')}}</a></li>
                <li id="aboutDirectoryText">
                    <a href="{{url('Distribution/distribution')}}?id={{$data['stronghold']['id']}}">
                        {{$data['frontend_menu']['distribution']['name']}}
                    </a>
                </li>
            </ul>
        </div>
    </section>
    

    <section class="container max-wideVersion min-heightVersion aboutUsBox strongholdBox">
        <!-- announcement start -->
        @include('home.Public.newsLink')
        <!-- announcement end -->

        <div class="row">
            <ul class="nav tabNavBox justify-content-center">
                @foreach($data['distrmenu'] as $vo)
                <li class="nav-item" id="distrmenu{{$vo['id']}}">
                    <a class="nav-link " href="{{url('Distribution/distribution')}}?id={{$vo['id']}}">{{$vo['title']}}</a>
                </li>
                @endforeach
            </ul>
            <div class="col-12">
                <!-- ///////////// -->
                <div class="activityGrid">
                    <!-- item strat -->
                    @if(empty($data['strongholds']) == false)
                    @foreach($data['strongholds'] as $vo)
                    <div class="activityGridItem">
                        <div class="bgColor">
                            
                            <a target="_blank" href="{{$vo->url}}">
                                @if($vo->pic != '')
                                <div class="activityBgImg" style="background-image: url('{{__PUBLIC__}}{{$vo->pic}}');">
                                </div>
                                @endif
                                <div class="content">
                                    @if($vo->title != '')
                                    <h3>{{$vo->title}}</h3>
                                    @endif
                                    @if($vo->content != '')
                                    <p>{{$vo->content}}</p>
                                    @endif
                                </div>
                               
                                <div class="sub_pic_area text-right">
                                    @foreach($vo->sub_pics as $pic)
                                        <img class="sub_pic" src="{{__PUBLIC__}}/$pic}}">
                                    @endforeach
                                </div>
                            </a>
                            <!--
                            <div class="moreBox d-flex justify-content-center">
                                <a target="_blank" href="{{$vo->url}}" class="more">{{Lang::get('查看')}}</a>
                            </div>
                            -->
                        </div>
                    </div>
                    @endforeach
                    @endif
                    <!-- item end -->
                </div>
                <!-- ///////////// -->
            </div>
        </div>
        <div class="row paginationBox">
            <div class="col-12 boxCenter">
                {{$data['strongholds']->links('pagination::default')}}
            </div>
        </div>
    </section>
@endsection

@section('ownJS')
    <script src="{{__PUBLIC__}}/js/masonry-pkgd-min.js"></script>
    <script src="{{__PUBLIC__}}/js/imagesloaded-pkgd.js"></script>
    <script>
        function activityGrid() {
            var $grid = $('.activityGrid').masonry({
                itemSelector: '.activityGridItem',
                percentPosition: true,
                // gutter: 5
            });
            $grid.imagesLoaded().progress(function() {
                $grid.masonry();
            });
        };
        activityGrid();

        /*自動active*/
        $(document).ready(function() {
            var distributionId =Request["id"];
            var selector ="#distrmenu" +distributionId;
            $(selector).addClass('active')
        });
    </script>
@endsection

